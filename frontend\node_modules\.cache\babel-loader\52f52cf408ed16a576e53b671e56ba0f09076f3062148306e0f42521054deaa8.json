{"ast": null, "code": "import * as React from 'react';\nimport { stylesheetSingleton } from './singleton';\n/**\n * creates a hook to control style singleton\n * @see {@link styleSingleton} for a safer component version\n * @example\n * ```tsx\n * const useStyle = styleHookSingleton();\n * ///\n * useStyle('body { overflow: hidden}');\n */\nexport var styleHookSingleton = function () {\n  var sheet = stylesheetSingleton();\n  return function (styles, isDynamic) {\n    React.useEffect(function () {\n      sheet.add(styles);\n      return function () {\n        sheet.remove();\n      };\n    }, [styles && isDynamic]);\n  };\n};", "map": {"version": 3, "names": ["React", "stylesheetSingleton", "styleHookSingleton", "sheet", "styles", "isDynamic", "useEffect", "add", "remove"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/react-style-singleton/dist/es2015/hook.js"], "sourcesContent": ["import * as React from 'react';\nimport { stylesheetSingleton } from './singleton';\n/**\n * creates a hook to control style singleton\n * @see {@link styleSingleton} for a safer component version\n * @example\n * ```tsx\n * const useStyle = styleHookSingleton();\n * ///\n * useStyle('body { overflow: hidden}');\n */\nexport var styleHookSingleton = function () {\n    var sheet = stylesheetSingleton();\n    return function (styles, isDynamic) {\n        React.useEffect(function () {\n            sheet.add(styles);\n            return function () {\n                sheet.remove();\n            };\n        }, [styles && isDynamic]);\n    };\n};\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,mBAAmB,QAAQ,aAAa;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,kBAAkB,GAAG,SAAAA,CAAA,EAAY;EACxC,IAAIC,KAAK,GAAGF,mBAAmB,CAAC,CAAC;EACjC,OAAO,UAAUG,MAAM,EAAEC,SAAS,EAAE;IAChCL,KAAK,CAACM,SAAS,CAAC,YAAY;MACxBH,KAAK,CAACI,GAAG,CAACH,MAAM,CAAC;MACjB,OAAO,YAAY;QACfD,KAAK,CAACK,MAAM,CAAC,CAAC;MAClB,CAAC;IACL,CAAC,EAAE,CAACJ,MAAM,IAAIC,SAAS,CAAC,CAAC;EAC7B,CAAC;AACL,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}