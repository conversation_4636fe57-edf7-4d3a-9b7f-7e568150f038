{"ast": null, "code": "import { isNode } from 'detect-node-es';\nexport var env = {\n  isNode: isNode,\n  forceCache: false\n};", "map": {"version": 3, "names": ["isNode", "env", "forceCache"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/use-sidecar/dist/es2015/env.js"], "sourcesContent": ["import { isNode } from 'detect-node-es';\nexport var env = {\n    isNode: isNode,\n    forceCache: false,\n};\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,gBAAgB;AACvC,OAAO,IAAIC,GAAG,GAAG;EACbD,MAAM,EAAEA,MAAM;EACdE,UAAU,EAAE;AAChB,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}