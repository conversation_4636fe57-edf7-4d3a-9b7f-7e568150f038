{"ast": null, "code": "import { toArray } from './array';\nimport { isAutoFocusAllowedCached, isVisibleCached, notHiddenInput } from './is';\nimport { orderByTabIndex } from './tabOrder';\nimport { getFocusables, getParentAutofocusables } from './tabUtils';\n/**\n * given list of focusable elements keeps the ones user can interact with\n * @param nodes\n * @param visibilityCache\n */\nexport var filterFocusable = function (nodes, visibilityCache) {\n  return toArray(nodes).filter(function (node) {\n    return isVisibleCached(visibilityCache, node);\n  }).filter(function (node) {\n    return notHiddenInput(node);\n  });\n};\nexport var filterAutoFocusable = function (nodes, cache) {\n  if (cache === void 0) {\n    cache = new Map();\n  }\n  return toArray(nodes).filter(function (node) {\n    return isAutoFocusAllowedCached(cache, node);\n  });\n};\n/**\n * !__WARNING__! Low level API.\n * @returns all tabbable nodes\n *\n * @see {@link getFocusableNodes} to get any focusable element\n *\n * @param topNodes - array of top level HTMLElements to search inside\n * @param visibilityCache - an cache to store intermediate measurements. Expected to be a fresh `new Map` on every call\n */\nexport var getTabbableNodes = function (topNodes, visibilityCache, withGuards) {\n  return orderByTabIndex(filterFocusable(getFocusables(topNodes, withGuards), visibilityCache), true, withGuards);\n};\n/**\n * !__WARNING__! Low level API.\n *\n * @returns anything \"focusable\", not only tabbable. The difference is in `tabIndex=-1`\n * (without guards, as long as they are not expected to be ever focused)\n *\n * @see {@link getTabbableNodes} to get only tabble nodes element\n *\n * @param topNodes - array of top level HTMLElements to search inside\n * @param visibilityCache - an cache to store intermediate measurements. Expected to be a fresh `new Map` on every call\n */\nexport var getFocusableNodes = function (topNodes, visibilityCache) {\n  return orderByTabIndex(filterFocusable(getFocusables(topNodes), visibilityCache), false);\n};\n/**\n * return list of nodes which are expected to be auto-focused\n * @param topNode\n * @param visibilityCache\n */\nexport var parentAutofocusables = function (topNode, visibilityCache) {\n  return filterFocusable(getParentAutofocusables(topNode), visibilityCache);\n};\n/*\n * Determines if element is contained in scope, including nested shadow DOMs\n */\nexport var contains = function (scope, element) {\n  if (scope.shadowRoot) {\n    return contains(scope.shadowRoot, element);\n  } else {\n    if (Object.getPrototypeOf(scope).contains !== undefined && Object.getPrototypeOf(scope).contains.call(scope, element)) {\n      return true;\n    }\n    return toArray(scope.children).some(function (child) {\n      var _a;\n      if (child instanceof HTMLIFrameElement) {\n        var iframeBody = (_a = child.contentDocument) === null || _a === void 0 ? void 0 : _a.body;\n        if (iframeBody) {\n          return contains(iframeBody, element);\n        }\n        return false;\n      }\n      return contains(child, element);\n    });\n  }\n};", "map": {"version": 3, "names": ["toArray", "isAutoFocusAllowedCached", "isVisibleCached", "notHiddenInput", "orderByTabIndex", "getFocusables", "getParentAutofocusables", "filterFocusable", "nodes", "visibilityCache", "filter", "node", "filterAutoFocusable", "cache", "Map", "getTabbableNodes", "topNodes", "<PERSON><PERSON><PERSON><PERSON>", "getFocusableNodes", "parentAutofocusables", "topNode", "contains", "scope", "element", "shadowRoot", "Object", "getPrototypeOf", "undefined", "call", "children", "some", "child", "_a", "HTMLIFrameElement", "iframeBody", "contentDocument", "body"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/focus-lock/dist/es2015/utils/DOMutils.js"], "sourcesContent": ["import { toArray } from './array';\nimport { isAutoFocusAllowedCached, isVisibleCached, notHiddenInput } from './is';\nimport { orderByTabIndex } from './tabOrder';\nimport { getFocusables, getParentAutofocusables } from './tabUtils';\n/**\n * given list of focusable elements keeps the ones user can interact with\n * @param nodes\n * @param visibilityCache\n */\nexport var filterFocusable = function (nodes, visibilityCache) {\n    return toArray(nodes)\n        .filter(function (node) { return isVisibleCached(visibilityCache, node); })\n        .filter(function (node) { return notHiddenInput(node); });\n};\nexport var filterAutoFocusable = function (nodes, cache) {\n    if (cache === void 0) { cache = new Map(); }\n    return toArray(nodes).filter(function (node) { return isAutoFocusAllowedCached(cache, node); });\n};\n/**\n * !__WARNING__! Low level API.\n * @returns all tabbable nodes\n *\n * @see {@link getFocusableNodes} to get any focusable element\n *\n * @param topNodes - array of top level HTMLElements to search inside\n * @param visibilityCache - an cache to store intermediate measurements. Expected to be a fresh `new Map` on every call\n */\nexport var getTabbableNodes = function (topNodes, visibilityCache, withGuards) {\n    return orderByTabIndex(filterFocusable(getFocusables(topNodes, withGuards), visibilityCache), true, withGuards);\n};\n/**\n * !__WARNING__! Low level API.\n *\n * @returns anything \"focusable\", not only tabbable. The difference is in `tabIndex=-1`\n * (without guards, as long as they are not expected to be ever focused)\n *\n * @see {@link getTabbableNodes} to get only tabble nodes element\n *\n * @param topNodes - array of top level HTMLElements to search inside\n * @param visibilityCache - an cache to store intermediate measurements. Expected to be a fresh `new Map` on every call\n */\nexport var getFocusableNodes = function (topNodes, visibilityCache) {\n    return orderByTabIndex(filterFocusable(getFocusables(topNodes), visibilityCache), false);\n};\n/**\n * return list of nodes which are expected to be auto-focused\n * @param topNode\n * @param visibilityCache\n */\nexport var parentAutofocusables = function (topNode, visibilityCache) {\n    return filterFocusable(getParentAutofocusables(topNode), visibilityCache);\n};\n/*\n * Determines if element is contained in scope, including nested shadow DOMs\n */\nexport var contains = function (scope, element) {\n    if (scope.shadowRoot) {\n        return contains(scope.shadowRoot, element);\n    }\n    else {\n        if (Object.getPrototypeOf(scope).contains !== undefined &&\n            Object.getPrototypeOf(scope).contains.call(scope, element)) {\n            return true;\n        }\n        return toArray(scope.children).some(function (child) {\n            var _a;\n            if (child instanceof HTMLIFrameElement) {\n                var iframeBody = (_a = child.contentDocument) === null || _a === void 0 ? void 0 : _a.body;\n                if (iframeBody) {\n                    return contains(iframeBody, element);\n                }\n                return false;\n            }\n            return contains(child, element);\n        });\n    }\n};\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,SAAS;AACjC,SAASC,wBAAwB,EAAEC,eAAe,EAAEC,cAAc,QAAQ,MAAM;AAChF,SAASC,eAAe,QAAQ,YAAY;AAC5C,SAASC,aAAa,EAAEC,uBAAuB,QAAQ,YAAY;AACnE;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,eAAe,GAAG,SAAAA,CAAUC,KAAK,EAAEC,eAAe,EAAE;EAC3D,OAAOT,OAAO,CAACQ,KAAK,CAAC,CAChBE,MAAM,CAAC,UAAUC,IAAI,EAAE;IAAE,OAAOT,eAAe,CAACO,eAAe,EAAEE,IAAI,CAAC;EAAE,CAAC,CAAC,CAC1ED,MAAM,CAAC,UAAUC,IAAI,EAAE;IAAE,OAAOR,cAAc,CAACQ,IAAI,CAAC;EAAE,CAAC,CAAC;AACjE,CAAC;AACD,OAAO,IAAIC,mBAAmB,GAAG,SAAAA,CAAUJ,KAAK,EAAEK,KAAK,EAAE;EACrD,IAAIA,KAAK,KAAK,KAAK,CAAC,EAAE;IAAEA,KAAK,GAAG,IAAIC,GAAG,CAAC,CAAC;EAAE;EAC3C,OAAOd,OAAO,CAACQ,KAAK,CAAC,CAACE,MAAM,CAAC,UAAUC,IAAI,EAAE;IAAE,OAAOV,wBAAwB,CAACY,KAAK,EAAEF,IAAI,CAAC;EAAE,CAAC,CAAC;AACnG,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAII,gBAAgB,GAAG,SAAAA,CAAUC,QAAQ,EAAEP,eAAe,EAAEQ,UAAU,EAAE;EAC3E,OAAOb,eAAe,CAACG,eAAe,CAACF,aAAa,CAACW,QAAQ,EAAEC,UAAU,CAAC,EAAER,eAAe,CAAC,EAAE,IAAI,EAAEQ,UAAU,CAAC;AACnH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,iBAAiB,GAAG,SAAAA,CAAUF,QAAQ,EAAEP,eAAe,EAAE;EAChE,OAAOL,eAAe,CAACG,eAAe,CAACF,aAAa,CAACW,QAAQ,CAAC,EAAEP,eAAe,CAAC,EAAE,KAAK,CAAC;AAC5F,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIU,oBAAoB,GAAG,SAAAA,CAAUC,OAAO,EAAEX,eAAe,EAAE;EAClE,OAAOF,eAAe,CAACD,uBAAuB,CAACc,OAAO,CAAC,EAAEX,eAAe,CAAC;AAC7E,CAAC;AACD;AACA;AACA;AACA,OAAO,IAAIY,QAAQ,GAAG,SAAAA,CAAUC,KAAK,EAAEC,OAAO,EAAE;EAC5C,IAAID,KAAK,CAACE,UAAU,EAAE;IAClB,OAAOH,QAAQ,CAACC,KAAK,CAACE,UAAU,EAAED,OAAO,CAAC;EAC9C,CAAC,MACI;IACD,IAAIE,MAAM,CAACC,cAAc,CAACJ,KAAK,CAAC,CAACD,QAAQ,KAAKM,SAAS,IACnDF,MAAM,CAACC,cAAc,CAACJ,KAAK,CAAC,CAACD,QAAQ,CAACO,IAAI,CAACN,KAAK,EAAEC,OAAO,CAAC,EAAE;MAC5D,OAAO,IAAI;IACf;IACA,OAAOvB,OAAO,CAACsB,KAAK,CAACO,QAAQ,CAAC,CAACC,IAAI,CAAC,UAAUC,KAAK,EAAE;MACjD,IAAIC,EAAE;MACN,IAAID,KAAK,YAAYE,iBAAiB,EAAE;QACpC,IAAIC,UAAU,GAAG,CAACF,EAAE,GAAGD,KAAK,CAACI,eAAe,MAAM,IAAI,IAAIH,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACI,IAAI;QAC1F,IAAIF,UAAU,EAAE;UACZ,OAAOb,QAAQ,CAACa,UAAU,EAAEX,OAAO,CAAC;QACxC;QACA,OAAO,KAAK;MAChB;MACA,OAAOF,QAAQ,CAACU,KAAK,EAAER,OAAO,CAAC;IACnC,CAAC,CAAC;EACN;AACJ,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}