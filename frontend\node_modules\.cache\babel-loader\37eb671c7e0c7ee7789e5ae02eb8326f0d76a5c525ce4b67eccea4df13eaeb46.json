{"ast": null, "code": "import { __assign, __rest } from \"tslib\";\nimport * as React from 'react';\nvar SideCar = function (_a) {\n  var sideCar = _a.sideCar,\n    rest = __rest(_a, [\"sideCar\"]);\n  if (!sideCar) {\n    throw new Error('Sidecar: please provide `sideCar` property to import the right car');\n  }\n  var Target = sideCar.read();\n  if (!Target) {\n    throw new Error('Sidecar medium not found');\n  }\n  return React.createElement(Target, __assign({}, rest));\n};\nSideCar.isSideCarExport = true;\nexport function exportSidecar(medium, exported) {\n  medium.useMedium(exported);\n  return SideCar;\n}", "map": {"version": 3, "names": ["__assign", "__rest", "React", "SideCar", "_a", "sideCar", "rest", "Error", "Target", "read", "createElement", "isSideCarExport", "exportSidecar", "medium", "exported", "useMedium"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/use-sidecar/dist/es2015/exports.js"], "sourcesContent": ["import { __assign, __rest } from \"tslib\";\nimport * as React from 'react';\nvar SideCar = function (_a) {\n    var sideCar = _a.sideCar, rest = __rest(_a, [\"sideCar\"]);\n    if (!sideCar) {\n        throw new Error('Sidecar: please provide `sideCar` property to import the right car');\n    }\n    var Target = sideCar.read();\n    if (!Target) {\n        throw new Error('Sidecar medium not found');\n    }\n    return React.createElement(Target, __assign({}, rest));\n};\nSideCar.isSideCarExport = true;\nexport function exportSidecar(medium, exported) {\n    medium.useMedium(exported);\n    return SideCar;\n}\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AACxC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,IAAIC,OAAO,GAAG,SAAAA,CAAUC,EAAE,EAAE;EACxB,IAAIC,OAAO,GAAGD,EAAE,CAACC,OAAO;IAAEC,IAAI,GAAGL,MAAM,CAACG,EAAE,EAAE,CAAC,SAAS,CAAC,CAAC;EACxD,IAAI,CAACC,OAAO,EAAE;IACV,MAAM,IAAIE,KAAK,CAAC,oEAAoE,CAAC;EACzF;EACA,IAAIC,MAAM,GAAGH,OAAO,CAACI,IAAI,CAAC,CAAC;EAC3B,IAAI,CAACD,MAAM,EAAE;IACT,MAAM,IAAID,KAAK,CAAC,0BAA0B,CAAC;EAC/C;EACA,OAAOL,KAAK,CAACQ,aAAa,CAACF,MAAM,EAAER,QAAQ,CAAC,CAAC,CAAC,EAAEM,IAAI,CAAC,CAAC;AAC1D,CAAC;AACDH,OAAO,CAACQ,eAAe,GAAG,IAAI;AAC9B,OAAO,SAASC,aAAaA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EAC5CD,MAAM,CAACE,SAAS,CAACD,QAAQ,CAAC;EAC1B,OAAOX,OAAO;AAClB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}