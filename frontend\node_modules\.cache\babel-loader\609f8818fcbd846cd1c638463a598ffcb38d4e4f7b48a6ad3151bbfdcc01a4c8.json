{"ast": null, "code": "import { parentAutofocusables } from './DOMutils';\nimport { contains } from './DOMutils';\nimport { asArray } from './array';\nvar getParents = function (node, parents) {\n  if (parents === void 0) {\n    parents = [];\n  }\n  parents.push(node);\n  if (node.parentNode) {\n    getParents(node.parentNode.host || node.parentNode, parents);\n  }\n  return parents;\n};\n/**\n * finds a parent for both nodeA and nodeB\n * @param nodeA\n * @param nodeB\n * @returns {boolean|*}\n */\nexport var getCommonParent = function (nodeA, nodeB) {\n  var parentsA = getParents(nodeA);\n  var parentsB = getParents(nodeB);\n  // tslint:disable-next-line:prefer-for-of\n  for (var i = 0; i < parentsA.length; i += 1) {\n    var currentParent = parentsA[i];\n    if (parentsB.indexOf(currentParent) >= 0) {\n      return currentParent;\n    }\n  }\n  return false;\n};\nexport var getTopCommonParent = function (baseActiveElement, leftEntry, rightEntries) {\n  var activeElements = asArray(baseActiveElement);\n  var leftEntries = asArray(leftEntry);\n  var activeElement = activeElements[0];\n  var topCommon = false;\n  leftEntries.filter(Boolean).forEach(function (entry) {\n    topCommon = getCommonParent(topCommon || entry, entry) || topCommon;\n    rightEntries.filter(Boolean).forEach(function (subEntry) {\n      var common = getCommonParent(activeElement, subEntry);\n      if (common) {\n        if (!topCommon || contains(common, topCommon)) {\n          topCommon = common;\n        } else {\n          topCommon = getCommonParent(common, topCommon);\n        }\n      }\n    });\n  });\n  // TODO: add assert here?\n  return topCommon;\n};\n/**\n * return list of nodes which are expected to be autofocused inside a given top nodes\n * @param entries\n * @param visibilityCache\n */\nexport var allParentAutofocusables = function (entries, visibilityCache) {\n  return entries.reduce(function (acc, node) {\n    return acc.concat(parentAutofocusables(node, visibilityCache));\n  }, []);\n};", "map": {"version": 3, "names": ["parentAutofocusables", "contains", "asArray", "getParents", "node", "parents", "push", "parentNode", "host", "getCommonParent", "nodeA", "nodeB", "parentsA", "parentsB", "i", "length", "currentParent", "indexOf", "getTopCommonParent", "baseActiveElement", "leftEntry", "rightEntries", "activeElements", "leftEntries", "activeElement", "topCommon", "filter", "Boolean", "for<PERSON>ach", "entry", "subEntry", "common", "allParentAutofocusables", "entries", "visibilityCache", "reduce", "acc", "concat"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/focus-lock/dist/es2015/utils/parenting.js"], "sourcesContent": ["import { parentAutofocusables } from './DOMutils';\nimport { contains } from './DOMutils';\nimport { asArray } from './array';\nvar getParents = function (node, parents) {\n    if (parents === void 0) { parents = []; }\n    parents.push(node);\n    if (node.parentNode) {\n        getParents(node.parentNode.host || node.parentNode, parents);\n    }\n    return parents;\n};\n/**\n * finds a parent for both nodeA and nodeB\n * @param nodeA\n * @param nodeB\n * @returns {boolean|*}\n */\nexport var getCommonParent = function (nodeA, nodeB) {\n    var parentsA = getParents(nodeA);\n    var parentsB = getParents(nodeB);\n    // tslint:disable-next-line:prefer-for-of\n    for (var i = 0; i < parentsA.length; i += 1) {\n        var currentParent = parentsA[i];\n        if (parentsB.indexOf(currentParent) >= 0) {\n            return currentParent;\n        }\n    }\n    return false;\n};\nexport var getTopCommonParent = function (baseActiveElement, leftEntry, rightEntries) {\n    var activeElements = asArray(baseActiveElement);\n    var leftEntries = asArray(leftEntry);\n    var activeElement = activeElements[0];\n    var topCommon = false;\n    leftEntries.filter(Boolean).forEach(function (entry) {\n        topCommon = getCommonParent(topCommon || entry, entry) || topCommon;\n        rightEntries.filter(Boolean).forEach(function (subEntry) {\n            var common = getCommonParent(activeElement, subEntry);\n            if (common) {\n                if (!topCommon || contains(common, topCommon)) {\n                    topCommon = common;\n                }\n                else {\n                    topCommon = getCommonParent(common, topCommon);\n                }\n            }\n        });\n    });\n    // TODO: add assert here?\n    return topCommon;\n};\n/**\n * return list of nodes which are expected to be autofocused inside a given top nodes\n * @param entries\n * @param visibilityCache\n */\nexport var allParentAutofocusables = function (entries, visibilityCache) {\n    return entries.reduce(function (acc, node) { return acc.concat(parentAutofocusables(node, visibilityCache)); }, []);\n};\n"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,YAAY;AACjD,SAASC,QAAQ,QAAQ,YAAY;AACrC,SAASC,OAAO,QAAQ,SAAS;AACjC,IAAIC,UAAU,GAAG,SAAAA,CAAUC,IAAI,EAAEC,OAAO,EAAE;EACtC,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IAAEA,OAAO,GAAG,EAAE;EAAE;EACxCA,OAAO,CAACC,IAAI,CAACF,IAAI,CAAC;EAClB,IAAIA,IAAI,CAACG,UAAU,EAAE;IACjBJ,UAAU,CAACC,IAAI,CAACG,UAAU,CAACC,IAAI,IAAIJ,IAAI,CAACG,UAAU,EAAEF,OAAO,CAAC;EAChE;EACA,OAAOA,OAAO;AAClB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAII,eAAe,GAAG,SAAAA,CAAUC,KAAK,EAAEC,KAAK,EAAE;EACjD,IAAIC,QAAQ,GAAGT,UAAU,CAACO,KAAK,CAAC;EAChC,IAAIG,QAAQ,GAAGV,UAAU,CAACQ,KAAK,CAAC;EAChC;EACA,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,QAAQ,CAACG,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;IACzC,IAAIE,aAAa,GAAGJ,QAAQ,CAACE,CAAC,CAAC;IAC/B,IAAID,QAAQ,CAACI,OAAO,CAACD,aAAa,CAAC,IAAI,CAAC,EAAE;MACtC,OAAOA,aAAa;IACxB;EACJ;EACA,OAAO,KAAK;AAChB,CAAC;AACD,OAAO,IAAIE,kBAAkB,GAAG,SAAAA,CAAUC,iBAAiB,EAAEC,SAAS,EAAEC,YAAY,EAAE;EAClF,IAAIC,cAAc,GAAGpB,OAAO,CAACiB,iBAAiB,CAAC;EAC/C,IAAII,WAAW,GAAGrB,OAAO,CAACkB,SAAS,CAAC;EACpC,IAAII,aAAa,GAAGF,cAAc,CAAC,CAAC,CAAC;EACrC,IAAIG,SAAS,GAAG,KAAK;EACrBF,WAAW,CAACG,MAAM,CAACC,OAAO,CAAC,CAACC,OAAO,CAAC,UAAUC,KAAK,EAAE;IACjDJ,SAAS,GAAGhB,eAAe,CAACgB,SAAS,IAAII,KAAK,EAAEA,KAAK,CAAC,IAAIJ,SAAS;IACnEJ,YAAY,CAACK,MAAM,CAACC,OAAO,CAAC,CAACC,OAAO,CAAC,UAAUE,QAAQ,EAAE;MACrD,IAAIC,MAAM,GAAGtB,eAAe,CAACe,aAAa,EAAEM,QAAQ,CAAC;MACrD,IAAIC,MAAM,EAAE;QACR,IAAI,CAACN,SAAS,IAAIxB,QAAQ,CAAC8B,MAAM,EAAEN,SAAS,CAAC,EAAE;UAC3CA,SAAS,GAAGM,MAAM;QACtB,CAAC,MACI;UACDN,SAAS,GAAGhB,eAAe,CAACsB,MAAM,EAAEN,SAAS,CAAC;QAClD;MACJ;IACJ,CAAC,CAAC;EACN,CAAC,CAAC;EACF;EACA,OAAOA,SAAS;AACpB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIO,uBAAuB,GAAG,SAAAA,CAAUC,OAAO,EAAEC,eAAe,EAAE;EACrE,OAAOD,OAAO,CAACE,MAAM,CAAC,UAAUC,GAAG,EAAEhC,IAAI,EAAE;IAAE,OAAOgC,GAAG,CAACC,MAAM,CAACrC,oBAAoB,CAACI,IAAI,EAAE8B,eAAe,CAAC,CAAC;EAAE,CAAC,EAAE,EAAE,CAAC;AACvH,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}