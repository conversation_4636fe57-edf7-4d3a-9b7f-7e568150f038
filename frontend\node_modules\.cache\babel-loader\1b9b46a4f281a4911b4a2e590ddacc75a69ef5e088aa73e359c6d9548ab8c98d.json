{"ast": null, "code": "import { trackElementSize } from './track-size.mjs';\nfunction trackElementsSize(options) {\n  const {\n    getNodes,\n    observeMutation = true,\n    callback\n  } = options;\n  const cleanups = [];\n  let firstNode = null;\n  function trigger() {\n    const elements = getNodes();\n    firstNode = elements[0];\n    const fns = elements.map((element, index) => trackElementSize(element, size => {\n      callback(size, index);\n    }));\n    cleanups.push(...fns);\n  }\n  trigger();\n  if (observeMutation) {\n    const fn = trackMutation(firstNode, trigger);\n    cleanups.push(fn);\n  }\n  return () => {\n    cleanups.forEach(cleanup => {\n      cleanup?.();\n    });\n  };\n}\nfunction trackMutation(el, cb) {\n  if (!el || !el.parentElement) return;\n  const win = el.ownerDocument?.defaultView ?? window;\n  const observer = new win.MutationObserver(() => {\n    cb();\n  });\n  observer.observe(el.parentElement, {\n    childList: true\n  });\n  return () => {\n    observer.disconnect();\n  };\n}\nexport { trackElementsSize };", "map": {"version": 3, "names": ["trackElementSize", "trackElementsSize", "options", "getNodes", "observeMutation", "callback", "cleanups", "firstNode", "trigger", "elements", "fns", "map", "element", "index", "size", "push", "fn", "trackMutation", "for<PERSON>ach", "cleanup", "el", "cb", "parentElement", "win", "ownerDocument", "defaultView", "window", "observer", "MutationObserver", "observe", "childList", "disconnect"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/@zag-js/element-size/dist/track-sizes.mjs"], "sourcesContent": ["import { trackElementSize } from './track-size.mjs';\n\nfunction trackElementsSize(options) {\n  const { getNodes, observeMutation = true, callback } = options;\n  const cleanups = [];\n  let firstNode = null;\n  function trigger() {\n    const elements = getNodes();\n    firstNode = elements[0];\n    const fns = elements.map(\n      (element, index) => trackElementSize(element, (size) => {\n        callback(size, index);\n      })\n    );\n    cleanups.push(...fns);\n  }\n  trigger();\n  if (observeMutation) {\n    const fn = trackMutation(firstNode, trigger);\n    cleanups.push(fn);\n  }\n  return () => {\n    cleanups.forEach((cleanup) => {\n      cleanup?.();\n    });\n  };\n}\nfunction trackMutation(el, cb) {\n  if (!el || !el.parentElement)\n    return;\n  const win = el.ownerDocument?.defaultView ?? window;\n  const observer = new win.MutationObserver(() => {\n    cb();\n  });\n  observer.observe(el.parentElement, { childList: true });\n  return () => {\n    observer.disconnect();\n  };\n}\n\nexport { trackElementsSize };\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,kBAAkB;AAEnD,SAASC,iBAAiBA,CAACC,OAAO,EAAE;EAClC,MAAM;IAAEC,QAAQ;IAAEC,eAAe,GAAG,IAAI;IAAEC;EAAS,CAAC,GAAGH,OAAO;EAC9D,MAAMI,QAAQ,GAAG,EAAE;EACnB,IAAIC,SAAS,GAAG,IAAI;EACpB,SAASC,OAAOA,CAAA,EAAG;IACjB,MAAMC,QAAQ,GAAGN,QAAQ,CAAC,CAAC;IAC3BI,SAAS,GAAGE,QAAQ,CAAC,CAAC,CAAC;IACvB,MAAMC,GAAG,GAAGD,QAAQ,CAACE,GAAG,CACtB,CAACC,OAAO,EAAEC,KAAK,KAAKb,gBAAgB,CAACY,OAAO,EAAGE,IAAI,IAAK;MACtDT,QAAQ,CAACS,IAAI,EAAED,KAAK,CAAC;IACvB,CAAC,CACH,CAAC;IACDP,QAAQ,CAACS,IAAI,CAAC,GAAGL,GAAG,CAAC;EACvB;EACAF,OAAO,CAAC,CAAC;EACT,IAAIJ,eAAe,EAAE;IACnB,MAAMY,EAAE,GAAGC,aAAa,CAACV,SAAS,EAAEC,OAAO,CAAC;IAC5CF,QAAQ,CAACS,IAAI,CAACC,EAAE,CAAC;EACnB;EACA,OAAO,MAAM;IACXV,QAAQ,CAACY,OAAO,CAAEC,OAAO,IAAK;MAC5BA,OAAO,GAAG,CAAC;IACb,CAAC,CAAC;EACJ,CAAC;AACH;AACA,SAASF,aAAaA,CAACG,EAAE,EAAEC,EAAE,EAAE;EAC7B,IAAI,CAACD,EAAE,IAAI,CAACA,EAAE,CAACE,aAAa,EAC1B;EACF,MAAMC,GAAG,GAAGH,EAAE,CAACI,aAAa,EAAEC,WAAW,IAAIC,MAAM;EACnD,MAAMC,QAAQ,GAAG,IAAIJ,GAAG,CAACK,gBAAgB,CAAC,MAAM;IAC9CP,EAAE,CAAC,CAAC;EACN,CAAC,CAAC;EACFM,QAAQ,CAACE,OAAO,CAACT,EAAE,CAACE,aAAa,EAAE;IAAEQ,SAAS,EAAE;EAAK,CAAC,CAAC;EACvD,OAAO,MAAM;IACXH,QAAQ,CAACI,UAAU,CAAC,CAAC;EACvB,CAAC;AACH;AAEA,SAAS9B,iBAAiB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}