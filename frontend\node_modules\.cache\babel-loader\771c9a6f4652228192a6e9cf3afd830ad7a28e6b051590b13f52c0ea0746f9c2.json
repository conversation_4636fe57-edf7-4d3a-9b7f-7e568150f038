{"ast": null, "code": "import { filterAutoFocusable } from './DOMutils';\nimport { pickFirstFocus } from './firstFocus';\nimport { getDataset } from './is';\nvar findAutoFocused = function (autoFocusables) {\n  return function (node) {\n    var _a;\n    var autofocus = (_a = getDataset(node)) === null || _a === void 0 ? void 0 : _a.autofocus;\n    return (\n      // @ts-expect-error\n      node.autofocus ||\n      //\n      autofocus !== undefined && autofocus !== 'false' ||\n      //\n      autoFocusables.indexOf(node) >= 0\n    );\n  };\n};\nexport var pickAutofocus = function (nodesIndexes, orderedNodes, groups) {\n  var nodes = nodesIndexes.map(function (_a) {\n    var node = _a.node;\n    return node;\n  });\n  var autoFocusable = filterAutoFocusable(nodes.filter(findAutoFocused(groups)));\n  if (autoFocusable && autoFocusable.length) {\n    return pickFirstFocus(autoFocusable);\n  }\n  return pickFirstFocus(filterAutoFocusable(orderedNodes));\n};", "map": {"version": 3, "names": ["filterAutoFocusable", "pickFirstFocus", "getDataset", "findAutoFocused", "autoFocusables", "node", "_a", "autofocus", "undefined", "indexOf", "pickAutofocus", "nodesIndexes", "orderedNodes", "groups", "nodes", "map", "autoFocusable", "filter", "length"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/focus-lock/dist/es2015/utils/auto-focus.js"], "sourcesContent": ["import { filterAutoFocusable } from './DOMutils';\nimport { pickFirstFocus } from './firstFocus';\nimport { getDataset } from './is';\nvar findAutoFocused = function (autoFocusables) {\n    return function (node) {\n        var _a;\n        var autofocus = (_a = getDataset(node)) === null || _a === void 0 ? void 0 : _a.autofocus;\n        return (\n        // @ts-expect-error\n        node.autofocus ||\n            //\n            (autofocus !== undefined && autofocus !== 'false') ||\n            //\n            autoFocusables.indexOf(node) >= 0);\n    };\n};\nexport var pickAutofocus = function (nodesIndexes, orderedNodes, groups) {\n    var nodes = nodesIndexes.map(function (_a) {\n        var node = _a.node;\n        return node;\n    });\n    var autoFocusable = filterAutoFocusable(nodes.filter(findAutoFocused(groups)));\n    if (autoFocusable && autoFocusable.length) {\n        return pickFirstFocus(autoFocusable);\n    }\n    return pickFirstFocus(filterAutoFocusable(orderedNodes));\n};\n"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,YAAY;AAChD,SAASC,cAAc,QAAQ,cAAc;AAC7C,SAASC,UAAU,QAAQ,MAAM;AACjC,IAAIC,eAAe,GAAG,SAAAA,CAAUC,cAAc,EAAE;EAC5C,OAAO,UAAUC,IAAI,EAAE;IACnB,IAAIC,EAAE;IACN,IAAIC,SAAS,GAAG,CAACD,EAAE,GAAGJ,UAAU,CAACG,IAAI,CAAC,MAAM,IAAI,IAAIC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,SAAS;IACzF;MACA;MACAF,IAAI,CAACE,SAAS;MACV;MACCA,SAAS,KAAKC,SAAS,IAAID,SAAS,KAAK,OAAQ;MAClD;MACAH,cAAc,CAACK,OAAO,CAACJ,IAAI,CAAC,IAAI;IAAC;EACzC,CAAC;AACL,CAAC;AACD,OAAO,IAAIK,aAAa,GAAG,SAAAA,CAAUC,YAAY,EAAEC,YAAY,EAAEC,MAAM,EAAE;EACrE,IAAIC,KAAK,GAAGH,YAAY,CAACI,GAAG,CAAC,UAAUT,EAAE,EAAE;IACvC,IAAID,IAAI,GAAGC,EAAE,CAACD,IAAI;IAClB,OAAOA,IAAI;EACf,CAAC,CAAC;EACF,IAAIW,aAAa,GAAGhB,mBAAmB,CAACc,KAAK,CAACG,MAAM,CAACd,eAAe,CAACU,MAAM,CAAC,CAAC,CAAC;EAC9E,IAAIG,aAAa,IAAIA,aAAa,CAACE,MAAM,EAAE;IACvC,OAAOjB,cAAc,CAACe,aAAa,CAAC;EACxC;EACA,OAAOf,cAAc,CAACD,mBAAmB,CAACY,YAAY,CAAC,CAAC;AAC5D,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}