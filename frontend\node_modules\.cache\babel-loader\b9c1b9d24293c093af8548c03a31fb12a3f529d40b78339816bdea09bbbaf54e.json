{"ast": null, "code": "import { focusOn } from './commands';\nimport { getTabbableNodes, contains } from './utils/DOMutils';\n/**\n * for a given `element` in a given `scope` returns focusable siblings\n * @param element - base element\n * @param scope - common parent. Can be document, but better to narrow it down for performance reasons\n * @returns {prev,next} - references to a focusable element before and after\n * @returns undefined - if operation is not applicable\n */\nexport var getRelativeFocusable = function (element, scope) {\n  if (!element || !scope || !contains(scope, element)) {\n    return {};\n  }\n  var focusables = getTabbableNodes([scope], new Map());\n  var current = focusables.findIndex(function (_a) {\n    var node = _a.node;\n    return node === element;\n  });\n  if (current === -1) {\n    // an edge case, when anchor element is not found\n    return undefined;\n  }\n  return {\n    prev: focusables[current - 1],\n    next: focusables[current + 1],\n    first: focusables[0],\n    last: focusables[focusables.length - 1]\n  };\n};\nvar defaultOptions = function (options) {\n  return Object.assign({\n    scope: document.body,\n    cycle: true\n  }, options);\n};\n/**\n * focuses next element in the tab-order\n * @param fromElement - common parent to scope active element search or tab cycle order\n * @param {FocusNextOptions} [options] - focus options\n */\nexport var focusNextElement = function (fromElement, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var _a = defaultOptions(options),\n    scope = _a.scope,\n    cycle = _a.cycle;\n  var solution = getRelativeFocusable(fromElement, scope);\n  if (!solution) {\n    return;\n  }\n  var next = solution.next,\n    first = solution.first;\n  var newTarget = next || cycle && first;\n  if (newTarget) {\n    focusOn(newTarget.node, options.focusOptions);\n  }\n};\n/**\n * focuses prev element in the tab order\n * @param fromElement - common parent to scope active element search or tab cycle order\n * @param {FocusNextOptions} [options] - focus options\n */\nexport var focusPrevElement = function (fromElement, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var _a = defaultOptions(options),\n    scope = _a.scope,\n    cycle = _a.cycle;\n  var solution = getRelativeFocusable(fromElement, scope);\n  if (!solution) {\n    return;\n  }\n  var prev = solution.prev,\n    last = solution.last;\n  var newTarget = prev || cycle && last;\n  if (newTarget) {\n    focusOn(newTarget.node, options.focusOptions);\n  }\n};", "map": {"version": 3, "names": ["focusOn", "getTabbableNodes", "contains", "getRelativeFocusable", "element", "scope", "focusables", "Map", "current", "findIndex", "_a", "node", "undefined", "prev", "next", "first", "last", "length", "defaultOptions", "options", "Object", "assign", "document", "body", "cycle", "focusNextElement", "fromElement", "solution", "newTarget", "focusOptions", "focusPrevElement"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/focus-lock/dist/es2015/sibling.js"], "sourcesContent": ["import { focusOn } from './commands';\nimport { getTabbableNodes, contains } from './utils/DOMutils';\n/**\n * for a given `element` in a given `scope` returns focusable siblings\n * @param element - base element\n * @param scope - common parent. Can be document, but better to narrow it down for performance reasons\n * @returns {prev,next} - references to a focusable element before and after\n * @returns undefined - if operation is not applicable\n */\nexport var getRelativeFocusable = function (element, scope) {\n    if (!element || !scope || !contains(scope, element)) {\n        return {};\n    }\n    var focusables = getTabbableNodes([scope], new Map());\n    var current = focusables.findIndex(function (_a) {\n        var node = _a.node;\n        return node === element;\n    });\n    if (current === -1) {\n        // an edge case, when anchor element is not found\n        return undefined;\n    }\n    return {\n        prev: focusables[current - 1],\n        next: focusables[current + 1],\n        first: focusables[0],\n        last: focusables[focusables.length - 1],\n    };\n};\nvar defaultOptions = function (options) {\n    return Object.assign({\n        scope: document.body,\n        cycle: true,\n    }, options);\n};\n/**\n * focuses next element in the tab-order\n * @param fromElement - common parent to scope active element search or tab cycle order\n * @param {FocusNextOptions} [options] - focus options\n */\nexport var focusNextElement = function (fromElement, options) {\n    if (options === void 0) { options = {}; }\n    var _a = defaultOptions(options), scope = _a.scope, cycle = _a.cycle;\n    var solution = getRelativeFocusable(fromElement, scope);\n    if (!solution) {\n        return;\n    }\n    var next = solution.next, first = solution.first;\n    var newTarget = next || (cycle && first);\n    if (newTarget) {\n        focusOn(newTarget.node, options.focusOptions);\n    }\n};\n/**\n * focuses prev element in the tab order\n * @param fromElement - common parent to scope active element search or tab cycle order\n * @param {FocusNextOptions} [options] - focus options\n */\nexport var focusPrevElement = function (fromElement, options) {\n    if (options === void 0) { options = {}; }\n    var _a = defaultOptions(options), scope = _a.scope, cycle = _a.cycle;\n    var solution = getRelativeFocusable(fromElement, scope);\n    if (!solution) {\n        return;\n    }\n    var prev = solution.prev, last = solution.last;\n    var newTarget = prev || (cycle && last);\n    if (newTarget) {\n        focusOn(newTarget.node, options.focusOptions);\n    }\n};\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,YAAY;AACpC,SAASC,gBAAgB,EAAEC,QAAQ,QAAQ,kBAAkB;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,oBAAoB,GAAG,SAAAA,CAAUC,OAAO,EAAEC,KAAK,EAAE;EACxD,IAAI,CAACD,OAAO,IAAI,CAACC,KAAK,IAAI,CAACH,QAAQ,CAACG,KAAK,EAAED,OAAO,CAAC,EAAE;IACjD,OAAO,CAAC,CAAC;EACb;EACA,IAAIE,UAAU,GAAGL,gBAAgB,CAAC,CAACI,KAAK,CAAC,EAAE,IAAIE,GAAG,CAAC,CAAC,CAAC;EACrD,IAAIC,OAAO,GAAGF,UAAU,CAACG,SAAS,CAAC,UAAUC,EAAE,EAAE;IAC7C,IAAIC,IAAI,GAAGD,EAAE,CAACC,IAAI;IAClB,OAAOA,IAAI,KAAKP,OAAO;EAC3B,CAAC,CAAC;EACF,IAAII,OAAO,KAAK,CAAC,CAAC,EAAE;IAChB;IACA,OAAOI,SAAS;EACpB;EACA,OAAO;IACHC,IAAI,EAAEP,UAAU,CAACE,OAAO,GAAG,CAAC,CAAC;IAC7BM,IAAI,EAAER,UAAU,CAACE,OAAO,GAAG,CAAC,CAAC;IAC7BO,KAAK,EAAET,UAAU,CAAC,CAAC,CAAC;IACpBU,IAAI,EAAEV,UAAU,CAACA,UAAU,CAACW,MAAM,GAAG,CAAC;EAC1C,CAAC;AACL,CAAC;AACD,IAAIC,cAAc,GAAG,SAAAA,CAAUC,OAAO,EAAE;EACpC,OAAOC,MAAM,CAACC,MAAM,CAAC;IACjBhB,KAAK,EAAEiB,QAAQ,CAACC,IAAI;IACpBC,KAAK,EAAE;EACX,CAAC,EAAEL,OAAO,CAAC;AACf,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIM,gBAAgB,GAAG,SAAAA,CAAUC,WAAW,EAAEP,OAAO,EAAE;EAC1D,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IAAEA,OAAO,GAAG,CAAC,CAAC;EAAE;EACxC,IAAIT,EAAE,GAAGQ,cAAc,CAACC,OAAO,CAAC;IAAEd,KAAK,GAAGK,EAAE,CAACL,KAAK;IAAEmB,KAAK,GAAGd,EAAE,CAACc,KAAK;EACpE,IAAIG,QAAQ,GAAGxB,oBAAoB,CAACuB,WAAW,EAAErB,KAAK,CAAC;EACvD,IAAI,CAACsB,QAAQ,EAAE;IACX;EACJ;EACA,IAAIb,IAAI,GAAGa,QAAQ,CAACb,IAAI;IAAEC,KAAK,GAAGY,QAAQ,CAACZ,KAAK;EAChD,IAAIa,SAAS,GAAGd,IAAI,IAAKU,KAAK,IAAIT,KAAM;EACxC,IAAIa,SAAS,EAAE;IACX5B,OAAO,CAAC4B,SAAS,CAACjB,IAAI,EAAEQ,OAAO,CAACU,YAAY,CAAC;EACjD;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,gBAAgB,GAAG,SAAAA,CAAUJ,WAAW,EAAEP,OAAO,EAAE;EAC1D,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IAAEA,OAAO,GAAG,CAAC,CAAC;EAAE;EACxC,IAAIT,EAAE,GAAGQ,cAAc,CAACC,OAAO,CAAC;IAAEd,KAAK,GAAGK,EAAE,CAACL,KAAK;IAAEmB,KAAK,GAAGd,EAAE,CAACc,KAAK;EACpE,IAAIG,QAAQ,GAAGxB,oBAAoB,CAACuB,WAAW,EAAErB,KAAK,CAAC;EACvD,IAAI,CAACsB,QAAQ,EAAE;IACX;EACJ;EACA,IAAId,IAAI,GAAGc,QAAQ,CAACd,IAAI;IAAEG,IAAI,GAAGW,QAAQ,CAACX,IAAI;EAC9C,IAAIY,SAAS,GAAGf,IAAI,IAAKW,KAAK,IAAIR,IAAK;EACvC,IAAIY,SAAS,EAAE;IACX5B,OAAO,CAAC4B,SAAS,CAACjB,IAAI,EAAEQ,OAAO,CAACU,YAAY,CAAC;EACjD;AACJ,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}