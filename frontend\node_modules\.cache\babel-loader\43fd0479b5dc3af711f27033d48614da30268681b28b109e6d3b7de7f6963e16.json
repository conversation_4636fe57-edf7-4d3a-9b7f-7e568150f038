{"ast": null, "code": "import { Socket } from \"./socket.js\";\nexport { Socket };\nexport const protocol = Socket.protocol;\nexport { Transport } from \"./transport.js\";\nexport { transports } from \"./transports/index.js\";\nexport { installTimerFunctions } from \"./util.js\";\nexport { parse } from \"./contrib/parseuri.js\";\nexport { nextTick } from \"./transports/websocket-constructor.js\";", "map": {"version": 3, "names": ["Socket", "protocol", "Transport", "transports", "installTimerFunctions", "parse", "nextTick"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/engine.io-client/build/esm/index.js"], "sourcesContent": ["import { Socket } from \"./socket.js\";\nexport { Socket };\nexport const protocol = Socket.protocol;\nexport { Transport } from \"./transport.js\";\nexport { transports } from \"./transports/index.js\";\nexport { installTimerFunctions } from \"./util.js\";\nexport { parse } from \"./contrib/parseuri.js\";\nexport { nextTick } from \"./transports/websocket-constructor.js\";\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,aAAa;AACpC,SAASA,MAAM;AACf,OAAO,MAAMC,QAAQ,GAAGD,MAAM,CAACC,QAAQ;AACvC,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,UAAU,QAAQ,uBAAuB;AAClD,SAASC,qBAAqB,QAAQ,WAAW;AACjD,SAASC,KAAK,QAAQ,uBAAuB;AAC7C,SAASC,QAAQ,QAAQ,uCAAuC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}