{"ast": null, "code": "import _inheritsLoose from '@babel/runtime/helpers/esm/inheritsLoose';\nimport _defineProperty from '@babel/runtime/helpers/esm/defineProperty';\nimport React, { PureComponent } from 'react';\nfunction withSideEffect(reducePropsToState, handleStateChangeOnClient) {\n  if (process.env.NODE_ENV !== \"production\") {\n    if (typeof reducePropsToState !== 'function') {\n      throw new Error('Expected reducePropsToState to be a function.');\n    }\n    if (typeof handleStateChangeOnClient !== 'function') {\n      throw new Error('Expected handleStateChangeOnClient to be a function.');\n    }\n  }\n  function getDisplayName(WrappedComponent) {\n    return WrappedComponent.displayName || WrappedComponent.name || 'Component';\n  }\n  return function wrap(WrappedComponent) {\n    if (process.env.NODE_ENV !== \"production\") {\n      if (typeof WrappedComponent !== 'function') {\n        throw new Error('Expected WrappedComponent to be a React component.');\n      }\n    }\n    var mountedInstances = [];\n    var state;\n    function emitChange() {\n      state = reducePropsToState(mountedInstances.map(function (instance) {\n        return instance.props;\n      }));\n      handleStateChangeOnClient(state);\n    }\n    var SideEffect = /*#__PURE__*/function (_PureComponent) {\n      _inheritsLoose(SideEffect, _PureComponent);\n      function SideEffect() {\n        return _PureComponent.apply(this, arguments) || this;\n      }\n\n      // Try to use displayName of wrapped component\n      SideEffect.peek = function peek() {\n        return state;\n      };\n      var _proto = SideEffect.prototype;\n      _proto.componentDidMount = function componentDidMount() {\n        mountedInstances.push(this);\n        emitChange();\n      };\n      _proto.componentDidUpdate = function componentDidUpdate() {\n        emitChange();\n      };\n      _proto.componentWillUnmount = function componentWillUnmount() {\n        var index = mountedInstances.indexOf(this);\n        mountedInstances.splice(index, 1);\n        emitChange();\n      };\n      _proto.render = function render() {\n        return /*#__PURE__*/React.createElement(WrappedComponent, this.props);\n      };\n      return SideEffect;\n    }(PureComponent);\n    _defineProperty(SideEffect, \"displayName\", \"SideEffect(\" + getDisplayName(WrappedComponent) + \")\");\n    return SideEffect;\n  };\n}\nexport default withSideEffect;", "map": {"version": 3, "names": ["_inherits<PERSON><PERSON>e", "_defineProperty", "React", "PureComponent", "withSideEffect", "reducePropsToState", "handleStateChangeOnClient", "process", "env", "NODE_ENV", "Error", "getDisplayName", "WrappedComponent", "displayName", "name", "wrap", "mountedInstances", "state", "emitChange", "map", "instance", "props", "SideEffect", "_PureComponent", "apply", "arguments", "peek", "_proto", "prototype", "componentDidMount", "push", "componentDidUpdate", "componentWillUnmount", "index", "indexOf", "splice", "render", "createElement"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/react-clientside-effect/lib/index.es.js"], "sourcesContent": ["import _inheritsLoose from '@babel/runtime/helpers/esm/inheritsLoose';\nimport _defineProperty from '@babel/runtime/helpers/esm/defineProperty';\nimport React, { PureComponent } from 'react';\n\nfunction withSideEffect(reducePropsToState, handleStateChangeOnClient) {\n  if (process.env.NODE_ENV !== \"production\") {\n    if (typeof reducePropsToState !== 'function') {\n      throw new Error('Expected reducePropsToState to be a function.');\n    }\n\n    if (typeof handleStateChangeOnClient !== 'function') {\n      throw new Error('Expected handleStateChangeOnClient to be a function.');\n    }\n  }\n\n  function getDisplayName(WrappedComponent) {\n    return WrappedComponent.displayName || WrappedComponent.name || 'Component';\n  }\n\n  return function wrap(WrappedComponent) {\n    if (process.env.NODE_ENV !== \"production\") {\n      if (typeof WrappedComponent !== 'function') {\n        throw new Error('Expected WrappedComponent to be a React component.');\n      }\n    }\n\n    var mountedInstances = [];\n    var state;\n\n    function emitChange() {\n      state = reducePropsToState(mountedInstances.map(function (instance) {\n        return instance.props;\n      }));\n      handleStateChangeOnClient(state);\n    }\n\n    var SideEffect = /*#__PURE__*/function (_PureComponent) {\n      _inheritsLoose(SideEffect, _PureComponent);\n\n      function SideEffect() {\n        return _PureComponent.apply(this, arguments) || this;\n      }\n\n      // Try to use displayName of wrapped component\n      SideEffect.peek = function peek() {\n        return state;\n      };\n\n      var _proto = SideEffect.prototype;\n\n      _proto.componentDidMount = function componentDidMount() {\n        mountedInstances.push(this);\n        emitChange();\n      };\n\n      _proto.componentDidUpdate = function componentDidUpdate() {\n        emitChange();\n      };\n\n      _proto.componentWillUnmount = function componentWillUnmount() {\n        var index = mountedInstances.indexOf(this);\n        mountedInstances.splice(index, 1);\n        emitChange();\n      };\n\n      _proto.render = function render() {\n        return /*#__PURE__*/React.createElement(WrappedComponent, this.props);\n      };\n\n      return SideEffect;\n    }(PureComponent);\n\n    _defineProperty(SideEffect, \"displayName\", \"SideEffect(\" + getDisplayName(WrappedComponent) + \")\");\n\n    return SideEffect;\n  };\n}\n\nexport default withSideEffect;\n"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,KAAK,IAAIC,aAAa,QAAQ,OAAO;AAE5C,SAASC,cAAcA,CAACC,kBAAkB,EAAEC,yBAAyB,EAAE;EACrE,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAI,OAAOJ,kBAAkB,KAAK,UAAU,EAAE;MAC5C,MAAM,IAAIK,KAAK,CAAC,+CAA+C,CAAC;IAClE;IAEA,IAAI,OAAOJ,yBAAyB,KAAK,UAAU,EAAE;MACnD,MAAM,IAAII,KAAK,CAAC,sDAAsD,CAAC;IACzE;EACF;EAEA,SAASC,cAAcA,CAACC,gBAAgB,EAAE;IACxC,OAAOA,gBAAgB,CAACC,WAAW,IAAID,gBAAgB,CAACE,IAAI,IAAI,WAAW;EAC7E;EAEA,OAAO,SAASC,IAAIA,CAACH,gBAAgB,EAAE;IACrC,IAAIL,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAI,OAAOG,gBAAgB,KAAK,UAAU,EAAE;QAC1C,MAAM,IAAIF,KAAK,CAAC,oDAAoD,CAAC;MACvE;IACF;IAEA,IAAIM,gBAAgB,GAAG,EAAE;IACzB,IAAIC,KAAK;IAET,SAASC,UAAUA,CAAA,EAAG;MACpBD,KAAK,GAAGZ,kBAAkB,CAACW,gBAAgB,CAACG,GAAG,CAAC,UAAUC,QAAQ,EAAE;QAClE,OAAOA,QAAQ,CAACC,KAAK;MACvB,CAAC,CAAC,CAAC;MACHf,yBAAyB,CAACW,KAAK,CAAC;IAClC;IAEA,IAAIK,UAAU,GAAG,aAAa,UAAUC,cAAc,EAAE;MACtDvB,cAAc,CAACsB,UAAU,EAAEC,cAAc,CAAC;MAE1C,SAASD,UAAUA,CAAA,EAAG;QACpB,OAAOC,cAAc,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;MACtD;;MAEA;MACAH,UAAU,CAACI,IAAI,GAAG,SAASA,IAAIA,CAAA,EAAG;QAChC,OAAOT,KAAK;MACd,CAAC;MAED,IAAIU,MAAM,GAAGL,UAAU,CAACM,SAAS;MAEjCD,MAAM,CAACE,iBAAiB,GAAG,SAASA,iBAAiBA,CAAA,EAAG;QACtDb,gBAAgB,CAACc,IAAI,CAAC,IAAI,CAAC;QAC3BZ,UAAU,CAAC,CAAC;MACd,CAAC;MAEDS,MAAM,CAACI,kBAAkB,GAAG,SAASA,kBAAkBA,CAAA,EAAG;QACxDb,UAAU,CAAC,CAAC;MACd,CAAC;MAEDS,MAAM,CAACK,oBAAoB,GAAG,SAASA,oBAAoBA,CAAA,EAAG;QAC5D,IAAIC,KAAK,GAAGjB,gBAAgB,CAACkB,OAAO,CAAC,IAAI,CAAC;QAC1ClB,gBAAgB,CAACmB,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;QACjCf,UAAU,CAAC,CAAC;MACd,CAAC;MAEDS,MAAM,CAACS,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;QAChC,OAAO,aAAalC,KAAK,CAACmC,aAAa,CAACzB,gBAAgB,EAAE,IAAI,CAACS,KAAK,CAAC;MACvE,CAAC;MAED,OAAOC,UAAU;IACnB,CAAC,CAACnB,aAAa,CAAC;IAEhBF,eAAe,CAACqB,UAAU,EAAE,aAAa,EAAE,aAAa,GAAGX,cAAc,CAACC,gBAAgB,CAAC,GAAG,GAAG,CAAC;IAElG,OAAOU,UAAU;EACnB,CAAC;AACH;AAEA,eAAelB,cAAc"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}