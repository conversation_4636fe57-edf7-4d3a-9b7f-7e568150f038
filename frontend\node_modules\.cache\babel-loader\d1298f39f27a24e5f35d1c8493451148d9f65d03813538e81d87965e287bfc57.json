{"ast": null, "code": "var isProduction = process.env.NODE_ENV === 'production';\nvar prefix = 'Invariant failed';\nfunction invariant(condition, message) {\n  if (condition) {\n    return;\n  }\n  if (isProduction) {\n    throw new Error(prefix);\n  }\n  var provided = typeof message === 'function' ? message() : message;\n  var value = provided ? \"\".concat(prefix, \": \").concat(provided) : prefix;\n  throw new Error(value);\n}\nexport { invariant as default };", "map": {"version": 3, "names": ["isProduction", "process", "env", "NODE_ENV", "prefix", "invariant", "condition", "message", "Error", "provided", "value", "concat", "default"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/tiny-invariant/dist/esm/tiny-invariant.js"], "sourcesContent": ["var isProduction = process.env.NODE_ENV === 'production';\nvar prefix = 'Invariant failed';\nfunction invariant(condition, message) {\n    if (condition) {\n        return;\n    }\n    if (isProduction) {\n        throw new Error(prefix);\n    }\n    var provided = typeof message === 'function' ? message() : message;\n    var value = provided ? \"\".concat(prefix, \": \").concat(provided) : prefix;\n    throw new Error(value);\n}\n\nexport { invariant as default };\n"], "mappings": "AAAA,IAAIA,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY;AACxD,IAAIC,MAAM,GAAG,kBAAkB;AAC/B,SAASC,SAASA,CAACC,SAAS,EAAEC,OAAO,EAAE;EACnC,IAAID,SAAS,EAAE;IACX;EACJ;EACA,IAAIN,YAAY,EAAE;IACd,MAAM,IAAIQ,KAAK,CAACJ,MAAM,CAAC;EAC3B;EACA,IAAIK,QAAQ,GAAG,OAAOF,OAAO,KAAK,UAAU,GAAGA,OAAO,CAAC,CAAC,GAAGA,OAAO;EAClE,IAAIG,KAAK,GAAGD,QAAQ,GAAG,EAAE,CAACE,MAAM,CAACP,MAAM,EAAE,IAAI,CAAC,CAACO,MAAM,CAACF,QAAQ,CAAC,GAAGL,MAAM;EACxE,MAAM,IAAII,KAAK,CAACE,KAAK,CAAC;AAC1B;AAEA,SAASL,SAAS,IAAIO,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}