{"ast": null, "code": "import { FOCUS_NO_AUTOFOCUS } from '../constants';\nvar isElementHidden = function (node) {\n  // we can measure only \"elements\"\n  // consider others as \"visible\"\n  if (node.nodeType !== Node.ELEMENT_NODE) {\n    return false;\n  }\n  var computedStyle = window.getComputedStyle(node, null);\n  if (!computedStyle || !computedStyle.getPropertyValue) {\n    return false;\n  }\n  return computedStyle.getPropertyValue('display') === 'none' || computedStyle.getPropertyValue('visibility') === 'hidden';\n};\nvar getParentNode = function (node) {\n  // DOCUMENT_FRAGMENT_NODE can also point on ShadowRoot. In this case .host will point on the next node\n  return node.parentNode && node.parentNode.nodeType === Node.DOCUMENT_FRAGMENT_NODE ?\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  node.parentNode.host : node.parentNode;\n};\nvar isTopNode = function (node) {\n  // @ts-ignore\n  return node === document || node && node.nodeType === Node.DOCUMENT_NODE;\n};\nvar isVisibleUncached = function (node, checkParent) {\n  return !node || isTopNode(node) || !isElementHidden(node) && checkParent(getParentNode(node));\n};\nexport var isVisibleCached = function (visibilityCache, node) {\n  var cached = visibilityCache.get(node);\n  if (cached !== undefined) {\n    return cached;\n  }\n  var result = isVisibleUncached(node, isVisibleCached.bind(undefined, visibilityCache));\n  visibilityCache.set(node, result);\n  return result;\n};\nvar isAutoFocusAllowedUncached = function (node, checkParent) {\n  return node && !isTopNode(node) ? isAutoFocusAllowed(node) ? checkParent(getParentNode(node)) : false : true;\n};\nexport var isAutoFocusAllowedCached = function (cache, node) {\n  var cached = cache.get(node);\n  if (cached !== undefined) {\n    return cached;\n  }\n  var result = isAutoFocusAllowedUncached(node, isAutoFocusAllowedCached.bind(undefined, cache));\n  cache.set(node, result);\n  return result;\n};\nexport var getDataset = function (node) {\n  // @ts-ignore\n  return node.dataset;\n};\nexport var isHTMLButtonElement = function (node) {\n  return node.tagName === 'BUTTON';\n};\nexport var isHTMLInputElement = function (node) {\n  return node.tagName === 'INPUT';\n};\nexport var isRadioElement = function (node) {\n  return isHTMLInputElement(node) && node.type === 'radio';\n};\nexport var notHiddenInput = function (node) {\n  return !((isHTMLInputElement(node) || isHTMLButtonElement(node)) && (node.type === 'hidden' || node.disabled));\n};\nexport var isAutoFocusAllowed = function (node) {\n  var attribute = node.getAttribute(FOCUS_NO_AUTOFOCUS);\n  return ![true, 'true', ''].includes(attribute);\n};\nexport var isGuard = function (node) {\n  var _a;\n  return Boolean(node && ((_a = getDataset(node)) === null || _a === void 0 ? void 0 : _a.focusGuard));\n};\nexport var isNotAGuard = function (node) {\n  return !isGuard(node);\n};\nexport var isDefined = function (x) {\n  return Boolean(x);\n};", "map": {"version": 3, "names": ["FOCUS_NO_AUTOFOCUS", "isElementHidden", "node", "nodeType", "Node", "ELEMENT_NODE", "computedStyle", "window", "getComputedStyle", "getPropertyValue", "getParentNode", "parentNode", "DOCUMENT_FRAGMENT_NODE", "host", "isTopNode", "document", "DOCUMENT_NODE", "isVisibleUncached", "checkParent", "isVisibleCached", "visibilityCache", "cached", "get", "undefined", "result", "bind", "set", "isAutoFocusAllowedUncached", "isAutoFocusAllowed", "isAutoFocusAllowedCached", "cache", "getDataset", "dataset", "isHTMLButtonElement", "tagName", "isHTMLInputElement", "isRadioElement", "type", "notHiddenInput", "disabled", "attribute", "getAttribute", "includes", "<PERSON><PERSON><PERSON>", "_a", "Boolean", "focusGuard", "isNotAGuard", "isDefined", "x"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/focus-lock/dist/es2015/utils/is.js"], "sourcesContent": ["import { FOCUS_NO_AUTOFOCUS } from '../constants';\nvar isElementHidden = function (node) {\n    // we can measure only \"elements\"\n    // consider others as \"visible\"\n    if (node.nodeType !== Node.ELEMENT_NODE) {\n        return false;\n    }\n    var computedStyle = window.getComputedStyle(node, null);\n    if (!computedStyle || !computedStyle.getPropertyValue) {\n        return false;\n    }\n    return (computedStyle.getPropertyValue('display') === 'none' || computedStyle.getPropertyValue('visibility') === 'hidden');\n};\nvar getParentNode = function (node) {\n    // DOCUMENT_FRAGMENT_NODE can also point on ShadowRoot. In this case .host will point on the next node\n    return node.parentNode && node.parentNode.nodeType === Node.DOCUMENT_FRAGMENT_NODE\n        ? // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            node.parentNode.host\n        : node.parentNode;\n};\nvar isTopNode = function (node) {\n    // @ts-ignore\n    return node === document || (node && node.nodeType === Node.DOCUMENT_NODE);\n};\nvar isVisibleUncached = function (node, checkParent) {\n    return !node || isTopNode(node) || (!isElementHidden(node) && checkParent(getParentNode(node)));\n};\nexport var isVisibleCached = function (visibilityCache, node) {\n    var cached = visibilityCache.get(node);\n    if (cached !== undefined) {\n        return cached;\n    }\n    var result = isVisibleUncached(node, isVisibleCached.bind(undefined, visibilityCache));\n    visibilityCache.set(node, result);\n    return result;\n};\nvar isAutoFocusAllowedUncached = function (node, checkParent) {\n    return node && !isTopNode(node) ? (isAutoFocusAllowed(node) ? checkParent(getParentNode(node)) : false) : true;\n};\nexport var isAutoFocusAllowedCached = function (cache, node) {\n    var cached = cache.get(node);\n    if (cached !== undefined) {\n        return cached;\n    }\n    var result = isAutoFocusAllowedUncached(node, isAutoFocusAllowedCached.bind(undefined, cache));\n    cache.set(node, result);\n    return result;\n};\nexport var getDataset = function (node) {\n    // @ts-ignore\n    return node.dataset;\n};\nexport var isHTMLButtonElement = function (node) { return node.tagName === 'BUTTON'; };\nexport var isHTMLInputElement = function (node) { return node.tagName === 'INPUT'; };\nexport var isRadioElement = function (node) {\n    return isHTMLInputElement(node) && node.type === 'radio';\n};\nexport var notHiddenInput = function (node) {\n    return !((isHTMLInputElement(node) || isHTMLButtonElement(node)) && (node.type === 'hidden' || node.disabled));\n};\nexport var isAutoFocusAllowed = function (node) {\n    var attribute = node.getAttribute(FOCUS_NO_AUTOFOCUS);\n    return ![true, 'true', ''].includes(attribute);\n};\nexport var isGuard = function (node) { var _a; return Boolean(node && ((_a = getDataset(node)) === null || _a === void 0 ? void 0 : _a.focusGuard)); };\nexport var isNotAGuard = function (node) { return !isGuard(node); };\nexport var isDefined = function (x) { return Boolean(x); };\n"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,cAAc;AACjD,IAAIC,eAAe,GAAG,SAAAA,CAAUC,IAAI,EAAE;EAClC;EACA;EACA,IAAIA,IAAI,CAACC,QAAQ,KAAKC,IAAI,CAACC,YAAY,EAAE;IACrC,OAAO,KAAK;EAChB;EACA,IAAIC,aAAa,GAAGC,MAAM,CAACC,gBAAgB,CAACN,IAAI,EAAE,IAAI,CAAC;EACvD,IAAI,CAACI,aAAa,IAAI,CAACA,aAAa,CAACG,gBAAgB,EAAE;IACnD,OAAO,KAAK;EAChB;EACA,OAAQH,aAAa,CAACG,gBAAgB,CAAC,SAAS,CAAC,KAAK,MAAM,IAAIH,aAAa,CAACG,gBAAgB,CAAC,YAAY,CAAC,KAAK,QAAQ;AAC7H,CAAC;AACD,IAAIC,aAAa,GAAG,SAAAA,CAAUR,IAAI,EAAE;EAChC;EACA,OAAOA,IAAI,CAACS,UAAU,IAAIT,IAAI,CAACS,UAAU,CAACR,QAAQ,KAAKC,IAAI,CAACQ,sBAAsB;EAC5E;EACEV,IAAI,CAACS,UAAU,CAACE,IAAI,GACtBX,IAAI,CAACS,UAAU;AACzB,CAAC;AACD,IAAIG,SAAS,GAAG,SAAAA,CAAUZ,IAAI,EAAE;EAC5B;EACA,OAAOA,IAAI,KAAKa,QAAQ,IAAKb,IAAI,IAAIA,IAAI,CAACC,QAAQ,KAAKC,IAAI,CAACY,aAAc;AAC9E,CAAC;AACD,IAAIC,iBAAiB,GAAG,SAAAA,CAAUf,IAAI,EAAEgB,WAAW,EAAE;EACjD,OAAO,CAAChB,IAAI,IAAIY,SAAS,CAACZ,IAAI,CAAC,IAAK,CAACD,eAAe,CAACC,IAAI,CAAC,IAAIgB,WAAW,CAACR,aAAa,CAACR,IAAI,CAAC,CAAE;AACnG,CAAC;AACD,OAAO,IAAIiB,eAAe,GAAG,SAAAA,CAAUC,eAAe,EAAElB,IAAI,EAAE;EAC1D,IAAImB,MAAM,GAAGD,eAAe,CAACE,GAAG,CAACpB,IAAI,CAAC;EACtC,IAAImB,MAAM,KAAKE,SAAS,EAAE;IACtB,OAAOF,MAAM;EACjB;EACA,IAAIG,MAAM,GAAGP,iBAAiB,CAACf,IAAI,EAAEiB,eAAe,CAACM,IAAI,CAACF,SAAS,EAAEH,eAAe,CAAC,CAAC;EACtFA,eAAe,CAACM,GAAG,CAACxB,IAAI,EAAEsB,MAAM,CAAC;EACjC,OAAOA,MAAM;AACjB,CAAC;AACD,IAAIG,0BAA0B,GAAG,SAAAA,CAAUzB,IAAI,EAAEgB,WAAW,EAAE;EAC1D,OAAOhB,IAAI,IAAI,CAACY,SAAS,CAACZ,IAAI,CAAC,GAAI0B,kBAAkB,CAAC1B,IAAI,CAAC,GAAGgB,WAAW,CAACR,aAAa,CAACR,IAAI,CAAC,CAAC,GAAG,KAAK,GAAI,IAAI;AAClH,CAAC;AACD,OAAO,IAAI2B,wBAAwB,GAAG,SAAAA,CAAUC,KAAK,EAAE5B,IAAI,EAAE;EACzD,IAAImB,MAAM,GAAGS,KAAK,CAACR,GAAG,CAACpB,IAAI,CAAC;EAC5B,IAAImB,MAAM,KAAKE,SAAS,EAAE;IACtB,OAAOF,MAAM;EACjB;EACA,IAAIG,MAAM,GAAGG,0BAA0B,CAACzB,IAAI,EAAE2B,wBAAwB,CAACJ,IAAI,CAACF,SAAS,EAAEO,KAAK,CAAC,CAAC;EAC9FA,KAAK,CAACJ,GAAG,CAACxB,IAAI,EAAEsB,MAAM,CAAC;EACvB,OAAOA,MAAM;AACjB,CAAC;AACD,OAAO,IAAIO,UAAU,GAAG,SAAAA,CAAU7B,IAAI,EAAE;EACpC;EACA,OAAOA,IAAI,CAAC8B,OAAO;AACvB,CAAC;AACD,OAAO,IAAIC,mBAAmB,GAAG,SAAAA,CAAU/B,IAAI,EAAE;EAAE,OAAOA,IAAI,CAACgC,OAAO,KAAK,QAAQ;AAAE,CAAC;AACtF,OAAO,IAAIC,kBAAkB,GAAG,SAAAA,CAAUjC,IAAI,EAAE;EAAE,OAAOA,IAAI,CAACgC,OAAO,KAAK,OAAO;AAAE,CAAC;AACpF,OAAO,IAAIE,cAAc,GAAG,SAAAA,CAAUlC,IAAI,EAAE;EACxC,OAAOiC,kBAAkB,CAACjC,IAAI,CAAC,IAAIA,IAAI,CAACmC,IAAI,KAAK,OAAO;AAC5D,CAAC;AACD,OAAO,IAAIC,cAAc,GAAG,SAAAA,CAAUpC,IAAI,EAAE;EACxC,OAAO,EAAE,CAACiC,kBAAkB,CAACjC,IAAI,CAAC,IAAI+B,mBAAmB,CAAC/B,IAAI,CAAC,MAAMA,IAAI,CAACmC,IAAI,KAAK,QAAQ,IAAInC,IAAI,CAACqC,QAAQ,CAAC,CAAC;AAClH,CAAC;AACD,OAAO,IAAIX,kBAAkB,GAAG,SAAAA,CAAU1B,IAAI,EAAE;EAC5C,IAAIsC,SAAS,GAAGtC,IAAI,CAACuC,YAAY,CAACzC,kBAAkB,CAAC;EACrD,OAAO,CAAC,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC0C,QAAQ,CAACF,SAAS,CAAC;AAClD,CAAC;AACD,OAAO,IAAIG,OAAO,GAAG,SAAAA,CAAUzC,IAAI,EAAE;EAAE,IAAI0C,EAAE;EAAE,OAAOC,OAAO,CAAC3C,IAAI,KAAK,CAAC0C,EAAE,GAAGb,UAAU,CAAC7B,IAAI,CAAC,MAAM,IAAI,IAAI0C,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,UAAU,CAAC,CAAC;AAAE,CAAC;AACtJ,OAAO,IAAIC,WAAW,GAAG,SAAAA,CAAU7C,IAAI,EAAE;EAAE,OAAO,CAACyC,OAAO,CAACzC,IAAI,CAAC;AAAE,CAAC;AACnE,OAAO,IAAI8C,SAAS,GAAG,SAAAA,CAAUC,CAAC,EAAE;EAAE,OAAOJ,OAAO,CAACI,CAAC,CAAC;AAAE,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}