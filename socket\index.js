const express = require("express");
const app = express();
const http = require("http");
const cors = require("cors");
const { Server } = require("socket.io")
app.use(cors({
  origin: ["https://concerned-picture-9849-frontend.vercel.app", "http://localhost:3000"],
  credentials: true
}));

const server = http.createServer(app);

const io = new Server(server, {
  cors: {
    origin: ["https://concerned-picture-9849-frontend.vercel.app", "http://localhost:3000"],
    methods: ["GET", "POST"],
    credentials: true
  }
})

io.on("connection", (socket) => {
  console.log("User connected", socket.id);

  socket.on("message", () => {
    io.emit("sendMessage");
  });

  socket.on("disconnect", () => {
    console.log("User disconnected");
  });
});


server.listen(3001, () => {
  console.log("Server is listening at port 3001");
})