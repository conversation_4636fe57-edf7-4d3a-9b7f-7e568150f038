{"ast": null, "code": "export { sidecar } from './hoc';\nexport { useSidecar } from './hook';\nexport { setConfig } from './config';\nexport { createMedium, createSidecarMedium } from './medium';\nexport { renderCar } from './renderProp';\nexport { exportSidecar } from './exports';", "map": {"version": 3, "names": ["sidecar", "useSidecar", "setConfig", "createMedium", "createSidecarMedium", "renderCar", "exportSidecar"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/use-sidecar/dist/es2015/index.js"], "sourcesContent": ["export { sidecar } from './hoc';\nexport { useSidecar } from './hook';\nexport { setConfig } from './config';\nexport { createMedium, createSidecarMedium } from './medium';\nexport { renderCar } from './renderProp';\nexport { exportSidecar } from './exports';\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,OAAO;AAC/B,SAASC,UAAU,QAAQ,QAAQ;AACnC,SAASC,SAAS,QAAQ,UAAU;AACpC,SAASC,YAAY,EAAEC,mBAAmB,QAAQ,UAAU;AAC5D,SAASC,SAAS,QAAQ,cAAc;AACxC,SAASC,aAAa,QAAQ,WAAW"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}