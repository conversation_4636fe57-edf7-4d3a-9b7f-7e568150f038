{"ast": null, "code": "// src/attrs.ts\nvar dataAttr = guard => {\n  return guard ? \"\" : void 0;\n};\nvar ariaAttr = guard => {\n  return guard ? \"true\" : void 0;\n};\n\n// src/is-html-element.ts\nfunction isHTMLElement(value) {\n  return typeof value === \"object\" && value?.nodeType === Node.ELEMENT_NODE && typeof value?.nodeName === \"string\";\n}\n\n// src/contains.ts\nfunction contains(parent, child) {\n  if (!parent || !child) return false;\n  if (!isHTMLElement(parent) || !isHTMLElement(child)) return false;\n  return parent === child || parent.contains(child);\n}\nvar isSelfEvent = event => contains(event.currentTarget, event.target);\n\n// src/create-scope.ts\nvar getDocument = node => {\n  if (node.nodeType === Node.DOCUMENT_NODE) return node;\n  return node.ownerDocument ?? document;\n};\nfunction createScope(methods) {\n  const screen = {\n    getRootNode: ctx => ctx.getRootNode?.() ?? document,\n    getDoc: ctx => getDocument(screen.getRootNode(ctx)),\n    getWin: ctx => screen.getDoc(ctx).defaultView ?? window,\n    getActiveElement: ctx => screen.getDoc(ctx).activeElement,\n    getById: (ctx, id) => screen.getRootNode(ctx).getElementById(id)\n  };\n  return {\n    ...screen,\n    ...methods\n  };\n}\n\n// src/env.ts\nvar isDocument = el => el.nodeType === Node.DOCUMENT_NODE;\nfunction getDocument2(el) {\n  if (isDocument(el)) return el;\n  return el?.ownerDocument ?? document;\n}\nfunction getWindow(el) {\n  return el?.ownerDocument.defaultView ?? window;\n}\n\n// src/get-active-element.ts\nfunction getActiveElement(el) {\n  let activeElement = el.ownerDocument.activeElement;\n  while (activeElement?.shadowRoot) {\n    const el2 = activeElement.shadowRoot.activeElement;\n    if (el2 === activeElement) break;else activeElement = el2;\n  }\n  return activeElement;\n}\n\n// src/get-by-id.ts\nfunction itemById(v, id) {\n  return v.find(node => node.id === id);\n}\nfunction indexOfId(v, id) {\n  const item = itemById(v, id);\n  return item ? v.indexOf(item) : -1;\n}\nfunction nextById(v, id, loop = true) {\n  let idx = indexOfId(v, id);\n  idx = loop ? (idx + 1) % v.length : Math.min(idx + 1, v.length - 1);\n  return v[idx];\n}\nfunction prevById(v, id, loop = true) {\n  let idx = indexOfId(v, id);\n  if (idx === -1) return loop ? v[v.length - 1] : null;\n  idx = loop ? (idx - 1 + v.length) % v.length : Math.max(0, idx - 1);\n  return v[idx];\n}\n\n// src/get-by-text.ts\nvar getValueText = item => item.dataset.valuetext ?? item.textContent ?? \"\";\nvar match = (valueText, query2) => valueText.toLowerCase().startsWith(query2.toLowerCase());\nvar wrap = (v, idx) => {\n  return v.map((_, index) => v[(Math.max(idx, 0) + index) % v.length]);\n};\nfunction getByText(v, text, currentId) {\n  const index = currentId ? indexOfId(v, currentId) : -1;\n  let items = currentId ? wrap(v, index) : v;\n  const isSingleKey = text.length === 1;\n  if (isSingleKey) {\n    items = items.filter(item => item.id !== currentId);\n  }\n  return items.find(item => match(getValueText(item), text));\n}\n\n// src/get-by-typeahead.ts\nfunction getByTypeaheadImpl(_items, options) {\n  const {\n    state,\n    activeId,\n    key,\n    timeout = 350\n  } = options;\n  const search = state.keysSoFar + key;\n  const isRepeated = search.length > 1 && Array.from(search).every(char => char === search[0]);\n  const query2 = isRepeated ? search[0] : search;\n  let items = _items.slice();\n  const next = getByText(items, query2, activeId);\n  function cleanup() {\n    clearTimeout(state.timer);\n    state.timer = -1;\n  }\n  function update(value) {\n    state.keysSoFar = value;\n    cleanup();\n    if (value !== \"\") {\n      state.timer = +setTimeout(() => {\n        update(\"\");\n        cleanup();\n      }, timeout);\n    }\n  }\n  update(search);\n  return next;\n}\nvar getByTypeahead = /* @__PURE__ */Object.assign(getByTypeaheadImpl, {\n  defaultOptions: {\n    keysSoFar: \"\",\n    timer: -1\n  },\n  isValidEvent: isValidTypeaheadEvent\n});\nfunction isValidTypeaheadEvent(event) {\n  return event.key.length === 1 && !event.ctrlKey && !event.metaKey;\n}\n\n// src/get-computed-style.ts\nvar styleCache = /* @__PURE__ */new WeakMap();\nfunction getComputedStyle(el) {\n  if (!styleCache.has(el)) {\n    const win = el.ownerDocument.defaultView || window;\n    styleCache.set(el, win.getComputedStyle(el));\n  }\n  return styleCache.get(el);\n}\n\n// src/get-event-target.ts\nfunction getEventTarget(event) {\n  return event.composedPath?.()[0] ?? event.target;\n}\n\n// src/get-scroll-parent.ts\nfunction isScrollParent(el) {\n  const win = el.ownerDocument.defaultView || window;\n  const {\n    overflow,\n    overflowX,\n    overflowY\n  } = win.getComputedStyle(el);\n  return /auto|scroll|overlay|hidden/.test(overflow + overflowY + overflowX);\n}\nfunction getParent(el) {\n  if (el.localName === \"html\") return el;\n  return el.assignedSlot || el.parentElement || el.ownerDocument.documentElement;\n}\nfunction getScrollParent(el) {\n  if ([\"html\", \"body\", \"#document\"].includes(el.localName)) {\n    return el.ownerDocument.body;\n  }\n  if (isHTMLElement(el) && isScrollParent(el)) {\n    return el;\n  }\n  return getScrollParent(getParent(el));\n}\nfunction getScrollParents(el, list = []) {\n  const parent = getScrollParent(el);\n  const isBody = parent === el.ownerDocument.body;\n  const win = parent.ownerDocument.defaultView || window;\n  const target = isBody ? [win].concat(win.visualViewport || [], isScrollParent(parent) ? parent : []) : parent;\n  const parents = list.concat(target);\n  return isBody ? parents : parents.concat(getScrollParents(getParent(target)));\n}\n\n// src/is-editable-element.ts\nfunction isEditableElement(el) {\n  if (el == null || !isHTMLElement(el)) {\n    return false;\n  }\n  try {\n    const win = el.ownerDocument.defaultView || window;\n    return el instanceof win.HTMLInputElement && el.selectionStart != null || /(textarea|select)/.test(el.localName) || el.isContentEditable;\n  } catch {\n    return false;\n  }\n}\n\n// src/platform.ts\nvar isDom = () => typeof document !== \"undefined\";\nfunction getPlatform() {\n  const agent = navigator.userAgentData;\n  return agent?.platform ?? navigator.platform;\n}\nvar pt = v => isDom() && v.test(getPlatform());\nvar ua = v => isDom() && v.test(navigator.userAgent);\nvar vn = v => isDom() && v.test(navigator.vendor);\nvar isTouchDevice = () => isDom() && !!navigator.maxTouchPoints;\nvar isMac = () => pt(/^Mac/) && !isTouchDevice();\nvar isIPhone = () => pt(/^iPhone/);\nvar isSafari = () => isApple() && vn(/apple/i);\nvar isFirefox = () => ua(/firefox\\//i);\nvar isApple = () => pt(/mac|iphone|ipad|ipod/i);\nvar isIos = () => isApple() && !isMac();\n\n// src/query.ts\nfunction queryAll(root, selector) {\n  return Array.from(root?.querySelectorAll(selector) ?? []);\n}\nfunction query(root, selector) {\n  return root?.querySelector(selector);\n}\n\n// src/raf.ts\nfunction nextTick(fn) {\n  const set = /* @__PURE__ */new Set();\n  function raf2(fn2) {\n    const id = globalThis.requestAnimationFrame(fn2);\n    set.add(() => globalThis.cancelAnimationFrame(id));\n  }\n  raf2(() => raf2(fn));\n  return function cleanup() {\n    set.forEach(fn2 => fn2());\n  };\n}\nfunction raf(fn) {\n  const id = globalThis.requestAnimationFrame(fn);\n  return () => {\n    globalThis.cancelAnimationFrame(id);\n  };\n}\n\n// src/index.ts\nvar MAX_Z_INDEX = 2147483647;\nexport { MAX_Z_INDEX, ariaAttr, contains, createScope, dataAttr, getActiveElement, getByText, getByTypeahead, getComputedStyle, getDocument2 as getDocument, getEventTarget, getParent, getPlatform, getScrollParent, getScrollParents, getWindow, indexOfId, isApple, isDom, isEditableElement, isFirefox, isHTMLElement, isIPhone, isIos, isMac, isSafari, isSelfEvent, isTouchDevice, itemById, nextById, nextTick, prevById, query, queryAll, raf };", "map": {"version": 3, "names": ["dataAttr", "guard", "ariaAttr", "isHTMLElement", "value", "nodeType", "Node", "ELEMENT_NODE", "nodeName", "contains", "parent", "child", "isSelfEvent", "event", "currentTarget", "target", "getDocument", "node", "DOCUMENT_NODE", "ownerDocument", "document", "createScope", "methods", "screen", "getRootNode", "ctx", "getDoc", "getWin", "defaultView", "window", "getActiveElement", "activeElement", "getById", "id", "getElementById", "isDocument", "el", "getDocument2", "getWindow", "shadowRoot", "el2", "itemById", "v", "find", "indexOfId", "item", "indexOf", "nextById", "loop", "idx", "length", "Math", "min", "prevById", "max", "getValueText", "dataset", "valuetext", "textContent", "match", "valueText", "query2", "toLowerCase", "startsWith", "wrap", "map", "_", "index", "getByText", "text", "currentId", "items", "isSingleKey", "filter", "getByTypeaheadImpl", "_items", "options", "state", "activeId", "key", "timeout", "search", "keysSoFar", "isRepeated", "Array", "from", "every", "char", "slice", "next", "cleanup", "clearTimeout", "timer", "update", "setTimeout", "getByTypeahead", "Object", "assign", "defaultOptions", "isValidEvent", "isValidTypeaheadEvent", "ctrl<PERSON>ey", "metaKey", "styleCache", "WeakMap", "getComputedStyle", "has", "win", "set", "get", "getEventTarget", "<PERSON><PERSON><PERSON>", "isScrollParent", "overflow", "overflowX", "overflowY", "test", "getParent", "localName", "assignedSlot", "parentElement", "documentElement", "getScrollParent", "includes", "body", "getScrollParents", "list", "isBody", "concat", "visualViewport", "parents", "isEditableElement", "HTMLInputElement", "selectionStart", "isContentEditable", "isDom", "getPlatform", "agent", "navigator", "userAgentData", "platform", "pt", "ua", "userAgent", "vn", "vendor", "isTouchDevice", "maxTouchPoints", "isMac", "isIPhone", "<PERSON><PERSON><PERSON><PERSON>", "isApple", "isFirefox", "isIos", "queryAll", "root", "selector", "querySelectorAll", "query", "querySelector", "nextTick", "fn", "Set", "raf2", "fn2", "globalThis", "requestAnimationFrame", "add", "cancelAnimationFrame", "for<PERSON>ach", "raf", "MAX_Z_INDEX"], "sources": ["C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\@zag-js\\dom-query\\src\\attrs.ts", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\@zag-js\\dom-query\\src\\is-html-element.ts", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\@zag-js\\dom-query\\src\\contains.ts", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\@zag-js\\dom-query\\src\\create-scope.ts", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\@zag-js\\dom-query\\src\\env.ts", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\@zag-js\\dom-query\\src\\get-active-element.ts", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\@zag-js\\dom-query\\src\\get-by-id.ts", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\@zag-js\\dom-query\\src\\get-by-text.ts", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\@zag-js\\dom-query\\src\\get-by-typeahead.ts", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\@zag-js\\dom-query\\src\\get-computed-style.ts", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\@zag-js\\dom-query\\src\\get-event-target.ts", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\@zag-js\\dom-query\\src\\get-scroll-parent.ts", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\@zag-js\\dom-query\\src\\is-editable-element.ts", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\@zag-js\\dom-query\\src\\platform.ts", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\@zag-js\\dom-query\\src\\query.ts", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\@zag-js\\dom-query\\src\\raf.ts", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\@zag-js\\dom-query\\src\\index.ts"], "sourcesContent": ["type Booleanish = boolean | \"true\" | \"false\"\n\nexport const dataAttr = (guard: boolean | undefined) => {\n  return (guard ? \"\" : undefined) as Booleani<PERSON>\n}\n\nexport const ariaAttr = (guard: boolean | undefined) => {\n  return guard ? \"true\" : undefined\n}\n", "export function isHTMLElement(value: any): value is HTMLElement {\n  return typeof value === \"object\" && value?.nodeType === Node.ELEMENT_NODE && typeof value?.nodeName === \"string\"\n}\n", "import { isHTMLElement } from \"./is-html-element\"\n\ntype Target = HTMLElement | EventTarget | null | undefined\n\nexport function contains(parent: Target, child: Target) {\n  if (!parent || !child) return false\n  if (!isHTMLElement(parent) || !isHTMLElement(child)) return false\n  return parent === child || parent.contains(child)\n}\n\nexport const isSelfEvent = (event: Pick<UIEvent, \"currentTarget\" | \"target\">) =>\n  contains(event.currentTarget, event.target)\n", "type Ctx = { getRootNode?: () => Document | ShadowRoot | Node }\n\nconst getDocument = (node: Document | ShadowRoot | Node) => {\n  if (node.nodeType === Node.DOCUMENT_NODE) return node as Document\n  return node.ownerDocument ?? document\n}\n\nexport function createScope<T>(methods: T) {\n  const screen = {\n    getRootNode: (ctx: Ctx) => (ctx.getRootNode?.() ?? document) as Document | ShadowRoot,\n    getDoc: (ctx: Ctx) => getDocument(screen.getRootNode(ctx)),\n    getWin: (ctx: Ctx) => screen.getDoc(ctx).defaultView ?? window,\n    getActiveElement: (ctx: Ctx) => screen.getDoc(ctx).activeElement as HTMLElement | null,\n    getById: <T extends HTMLElement = HTMLElement>(ctx: Ctx, id: string) =>\n      screen.getRootNode(ctx).getElementById(id) as T | null,\n  }\n  return { ...screen, ...methods }\n}\n", "const isDocument = (el: any): el is Document => el.nodeType === Node.DOCUMENT_NODE\n\nexport function getDocument(el: Element | Node | Document | null) {\n  if (isDocument(el)) return el\n  return el?.ownerDocument ?? document\n}\n\nexport function getWindow(el: HTMLElement) {\n  return el?.ownerDocument.defaultView ?? window\n}\n", "export function getActiveElement(el: HTMLElement): HTMLElement | null {\n  let activeElement = el.ownerDocument.activeElement as HTMLElement | null\n\n  while (activeElement?.shadowRoot) {\n    const el = activeElement.shadowRoot.activeElement as HTMLElement | null\n    if (el === activeElement) break\n    else activeElement = el\n  }\n\n  return activeElement\n}\n", "export function itemById<T extends HTMLElement>(v: T[], id: string) {\n  return v.find((node) => node.id === id)\n}\n\nexport function indexOfId<T extends HTMLElement>(v: T[], id: string) {\n  const item = itemById(v, id)\n  return item ? v.indexOf(item) : -1\n}\n\nexport function nextById<T extends HTMLElement>(v: T[], id: string, loop = true) {\n  let idx = indexOfId(v, id)\n  idx = loop ? (idx + 1) % v.length : Math.min(idx + 1, v.length - 1)\n  return v[idx]\n}\n\nexport function prevById<T extends HTMLElement>(v: T[], id: string, loop = true) {\n  let idx = indexOfId(v, id)\n  if (idx === -1) return loop ? v[v.length - 1] : null\n  idx = loop ? (idx - 1 + v.length) % v.length : Math.max(0, idx - 1)\n  return v[idx]\n}\n", "import { indexOfId } from \"./get-by-id\"\n\nconst getValueText = <T extends HTMLElement>(item: T) => item.dataset.valuetext ?? item.textContent ?? \"\"\n\nconst match = (valueText: string, query: string) => valueText.toLowerCase().startsWith(query.toLowerCase())\n\nconst wrap = <T>(v: T[], idx: number) => {\n  return v.map((_, index) => v[(Math.max(idx, 0) + index) % v.length])\n}\n\nexport function getByText<T extends HTMLElement>(v: T[], text: string, currentId?: string | null) {\n  const index = currentId ? indexOfId(v, currentId) : -1\n  let items = currentId ? wrap(v, index) : v\n\n  const isSingleKey = text.length === 1\n\n  if (isSingleKey) {\n    items = items.filter((item) => item.id !== currentId)\n  }\n\n  return items.find((item) => match(getValueText(item), text))\n}\n", "import { getByText } from \"./get-by-text\"\n\nexport type TypeaheadState = {\n  keysSoFar: string\n  timer: number\n}\n\nexport type TypeaheadOptions = {\n  state: TypeaheadState\n  activeId: string | null\n  key: string\n  timeout?: number\n}\n\nfunction getByTypeaheadImpl<T extends HTMLElement>(_items: T[], options: TypeaheadOptions) {\n  const { state, activeId, key, timeout = 350 } = options\n\n  const search = state.keysSoFar + key\n  const isRepeated = search.length > 1 && Array.from(search).every((char) => char === search[0])\n\n  const query = isRepeated ? search[0] : search\n\n  let items = _items.slice()\n\n  const next = getByText(items, query, activeId)\n\n  function cleanup() {\n    clearTimeout(state.timer)\n    state.timer = -1\n  }\n\n  function update(value: string) {\n    state.keysSoFar = value\n    cleanup()\n\n    if (value !== \"\") {\n      state.timer = +setTimeout(() => {\n        update(\"\")\n        cleanup()\n      }, timeout)\n    }\n  }\n\n  update(search)\n\n  return next\n}\nexport const getByTypeahead = /*#__PURE__*/ Object.assign(getByTypeaheadImpl, {\n  defaultOptions: { keysSoFar: \"\", timer: -1 },\n  isValidEvent: isValidTypeaheadEvent,\n})\n\nfunction isValidTypeaheadEvent(event: Pick<KeyboardEvent, \"key\" | \"ctrlKey\" | \"metaKey\">) {\n  return event.key.length === 1 && !event.ctrlKey && !event.metaKey\n}\n", "const styleCache = new WeakMap<HTMLElement, any>()\n\nexport function getComputedStyle(el: HTMLElement) {\n  if (!styleCache.has(el)) {\n    const win = el.ownerDocument.defaultView || window\n    styleCache.set(el, win.getComputedStyle(el))\n  }\n  return styleCache.get(el)\n}\n", "export function getEventTarget<T extends EventTarget>(event: Event): T | null {\n  return (event.composedPath?.()[0] ?? event.target) as T | null\n}\n", "import { isHTMLElement } from \"./is-html-element\"\n\nfunction isScrollParent(el: HTMLElement): boolean {\n  const win = el.ownerDocument.defaultView || window\n  const { overflow, overflowX, overflowY } = win.getComputedStyle(el)\n  return /auto|scroll|overlay|hidden/.test(overflow + overflowY + overflowX)\n}\n\nexport function getParent(el: HTMLElement): HTMLElement {\n  if (el.localName === \"html\") return el\n  return el.assignedSlot || el.parentElement || el.ownerDocument.documentElement\n}\n\nexport function getScrollParent(el: HTMLElement): HTMLElement {\n  if ([\"html\", \"body\", \"#document\"].includes(el.localName)) {\n    return el.ownerDocument.body\n  }\n\n  if (isHTMLElement(el) && isScrollParent(el)) {\n    return el\n  }\n\n  return getScrollParent(getParent(el))\n}\n\ntype Target = Array<VisualViewport | Window | HTMLElement | null>\n\nexport function getScrollParents(el: HTMLElement, list: Target = []): Target {\n  const parent = getScrollParent(el)\n  const isBody = parent === el.ownerDocument.body\n  const win = parent.ownerDocument.defaultView || window\n\n  //@ts-expect-error\n  const target = isBody ? [win].concat(win.visualViewport || [], isScrollParent(parent) ? parent : []) : parent\n\n  const parents = list.concat(target)\n  return isBody ? parents : parents.concat(getScrollParents(getParent(<HTMLElement>target)))\n}\n", "import { isHTMLElement } from \"./is-html-element\"\n\nexport function isEditableElement(el: HTMLElement | EventTarget | null) {\n  if (el == null || !isHTMLElement(el)) {\n    return false\n  }\n\n  try {\n    const win = el.ownerDocument.defaultView || window\n    return (\n      (el instanceof win.HTMLInputElement && el.selectionStart != null) ||\n      /(textarea|select)/.test(el.localName) ||\n      el.isContentEditable\n    )\n  } catch {\n    return false\n  }\n}\n", "export const isDom = () => typeof document !== \"undefined\"\n\nexport function getPlatform() {\n  const agent = (navigator as any).userAgentData\n  return agent?.platform ?? navigator.platform\n}\n\nconst pt = (v: RegExp) => isDom() && v.test(getPlatform())\nconst ua = (v: RegExp) => isDom() && v.test(navigator.userAgent)\nconst vn = (v: RegExp) => isDom() && v.test(navigator.vendor)\n\nexport const isTouchDevice = () => isDom() && !!navigator.maxTouchPoints\nexport const isMac = () => pt(/^Mac/) && !isTouchDevice()\nexport const isIPhone = () => pt(/^iPhone/)\nexport const isSafari = () => isApple() && vn(/apple/i)\nexport const isFirefox = () => ua(/firefox\\//i)\nexport const isApple = () => pt(/mac|iphone|ipad|ipod/i)\nexport const isIos = () => isApple() && !isMac()\n", "type Root = Document | Element | null | undefined\n\nexport function queryAll<T extends HTMLElement = HTMLElement>(root: Root, selector: string) {\n  return Array.from(root?.querySelectorAll<T>(selector) ?? [])\n}\n\nexport function query<T extends HTMLElement = HTMLElement>(root: Root, selector: string) {\n  return root?.querySelector<T>(selector)\n}\n", "export function nextTick(fn: VoidFunction) {\n  const set = new Set<VoidFunction>()\n  function raf(fn: VoidFunction) {\n    const id = globalThis.requestAnimationFrame(fn)\n    set.add(() => globalThis.cancelAnimationFrame(id))\n  }\n  raf(() => raf(fn))\n  return function cleanup() {\n    set.forEach((fn) => fn())\n  }\n}\n\nexport function raf(fn: VoidFunction) {\n  const id = globalThis.requestAnimationFrame(fn)\n  return () => {\n    globalThis.cancelAnimationFrame(id)\n  }\n}\n", "export * from \"./attrs\"\nexport * from \"./contains\"\nexport * from \"./create-scope\"\nexport * from \"./env\"\nexport * from \"./get-active-element\"\nexport * from \"./get-by-id\"\nexport * from \"./get-by-text\"\nexport * from \"./get-by-typeahead\"\nexport * from \"./get-computed-style\"\nexport * from \"./get-event-target\"\nexport * from \"./get-scroll-parent\"\nexport * from \"./is-editable-element\"\nexport * from \"./is-html-element\"\nexport * from \"./platform\"\nexport * from \"./query\"\nexport * from \"./raf\"\n\nexport const MAX_Z_INDEX = 2147483647\n"], "mappings": ";AAEO,IAAMA,QAAA,GAAYC,KAAA,IAA+B;EACtD,OAAQA,KAAA,GAAQ,KAAK;AACvB;AAEO,IAAMC,QAAA,GAAYD,KAAA,IAA+B;EACtD,OAAOA,KAAA,GAAQ,SAAS;AAC1B;;;ACRO,SAASE,cAAcC,KAAA,EAAkC;EAC9D,OAAO,OAAOA,KAAA,KAAU,YAAYA,KAAA,EAAOC,QAAA,KAAaC,IAAA,CAAKC,YAAA,IAAgB,OAAOH,KAAA,EAAOI,QAAA,KAAa;AAC1G;;;ACEO,SAASC,SAASC,MAAA,EAAgBC,KAAA,EAAe;EACtD,IAAI,CAACD,MAAA,IAAU,CAACC,KAAA,EAAO,OAAO;EAC9B,IAAI,CAACR,aAAA,CAAcO,MAAM,KAAK,CAACP,aAAA,CAAcQ,KAAK,GAAG,OAAO;EAC5D,OAAOD,MAAA,KAAWC,KAAA,IAASD,MAAA,CAAOD,QAAA,CAASE,KAAK;AAClD;AAEO,IAAMC,WAAA,GAAeC,KAAA,IAC1BJ,QAAA,CAASI,KAAA,CAAMC,aAAA,EAAeD,KAAA,CAAME,MAAM;;;ACT5C,IAAMC,WAAA,GAAeC,IAAA,IAAuC;EAC1D,IAAIA,IAAA,CAAKZ,QAAA,KAAaC,IAAA,CAAKY,aAAA,EAAe,OAAOD,IAAA;EACjD,OAAOA,IAAA,CAAKE,aAAA,IAAiBC,QAAA;AAC/B;AAEO,SAASC,YAAeC,OAAA,EAAY;EACzC,MAAMC,MAAA,GAAS;IACbC,WAAA,EAAcC,GAAA,IAAcA,GAAA,CAAID,WAAA,GAAc,KAAKJ,QAAA;IACnDM,MAAA,EAASD,GAAA,IAAaT,WAAA,CAAYO,MAAA,CAAOC,WAAA,CAAYC,GAAG,CAAC;IACzDE,MAAA,EAASF,GAAA,IAAaF,MAAA,CAAOG,MAAA,CAAOD,GAAG,EAAEG,WAAA,IAAeC,MAAA;IACxDC,gBAAA,EAAmBL,GAAA,IAAaF,MAAA,CAAOG,MAAA,CAAOD,GAAG,EAAEM,aAAA;IACnDC,OAAA,EAASA,CAAsCP,GAAA,EAAUQ,EAAA,KACvDV,MAAA,CAAOC,WAAA,CAAYC,GAAG,EAAES,cAAA,CAAeD,EAAE;EAC7C;EACA,OAAO;IAAE,GAAGV,MAAA;IAAQ,GAAGD;EAAQ;AACjC;;;ACjBA,IAAMa,UAAA,GAAcC,EAAA,IAA4BA,EAAA,CAAG/B,QAAA,KAAaC,IAAA,CAAKY,aAAA;AAE9D,SAASmB,aAAYD,EAAA,EAAsC;EAChE,IAAID,UAAA,CAAWC,EAAE,GAAG,OAAOA,EAAA;EAC3B,OAAOA,EAAA,EAAIjB,aAAA,IAAiBC,QAAA;AAC9B;AAEO,SAASkB,UAAUF,EAAA,EAAiB;EACzC,OAAOA,EAAA,EAAIjB,aAAA,CAAcS,WAAA,IAAeC,MAAA;AAC1C;;;ACTO,SAASC,iBAAiBM,EAAA,EAAqC;EACpE,IAAIL,aAAA,GAAgBK,EAAA,CAAGjB,aAAA,CAAcY,aAAA;EAErC,OAAOA,aAAA,EAAeQ,UAAA,EAAY;IAChC,MAAMC,GAAA,GAAKT,aAAA,CAAcQ,UAAA,CAAWR,aAAA;IACpC,IAAIS,GAAA,KAAOT,aAAA,EAAe,WACrBA,aAAA,GAAgBS,GAAA;EACvB;EAEA,OAAOT,aAAA;AACT;;;ACVO,SAASU,SAAgCC,CAAA,EAAQT,EAAA,EAAY;EAClE,OAAOS,CAAA,CAAEC,IAAA,CAAM1B,IAAA,IAASA,IAAA,CAAKgB,EAAA,KAAOA,EAAE;AACxC;AAEO,SAASW,UAAiCF,CAAA,EAAQT,EAAA,EAAY;EACnE,MAAMY,IAAA,GAAOJ,QAAA,CAASC,CAAA,EAAGT,EAAE;EAC3B,OAAOY,IAAA,GAAOH,CAAA,CAAEI,OAAA,CAAQD,IAAI,IAAI;AAClC;AAEO,SAASE,SAAgCL,CAAA,EAAQT,EAAA,EAAYe,IAAA,GAAO,MAAM;EAC/E,IAAIC,GAAA,GAAML,SAAA,CAAUF,CAAA,EAAGT,EAAE;EACzBgB,GAAA,GAAMD,IAAA,IAAQC,GAAA,GAAM,KAAKP,CAAA,CAAEQ,MAAA,GAASC,IAAA,CAAKC,GAAA,CAAIH,GAAA,GAAM,GAAGP,CAAA,CAAEQ,MAAA,GAAS,CAAC;EAClE,OAAOR,CAAA,CAAEO,GAAG;AACd;AAEO,SAASI,SAAgCX,CAAA,EAAQT,EAAA,EAAYe,IAAA,GAAO,MAAM;EAC/E,IAAIC,GAAA,GAAML,SAAA,CAAUF,CAAA,EAAGT,EAAE;EACzB,IAAIgB,GAAA,KAAQ,IAAI,OAAOD,IAAA,GAAON,CAAA,CAAEA,CAAA,CAAEQ,MAAA,GAAS,CAAC,IAAI;EAChDD,GAAA,GAAMD,IAAA,IAAQC,GAAA,GAAM,IAAIP,CAAA,CAAEQ,MAAA,IAAUR,CAAA,CAAEQ,MAAA,GAASC,IAAA,CAAKG,GAAA,CAAI,GAAGL,GAAA,GAAM,CAAC;EAClE,OAAOP,CAAA,CAAEO,GAAG;AACd;;;AClBA,IAAMM,YAAA,GAAuCV,IAAA,IAAYA,IAAA,CAAKW,OAAA,CAAQC,SAAA,IAAaZ,IAAA,CAAKa,WAAA,IAAe;AAEvG,IAAMC,KAAA,GAAQA,CAACC,SAAA,EAAmBC,MAAA,KAAkBD,SAAA,CAAUE,WAAA,CAAY,EAAEC,UAAA,CAAWF,MAAA,CAAMC,WAAA,CAAY,CAAC;AAE1G,IAAME,IAAA,GAAOA,CAAItB,CAAA,EAAQO,GAAA,KAAgB;EACvC,OAAOP,CAAA,CAAEuB,GAAA,CAAI,CAACC,CAAA,EAAGC,KAAA,KAAUzB,CAAA,EAAGS,IAAA,CAAKG,GAAA,CAAIL,GAAA,EAAK,CAAC,IAAIkB,KAAA,IAASzB,CAAA,CAAEQ,MAAM,CAAC;AACrE;AAEO,SAASkB,UAAiC1B,CAAA,EAAQ2B,IAAA,EAAcC,SAAA,EAA2B;EAChG,MAAMH,KAAA,GAAQG,SAAA,GAAY1B,SAAA,CAAUF,CAAA,EAAG4B,SAAS,IAAI;EACpD,IAAIC,KAAA,GAAQD,SAAA,GAAYN,IAAA,CAAKtB,CAAA,EAAGyB,KAAK,IAAIzB,CAAA;EAEzC,MAAM8B,WAAA,GAAcH,IAAA,CAAKnB,MAAA,KAAW;EAEpC,IAAIsB,WAAA,EAAa;IACfD,KAAA,GAAQA,KAAA,CAAME,MAAA,CAAQ5B,IAAA,IAASA,IAAA,CAAKZ,EAAA,KAAOqC,SAAS;EACtD;EAEA,OAAOC,KAAA,CAAM5B,IAAA,CAAME,IAAA,IAASc,KAAA,CAAMJ,YAAA,CAAaV,IAAI,GAAGwB,IAAI,CAAC;AAC7D;;;ACPA,SAASK,mBAA0CC,MAAA,EAAaC,OAAA,EAA2B;EACzF,MAAM;IAAEC,KAAA;IAAOC,QAAA;IAAUC,GAAA;IAAKC,OAAA,GAAU;EAAI,IAAIJ,OAAA;EAEhD,MAAMK,MAAA,GAASJ,KAAA,CAAMK,SAAA,GAAYH,GAAA;EACjC,MAAMI,UAAA,GAAaF,MAAA,CAAO/B,MAAA,GAAS,KAAKkC,KAAA,CAAMC,IAAA,CAAKJ,MAAM,EAAEK,KAAA,CAAOC,IAAA,IAASA,IAAA,KAASN,MAAA,CAAO,CAAC,CAAC;EAE7F,MAAMpB,MAAA,GAAQsB,UAAA,GAAaF,MAAA,CAAO,CAAC,IAAIA,MAAA;EAEvC,IAAIV,KAAA,GAAQI,MAAA,CAAOa,KAAA,CAAM;EAEzB,MAAMC,IAAA,GAAOrB,SAAA,CAAUG,KAAA,EAAOV,MAAA,EAAOiB,QAAQ;EAE7C,SAASY,QAAA,EAAU;IACjBC,YAAA,CAAad,KAAA,CAAMe,KAAK;IACxBf,KAAA,CAAMe,KAAA,GAAQ;EAChB;EAEA,SAASC,OAAOzF,KAAA,EAAe;IAC7ByE,KAAA,CAAMK,SAAA,GAAY9E,KAAA;IAClBsF,OAAA,CAAQ;IAER,IAAItF,KAAA,KAAU,IAAI;MAChByE,KAAA,CAAMe,KAAA,GAAQ,CAACE,UAAA,CAAW,MAAM;QAC9BD,MAAA,CAAO,EAAE;QACTH,OAAA,CAAQ;MACV,GAAGV,OAAO;IACZ;EACF;EAEAa,MAAA,CAAOZ,MAAM;EAEb,OAAOQ,IAAA;AACT;AACO,IAAMM,cAAA,GAA+B,eAAAC,MAAA,CAAOC,MAAA,CAAOvB,kBAAA,EAAoB;EAC5EwB,cAAA,EAAgB;IAAEhB,SAAA,EAAW;IAAIU,KAAA,EAAO;EAAG;EAC3CO,YAAA,EAAcC;AAChB,CAAC;AAED,SAASA,sBAAsBvF,KAAA,EAA2D;EACxF,OAAOA,KAAA,CAAMkE,GAAA,CAAI7B,MAAA,KAAW,KAAK,CAACrC,KAAA,CAAMwF,OAAA,IAAW,CAACxF,KAAA,CAAMyF,OAAA;AAC5D;;;ACtDA,IAAMC,UAAA,GAAa,mBAAIC,OAAA,CAA0B;AAE1C,SAASC,iBAAiBrE,EAAA,EAAiB;EAChD,IAAI,CAACmE,UAAA,CAAWG,GAAA,CAAItE,EAAE,GAAG;IACvB,MAAMuE,GAAA,GAAMvE,EAAA,CAAGjB,aAAA,CAAcS,WAAA,IAAeC,MAAA;IAC5C0E,UAAA,CAAWK,GAAA,CAAIxE,EAAA,EAAIuE,GAAA,CAAIF,gBAAA,CAAiBrE,EAAE,CAAC;EAC7C;EACA,OAAOmE,UAAA,CAAWM,GAAA,CAAIzE,EAAE;AAC1B;;;ACRO,SAAS0E,eAAsCjG,KAAA,EAAwB;EAC5E,OAAQA,KAAA,CAAMkG,YAAA,GAAe,EAAE,CAAC,KAAKlG,KAAA,CAAME,MAAA;AAC7C;;;ACAA,SAASiG,eAAe5E,EAAA,EAA0B;EAChD,MAAMuE,GAAA,GAAMvE,EAAA,CAAGjB,aAAA,CAAcS,WAAA,IAAeC,MAAA;EAC5C,MAAM;IAAEoF,QAAA;IAAUC,SAAA;IAAWC;EAAU,IAAIR,GAAA,CAAIF,gBAAA,CAAiBrE,EAAE;EAClE,OAAO,6BAA6BgF,IAAA,CAAKH,QAAA,GAAWE,SAAA,GAAYD,SAAS;AAC3E;AAEO,SAASG,UAAUjF,EAAA,EAA8B;EACtD,IAAIA,EAAA,CAAGkF,SAAA,KAAc,QAAQ,OAAOlF,EAAA;EACpC,OAAOA,EAAA,CAAGmF,YAAA,IAAgBnF,EAAA,CAAGoF,aAAA,IAAiBpF,EAAA,CAAGjB,aAAA,CAAcsG,eAAA;AACjE;AAEO,SAASC,gBAAgBtF,EAAA,EAA8B;EAC5D,IAAI,CAAC,QAAQ,QAAQ,WAAW,EAAEuF,QAAA,CAASvF,EAAA,CAAGkF,SAAS,GAAG;IACxD,OAAOlF,EAAA,CAAGjB,aAAA,CAAcyG,IAAA;EAC1B;EAEA,IAAIzH,aAAA,CAAciC,EAAE,KAAK4E,cAAA,CAAe5E,EAAE,GAAG;IAC3C,OAAOA,EAAA;EACT;EAEA,OAAOsF,eAAA,CAAgBL,SAAA,CAAUjF,EAAE,CAAC;AACtC;AAIO,SAASyF,iBAAiBzF,EAAA,EAAiB0F,IAAA,GAAe,EAAC,EAAW;EAC3E,MAAMpH,MAAA,GAASgH,eAAA,CAAgBtF,EAAE;EACjC,MAAM2F,MAAA,GAASrH,MAAA,KAAW0B,EAAA,CAAGjB,aAAA,CAAcyG,IAAA;EAC3C,MAAMjB,GAAA,GAAMjG,MAAA,CAAOS,aAAA,CAAcS,WAAA,IAAeC,MAAA;EAGhD,MAAMd,MAAA,GAASgH,MAAA,GAAS,CAACpB,GAAG,EAAEqB,MAAA,CAAOrB,GAAA,CAAIsB,cAAA,IAAkB,EAAC,EAAGjB,cAAA,CAAetG,MAAM,IAAIA,MAAA,GAAS,EAAE,IAAIA,MAAA;EAEvG,MAAMwH,OAAA,GAAUJ,IAAA,CAAKE,MAAA,CAAOjH,MAAM;EAClC,OAAOgH,MAAA,GAASG,OAAA,GAAUA,OAAA,CAAQF,MAAA,CAAOH,gBAAA,CAAiBR,SAAA,CAAuBtG,MAAM,CAAC,CAAC;AAC3F;;;ACnCO,SAASoH,kBAAkB/F,EAAA,EAAsC;EACtE,IAAIA,EAAA,IAAM,QAAQ,CAACjC,aAAA,CAAciC,EAAE,GAAG;IACpC,OAAO;EACT;EAEA,IAAI;IACF,MAAMuE,GAAA,GAAMvE,EAAA,CAAGjB,aAAA,CAAcS,WAAA,IAAeC,MAAA;IAC5C,OACGO,EAAA,YAAcuE,GAAA,CAAIyB,gBAAA,IAAoBhG,EAAA,CAAGiG,cAAA,IAAkB,QAC5D,oBAAoBjB,IAAA,CAAKhF,EAAA,CAAGkF,SAAS,KACrClF,EAAA,CAAGkG,iBAAA;EAEP,QAAQ;IACN,OAAO;EACT;AACF;;;ACjBO,IAAMC,KAAA,GAAQA,CAAA,KAAM,OAAOnH,QAAA,KAAa;AAExC,SAASoH,YAAA,EAAc;EAC5B,MAAMC,KAAA,GAASC,SAAA,CAAkBC,aAAA;EACjC,OAAOF,KAAA,EAAOG,QAAA,IAAYF,SAAA,CAAUE,QAAA;AACtC;AAEA,IAAMC,EAAA,GAAMnG,CAAA,IAAc6F,KAAA,CAAM,KAAK7F,CAAA,CAAE0E,IAAA,CAAKoB,WAAA,CAAY,CAAC;AACzD,IAAMM,EAAA,GAAMpG,CAAA,IAAc6F,KAAA,CAAM,KAAK7F,CAAA,CAAE0E,IAAA,CAAKsB,SAAA,CAAUK,SAAS;AAC/D,IAAMC,EAAA,GAAMtG,CAAA,IAAc6F,KAAA,CAAM,KAAK7F,CAAA,CAAE0E,IAAA,CAAKsB,SAAA,CAAUO,MAAM;AAErD,IAAMC,aAAA,GAAgBA,CAAA,KAAMX,KAAA,CAAM,KAAK,CAAC,CAACG,SAAA,CAAUS,cAAA;AACnD,IAAMC,KAAA,GAAQA,CAAA,KAAMP,EAAA,CAAG,MAAM,KAAK,CAACK,aAAA,CAAc;AACjD,IAAMG,QAAA,GAAWA,CAAA,KAAMR,EAAA,CAAG,SAAS;AACnC,IAAMS,QAAA,GAAWA,CAAA,KAAMC,OAAA,CAAQ,KAAKP,EAAA,CAAG,QAAQ;AAC/C,IAAMQ,SAAA,GAAYA,CAAA,KAAMV,EAAA,CAAG,YAAY;AACvC,IAAMS,OAAA,GAAUA,CAAA,KAAMV,EAAA,CAAG,uBAAuB;AAChD,IAAMY,KAAA,GAAQA,CAAA,KAAMF,OAAA,CAAQ,KAAK,CAACH,KAAA,CAAM;;;ACfxC,SAASM,SAA8CC,IAAA,EAAYC,QAAA,EAAkB;EAC1F,OAAOxE,KAAA,CAAMC,IAAA,CAAKsE,IAAA,EAAME,gBAAA,CAAoBD,QAAQ,KAAK,EAAE;AAC7D;AAEO,SAASE,MAA2CH,IAAA,EAAYC,QAAA,EAAkB;EACvF,OAAOD,IAAA,EAAMI,aAAA,CAAiBH,QAAQ;AACxC;;;ACRO,SAASI,SAASC,EAAA,EAAkB;EACzC,MAAMrD,GAAA,GAAM,mBAAIsD,GAAA,CAAkB;EAClC,SAASC,KAAIC,GAAA,EAAkB;IAC7B,MAAMnI,EAAA,GAAKoI,UAAA,CAAWC,qBAAA,CAAsBF,GAAE;IAC9CxD,GAAA,CAAI2D,GAAA,CAAI,MAAMF,UAAA,CAAWG,oBAAA,CAAqBvI,EAAE,CAAC;EACnD;EACAkI,IAAA,CAAI,MAAMA,IAAA,CAAIF,EAAE,CAAC;EACjB,OAAO,SAASvE,QAAA,EAAU;IACxBkB,GAAA,CAAI6D,OAAA,CAASL,GAAA,IAAOA,GAAA,CAAG,CAAC;EAC1B;AACF;AAEO,SAASM,IAAIT,EAAA,EAAkB;EACpC,MAAMhI,EAAA,GAAKoI,UAAA,CAAWC,qBAAA,CAAsBL,EAAE;EAC9C,OAAO,MAAM;IACXI,UAAA,CAAWG,oBAAA,CAAqBvI,EAAE;EACpC;AACF;;;ACAO,IAAM0I,WAAA,GAAc"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}