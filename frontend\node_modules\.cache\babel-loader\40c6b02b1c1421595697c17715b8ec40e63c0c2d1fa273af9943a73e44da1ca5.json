{"ast": null, "code": "import { transformProps } from '../../render/html/utils/transform.mjs';\nimport { optimizedAppearDataAttribute } from '../optimized-appear/data-id.mjs';\nimport { animateMotionValue } from './motion-value.mjs';\nimport { isWillChangeMotionValue } from '../../value/use-will-change/is.mjs';\nimport { setTarget } from '../../render/utils/setters.mjs';\nimport { frame } from '../../frameloop/frame.mjs';\n\n/**\n * Decide whether we should block this animation. Previously, we achieved this\n * just by checking whether the key was listed in protectedKeys, but this\n * posed problems if an animation was triggered by afterChildren and protectedKeys\n * had been set to true in the meantime.\n */\nfunction shouldBlockAnimation({\n  protectedKeys,\n  needsAnimating\n}, key) {\n  const shouldBlock = protectedKeys.hasOwnProperty(key) && needsAnimating[key] !== true;\n  needsAnimating[key] = false;\n  return shouldBlock;\n}\nfunction animateTarget(visualElement, definition, {\n  delay = 0,\n  transitionOverride,\n  type\n} = {}) {\n  let {\n    transition = visualElement.getDefaultTransition(),\n    transitionEnd,\n    ...target\n  } = visualElement.makeTargetAnimatable(definition);\n  const willChange = visualElement.getValue(\"willChange\");\n  if (transitionOverride) transition = transitionOverride;\n  const animations = [];\n  const animationTypeState = type && visualElement.animationState && visualElement.animationState.getState()[type];\n  for (const key in target) {\n    const value = visualElement.getValue(key);\n    const valueTarget = target[key];\n    if (!value || valueTarget === undefined || animationTypeState && shouldBlockAnimation(animationTypeState, key)) {\n      continue;\n    }\n    const valueTransition = {\n      delay,\n      elapsed: 0,\n      ...transition\n    };\n    /**\n     * If this is the first time a value is being animated, check\n     * to see if we're handling off from an existing animation.\n     */\n    if (window.HandoffAppearAnimations && !value.hasAnimated) {\n      const appearId = visualElement.getProps()[optimizedAppearDataAttribute];\n      if (appearId) {\n        valueTransition.elapsed = window.HandoffAppearAnimations(appearId, key, value, frame);\n        valueTransition.syncStart = true;\n      }\n    }\n    value.start(animateMotionValue(key, value, valueTarget, visualElement.shouldReduceMotion && transformProps.has(key) ? {\n      type: false\n    } : valueTransition));\n    const animation = value.animation;\n    if (isWillChangeMotionValue(willChange)) {\n      willChange.add(key);\n      animation.then(() => willChange.remove(key));\n    }\n    animations.push(animation);\n  }\n  if (transitionEnd) {\n    Promise.all(animations).then(() => {\n      transitionEnd && setTarget(visualElement, transitionEnd);\n    });\n  }\n  return animations;\n}\nexport { animateTarget };", "map": {"version": 3, "names": ["transformProps", "optimizedAppearDataAttribute", "animateMotionValue", "isWillChangeMotionValue", "<PERSON><PERSON><PERSON><PERSON>", "frame", "shouldBlockAnimation", "protected<PERSON><PERSON>s", "needsAnimating", "key", "shouldBlock", "hasOwnProperty", "animate<PERSON>arget", "visualElement", "definition", "delay", "transitionOverride", "type", "transition", "getDefaultTransition", "transitionEnd", "target", "makeTargetAnimatable", "<PERSON><PERSON><PERSON><PERSON>", "getValue", "animations", "animationTypeState", "animationState", "getState", "value", "valueTarget", "undefined", "valueTransition", "elapsed", "window", "HandoffAppearAnimations", "hasAnimated", "appearId", "getProps", "syncStart", "start", "shouldReduceMotion", "has", "animation", "add", "then", "remove", "push", "Promise", "all"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/framer-motion/dist/es/animation/interfaces/visual-element-target.mjs"], "sourcesContent": ["import { transformProps } from '../../render/html/utils/transform.mjs';\nimport { optimizedAppearDataAttribute } from '../optimized-appear/data-id.mjs';\nimport { animateMotionValue } from './motion-value.mjs';\nimport { isWillChangeMotionValue } from '../../value/use-will-change/is.mjs';\nimport { setTarget } from '../../render/utils/setters.mjs';\nimport { frame } from '../../frameloop/frame.mjs';\n\n/**\n * Decide whether we should block this animation. Previously, we achieved this\n * just by checking whether the key was listed in protectedKeys, but this\n * posed problems if an animation was triggered by afterChildren and protectedKeys\n * had been set to true in the meantime.\n */\nfunction shouldBlockAnimation({ protectedKeys, needsAnimating }, key) {\n    const shouldBlock = protectedKeys.hasOwnProperty(key) && needsAnimating[key] !== true;\n    needsAnimating[key] = false;\n    return shouldBlock;\n}\nfunction animateTarget(visualElement, definition, { delay = 0, transitionOverride, type } = {}) {\n    let { transition = visualElement.getDefaultTransition(), transitionEnd, ...target } = visualElement.makeTargetAnimatable(definition);\n    const willChange = visualElement.getValue(\"willChange\");\n    if (transitionOverride)\n        transition = transitionOverride;\n    const animations = [];\n    const animationTypeState = type &&\n        visualElement.animationState &&\n        visualElement.animationState.getState()[type];\n    for (const key in target) {\n        const value = visualElement.getValue(key);\n        const valueTarget = target[key];\n        if (!value ||\n            valueTarget === undefined ||\n            (animationTypeState &&\n                shouldBlockAnimation(animationTypeState, key))) {\n            continue;\n        }\n        const valueTransition = {\n            delay,\n            elapsed: 0,\n            ...transition,\n        };\n        /**\n         * If this is the first time a value is being animated, check\n         * to see if we're handling off from an existing animation.\n         */\n        if (window.HandoffAppearAnimations && !value.hasAnimated) {\n            const appearId = visualElement.getProps()[optimizedAppearDataAttribute];\n            if (appearId) {\n                valueTransition.elapsed = window.HandoffAppearAnimations(appearId, key, value, frame);\n                valueTransition.syncStart = true;\n            }\n        }\n        value.start(animateMotionValue(key, value, valueTarget, visualElement.shouldReduceMotion && transformProps.has(key)\n            ? { type: false }\n            : valueTransition));\n        const animation = value.animation;\n        if (isWillChangeMotionValue(willChange)) {\n            willChange.add(key);\n            animation.then(() => willChange.remove(key));\n        }\n        animations.push(animation);\n    }\n    if (transitionEnd) {\n        Promise.all(animations).then(() => {\n            transitionEnd && setTarget(visualElement, transitionEnd);\n        });\n    }\n    return animations;\n}\n\nexport { animateTarget };\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,uCAAuC;AACtE,SAASC,4BAA4B,QAAQ,iCAAiC;AAC9E,SAASC,kBAAkB,QAAQ,oBAAoB;AACvD,SAASC,uBAAuB,QAAQ,oCAAoC;AAC5E,SAASC,SAAS,QAAQ,gCAAgC;AAC1D,SAASC,KAAK,QAAQ,2BAA2B;;AAEjD;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,oBAAoBA,CAAC;EAAEC,aAAa;EAAEC;AAAe,CAAC,EAAEC,GAAG,EAAE;EAClE,MAAMC,WAAW,GAAGH,aAAa,CAACI,cAAc,CAACF,GAAG,CAAC,IAAID,cAAc,CAACC,GAAG,CAAC,KAAK,IAAI;EACrFD,cAAc,CAACC,GAAG,CAAC,GAAG,KAAK;EAC3B,OAAOC,WAAW;AACtB;AACA,SAASE,aAAaA,CAACC,aAAa,EAAEC,UAAU,EAAE;EAAEC,KAAK,GAAG,CAAC;EAAEC,kBAAkB;EAAEC;AAAK,CAAC,GAAG,CAAC,CAAC,EAAE;EAC5F,IAAI;IAAEC,UAAU,GAAGL,aAAa,CAACM,oBAAoB,CAAC,CAAC;IAAEC,aAAa;IAAE,GAAGC;EAAO,CAAC,GAAGR,aAAa,CAACS,oBAAoB,CAACR,UAAU,CAAC;EACpI,MAAMS,UAAU,GAAGV,aAAa,CAACW,QAAQ,CAAC,YAAY,CAAC;EACvD,IAAIR,kBAAkB,EAClBE,UAAU,GAAGF,kBAAkB;EACnC,MAAMS,UAAU,GAAG,EAAE;EACrB,MAAMC,kBAAkB,GAAGT,IAAI,IAC3BJ,aAAa,CAACc,cAAc,IAC5Bd,aAAa,CAACc,cAAc,CAACC,QAAQ,CAAC,CAAC,CAACX,IAAI,CAAC;EACjD,KAAK,MAAMR,GAAG,IAAIY,MAAM,EAAE;IACtB,MAAMQ,KAAK,GAAGhB,aAAa,CAACW,QAAQ,CAACf,GAAG,CAAC;IACzC,MAAMqB,WAAW,GAAGT,MAAM,CAACZ,GAAG,CAAC;IAC/B,IAAI,CAACoB,KAAK,IACNC,WAAW,KAAKC,SAAS,IACxBL,kBAAkB,IACfpB,oBAAoB,CAACoB,kBAAkB,EAAEjB,GAAG,CAAE,EAAE;MACpD;IACJ;IACA,MAAMuB,eAAe,GAAG;MACpBjB,KAAK;MACLkB,OAAO,EAAE,CAAC;MACV,GAAGf;IACP,CAAC;IACD;AACR;AACA;AACA;IACQ,IAAIgB,MAAM,CAACC,uBAAuB,IAAI,CAACN,KAAK,CAACO,WAAW,EAAE;MACtD,MAAMC,QAAQ,GAAGxB,aAAa,CAACyB,QAAQ,CAAC,CAAC,CAACrC,4BAA4B,CAAC;MACvE,IAAIoC,QAAQ,EAAE;QACVL,eAAe,CAACC,OAAO,GAAGC,MAAM,CAACC,uBAAuB,CAACE,QAAQ,EAAE5B,GAAG,EAAEoB,KAAK,EAAExB,KAAK,CAAC;QACrF2B,eAAe,CAACO,SAAS,GAAG,IAAI;MACpC;IACJ;IACAV,KAAK,CAACW,KAAK,CAACtC,kBAAkB,CAACO,GAAG,EAAEoB,KAAK,EAAEC,WAAW,EAAEjB,aAAa,CAAC4B,kBAAkB,IAAIzC,cAAc,CAAC0C,GAAG,CAACjC,GAAG,CAAC,GAC7G;MAAEQ,IAAI,EAAE;IAAM,CAAC,GACfe,eAAe,CAAC,CAAC;IACvB,MAAMW,SAAS,GAAGd,KAAK,CAACc,SAAS;IACjC,IAAIxC,uBAAuB,CAACoB,UAAU,CAAC,EAAE;MACrCA,UAAU,CAACqB,GAAG,CAACnC,GAAG,CAAC;MACnBkC,SAAS,CAACE,IAAI,CAAC,MAAMtB,UAAU,CAACuB,MAAM,CAACrC,GAAG,CAAC,CAAC;IAChD;IACAgB,UAAU,CAACsB,IAAI,CAACJ,SAAS,CAAC;EAC9B;EACA,IAAIvB,aAAa,EAAE;IACf4B,OAAO,CAACC,GAAG,CAACxB,UAAU,CAAC,CAACoB,IAAI,CAAC,MAAM;MAC/BzB,aAAa,IAAIhB,SAAS,CAACS,aAAa,EAAEO,aAAa,CAAC;IAC5D,CAAC,CAAC;EACN;EACA,OAAOK,UAAU;AACrB;AAEA,SAASb,aAAa"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}