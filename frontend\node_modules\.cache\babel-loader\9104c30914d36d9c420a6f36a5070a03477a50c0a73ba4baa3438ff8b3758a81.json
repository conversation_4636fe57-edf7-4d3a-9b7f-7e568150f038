{"ast": null, "code": "// src/add-dom-event.ts\nfunction addDomEvent(target, eventName, handler, options) {\n  target.addEventListener(eventName, handler, options);\n  return () => {\n    target.removeEventListener(eventName, handler, options);\n  };\n}\nexport { addDomEvent };", "map": {"version": 3, "names": ["addDomEvent", "target", "eventName", "handler", "options", "addEventListener", "removeEventListener"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/@chakra-ui/event-utils/dist/chunk-6K7SS4J6.mjs"], "sourcesContent": ["// src/add-dom-event.ts\nfunction addDomEvent(target, eventName, handler, options) {\n  target.addEventListener(eventName, handler, options);\n  return () => {\n    target.removeEventListener(eventName, handler, options);\n  };\n}\n\nexport {\n  addDomEvent\n};\n"], "mappings": "AAAA;AACA,SAASA,WAAWA,CAACC,MAAM,EAAEC,SAAS,EAAEC,OAAO,EAAEC,OAAO,EAAE;EACxDH,MAAM,CAACI,gBAAgB,CAACH,SAAS,EAAEC,OAAO,EAAEC,OAAO,CAAC;EACpD,OAAO,MAAM;IACXH,MAAM,CAACK,mBAAmB,CAACJ,SAAS,EAAEC,OAAO,EAAEC,OAAO,CAAC;EACzD,CAAC;AACH;AAEA,SACEJ,WAAW"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}