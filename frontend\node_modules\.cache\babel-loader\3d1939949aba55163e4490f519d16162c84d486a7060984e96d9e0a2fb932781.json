{"ast": null, "code": "import { invariant } from '../../utils/errors.mjs';\nimport { PanSession } from '../pan/PanSession.mjs';\nimport { getGlobalLock } from './utils/lock.mjs';\nimport { isRefObject } from '../../utils/is-ref-object.mjs';\nimport { addPointerEvent } from '../../events/add-pointer-event.mjs';\nimport { applyConstraints, calcRelativeConstraints, resolveDragElastic, calcViewportConstraints, defaultElastic, rebaseAxisConstraints, calcOrigin } from './utils/constraints.mjs';\nimport { createBox } from '../../projection/geometry/models.mjs';\nimport { eachAxis } from '../../projection/utils/each-axis.mjs';\nimport { measurePageBox } from '../../projection/utils/measure.mjs';\nimport { extractEventInfo } from '../../events/event-info.mjs';\nimport { convertBoxToBoundingBox, convertBoundingBoxToBox } from '../../projection/geometry/conversion.mjs';\nimport { addDomEvent } from '../../events/add-dom-event.mjs';\nimport { calcLength } from '../../projection/geometry/delta-calc.mjs';\nimport { mix } from '../../utils/mix.mjs';\nimport { percent } from '../../value/types/numbers/units.mjs';\nimport { animateMotionValue } from '../../animation/interfaces/motion-value.mjs';\nimport { frame } from '../../frameloop/frame.mjs';\nconst elementDragControls = new WeakMap();\n/**\n *\n */\n// let latestPointerEvent: PointerEvent\nclass VisualElementDragControls {\n  constructor(visualElement) {\n    // This is a reference to the global drag gesture lock, ensuring only one component\n    // can \"capture\" the drag of one or both axes.\n    // TODO: Look into moving this into pansession?\n    this.openGlobalLock = null;\n    this.isDragging = false;\n    this.currentDirection = null;\n    this.originPoint = {\n      x: 0,\n      y: 0\n    };\n    /**\n     * The permitted boundaries of travel, in pixels.\n     */\n    this.constraints = false;\n    this.hasMutatedConstraints = false;\n    /**\n     * The per-axis resolved elastic values.\n     */\n    this.elastic = createBox();\n    this.visualElement = visualElement;\n  }\n  start(originEvent, {\n    snapToCursor = false\n  } = {}) {\n    /**\n     * Don't start dragging if this component is exiting\n     */\n    const {\n      presenceContext\n    } = this.visualElement;\n    if (presenceContext && presenceContext.isPresent === false) return;\n    const onSessionStart = event => {\n      // Stop any animations on both axis values immediately. This allows the user to throw and catch\n      // the component.\n      this.stopAnimation();\n      if (snapToCursor) {\n        this.snapToCursor(extractEventInfo(event, \"page\").point);\n      }\n    };\n    const onStart = (event, info) => {\n      // Attempt to grab the global drag gesture lock - maybe make this part of PanSession\n      const {\n        drag,\n        dragPropagation,\n        onDragStart\n      } = this.getProps();\n      if (drag && !dragPropagation) {\n        if (this.openGlobalLock) this.openGlobalLock();\n        this.openGlobalLock = getGlobalLock(drag);\n        // If we don 't have the lock, don't start dragging\n        if (!this.openGlobalLock) return;\n      }\n      this.isDragging = true;\n      this.currentDirection = null;\n      this.resolveConstraints();\n      if (this.visualElement.projection) {\n        this.visualElement.projection.isAnimationBlocked = true;\n        this.visualElement.projection.target = undefined;\n      }\n      /**\n       * Record gesture origin\n       */\n      eachAxis(axis => {\n        let current = this.getAxisMotionValue(axis).get() || 0;\n        /**\n         * If the MotionValue is a percentage value convert to px\n         */\n        if (percent.test(current)) {\n          const {\n            projection\n          } = this.visualElement;\n          if (projection && projection.layout) {\n            const measuredAxis = projection.layout.layoutBox[axis];\n            if (measuredAxis) {\n              const length = calcLength(measuredAxis);\n              current = length * (parseFloat(current) / 100);\n            }\n          }\n        }\n        this.originPoint[axis] = current;\n      });\n      // Fire onDragStart event\n      if (onDragStart) {\n        frame.update(() => onDragStart(event, info), false, true);\n      }\n      const {\n        animationState\n      } = this.visualElement;\n      animationState && animationState.setActive(\"whileDrag\", true);\n    };\n    const onMove = (event, info) => {\n      // latestPointerEvent = event\n      const {\n        dragPropagation,\n        dragDirectionLock,\n        onDirectionLock,\n        onDrag\n      } = this.getProps();\n      // If we didn't successfully receive the gesture lock, early return.\n      if (!dragPropagation && !this.openGlobalLock) return;\n      const {\n        offset\n      } = info;\n      // Attempt to detect drag direction if directionLock is true\n      if (dragDirectionLock && this.currentDirection === null) {\n        this.currentDirection = getCurrentDirection(offset);\n        // If we've successfully set a direction, notify listener\n        if (this.currentDirection !== null) {\n          onDirectionLock && onDirectionLock(this.currentDirection);\n        }\n        return;\n      }\n      // Update each point with the latest position\n      this.updateAxis(\"x\", info.point, offset);\n      this.updateAxis(\"y\", info.point, offset);\n      /**\n       * Ideally we would leave the renderer to fire naturally at the end of\n       * this frame but if the element is about to change layout as the result\n       * of a re-render we want to ensure the browser can read the latest\n       * bounding box to ensure the pointer and element don't fall out of sync.\n       */\n      this.visualElement.render();\n      /**\n       * This must fire after the render call as it might trigger a state\n       * change which itself might trigger a layout update.\n       */\n      onDrag && onDrag(event, info);\n    };\n    const onSessionEnd = (event, info) => this.stop(event, info);\n    this.panSession = new PanSession(originEvent, {\n      onSessionStart,\n      onStart,\n      onMove,\n      onSessionEnd\n    }, {\n      transformPagePoint: this.visualElement.getTransformPagePoint()\n    });\n  }\n  stop(event, info) {\n    const isDragging = this.isDragging;\n    this.cancel();\n    if (!isDragging) return;\n    const {\n      velocity\n    } = info;\n    this.startAnimation(velocity);\n    const {\n      onDragEnd\n    } = this.getProps();\n    if (onDragEnd) {\n      frame.update(() => onDragEnd(event, info));\n    }\n  }\n  cancel() {\n    this.isDragging = false;\n    const {\n      projection,\n      animationState\n    } = this.visualElement;\n    if (projection) {\n      projection.isAnimationBlocked = false;\n    }\n    this.panSession && this.panSession.end();\n    this.panSession = undefined;\n    const {\n      dragPropagation\n    } = this.getProps();\n    if (!dragPropagation && this.openGlobalLock) {\n      this.openGlobalLock();\n      this.openGlobalLock = null;\n    }\n    animationState && animationState.setActive(\"whileDrag\", false);\n  }\n  updateAxis(axis, _point, offset) {\n    const {\n      drag\n    } = this.getProps();\n    // If we're not dragging this axis, do an early return.\n    if (!offset || !shouldDrag(axis, drag, this.currentDirection)) return;\n    const axisValue = this.getAxisMotionValue(axis);\n    let next = this.originPoint[axis] + offset[axis];\n    // Apply constraints\n    if (this.constraints && this.constraints[axis]) {\n      next = applyConstraints(next, this.constraints[axis], this.elastic[axis]);\n    }\n    axisValue.set(next);\n  }\n  resolveConstraints() {\n    const {\n      dragConstraints,\n      dragElastic\n    } = this.getProps();\n    const {\n      layout\n    } = this.visualElement.projection || {};\n    const prevConstraints = this.constraints;\n    if (dragConstraints && isRefObject(dragConstraints)) {\n      if (!this.constraints) {\n        this.constraints = this.resolveRefConstraints();\n      }\n    } else {\n      if (dragConstraints && layout) {\n        this.constraints = calcRelativeConstraints(layout.layoutBox, dragConstraints);\n      } else {\n        this.constraints = false;\n      }\n    }\n    this.elastic = resolveDragElastic(dragElastic);\n    /**\n     * If we're outputting to external MotionValues, we want to rebase the measured constraints\n     * from viewport-relative to component-relative.\n     */\n    if (prevConstraints !== this.constraints && layout && this.constraints && !this.hasMutatedConstraints) {\n      eachAxis(axis => {\n        if (this.getAxisMotionValue(axis)) {\n          this.constraints[axis] = rebaseAxisConstraints(layout.layoutBox[axis], this.constraints[axis]);\n        }\n      });\n    }\n  }\n  resolveRefConstraints() {\n    const {\n      dragConstraints: constraints,\n      onMeasureDragConstraints\n    } = this.getProps();\n    if (!constraints || !isRefObject(constraints)) return false;\n    const constraintsElement = constraints.current;\n    invariant(constraintsElement !== null, \"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.\");\n    const {\n      projection\n    } = this.visualElement;\n    // TODO\n    if (!projection || !projection.layout) return false;\n    const constraintsBox = measurePageBox(constraintsElement, projection.root, this.visualElement.getTransformPagePoint());\n    let measuredConstraints = calcViewportConstraints(projection.layout.layoutBox, constraintsBox);\n    /**\n     * If there's an onMeasureDragConstraints listener we call it and\n     * if different constraints are returned, set constraints to that\n     */\n    if (onMeasureDragConstraints) {\n      const userConstraints = onMeasureDragConstraints(convertBoxToBoundingBox(measuredConstraints));\n      this.hasMutatedConstraints = !!userConstraints;\n      if (userConstraints) {\n        measuredConstraints = convertBoundingBoxToBox(userConstraints);\n      }\n    }\n    return measuredConstraints;\n  }\n  startAnimation(velocity) {\n    const {\n      drag,\n      dragMomentum,\n      dragElastic,\n      dragTransition,\n      dragSnapToOrigin,\n      onDragTransitionEnd\n    } = this.getProps();\n    const constraints = this.constraints || {};\n    const momentumAnimations = eachAxis(axis => {\n      if (!shouldDrag(axis, drag, this.currentDirection)) {\n        return;\n      }\n      let transition = constraints && constraints[axis] || {};\n      if (dragSnapToOrigin) transition = {\n        min: 0,\n        max: 0\n      };\n      /**\n       * Overdamp the boundary spring if `dragElastic` is disabled. There's still a frame\n       * of spring animations so we should look into adding a disable spring option to `inertia`.\n       * We could do something here where we affect the `bounceStiffness` and `bounceDamping`\n       * using the value of `dragElastic`.\n       */\n      const bounceStiffness = dragElastic ? 200 : 1000000;\n      const bounceDamping = dragElastic ? 40 : 10000000;\n      const inertia = {\n        type: \"inertia\",\n        velocity: dragMomentum ? velocity[axis] : 0,\n        bounceStiffness,\n        bounceDamping,\n        timeConstant: 750,\n        restDelta: 1,\n        restSpeed: 10,\n        ...dragTransition,\n        ...transition\n      };\n      // If we're not animating on an externally-provided `MotionValue` we can use the\n      // component's animation controls which will handle interactions with whileHover (etc),\n      // otherwise we just have to animate the `MotionValue` itself.\n      return this.startAxisValueAnimation(axis, inertia);\n    });\n    // Run all animations and then resolve the new drag constraints.\n    return Promise.all(momentumAnimations).then(onDragTransitionEnd);\n  }\n  startAxisValueAnimation(axis, transition) {\n    const axisValue = this.getAxisMotionValue(axis);\n    return axisValue.start(animateMotionValue(axis, axisValue, 0, transition));\n  }\n  stopAnimation() {\n    eachAxis(axis => this.getAxisMotionValue(axis).stop());\n  }\n  /**\n   * Drag works differently depending on which props are provided.\n   *\n   * - If _dragX and _dragY are provided, we output the gesture delta directly to those motion values.\n   * - Otherwise, we apply the delta to the x/y motion values.\n   */\n  getAxisMotionValue(axis) {\n    const dragKey = \"_drag\" + axis.toUpperCase();\n    const props = this.visualElement.getProps();\n    const externalMotionValue = props[dragKey];\n    return externalMotionValue ? externalMotionValue : this.visualElement.getValue(axis, (props.initial ? props.initial[axis] : undefined) || 0);\n  }\n  snapToCursor(point) {\n    eachAxis(axis => {\n      const {\n        drag\n      } = this.getProps();\n      // If we're not dragging this axis, do an early return.\n      if (!shouldDrag(axis, drag, this.currentDirection)) return;\n      const {\n        projection\n      } = this.visualElement;\n      const axisValue = this.getAxisMotionValue(axis);\n      if (projection && projection.layout) {\n        const {\n          min,\n          max\n        } = projection.layout.layoutBox[axis];\n        axisValue.set(point[axis] - mix(min, max, 0.5));\n      }\n    });\n  }\n  /**\n   * When the viewport resizes we want to check if the measured constraints\n   * have changed and, if so, reposition the element within those new constraints\n   * relative to where it was before the resize.\n   */\n  scalePositionWithinConstraints() {\n    if (!this.visualElement.current) return;\n    const {\n      drag,\n      dragConstraints\n    } = this.getProps();\n    const {\n      projection\n    } = this.visualElement;\n    if (!isRefObject(dragConstraints) || !projection || !this.constraints) return;\n    /**\n     * Stop current animations as there can be visual glitching if we try to do\n     * this mid-animation\n     */\n    this.stopAnimation();\n    /**\n     * Record the relative position of the dragged element relative to the\n     * constraints box and save as a progress value.\n     */\n    const boxProgress = {\n      x: 0,\n      y: 0\n    };\n    eachAxis(axis => {\n      const axisValue = this.getAxisMotionValue(axis);\n      if (axisValue) {\n        const latest = axisValue.get();\n        boxProgress[axis] = calcOrigin({\n          min: latest,\n          max: latest\n        }, this.constraints[axis]);\n      }\n    });\n    /**\n     * Update the layout of this element and resolve the latest drag constraints\n     */\n    const {\n      transformTemplate\n    } = this.visualElement.getProps();\n    this.visualElement.current.style.transform = transformTemplate ? transformTemplate({}, \"\") : \"none\";\n    projection.root && projection.root.updateScroll();\n    projection.updateLayout();\n    this.resolveConstraints();\n    /**\n     * For each axis, calculate the current progress of the layout axis\n     * within the new constraints.\n     */\n    eachAxis(axis => {\n      if (!shouldDrag(axis, drag, null)) return;\n      /**\n       * Calculate a new transform based on the previous box progress\n       */\n      const axisValue = this.getAxisMotionValue(axis);\n      const {\n        min,\n        max\n      } = this.constraints[axis];\n      axisValue.set(mix(min, max, boxProgress[axis]));\n    });\n  }\n  addListeners() {\n    if (!this.visualElement.current) return;\n    elementDragControls.set(this.visualElement, this);\n    const element = this.visualElement.current;\n    /**\n     * Attach a pointerdown event listener on this DOM element to initiate drag tracking.\n     */\n    const stopPointerListener = addPointerEvent(element, \"pointerdown\", event => {\n      const {\n        drag,\n        dragListener = true\n      } = this.getProps();\n      drag && dragListener && this.start(event);\n    });\n    const measureDragConstraints = () => {\n      const {\n        dragConstraints\n      } = this.getProps();\n      if (isRefObject(dragConstraints)) {\n        this.constraints = this.resolveRefConstraints();\n      }\n    };\n    const {\n      projection\n    } = this.visualElement;\n    const stopMeasureLayoutListener = projection.addEventListener(\"measure\", measureDragConstraints);\n    if (projection && !projection.layout) {\n      projection.root && projection.root.updateScroll();\n      projection.updateLayout();\n    }\n    measureDragConstraints();\n    /**\n     * Attach a window resize listener to scale the draggable target within its defined\n     * constraints as the window resizes.\n     */\n    const stopResizeListener = addDomEvent(window, \"resize\", () => this.scalePositionWithinConstraints());\n    /**\n     * If the element's layout changes, calculate the delta and apply that to\n     * the drag gesture's origin point.\n     */\n    const stopLayoutUpdateListener = projection.addEventListener(\"didUpdate\", ({\n      delta,\n      hasLayoutChanged\n    }) => {\n      if (this.isDragging && hasLayoutChanged) {\n        eachAxis(axis => {\n          const motionValue = this.getAxisMotionValue(axis);\n          if (!motionValue) return;\n          this.originPoint[axis] += delta[axis].translate;\n          motionValue.set(motionValue.get() + delta[axis].translate);\n        });\n        this.visualElement.render();\n      }\n    });\n    return () => {\n      stopResizeListener();\n      stopPointerListener();\n      stopMeasureLayoutListener();\n      stopLayoutUpdateListener && stopLayoutUpdateListener();\n    };\n  }\n  getProps() {\n    const props = this.visualElement.getProps();\n    const {\n      drag = false,\n      dragDirectionLock = false,\n      dragPropagation = false,\n      dragConstraints = false,\n      dragElastic = defaultElastic,\n      dragMomentum = true\n    } = props;\n    return {\n      ...props,\n      drag,\n      dragDirectionLock,\n      dragPropagation,\n      dragConstraints,\n      dragElastic,\n      dragMomentum\n    };\n  }\n}\nfunction shouldDrag(direction, drag, currentDirection) {\n  return (drag === true || drag === direction) && (currentDirection === null || currentDirection === direction);\n}\n/**\n * Based on an x/y offset determine the current drag direction. If both axis' offsets are lower\n * than the provided threshold, return `null`.\n *\n * @param offset - The x/y offset from origin.\n * @param lockThreshold - (Optional) - the minimum absolute offset before we can determine a drag direction.\n */\nfunction getCurrentDirection(offset, lockThreshold = 10) {\n  let direction = null;\n  if (Math.abs(offset.y) > lockThreshold) {\n    direction = \"y\";\n  } else if (Math.abs(offset.x) > lockThreshold) {\n    direction = \"x\";\n  }\n  return direction;\n}\nexport { VisualElementDragControls, elementDragControls };", "map": {"version": 3, "names": ["invariant", "PanSession", "getGlobalLock", "isRefObject", "addPointerEvent", "applyConstraints", "calcRelativeConstraints", "resolveDragElastic", "calcViewportConstraints", "defaultElastic", "rebaseAxisConstraints", "calcOrigin", "createBox", "eachAxis", "measurePageBox", "extractEventInfo", "convertBoxToBoundingBox", "convertBoundingBoxToBox", "addDomEvent", "calcLength", "mix", "percent", "animateMotionValue", "frame", "elementDragControls", "WeakMap", "VisualElementDragControls", "constructor", "visualElement", "openGlobalLock", "isDragging", "currentDirection", "originPoint", "x", "y", "constraints", "hasMutatedConstraints", "elastic", "start", "originEvent", "snapToCursor", "presenceContext", "isPresent", "onSessionStart", "event", "stopAnimation", "point", "onStart", "info", "drag", "dragPropagation", "onDragStart", "getProps", "resolveConstraints", "projection", "isAnimationBlocked", "target", "undefined", "axis", "current", "getAxisMotionValue", "get", "test", "layout", "measuredAxis", "layoutBox", "length", "parseFloat", "update", "animationState", "setActive", "onMove", "dragDirectionLock", "onDirectionLock", "onDrag", "offset", "getCurrentDirection", "updateAxis", "render", "onSessionEnd", "stop", "panSession", "transformPagePoint", "getTransformPagePoint", "cancel", "velocity", "startAnimation", "onDragEnd", "end", "_point", "shouldDrag", "axisValue", "next", "set", "dragConstraints", "dragElastic", "prevConstraints", "resolveRefConstraints", "onMeasureDragConstraints", "constraintsElement", "constraintsBox", "root", "measuredConstraints", "userConstraints", "dragMomentum", "dragTransition", "dragSnapToO<PERSON>in", "onDragTransitionEnd", "momentumAnimations", "transition", "min", "max", "bounceStiffness", "bounceDamping", "inertia", "type", "timeConstant", "restDelta", "restSpeed", "startAxisValueAnimation", "Promise", "all", "then", "drag<PERSON>ey", "toUpperCase", "props", "externalMotionValue", "getValue", "initial", "scalePositionWithinConstraints", "boxProgress", "latest", "transformTemplate", "style", "transform", "updateScroll", "updateLayout", "addListeners", "element", "stopPointerListener", "dragListener", "measureDragConstraints", "stopMeasureLayoutListener", "addEventListener", "stopResizeListener", "window", "stopLayoutUpdateListener", "delta", "hasLayoutChanged", "motionValue", "translate", "direction", "lockThreshold", "Math", "abs"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/framer-motion/dist/es/gestures/drag/VisualElementDragControls.mjs"], "sourcesContent": ["import { invariant } from '../../utils/errors.mjs';\nimport { PanSession } from '../pan/PanSession.mjs';\nimport { getGlobalLock } from './utils/lock.mjs';\nimport { isRefObject } from '../../utils/is-ref-object.mjs';\nimport { addPointerEvent } from '../../events/add-pointer-event.mjs';\nimport { applyConstraints, calcRelativeConstraints, resolveDragElastic, calcViewportConstraints, defaultElastic, rebaseAxisConstraints, calcOrigin } from './utils/constraints.mjs';\nimport { createBox } from '../../projection/geometry/models.mjs';\nimport { eachAxis } from '../../projection/utils/each-axis.mjs';\nimport { measurePageBox } from '../../projection/utils/measure.mjs';\nimport { extractEventInfo } from '../../events/event-info.mjs';\nimport { convertBoxToBoundingBox, convertBoundingBoxToBox } from '../../projection/geometry/conversion.mjs';\nimport { addDomEvent } from '../../events/add-dom-event.mjs';\nimport { calcLength } from '../../projection/geometry/delta-calc.mjs';\nimport { mix } from '../../utils/mix.mjs';\nimport { percent } from '../../value/types/numbers/units.mjs';\nimport { animateMotionValue } from '../../animation/interfaces/motion-value.mjs';\nimport { frame } from '../../frameloop/frame.mjs';\n\nconst elementDragControls = new WeakMap();\n/**\n *\n */\n// let latestPointerEvent: PointerEvent\nclass VisualElementDragControls {\n    constructor(visualElement) {\n        // This is a reference to the global drag gesture lock, ensuring only one component\n        // can \"capture\" the drag of one or both axes.\n        // TODO: Look into moving this into pansession?\n        this.openGlobalLock = null;\n        this.isDragging = false;\n        this.currentDirection = null;\n        this.originPoint = { x: 0, y: 0 };\n        /**\n         * The permitted boundaries of travel, in pixels.\n         */\n        this.constraints = false;\n        this.hasMutatedConstraints = false;\n        /**\n         * The per-axis resolved elastic values.\n         */\n        this.elastic = createBox();\n        this.visualElement = visualElement;\n    }\n    start(originEvent, { snapToCursor = false } = {}) {\n        /**\n         * Don't start dragging if this component is exiting\n         */\n        const { presenceContext } = this.visualElement;\n        if (presenceContext && presenceContext.isPresent === false)\n            return;\n        const onSessionStart = (event) => {\n            // Stop any animations on both axis values immediately. This allows the user to throw and catch\n            // the component.\n            this.stopAnimation();\n            if (snapToCursor) {\n                this.snapToCursor(extractEventInfo(event, \"page\").point);\n            }\n        };\n        const onStart = (event, info) => {\n            // Attempt to grab the global drag gesture lock - maybe make this part of PanSession\n            const { drag, dragPropagation, onDragStart } = this.getProps();\n            if (drag && !dragPropagation) {\n                if (this.openGlobalLock)\n                    this.openGlobalLock();\n                this.openGlobalLock = getGlobalLock(drag);\n                // If we don 't have the lock, don't start dragging\n                if (!this.openGlobalLock)\n                    return;\n            }\n            this.isDragging = true;\n            this.currentDirection = null;\n            this.resolveConstraints();\n            if (this.visualElement.projection) {\n                this.visualElement.projection.isAnimationBlocked = true;\n                this.visualElement.projection.target = undefined;\n            }\n            /**\n             * Record gesture origin\n             */\n            eachAxis((axis) => {\n                let current = this.getAxisMotionValue(axis).get() || 0;\n                /**\n                 * If the MotionValue is a percentage value convert to px\n                 */\n                if (percent.test(current)) {\n                    const { projection } = this.visualElement;\n                    if (projection && projection.layout) {\n                        const measuredAxis = projection.layout.layoutBox[axis];\n                        if (measuredAxis) {\n                            const length = calcLength(measuredAxis);\n                            current = length * (parseFloat(current) / 100);\n                        }\n                    }\n                }\n                this.originPoint[axis] = current;\n            });\n            // Fire onDragStart event\n            if (onDragStart) {\n                frame.update(() => onDragStart(event, info), false, true);\n            }\n            const { animationState } = this.visualElement;\n            animationState && animationState.setActive(\"whileDrag\", true);\n        };\n        const onMove = (event, info) => {\n            // latestPointerEvent = event\n            const { dragPropagation, dragDirectionLock, onDirectionLock, onDrag, } = this.getProps();\n            // If we didn't successfully receive the gesture lock, early return.\n            if (!dragPropagation && !this.openGlobalLock)\n                return;\n            const { offset } = info;\n            // Attempt to detect drag direction if directionLock is true\n            if (dragDirectionLock && this.currentDirection === null) {\n                this.currentDirection = getCurrentDirection(offset);\n                // If we've successfully set a direction, notify listener\n                if (this.currentDirection !== null) {\n                    onDirectionLock && onDirectionLock(this.currentDirection);\n                }\n                return;\n            }\n            // Update each point with the latest position\n            this.updateAxis(\"x\", info.point, offset);\n            this.updateAxis(\"y\", info.point, offset);\n            /**\n             * Ideally we would leave the renderer to fire naturally at the end of\n             * this frame but if the element is about to change layout as the result\n             * of a re-render we want to ensure the browser can read the latest\n             * bounding box to ensure the pointer and element don't fall out of sync.\n             */\n            this.visualElement.render();\n            /**\n             * This must fire after the render call as it might trigger a state\n             * change which itself might trigger a layout update.\n             */\n            onDrag && onDrag(event, info);\n        };\n        const onSessionEnd = (event, info) => this.stop(event, info);\n        this.panSession = new PanSession(originEvent, {\n            onSessionStart,\n            onStart,\n            onMove,\n            onSessionEnd,\n        }, { transformPagePoint: this.visualElement.getTransformPagePoint() });\n    }\n    stop(event, info) {\n        const isDragging = this.isDragging;\n        this.cancel();\n        if (!isDragging)\n            return;\n        const { velocity } = info;\n        this.startAnimation(velocity);\n        const { onDragEnd } = this.getProps();\n        if (onDragEnd) {\n            frame.update(() => onDragEnd(event, info));\n        }\n    }\n    cancel() {\n        this.isDragging = false;\n        const { projection, animationState } = this.visualElement;\n        if (projection) {\n            projection.isAnimationBlocked = false;\n        }\n        this.panSession && this.panSession.end();\n        this.panSession = undefined;\n        const { dragPropagation } = this.getProps();\n        if (!dragPropagation && this.openGlobalLock) {\n            this.openGlobalLock();\n            this.openGlobalLock = null;\n        }\n        animationState && animationState.setActive(\"whileDrag\", false);\n    }\n    updateAxis(axis, _point, offset) {\n        const { drag } = this.getProps();\n        // If we're not dragging this axis, do an early return.\n        if (!offset || !shouldDrag(axis, drag, this.currentDirection))\n            return;\n        const axisValue = this.getAxisMotionValue(axis);\n        let next = this.originPoint[axis] + offset[axis];\n        // Apply constraints\n        if (this.constraints && this.constraints[axis]) {\n            next = applyConstraints(next, this.constraints[axis], this.elastic[axis]);\n        }\n        axisValue.set(next);\n    }\n    resolveConstraints() {\n        const { dragConstraints, dragElastic } = this.getProps();\n        const { layout } = this.visualElement.projection || {};\n        const prevConstraints = this.constraints;\n        if (dragConstraints && isRefObject(dragConstraints)) {\n            if (!this.constraints) {\n                this.constraints = this.resolveRefConstraints();\n            }\n        }\n        else {\n            if (dragConstraints && layout) {\n                this.constraints = calcRelativeConstraints(layout.layoutBox, dragConstraints);\n            }\n            else {\n                this.constraints = false;\n            }\n        }\n        this.elastic = resolveDragElastic(dragElastic);\n        /**\n         * If we're outputting to external MotionValues, we want to rebase the measured constraints\n         * from viewport-relative to component-relative.\n         */\n        if (prevConstraints !== this.constraints &&\n            layout &&\n            this.constraints &&\n            !this.hasMutatedConstraints) {\n            eachAxis((axis) => {\n                if (this.getAxisMotionValue(axis)) {\n                    this.constraints[axis] = rebaseAxisConstraints(layout.layoutBox[axis], this.constraints[axis]);\n                }\n            });\n        }\n    }\n    resolveRefConstraints() {\n        const { dragConstraints: constraints, onMeasureDragConstraints } = this.getProps();\n        if (!constraints || !isRefObject(constraints))\n            return false;\n        const constraintsElement = constraints.current;\n        invariant(constraintsElement !== null, \"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.\");\n        const { projection } = this.visualElement;\n        // TODO\n        if (!projection || !projection.layout)\n            return false;\n        const constraintsBox = measurePageBox(constraintsElement, projection.root, this.visualElement.getTransformPagePoint());\n        let measuredConstraints = calcViewportConstraints(projection.layout.layoutBox, constraintsBox);\n        /**\n         * If there's an onMeasureDragConstraints listener we call it and\n         * if different constraints are returned, set constraints to that\n         */\n        if (onMeasureDragConstraints) {\n            const userConstraints = onMeasureDragConstraints(convertBoxToBoundingBox(measuredConstraints));\n            this.hasMutatedConstraints = !!userConstraints;\n            if (userConstraints) {\n                measuredConstraints = convertBoundingBoxToBox(userConstraints);\n            }\n        }\n        return measuredConstraints;\n    }\n    startAnimation(velocity) {\n        const { drag, dragMomentum, dragElastic, dragTransition, dragSnapToOrigin, onDragTransitionEnd, } = this.getProps();\n        const constraints = this.constraints || {};\n        const momentumAnimations = eachAxis((axis) => {\n            if (!shouldDrag(axis, drag, this.currentDirection)) {\n                return;\n            }\n            let transition = (constraints && constraints[axis]) || {};\n            if (dragSnapToOrigin)\n                transition = { min: 0, max: 0 };\n            /**\n             * Overdamp the boundary spring if `dragElastic` is disabled. There's still a frame\n             * of spring animations so we should look into adding a disable spring option to `inertia`.\n             * We could do something here where we affect the `bounceStiffness` and `bounceDamping`\n             * using the value of `dragElastic`.\n             */\n            const bounceStiffness = dragElastic ? 200 : 1000000;\n            const bounceDamping = dragElastic ? 40 : 10000000;\n            const inertia = {\n                type: \"inertia\",\n                velocity: dragMomentum ? velocity[axis] : 0,\n                bounceStiffness,\n                bounceDamping,\n                timeConstant: 750,\n                restDelta: 1,\n                restSpeed: 10,\n                ...dragTransition,\n                ...transition,\n            };\n            // If we're not animating on an externally-provided `MotionValue` we can use the\n            // component's animation controls which will handle interactions with whileHover (etc),\n            // otherwise we just have to animate the `MotionValue` itself.\n            return this.startAxisValueAnimation(axis, inertia);\n        });\n        // Run all animations and then resolve the new drag constraints.\n        return Promise.all(momentumAnimations).then(onDragTransitionEnd);\n    }\n    startAxisValueAnimation(axis, transition) {\n        const axisValue = this.getAxisMotionValue(axis);\n        return axisValue.start(animateMotionValue(axis, axisValue, 0, transition));\n    }\n    stopAnimation() {\n        eachAxis((axis) => this.getAxisMotionValue(axis).stop());\n    }\n    /**\n     * Drag works differently depending on which props are provided.\n     *\n     * - If _dragX and _dragY are provided, we output the gesture delta directly to those motion values.\n     * - Otherwise, we apply the delta to the x/y motion values.\n     */\n    getAxisMotionValue(axis) {\n        const dragKey = \"_drag\" + axis.toUpperCase();\n        const props = this.visualElement.getProps();\n        const externalMotionValue = props[dragKey];\n        return externalMotionValue\n            ? externalMotionValue\n            : this.visualElement.getValue(axis, (props.initial ? props.initial[axis] : undefined) || 0);\n    }\n    snapToCursor(point) {\n        eachAxis((axis) => {\n            const { drag } = this.getProps();\n            // If we're not dragging this axis, do an early return.\n            if (!shouldDrag(axis, drag, this.currentDirection))\n                return;\n            const { projection } = this.visualElement;\n            const axisValue = this.getAxisMotionValue(axis);\n            if (projection && projection.layout) {\n                const { min, max } = projection.layout.layoutBox[axis];\n                axisValue.set(point[axis] - mix(min, max, 0.5));\n            }\n        });\n    }\n    /**\n     * When the viewport resizes we want to check if the measured constraints\n     * have changed and, if so, reposition the element within those new constraints\n     * relative to where it was before the resize.\n     */\n    scalePositionWithinConstraints() {\n        if (!this.visualElement.current)\n            return;\n        const { drag, dragConstraints } = this.getProps();\n        const { projection } = this.visualElement;\n        if (!isRefObject(dragConstraints) || !projection || !this.constraints)\n            return;\n        /**\n         * Stop current animations as there can be visual glitching if we try to do\n         * this mid-animation\n         */\n        this.stopAnimation();\n        /**\n         * Record the relative position of the dragged element relative to the\n         * constraints box and save as a progress value.\n         */\n        const boxProgress = { x: 0, y: 0 };\n        eachAxis((axis) => {\n            const axisValue = this.getAxisMotionValue(axis);\n            if (axisValue) {\n                const latest = axisValue.get();\n                boxProgress[axis] = calcOrigin({ min: latest, max: latest }, this.constraints[axis]);\n            }\n        });\n        /**\n         * Update the layout of this element and resolve the latest drag constraints\n         */\n        const { transformTemplate } = this.visualElement.getProps();\n        this.visualElement.current.style.transform = transformTemplate\n            ? transformTemplate({}, \"\")\n            : \"none\";\n        projection.root && projection.root.updateScroll();\n        projection.updateLayout();\n        this.resolveConstraints();\n        /**\n         * For each axis, calculate the current progress of the layout axis\n         * within the new constraints.\n         */\n        eachAxis((axis) => {\n            if (!shouldDrag(axis, drag, null))\n                return;\n            /**\n             * Calculate a new transform based on the previous box progress\n             */\n            const axisValue = this.getAxisMotionValue(axis);\n            const { min, max } = this.constraints[axis];\n            axisValue.set(mix(min, max, boxProgress[axis]));\n        });\n    }\n    addListeners() {\n        if (!this.visualElement.current)\n            return;\n        elementDragControls.set(this.visualElement, this);\n        const element = this.visualElement.current;\n        /**\n         * Attach a pointerdown event listener on this DOM element to initiate drag tracking.\n         */\n        const stopPointerListener = addPointerEvent(element, \"pointerdown\", (event) => {\n            const { drag, dragListener = true } = this.getProps();\n            drag && dragListener && this.start(event);\n        });\n        const measureDragConstraints = () => {\n            const { dragConstraints } = this.getProps();\n            if (isRefObject(dragConstraints)) {\n                this.constraints = this.resolveRefConstraints();\n            }\n        };\n        const { projection } = this.visualElement;\n        const stopMeasureLayoutListener = projection.addEventListener(\"measure\", measureDragConstraints);\n        if (projection && !projection.layout) {\n            projection.root && projection.root.updateScroll();\n            projection.updateLayout();\n        }\n        measureDragConstraints();\n        /**\n         * Attach a window resize listener to scale the draggable target within its defined\n         * constraints as the window resizes.\n         */\n        const stopResizeListener = addDomEvent(window, \"resize\", () => this.scalePositionWithinConstraints());\n        /**\n         * If the element's layout changes, calculate the delta and apply that to\n         * the drag gesture's origin point.\n         */\n        const stopLayoutUpdateListener = projection.addEventListener(\"didUpdate\", (({ delta, hasLayoutChanged }) => {\n            if (this.isDragging && hasLayoutChanged) {\n                eachAxis((axis) => {\n                    const motionValue = this.getAxisMotionValue(axis);\n                    if (!motionValue)\n                        return;\n                    this.originPoint[axis] += delta[axis].translate;\n                    motionValue.set(motionValue.get() + delta[axis].translate);\n                });\n                this.visualElement.render();\n            }\n        }));\n        return () => {\n            stopResizeListener();\n            stopPointerListener();\n            stopMeasureLayoutListener();\n            stopLayoutUpdateListener && stopLayoutUpdateListener();\n        };\n    }\n    getProps() {\n        const props = this.visualElement.getProps();\n        const { drag = false, dragDirectionLock = false, dragPropagation = false, dragConstraints = false, dragElastic = defaultElastic, dragMomentum = true, } = props;\n        return {\n            ...props,\n            drag,\n            dragDirectionLock,\n            dragPropagation,\n            dragConstraints,\n            dragElastic,\n            dragMomentum,\n        };\n    }\n}\nfunction shouldDrag(direction, drag, currentDirection) {\n    return ((drag === true || drag === direction) &&\n        (currentDirection === null || currentDirection === direction));\n}\n/**\n * Based on an x/y offset determine the current drag direction. If both axis' offsets are lower\n * than the provided threshold, return `null`.\n *\n * @param offset - The x/y offset from origin.\n * @param lockThreshold - (Optional) - the minimum absolute offset before we can determine a drag direction.\n */\nfunction getCurrentDirection(offset, lockThreshold = 10) {\n    let direction = null;\n    if (Math.abs(offset.y) > lockThreshold) {\n        direction = \"y\";\n    }\n    else if (Math.abs(offset.x) > lockThreshold) {\n        direction = \"x\";\n    }\n    return direction;\n}\n\nexport { VisualElementDragControls, elementDragControls };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,wBAAwB;AAClD,SAASC,UAAU,QAAQ,uBAAuB;AAClD,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,WAAW,QAAQ,+BAA+B;AAC3D,SAASC,eAAe,QAAQ,oCAAoC;AACpE,SAASC,gBAAgB,EAAEC,uBAAuB,EAAEC,kBAAkB,EAAEC,uBAAuB,EAAEC,cAAc,EAAEC,qBAAqB,EAAEC,UAAU,QAAQ,yBAAyB;AACnL,SAASC,SAAS,QAAQ,sCAAsC;AAChE,SAASC,QAAQ,QAAQ,sCAAsC;AAC/D,SAASC,cAAc,QAAQ,oCAAoC;AACnE,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,uBAAuB,EAAEC,uBAAuB,QAAQ,0CAA0C;AAC3G,SAASC,WAAW,QAAQ,gCAAgC;AAC5D,SAASC,UAAU,QAAQ,0CAA0C;AACrE,SAASC,GAAG,QAAQ,qBAAqB;AACzC,SAASC,OAAO,QAAQ,qCAAqC;AAC7D,SAASC,kBAAkB,QAAQ,6CAA6C;AAChF,SAASC,KAAK,QAAQ,2BAA2B;AAEjD,MAAMC,mBAAmB,GAAG,IAAIC,OAAO,CAAC,CAAC;AACzC;AACA;AACA;AACA;AACA,MAAMC,yBAAyB,CAAC;EAC5BC,WAAWA,CAACC,aAAa,EAAE;IACvB;IACA;IACA;IACA,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,WAAW,GAAG;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;IACjC;AACR;AACA;IACQ,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,qBAAqB,GAAG,KAAK;IAClC;AACR;AACA;IACQ,IAAI,CAACC,OAAO,GAAGzB,SAAS,CAAC,CAAC;IAC1B,IAAI,CAACgB,aAAa,GAAGA,aAAa;EACtC;EACAU,KAAKA,CAACC,WAAW,EAAE;IAAEC,YAAY,GAAG;EAAM,CAAC,GAAG,CAAC,CAAC,EAAE;IAC9C;AACR;AACA;IACQ,MAAM;MAAEC;IAAgB,CAAC,GAAG,IAAI,CAACb,aAAa;IAC9C,IAAIa,eAAe,IAAIA,eAAe,CAACC,SAAS,KAAK,KAAK,EACtD;IACJ,MAAMC,cAAc,GAAIC,KAAK,IAAK;MAC9B;MACA;MACA,IAAI,CAACC,aAAa,CAAC,CAAC;MACpB,IAAIL,YAAY,EAAE;QACd,IAAI,CAACA,YAAY,CAACzB,gBAAgB,CAAC6B,KAAK,EAAE,MAAM,CAAC,CAACE,KAAK,CAAC;MAC5D;IACJ,CAAC;IACD,MAAMC,OAAO,GAAGA,CAACH,KAAK,EAAEI,IAAI,KAAK;MAC7B;MACA,MAAM;QAAEC,IAAI;QAAEC,eAAe;QAAEC;MAAY,CAAC,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;MAC9D,IAAIH,IAAI,IAAI,CAACC,eAAe,EAAE;QAC1B,IAAI,IAAI,CAACrB,cAAc,EACnB,IAAI,CAACA,cAAc,CAAC,CAAC;QACzB,IAAI,CAACA,cAAc,GAAG3B,aAAa,CAAC+C,IAAI,CAAC;QACzC;QACA,IAAI,CAAC,IAAI,CAACpB,cAAc,EACpB;MACR;MACA,IAAI,CAACC,UAAU,GAAG,IAAI;MACtB,IAAI,CAACC,gBAAgB,GAAG,IAAI;MAC5B,IAAI,CAACsB,kBAAkB,CAAC,CAAC;MACzB,IAAI,IAAI,CAACzB,aAAa,CAAC0B,UAAU,EAAE;QAC/B,IAAI,CAAC1B,aAAa,CAAC0B,UAAU,CAACC,kBAAkB,GAAG,IAAI;QACvD,IAAI,CAAC3B,aAAa,CAAC0B,UAAU,CAACE,MAAM,GAAGC,SAAS;MACpD;MACA;AACZ;AACA;MACY5C,QAAQ,CAAE6C,IAAI,IAAK;QACf,IAAIC,OAAO,GAAG,IAAI,CAACC,kBAAkB,CAACF,IAAI,CAAC,CAACG,GAAG,CAAC,CAAC,IAAI,CAAC;QACtD;AAChB;AACA;QACgB,IAAIxC,OAAO,CAACyC,IAAI,CAACH,OAAO,CAAC,EAAE;UACvB,MAAM;YAAEL;UAAW,CAAC,GAAG,IAAI,CAAC1B,aAAa;UACzC,IAAI0B,UAAU,IAAIA,UAAU,CAACS,MAAM,EAAE;YACjC,MAAMC,YAAY,GAAGV,UAAU,CAACS,MAAM,CAACE,SAAS,CAACP,IAAI,CAAC;YACtD,IAAIM,YAAY,EAAE;cACd,MAAME,MAAM,GAAG/C,UAAU,CAAC6C,YAAY,CAAC;cACvCL,OAAO,GAAGO,MAAM,IAAIC,UAAU,CAACR,OAAO,CAAC,GAAG,GAAG,CAAC;YAClD;UACJ;QACJ;QACA,IAAI,CAAC3B,WAAW,CAAC0B,IAAI,CAAC,GAAGC,OAAO;MACpC,CAAC,CAAC;MACF;MACA,IAAIR,WAAW,EAAE;QACb5B,KAAK,CAAC6C,MAAM,CAAC,MAAMjB,WAAW,CAACP,KAAK,EAAEI,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;MAC7D;MACA,MAAM;QAAEqB;MAAe,CAAC,GAAG,IAAI,CAACzC,aAAa;MAC7CyC,cAAc,IAAIA,cAAc,CAACC,SAAS,CAAC,WAAW,EAAE,IAAI,CAAC;IACjE,CAAC;IACD,MAAMC,MAAM,GAAGA,CAAC3B,KAAK,EAAEI,IAAI,KAAK;MAC5B;MACA,MAAM;QAAEE,eAAe;QAAEsB,iBAAiB;QAAEC,eAAe;QAAEC;MAAQ,CAAC,GAAG,IAAI,CAACtB,QAAQ,CAAC,CAAC;MACxF;MACA,IAAI,CAACF,eAAe,IAAI,CAAC,IAAI,CAACrB,cAAc,EACxC;MACJ,MAAM;QAAE8C;MAAO,CAAC,GAAG3B,IAAI;MACvB;MACA,IAAIwB,iBAAiB,IAAI,IAAI,CAACzC,gBAAgB,KAAK,IAAI,EAAE;QACrD,IAAI,CAACA,gBAAgB,GAAG6C,mBAAmB,CAACD,MAAM,CAAC;QACnD;QACA,IAAI,IAAI,CAAC5C,gBAAgB,KAAK,IAAI,EAAE;UAChC0C,eAAe,IAAIA,eAAe,CAAC,IAAI,CAAC1C,gBAAgB,CAAC;QAC7D;QACA;MACJ;MACA;MACA,IAAI,CAAC8C,UAAU,CAAC,GAAG,EAAE7B,IAAI,CAACF,KAAK,EAAE6B,MAAM,CAAC;MACxC,IAAI,CAACE,UAAU,CAAC,GAAG,EAAE7B,IAAI,CAACF,KAAK,EAAE6B,MAAM,CAAC;MACxC;AACZ;AACA;AACA;AACA;AACA;MACY,IAAI,CAAC/C,aAAa,CAACkD,MAAM,CAAC,CAAC;MAC3B;AACZ;AACA;AACA;MACYJ,MAAM,IAAIA,MAAM,CAAC9B,KAAK,EAAEI,IAAI,CAAC;IACjC,CAAC;IACD,MAAM+B,YAAY,GAAGA,CAACnC,KAAK,EAAEI,IAAI,KAAK,IAAI,CAACgC,IAAI,CAACpC,KAAK,EAAEI,IAAI,CAAC;IAC5D,IAAI,CAACiC,UAAU,GAAG,IAAIhF,UAAU,CAACsC,WAAW,EAAE;MAC1CI,cAAc;MACdI,OAAO;MACPwB,MAAM;MACNQ;IACJ,CAAC,EAAE;MAAEG,kBAAkB,EAAE,IAAI,CAACtD,aAAa,CAACuD,qBAAqB,CAAC;IAAE,CAAC,CAAC;EAC1E;EACAH,IAAIA,CAACpC,KAAK,EAAEI,IAAI,EAAE;IACd,MAAMlB,UAAU,GAAG,IAAI,CAACA,UAAU;IAClC,IAAI,CAACsD,MAAM,CAAC,CAAC;IACb,IAAI,CAACtD,UAAU,EACX;IACJ,MAAM;MAAEuD;IAAS,CAAC,GAAGrC,IAAI;IACzB,IAAI,CAACsC,cAAc,CAACD,QAAQ,CAAC;IAC7B,MAAM;MAAEE;IAAU,CAAC,GAAG,IAAI,CAACnC,QAAQ,CAAC,CAAC;IACrC,IAAImC,SAAS,EAAE;MACXhE,KAAK,CAAC6C,MAAM,CAAC,MAAMmB,SAAS,CAAC3C,KAAK,EAAEI,IAAI,CAAC,CAAC;IAC9C;EACJ;EACAoC,MAAMA,CAAA,EAAG;IACL,IAAI,CAACtD,UAAU,GAAG,KAAK;IACvB,MAAM;MAAEwB,UAAU;MAAEe;IAAe,CAAC,GAAG,IAAI,CAACzC,aAAa;IACzD,IAAI0B,UAAU,EAAE;MACZA,UAAU,CAACC,kBAAkB,GAAG,KAAK;IACzC;IACA,IAAI,CAAC0B,UAAU,IAAI,IAAI,CAACA,UAAU,CAACO,GAAG,CAAC,CAAC;IACxC,IAAI,CAACP,UAAU,GAAGxB,SAAS;IAC3B,MAAM;MAAEP;IAAgB,CAAC,GAAG,IAAI,CAACE,QAAQ,CAAC,CAAC;IAC3C,IAAI,CAACF,eAAe,IAAI,IAAI,CAACrB,cAAc,EAAE;MACzC,IAAI,CAACA,cAAc,CAAC,CAAC;MACrB,IAAI,CAACA,cAAc,GAAG,IAAI;IAC9B;IACAwC,cAAc,IAAIA,cAAc,CAACC,SAAS,CAAC,WAAW,EAAE,KAAK,CAAC;EAClE;EACAO,UAAUA,CAACnB,IAAI,EAAE+B,MAAM,EAAEd,MAAM,EAAE;IAC7B,MAAM;MAAE1B;IAAK,CAAC,GAAG,IAAI,CAACG,QAAQ,CAAC,CAAC;IAChC;IACA,IAAI,CAACuB,MAAM,IAAI,CAACe,UAAU,CAAChC,IAAI,EAAET,IAAI,EAAE,IAAI,CAAClB,gBAAgB,CAAC,EACzD;IACJ,MAAM4D,SAAS,GAAG,IAAI,CAAC/B,kBAAkB,CAACF,IAAI,CAAC;IAC/C,IAAIkC,IAAI,GAAG,IAAI,CAAC5D,WAAW,CAAC0B,IAAI,CAAC,GAAGiB,MAAM,CAACjB,IAAI,CAAC;IAChD;IACA,IAAI,IAAI,CAACvB,WAAW,IAAI,IAAI,CAACA,WAAW,CAACuB,IAAI,CAAC,EAAE;MAC5CkC,IAAI,GAAGvF,gBAAgB,CAACuF,IAAI,EAAE,IAAI,CAACzD,WAAW,CAACuB,IAAI,CAAC,EAAE,IAAI,CAACrB,OAAO,CAACqB,IAAI,CAAC,CAAC;IAC7E;IACAiC,SAAS,CAACE,GAAG,CAACD,IAAI,CAAC;EACvB;EACAvC,kBAAkBA,CAAA,EAAG;IACjB,MAAM;MAAEyC,eAAe;MAAEC;IAAY,CAAC,GAAG,IAAI,CAAC3C,QAAQ,CAAC,CAAC;IACxD,MAAM;MAAEW;IAAO,CAAC,GAAG,IAAI,CAACnC,aAAa,CAAC0B,UAAU,IAAI,CAAC,CAAC;IACtD,MAAM0C,eAAe,GAAG,IAAI,CAAC7D,WAAW;IACxC,IAAI2D,eAAe,IAAI3F,WAAW,CAAC2F,eAAe,CAAC,EAAE;MACjD,IAAI,CAAC,IAAI,CAAC3D,WAAW,EAAE;QACnB,IAAI,CAACA,WAAW,GAAG,IAAI,CAAC8D,qBAAqB,CAAC,CAAC;MACnD;IACJ,CAAC,MACI;MACD,IAAIH,eAAe,IAAI/B,MAAM,EAAE;QAC3B,IAAI,CAAC5B,WAAW,GAAG7B,uBAAuB,CAACyD,MAAM,CAACE,SAAS,EAAE6B,eAAe,CAAC;MACjF,CAAC,MACI;QACD,IAAI,CAAC3D,WAAW,GAAG,KAAK;MAC5B;IACJ;IACA,IAAI,CAACE,OAAO,GAAG9B,kBAAkB,CAACwF,WAAW,CAAC;IAC9C;AACR;AACA;AACA;IACQ,IAAIC,eAAe,KAAK,IAAI,CAAC7D,WAAW,IACpC4B,MAAM,IACN,IAAI,CAAC5B,WAAW,IAChB,CAAC,IAAI,CAACC,qBAAqB,EAAE;MAC7BvB,QAAQ,CAAE6C,IAAI,IAAK;QACf,IAAI,IAAI,CAACE,kBAAkB,CAACF,IAAI,CAAC,EAAE;UAC/B,IAAI,CAACvB,WAAW,CAACuB,IAAI,CAAC,GAAGhD,qBAAqB,CAACqD,MAAM,CAACE,SAAS,CAACP,IAAI,CAAC,EAAE,IAAI,CAACvB,WAAW,CAACuB,IAAI,CAAC,CAAC;QAClG;MACJ,CAAC,CAAC;IACN;EACJ;EACAuC,qBAAqBA,CAAA,EAAG;IACpB,MAAM;MAAEH,eAAe,EAAE3D,WAAW;MAAE+D;IAAyB,CAAC,GAAG,IAAI,CAAC9C,QAAQ,CAAC,CAAC;IAClF,IAAI,CAACjB,WAAW,IAAI,CAAChC,WAAW,CAACgC,WAAW,CAAC,EACzC,OAAO,KAAK;IAChB,MAAMgE,kBAAkB,GAAGhE,WAAW,CAACwB,OAAO;IAC9C3D,SAAS,CAACmG,kBAAkB,KAAK,IAAI,EAAE,wGAAwG,CAAC;IAChJ,MAAM;MAAE7C;IAAW,CAAC,GAAG,IAAI,CAAC1B,aAAa;IACzC;IACA,IAAI,CAAC0B,UAAU,IAAI,CAACA,UAAU,CAACS,MAAM,EACjC,OAAO,KAAK;IAChB,MAAMqC,cAAc,GAAGtF,cAAc,CAACqF,kBAAkB,EAAE7C,UAAU,CAAC+C,IAAI,EAAE,IAAI,CAACzE,aAAa,CAACuD,qBAAqB,CAAC,CAAC,CAAC;IACtH,IAAImB,mBAAmB,GAAG9F,uBAAuB,CAAC8C,UAAU,CAACS,MAAM,CAACE,SAAS,EAAEmC,cAAc,CAAC;IAC9F;AACR;AACA;AACA;IACQ,IAAIF,wBAAwB,EAAE;MAC1B,MAAMK,eAAe,GAAGL,wBAAwB,CAAClF,uBAAuB,CAACsF,mBAAmB,CAAC,CAAC;MAC9F,IAAI,CAAClE,qBAAqB,GAAG,CAAC,CAACmE,eAAe;MAC9C,IAAIA,eAAe,EAAE;QACjBD,mBAAmB,GAAGrF,uBAAuB,CAACsF,eAAe,CAAC;MAClE;IACJ;IACA,OAAOD,mBAAmB;EAC9B;EACAhB,cAAcA,CAACD,QAAQ,EAAE;IACrB,MAAM;MAAEpC,IAAI;MAAEuD,YAAY;MAAET,WAAW;MAAEU,cAAc;MAAEC,gBAAgB;MAAEC;IAAqB,CAAC,GAAG,IAAI,CAACvD,QAAQ,CAAC,CAAC;IACnH,MAAMjB,WAAW,GAAG,IAAI,CAACA,WAAW,IAAI,CAAC,CAAC;IAC1C,MAAMyE,kBAAkB,GAAG/F,QAAQ,CAAE6C,IAAI,IAAK;MAC1C,IAAI,CAACgC,UAAU,CAAChC,IAAI,EAAET,IAAI,EAAE,IAAI,CAAClB,gBAAgB,CAAC,EAAE;QAChD;MACJ;MACA,IAAI8E,UAAU,GAAI1E,WAAW,IAAIA,WAAW,CAACuB,IAAI,CAAC,IAAK,CAAC,CAAC;MACzD,IAAIgD,gBAAgB,EAChBG,UAAU,GAAG;QAAEC,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAE,CAAC;MACnC;AACZ;AACA;AACA;AACA;AACA;MACY,MAAMC,eAAe,GAAGjB,WAAW,GAAG,GAAG,GAAG,OAAO;MACnD,MAAMkB,aAAa,GAAGlB,WAAW,GAAG,EAAE,GAAG,QAAQ;MACjD,MAAMmB,OAAO,GAAG;QACZC,IAAI,EAAE,SAAS;QACf9B,QAAQ,EAAEmB,YAAY,GAAGnB,QAAQ,CAAC3B,IAAI,CAAC,GAAG,CAAC;QAC3CsD,eAAe;QACfC,aAAa;QACbG,YAAY,EAAE,GAAG;QACjBC,SAAS,EAAE,CAAC;QACZC,SAAS,EAAE,EAAE;QACb,GAAGb,cAAc;QACjB,GAAGI;MACP,CAAC;MACD;MACA;MACA;MACA,OAAO,IAAI,CAACU,uBAAuB,CAAC7D,IAAI,EAAEwD,OAAO,CAAC;IACtD,CAAC,CAAC;IACF;IACA,OAAOM,OAAO,CAACC,GAAG,CAACb,kBAAkB,CAAC,CAACc,IAAI,CAACf,mBAAmB,CAAC;EACpE;EACAY,uBAAuBA,CAAC7D,IAAI,EAAEmD,UAAU,EAAE;IACtC,MAAMlB,SAAS,GAAG,IAAI,CAAC/B,kBAAkB,CAACF,IAAI,CAAC;IAC/C,OAAOiC,SAAS,CAACrD,KAAK,CAAChB,kBAAkB,CAACoC,IAAI,EAAEiC,SAAS,EAAE,CAAC,EAAEkB,UAAU,CAAC,CAAC;EAC9E;EACAhE,aAAaA,CAAA,EAAG;IACZhC,QAAQ,CAAE6C,IAAI,IAAK,IAAI,CAACE,kBAAkB,CAACF,IAAI,CAAC,CAACsB,IAAI,CAAC,CAAC,CAAC;EAC5D;EACA;AACJ;AACA;AACA;AACA;AACA;EACIpB,kBAAkBA,CAACF,IAAI,EAAE;IACrB,MAAMiE,OAAO,GAAG,OAAO,GAAGjE,IAAI,CAACkE,WAAW,CAAC,CAAC;IAC5C,MAAMC,KAAK,GAAG,IAAI,CAACjG,aAAa,CAACwB,QAAQ,CAAC,CAAC;IAC3C,MAAM0E,mBAAmB,GAAGD,KAAK,CAACF,OAAO,CAAC;IAC1C,OAAOG,mBAAmB,GACpBA,mBAAmB,GACnB,IAAI,CAAClG,aAAa,CAACmG,QAAQ,CAACrE,IAAI,EAAE,CAACmE,KAAK,CAACG,OAAO,GAAGH,KAAK,CAACG,OAAO,CAACtE,IAAI,CAAC,GAAGD,SAAS,KAAK,CAAC,CAAC;EACnG;EACAjB,YAAYA,CAACM,KAAK,EAAE;IAChBjC,QAAQ,CAAE6C,IAAI,IAAK;MACf,MAAM;QAAET;MAAK,CAAC,GAAG,IAAI,CAACG,QAAQ,CAAC,CAAC;MAChC;MACA,IAAI,CAACsC,UAAU,CAAChC,IAAI,EAAET,IAAI,EAAE,IAAI,CAAClB,gBAAgB,CAAC,EAC9C;MACJ,MAAM;QAAEuB;MAAW,CAAC,GAAG,IAAI,CAAC1B,aAAa;MACzC,MAAM+D,SAAS,GAAG,IAAI,CAAC/B,kBAAkB,CAACF,IAAI,CAAC;MAC/C,IAAIJ,UAAU,IAAIA,UAAU,CAACS,MAAM,EAAE;QACjC,MAAM;UAAE+C,GAAG;UAAEC;QAAI,CAAC,GAAGzD,UAAU,CAACS,MAAM,CAACE,SAAS,CAACP,IAAI,CAAC;QACtDiC,SAAS,CAACE,GAAG,CAAC/C,KAAK,CAACY,IAAI,CAAC,GAAGtC,GAAG,CAAC0F,GAAG,EAAEC,GAAG,EAAE,GAAG,CAAC,CAAC;MACnD;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACIkB,8BAA8BA,CAAA,EAAG;IAC7B,IAAI,CAAC,IAAI,CAACrG,aAAa,CAAC+B,OAAO,EAC3B;IACJ,MAAM;MAAEV,IAAI;MAAE6C;IAAgB,CAAC,GAAG,IAAI,CAAC1C,QAAQ,CAAC,CAAC;IACjD,MAAM;MAAEE;IAAW,CAAC,GAAG,IAAI,CAAC1B,aAAa;IACzC,IAAI,CAACzB,WAAW,CAAC2F,eAAe,CAAC,IAAI,CAACxC,UAAU,IAAI,CAAC,IAAI,CAACnB,WAAW,EACjE;IACJ;AACR;AACA;AACA;IACQ,IAAI,CAACU,aAAa,CAAC,CAAC;IACpB;AACR;AACA;AACA;IACQ,MAAMqF,WAAW,GAAG;MAAEjG,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;IAClCrB,QAAQ,CAAE6C,IAAI,IAAK;MACf,MAAMiC,SAAS,GAAG,IAAI,CAAC/B,kBAAkB,CAACF,IAAI,CAAC;MAC/C,IAAIiC,SAAS,EAAE;QACX,MAAMwC,MAAM,GAAGxC,SAAS,CAAC9B,GAAG,CAAC,CAAC;QAC9BqE,WAAW,CAACxE,IAAI,CAAC,GAAG/C,UAAU,CAAC;UAAEmG,GAAG,EAAEqB,MAAM;UAAEpB,GAAG,EAAEoB;QAAO,CAAC,EAAE,IAAI,CAAChG,WAAW,CAACuB,IAAI,CAAC,CAAC;MACxF;IACJ,CAAC,CAAC;IACF;AACR;AACA;IACQ,MAAM;MAAE0E;IAAkB,CAAC,GAAG,IAAI,CAACxG,aAAa,CAACwB,QAAQ,CAAC,CAAC;IAC3D,IAAI,CAACxB,aAAa,CAAC+B,OAAO,CAAC0E,KAAK,CAACC,SAAS,GAAGF,iBAAiB,GACxDA,iBAAiB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GACzB,MAAM;IACZ9E,UAAU,CAAC+C,IAAI,IAAI/C,UAAU,CAAC+C,IAAI,CAACkC,YAAY,CAAC,CAAC;IACjDjF,UAAU,CAACkF,YAAY,CAAC,CAAC;IACzB,IAAI,CAACnF,kBAAkB,CAAC,CAAC;IACzB;AACR;AACA;AACA;IACQxC,QAAQ,CAAE6C,IAAI,IAAK;MACf,IAAI,CAACgC,UAAU,CAAChC,IAAI,EAAET,IAAI,EAAE,IAAI,CAAC,EAC7B;MACJ;AACZ;AACA;MACY,MAAM0C,SAAS,GAAG,IAAI,CAAC/B,kBAAkB,CAACF,IAAI,CAAC;MAC/C,MAAM;QAAEoD,GAAG;QAAEC;MAAI,CAAC,GAAG,IAAI,CAAC5E,WAAW,CAACuB,IAAI,CAAC;MAC3CiC,SAAS,CAACE,GAAG,CAACzE,GAAG,CAAC0F,GAAG,EAAEC,GAAG,EAAEmB,WAAW,CAACxE,IAAI,CAAC,CAAC,CAAC;IACnD,CAAC,CAAC;EACN;EACA+E,YAAYA,CAAA,EAAG;IACX,IAAI,CAAC,IAAI,CAAC7G,aAAa,CAAC+B,OAAO,EAC3B;IACJnC,mBAAmB,CAACqE,GAAG,CAAC,IAAI,CAACjE,aAAa,EAAE,IAAI,CAAC;IACjD,MAAM8G,OAAO,GAAG,IAAI,CAAC9G,aAAa,CAAC+B,OAAO;IAC1C;AACR;AACA;IACQ,MAAMgF,mBAAmB,GAAGvI,eAAe,CAACsI,OAAO,EAAE,aAAa,EAAG9F,KAAK,IAAK;MAC3E,MAAM;QAAEK,IAAI;QAAE2F,YAAY,GAAG;MAAK,CAAC,GAAG,IAAI,CAACxF,QAAQ,CAAC,CAAC;MACrDH,IAAI,IAAI2F,YAAY,IAAI,IAAI,CAACtG,KAAK,CAACM,KAAK,CAAC;IAC7C,CAAC,CAAC;IACF,MAAMiG,sBAAsB,GAAGA,CAAA,KAAM;MACjC,MAAM;QAAE/C;MAAgB,CAAC,GAAG,IAAI,CAAC1C,QAAQ,CAAC,CAAC;MAC3C,IAAIjD,WAAW,CAAC2F,eAAe,CAAC,EAAE;QAC9B,IAAI,CAAC3D,WAAW,GAAG,IAAI,CAAC8D,qBAAqB,CAAC,CAAC;MACnD;IACJ,CAAC;IACD,MAAM;MAAE3C;IAAW,CAAC,GAAG,IAAI,CAAC1B,aAAa;IACzC,MAAMkH,yBAAyB,GAAGxF,UAAU,CAACyF,gBAAgB,CAAC,SAAS,EAAEF,sBAAsB,CAAC;IAChG,IAAIvF,UAAU,IAAI,CAACA,UAAU,CAACS,MAAM,EAAE;MAClCT,UAAU,CAAC+C,IAAI,IAAI/C,UAAU,CAAC+C,IAAI,CAACkC,YAAY,CAAC,CAAC;MACjDjF,UAAU,CAACkF,YAAY,CAAC,CAAC;IAC7B;IACAK,sBAAsB,CAAC,CAAC;IACxB;AACR;AACA;AACA;IACQ,MAAMG,kBAAkB,GAAG9H,WAAW,CAAC+H,MAAM,EAAE,QAAQ,EAAE,MAAM,IAAI,CAAChB,8BAA8B,CAAC,CAAC,CAAC;IACrG;AACR;AACA;AACA;IACQ,MAAMiB,wBAAwB,GAAG5F,UAAU,CAACyF,gBAAgB,CAAC,WAAW,EAAG,CAAC;MAAEI,KAAK;MAAEC;IAAiB,CAAC,KAAK;MACxG,IAAI,IAAI,CAACtH,UAAU,IAAIsH,gBAAgB,EAAE;QACrCvI,QAAQ,CAAE6C,IAAI,IAAK;UACf,MAAM2F,WAAW,GAAG,IAAI,CAACzF,kBAAkB,CAACF,IAAI,CAAC;UACjD,IAAI,CAAC2F,WAAW,EACZ;UACJ,IAAI,CAACrH,WAAW,CAAC0B,IAAI,CAAC,IAAIyF,KAAK,CAACzF,IAAI,CAAC,CAAC4F,SAAS;UAC/CD,WAAW,CAACxD,GAAG,CAACwD,WAAW,CAACxF,GAAG,CAAC,CAAC,GAAGsF,KAAK,CAACzF,IAAI,CAAC,CAAC4F,SAAS,CAAC;QAC9D,CAAC,CAAC;QACF,IAAI,CAAC1H,aAAa,CAACkD,MAAM,CAAC,CAAC;MAC/B;IACJ,CAAE,CAAC;IACH,OAAO,MAAM;MACTkE,kBAAkB,CAAC,CAAC;MACpBL,mBAAmB,CAAC,CAAC;MACrBG,yBAAyB,CAAC,CAAC;MAC3BI,wBAAwB,IAAIA,wBAAwB,CAAC,CAAC;IAC1D,CAAC;EACL;EACA9F,QAAQA,CAAA,EAAG;IACP,MAAMyE,KAAK,GAAG,IAAI,CAACjG,aAAa,CAACwB,QAAQ,CAAC,CAAC;IAC3C,MAAM;MAAEH,IAAI,GAAG,KAAK;MAAEuB,iBAAiB,GAAG,KAAK;MAAEtB,eAAe,GAAG,KAAK;MAAE4C,eAAe,GAAG,KAAK;MAAEC,WAAW,GAAGtF,cAAc;MAAE+F,YAAY,GAAG;IAAM,CAAC,GAAGqB,KAAK;IAC/J,OAAO;MACH,GAAGA,KAAK;MACR5E,IAAI;MACJuB,iBAAiB;MACjBtB,eAAe;MACf4C,eAAe;MACfC,WAAW;MACXS;IACJ,CAAC;EACL;AACJ;AACA,SAASd,UAAUA,CAAC6D,SAAS,EAAEtG,IAAI,EAAElB,gBAAgB,EAAE;EACnD,OAAQ,CAACkB,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAKsG,SAAS,MACvCxH,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAKwH,SAAS,CAAC;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS3E,mBAAmBA,CAACD,MAAM,EAAE6E,aAAa,GAAG,EAAE,EAAE;EACrD,IAAID,SAAS,GAAG,IAAI;EACpB,IAAIE,IAAI,CAACC,GAAG,CAAC/E,MAAM,CAACzC,CAAC,CAAC,GAAGsH,aAAa,EAAE;IACpCD,SAAS,GAAG,GAAG;EACnB,CAAC,MACI,IAAIE,IAAI,CAACC,GAAG,CAAC/E,MAAM,CAAC1C,CAAC,CAAC,GAAGuH,aAAa,EAAE;IACzCD,SAAS,GAAG,GAAG;EACnB;EACA,OAAOA,SAAS;AACpB;AAEA,SAAS7H,yBAAyB,EAAEF,mBAAmB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}