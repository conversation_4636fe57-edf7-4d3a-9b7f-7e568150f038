{"ast": null, "code": "function eachAxis(callback) {\n  return [callback(\"x\"), callback(\"y\")];\n}\nexport { eachAxis };", "map": {"version": 3, "names": ["eachAxis", "callback"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/framer-motion/dist/es/projection/utils/each-axis.mjs"], "sourcesContent": ["function eachAxis(callback) {\n    return [callback(\"x\"), callback(\"y\")];\n}\n\nexport { eachAxis };\n"], "mappings": "AAAA,SAASA,QAAQA,CAACC,QAAQ,EAAE;EACxB,OAAO,CAACA,QAAQ,CAAC,GAAG,CAAC,EAAEA,QAAQ,CAAC,GAAG,CAAC,CAAC;AACzC;AAEA,SAASD,QAAQ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}