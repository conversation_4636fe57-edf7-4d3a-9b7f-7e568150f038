{"ast": null, "code": "import { resolveElements } from '../utils/resolve-element.mjs';\nconst resizeHandlers = new WeakMap();\nlet observer;\nfunction getElementSize(target, borderBoxSize) {\n  if (borderBoxSize) {\n    const {\n      inlineSize,\n      blockSize\n    } = borderBoxSize[0];\n    return {\n      width: inlineSize,\n      height: blockSize\n    };\n  } else if (target instanceof SVGElement && \"getBBox\" in target) {\n    return target.getBBox();\n  } else {\n    return {\n      width: target.offsetWidth,\n      height: target.offsetHeight\n    };\n  }\n}\nfunction notifyTarget({\n  target,\n  contentRect,\n  borderBoxSize\n}) {\n  var _a;\n  (_a = resizeHandlers.get(target)) === null || _a === void 0 ? void 0 : _a.forEach(handler => {\n    handler({\n      target,\n      contentSize: contentRect,\n      get size() {\n        return getElementSize(target, borderBoxSize);\n      }\n    });\n  });\n}\nfunction notifyAll(entries) {\n  entries.forEach(notifyTarget);\n}\nfunction createResizeObserver() {\n  if (typeof ResizeObserver === \"undefined\") return;\n  observer = new ResizeObserver(notifyAll);\n}\nfunction resizeElement(target, handler) {\n  if (!observer) createResizeObserver();\n  const elements = resolveElements(target);\n  elements.forEach(element => {\n    let elementHandlers = resizeHandlers.get(element);\n    if (!elementHandlers) {\n      elementHandlers = new Set();\n      resizeHandlers.set(element, elementHandlers);\n    }\n    elementHandlers.add(handler);\n    observer === null || observer === void 0 ? void 0 : observer.observe(element);\n  });\n  return () => {\n    elements.forEach(element => {\n      const elementHandlers = resizeHandlers.get(element);\n      elementHandlers === null || elementHandlers === void 0 ? void 0 : elementHandlers.delete(handler);\n      if (!(elementHandlers === null || elementHandlers === void 0 ? void 0 : elementHandlers.size)) {\n        observer === null || observer === void 0 ? void 0 : observer.unobserve(element);\n      }\n    });\n  };\n}\nexport { resizeElement };", "map": {"version": 3, "names": ["resolveElements", "resizeHandlers", "WeakMap", "observer", "getElementSize", "target", "borderBoxSize", "inlineSize", "blockSize", "width", "height", "SVGElement", "getBBox", "offsetWidth", "offsetHeight", "notify<PERSON><PERSON><PERSON>", "contentRect", "_a", "get", "for<PERSON>ach", "handler", "contentSize", "size", "notifyAll", "entries", "createResizeObserver", "ResizeObserver", "resizeElement", "elements", "element", "elementHandlers", "Set", "set", "add", "observe", "delete", "unobserve"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/framer-motion/dist/es/render/dom/resize/handle-element.mjs"], "sourcesContent": ["import { resolveElements } from '../utils/resolve-element.mjs';\n\nconst resizeHandlers = new WeakMap();\nlet observer;\nfunction getElementSize(target, borderBoxSize) {\n    if (borderBoxSize) {\n        const { inlineSize, blockSize } = borderBoxSize[0];\n        return { width: inlineSize, height: blockSize };\n    }\n    else if (target instanceof SVGElement && \"getBBox\" in target) {\n        return target.getBBox();\n    }\n    else {\n        return {\n            width: target.offsetWidth,\n            height: target.offsetHeight,\n        };\n    }\n}\nfunction notifyTarget({ target, contentRect, borderBoxSize, }) {\n    var _a;\n    (_a = resizeHandlers.get(target)) === null || _a === void 0 ? void 0 : _a.forEach((handler) => {\n        handler({\n            target,\n            contentSize: contentRect,\n            get size() {\n                return getElementSize(target, borderBoxSize);\n            },\n        });\n    });\n}\nfunction notifyAll(entries) {\n    entries.forEach(notifyTarget);\n}\nfunction createResizeObserver() {\n    if (typeof ResizeObserver === \"undefined\")\n        return;\n    observer = new ResizeObserver(notifyAll);\n}\nfunction resizeElement(target, handler) {\n    if (!observer)\n        createResizeObserver();\n    const elements = resolveElements(target);\n    elements.forEach((element) => {\n        let elementHandlers = resizeHandlers.get(element);\n        if (!elementHandlers) {\n            elementHandlers = new Set();\n            resizeHandlers.set(element, elementHandlers);\n        }\n        elementHandlers.add(handler);\n        observer === null || observer === void 0 ? void 0 : observer.observe(element);\n    });\n    return () => {\n        elements.forEach((element) => {\n            const elementHandlers = resizeHandlers.get(element);\n            elementHandlers === null || elementHandlers === void 0 ? void 0 : elementHandlers.delete(handler);\n            if (!(elementHandlers === null || elementHandlers === void 0 ? void 0 : elementHandlers.size)) {\n                observer === null || observer === void 0 ? void 0 : observer.unobserve(element);\n            }\n        });\n    };\n}\n\nexport { resizeElement };\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,8BAA8B;AAE9D,MAAMC,cAAc,GAAG,IAAIC,OAAO,CAAC,CAAC;AACpC,IAAIC,QAAQ;AACZ,SAASC,cAAcA,CAACC,MAAM,EAAEC,aAAa,EAAE;EAC3C,IAAIA,aAAa,EAAE;IACf,MAAM;MAAEC,UAAU;MAAEC;IAAU,CAAC,GAAGF,aAAa,CAAC,CAAC,CAAC;IAClD,OAAO;MAAEG,KAAK,EAAEF,UAAU;MAAEG,MAAM,EAAEF;IAAU,CAAC;EACnD,CAAC,MACI,IAAIH,MAAM,YAAYM,UAAU,IAAI,SAAS,IAAIN,MAAM,EAAE;IAC1D,OAAOA,MAAM,CAACO,OAAO,CAAC,CAAC;EAC3B,CAAC,MACI;IACD,OAAO;MACHH,KAAK,EAAEJ,MAAM,CAACQ,WAAW;MACzBH,MAAM,EAAEL,MAAM,CAACS;IACnB,CAAC;EACL;AACJ;AACA,SAASC,YAAYA,CAAC;EAAEV,MAAM;EAAEW,WAAW;EAAEV;AAAe,CAAC,EAAE;EAC3D,IAAIW,EAAE;EACN,CAACA,EAAE,GAAGhB,cAAc,CAACiB,GAAG,CAACb,MAAM,CAAC,MAAM,IAAI,IAAIY,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,OAAO,CAAEC,OAAO,IAAK;IAC3FA,OAAO,CAAC;MACJf,MAAM;MACNgB,WAAW,EAAEL,WAAW;MACxB,IAAIM,IAAIA,CAAA,EAAG;QACP,OAAOlB,cAAc,CAACC,MAAM,EAAEC,aAAa,CAAC;MAChD;IACJ,CAAC,CAAC;EACN,CAAC,CAAC;AACN;AACA,SAASiB,SAASA,CAACC,OAAO,EAAE;EACxBA,OAAO,CAACL,OAAO,CAACJ,YAAY,CAAC;AACjC;AACA,SAASU,oBAAoBA,CAAA,EAAG;EAC5B,IAAI,OAAOC,cAAc,KAAK,WAAW,EACrC;EACJvB,QAAQ,GAAG,IAAIuB,cAAc,CAACH,SAAS,CAAC;AAC5C;AACA,SAASI,aAAaA,CAACtB,MAAM,EAAEe,OAAO,EAAE;EACpC,IAAI,CAACjB,QAAQ,EACTsB,oBAAoB,CAAC,CAAC;EAC1B,MAAMG,QAAQ,GAAG5B,eAAe,CAACK,MAAM,CAAC;EACxCuB,QAAQ,CAACT,OAAO,CAAEU,OAAO,IAAK;IAC1B,IAAIC,eAAe,GAAG7B,cAAc,CAACiB,GAAG,CAACW,OAAO,CAAC;IACjD,IAAI,CAACC,eAAe,EAAE;MAClBA,eAAe,GAAG,IAAIC,GAAG,CAAC,CAAC;MAC3B9B,cAAc,CAAC+B,GAAG,CAACH,OAAO,EAAEC,eAAe,CAAC;IAChD;IACAA,eAAe,CAACG,GAAG,CAACb,OAAO,CAAC;IAC5BjB,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC+B,OAAO,CAACL,OAAO,CAAC;EACjF,CAAC,CAAC;EACF,OAAO,MAAM;IACTD,QAAQ,CAACT,OAAO,CAAEU,OAAO,IAAK;MAC1B,MAAMC,eAAe,GAAG7B,cAAc,CAACiB,GAAG,CAACW,OAAO,CAAC;MACnDC,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACK,MAAM,CAACf,OAAO,CAAC;MACjG,IAAI,EAAEU,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACR,IAAI,CAAC,EAAE;QAC3FnB,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACiC,SAAS,CAACP,OAAO,CAAC;MACnF;IACJ,CAAC,CAAC;EACN,CAAC;AACL;AAEA,SAASF,aAAa"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}