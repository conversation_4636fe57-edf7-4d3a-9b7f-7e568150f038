[{"C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\redux\\store.js": "3", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\components\\Theme.jsx": "4", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\components\\Footer.jsx": "5", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\pages\\AddRecipeModal.jsx": "6", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\routes\\AllRoutes.jsx": "7", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\components\\common\\Navbar.jsx": "8", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\redux\\recipeReducer\\reducer.js": "9", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\redux\\authReducer\\reducer.js": "10", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\redux\\userReducer\\reducer.js": "11", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\redux\\authReducer\\actions.js": "12", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\pages\\Feed.jsx": "13", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\pages\\Home.jsx": "14", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\pages\\Explore.jsx": "15", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\pages\\SignUp.jsx": "16", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\pages\\Login.jsx": "17", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\pages\\Account.jsx": "18", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\redux\\recipeReducer\\actionTypes.js": "19", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\redux\\authReducer\\actionTypes.js": "20", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\routes\\PrivateRoute.jsx": "21", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\pages\\SingleRecipe.jsx": "22", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\redux\\userReducer\\actionTypes.js": "23", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\pages\\SingleUser.jsx": "24", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\pages\\Admin.jsx": "25", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\pages\\AdminNew.jsx": "26", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\components\\forms\\AddRecipeForm.jsx": "27", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\components\\common\\Notifications.jsx": "28", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\redux\\userReducer\\actions.js": "29", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\redux\\recipeReducer\\actions.js": "30", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\components\\forms\\LoginForm.jsx": "31", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\components\\forms\\SignUpForm.jsx": "32", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\components\\common\\Reveal.jsx": "33", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\components\\Feed\\MiniCard.jsx": "34", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\components\\Feed\\UserFeed.jsx": "35", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\components\\Feed\\NonFriends.jsx": "36", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\components\\Feed\\SingleRecipeCarousel.jsx": "37", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\components\\Feed\\FeedCard.jsx": "38", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\components\\home\\HomeCard.jsx": "39", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\components\\Feed\\Requests.jsx": "40", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\components\\home\\Card.jsx": "41", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\components\\home\\ImageGrid.jsx": "42", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\components\\home\\RecipeCard.jsx": "43", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\components\\Charts\\VegNonVeg.jsx": "44", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\components\\Charts\\MostLikes.jsx": "45", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\components\\Charts\\Cusines.jsx": "46", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\components\\Charts\\Contribution.jsx": "47", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\components\\Feed\\Carousel.jsx": "48"}, {"size": 573, "mtime": 1750259494082, "results": "49", "hashOfConfig": "50"}, {"size": 391, "mtime": 1750259494055, "results": "51", "hashOfConfig": "50"}, {"size": 489, "mtime": 1750259494094, "results": "52", "hashOfConfig": "50"}, {"size": 2025, "mtime": 1750259494068, "results": "53", "hashOfConfig": "50"}, {"size": 3304, "mtime": 1750259494066, "results": "54", "hashOfConfig": "50"}, {"size": 1509, "mtime": 1750259494084, "results": "55", "hashOfConfig": "50"}, {"size": 1869, "mtime": 1750259494097, "results": "56", "hashOfConfig": "50"}, {"size": 8458, "mtime": 1750259494072, "results": "57", "hashOfConfig": "50"}, {"size": 1879, "mtime": 1750259494093, "results": "58", "hashOfConfig": "50"}, {"size": 2599, "mtime": 1750259494092, "results": "59", "hashOfConfig": "50"}, {"size": 4299, "mtime": 1750259494096, "results": "60", "hashOfConfig": "50"}, {"size": 6492, "mtime": 1750259494092, "results": "61", "hashOfConfig": "50"}, {"size": 2513, "mtime": 1750259494087, "results": "62", "hashOfConfig": "50"}, {"size": 9310, "mtime": 1750259494087, "results": "63", "hashOfConfig": "50"}, {"size": 13419, "mtime": 1750259494086, "results": "64", "hashOfConfig": "50"}, {"size": 785, "mtime": 1750259494089, "results": "65", "hashOfConfig": "50"}, {"size": 771, "mtime": 1750259494089, "results": "66", "hashOfConfig": "50"}, {"size": 13372, "mtime": 1750259494084, "results": "67", "hashOfConfig": "50"}, {"size": 523, "mtime": 1750259494093, "results": "68", "hashOfConfig": "50"}, {"size": 747, "mtime": 1750259494091, "results": "69", "hashOfConfig": "50"}, {"size": 452, "mtime": 1750259494098, "results": "70", "hashOfConfig": "50"}, {"size": 7881, "mtime": 1750259494090, "results": "71", "hashOfConfig": "50"}, {"size": 1178, "mtime": 1750259494094, "results": "72", "hashOfConfig": "50"}, {"size": 6388, "mtime": 1750259494090, "results": "73", "hashOfConfig": "50"}, {"size": 25699, "mtime": 1750259494085, "results": "74", "hashOfConfig": "50"}, {"size": 25028, "mtime": 1750259494086, "results": "75", "hashOfConfig": "50"}, {"size": 15349, "mtime": 1750260897985, "results": "76", "hashOfConfig": "50"}, {"size": 3036, "mtime": 1750259494073, "results": "77", "hashOfConfig": "50"}, {"size": 5931, "mtime": 1750259494094, "results": "78", "hashOfConfig": "50"}, {"size": 3511, "mtime": 1750259494093, "results": "79", "hashOfConfig": "50"}, {"size": 2761, "mtime": 1750259494078, "results": "80", "hashOfConfig": "50"}, {"size": 3347, "mtime": 1750259494078, "results": "81", "hashOfConfig": "50"}, {"size": 1252, "mtime": 1750259494075, "results": "82", "hashOfConfig": "50"}, {"size": 20424, "mtime": 1750260941757, "results": "83", "hashOfConfig": "50"}, {"size": 1529, "mtime": 1750259494065, "results": "84", "hashOfConfig": "50"}, {"size": 1537, "mtime": 1750259494063, "results": "85", "hashOfConfig": "50"}, {"size": 3523, "mtime": 1750259494064, "results": "86", "hashOfConfig": "50"}, {"size": 17036, "mtime": 1750259494062, "results": "87", "hashOfConfig": "50"}, {"size": 1223, "mtime": 1750259494080, "results": "88", "hashOfConfig": "50"}, {"size": 2451, "mtime": 1750259494064, "results": "89", "hashOfConfig": "50"}, {"size": 2739, "mtime": 1750259494079, "results": "90", "hashOfConfig": "50"}, {"size": 1317, "mtime": 1750259494080, "results": "91", "hashOfConfig": "50"}, {"size": 1478, "mtime": 1750259494080, "results": "92", "hashOfConfig": "50"}, {"size": 861, "mtime": 1750259494060, "results": "93", "hashOfConfig": "50"}, {"size": 1336, "mtime": 1750259494060, "results": "94", "hashOfConfig": "50"}, {"size": 1362, "mtime": 1750259494059, "results": "95", "hashOfConfig": "50"}, {"size": 964, "mtime": 1750259494059, "results": "96", "hashOfConfig": "50"}, {"size": 3458, "mtime": 1750259494062, "results": "97", "hashOfConfig": "50"}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "101"}, "j1njmz", {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "101"}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "101"}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "101"}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "101"}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "117", "usedDeprecatedRules": "101"}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "121", "usedDeprecatedRules": "101"}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "125", "usedDeprecatedRules": "101"}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "129", "usedDeprecatedRules": "101"}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "133", "usedDeprecatedRules": "101"}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "137", "usedDeprecatedRules": "101"}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "141", "usedDeprecatedRules": "101"}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "145", "usedDeprecatedRules": "101"}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "149", "usedDeprecatedRules": "101"}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "153", "usedDeprecatedRules": "101"}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "101"}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "101"}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "163", "usedDeprecatedRules": "101"}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "101"}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "101"}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "101"}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "176", "usedDeprecatedRules": "101"}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "101"}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "183", "usedDeprecatedRules": "101"}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "187", "usedDeprecatedRules": "101"}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "191", "usedDeprecatedRules": "101"}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "198", "usedDeprecatedRules": "101"}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "202", "usedDeprecatedRules": "101"}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "206", "usedDeprecatedRules": "101"}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "210", "usedDeprecatedRules": "101"}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "101"}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "217", "usedDeprecatedRules": "101"}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "224", "usedDeprecatedRules": "101"}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "228", "usedDeprecatedRules": "101"}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "101"}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "235", "usedDeprecatedRules": "101"}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "239", "usedDeprecatedRules": "101"}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "243", "usedDeprecatedRules": "101"}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "247", "usedDeprecatedRules": "101"}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "251", "usedDeprecatedRules": "101"}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "101"}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "101"}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "261", "usedDeprecatedRules": "101"}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "101"}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "268", "usedDeprecatedRules": "101"}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "101"}, "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\index.js", [], [], ["272"], "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\redux\\store.js", [], [], "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\components\\Theme.jsx", [], [], "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\components\\Footer.jsx", [], [], "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\pages\\AddRecipeModal.jsx", ["273"], [], "import React, { useState } from \"react\";\r\nimport { useSelector } from \"react-redux\";\r\nimport { AddRecipeForm } from \"../components/forms/AddRecipeForm\";\r\nimport {\r\n  Box,\r\n  Button,\r\n  Modal,\r\n  ModalOverlay,\r\n  ModalContent,\r\n  Modal<PERSON>eader,\r\n  <PERSON>dal<PERSON><PERSON>er,\r\n  <PERSON>dalBody,\r\n  ModalCloseButton,\r\n} from \"@chakra-ui/react\";\r\nimport { AddIcon } from \"@chakra-ui/icons\";\r\n\r\nexport const AddRecipeModal = () => {\r\n  const isAuth = useSelector((store) => store.authReducer.isAuth);\r\n  const [isOpen, setIsOpen] = useState(false);\r\n\r\n  const openModal = () => {\r\n    setIsOpen(true);\r\n  };\r\n\r\n  const closeModal = () => {\r\n    setIsOpen(false);\r\n  };\r\n\r\n  if (!isAuth) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <>\r\n      <Box position=\"fixed\" bottom=\"2rem\" right=\"2rem\" zIndex=\"999\">\r\n        <Button\r\n          onClick={openModal}\r\n          size=\"md\"\r\n          bgColor=\"accent\"\r\n          color=\"background\"\r\n          borderRadius=\"50%\"\r\n          width={4}\r\n          p={6}\r\n          boxShadow=\"xl\"\r\n        >\r\n          <AddIcon />\r\n        </Button>\r\n      </Box>\r\n\r\n      <Modal isOpen={isOpen} onClose={closeModal}>\r\n        <ModalOverlay />\r\n        <ModalContent width=\"min(50rem,100%)\">\r\n          <ModalHeader textTransform={\"uppercase\"} fontSize={\"2xl\"}>\r\n            Add Recipe\r\n          </ModalHeader>\r\n          <ModalCloseButton />\r\n          <ModalBody>\r\n            <AddRecipeForm closeModal={closeModal} />\r\n          </ModalBody>\r\n        </ModalContent>\r\n      </Modal>\r\n    </>\r\n  );\r\n};\r\n", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\routes\\AllRoutes.jsx", ["274"], [], "import { Route, Routes } from \"react-router-dom\";\r\nimport { Home } from \"../pages/Home\";\r\nimport { Feed } from \"../pages/Feed\";\r\nimport { Explore } from \"../pages/Explore\";\r\nimport { Account } from \"../pages/Account\";\r\nimport { Login } from \"../pages/Login\";\r\nimport { SignUp } from \"../pages/SignUp\";\r\nimport { PrivateRoute } from \"./PrivateRoute\";\r\nimport { AddRecipeModal } from \"../pages/AddRecipeModal\";\r\nimport SingleRecipe from \"../pages/SingleRecipe\";\r\nimport Admin from \"../pages/Admin\";\r\nimport { SingleUser } from \"../pages/SingleUser\";\r\nimport AdminNew from \"../pages/AdminNew\";\r\n\r\nexport const AllRoutes = () => {\r\n  return (\r\n    <Routes>\r\n      <Route path=\"/\" element={<Home />}></Route>\r\n      <Route path=\"/explore\" element={<Explore />}></Route>\r\n      <Route\r\n        path=\"/feed\"\r\n        element={\r\n          <PrivateRoute>\r\n            <Feed />\r\n          </PrivateRoute>\r\n        }\r\n      ></Route>\r\n      <Route\r\n        path=\"/user-recipes\"\r\n        element={\r\n          <PrivateRoute>\r\n            <AddRecipeModal />\r\n          </PrivateRoute>\r\n        }\r\n      ></Route>\r\n      <Route\r\n        path=\"/account\"\r\n        element={\r\n          <PrivateRoute>\r\n            <Account />\r\n          </PrivateRoute>\r\n        }\r\n      ></Route>\r\n      <Route\r\n        path=\"/recipe/:postId\"\r\n        element={\r\n          <PrivateRoute>\r\n            <SingleRecipe />\r\n          </PrivateRoute>\r\n        }\r\n      ></Route>\r\n      <Route\r\n        path=\"/user/:userId\"\r\n        element={\r\n            <SingleUser />\r\n        }\r\n      ></Route>\r\n      <Route\r\n        path=\"/admin\"\r\n        element={\r\n          // <PrivateRoute>\r\n            <AdminNew />\r\n          // </PrivateRoute>\r\n        }\r\n      ></Route>\r\n      <Route path=\"/signup\" element={<SignUp />}></Route>\r\n      <Route path=\"/login\" element={<Login />}></Route>\r\n    </Routes>\r\n  );\r\n};\r\n", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\components\\common\\Navbar.jsx", ["275", "276", "277", "278", "279"], [], "import React from \"react\";\r\nimport {\r\n  Box,\r\n  Flex,\r\n  Text,\r\n  IconButton,\r\n  Button,\r\n  Stack,\r\n  Collapse,\r\n  useColorModeValue,\r\n  useDisclosure,\r\n  useToast,\r\n  Popover,\r\n  PopoverTrigger,\r\n  PopoverContent,\r\n} from \"@chakra-ui/react\";\r\nimport { HamburgerIcon, CloseIcon } from \"@chakra-ui/icons\";\r\nimport { FaBell } from \"react-icons/fa\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { useNavigate, Link, useLocation } from \"react-router-dom\";\r\nimport { logoutUser } from \"../../redux/authReducer/actions\";\r\nimport { Notifications } from \"./Notifications\";\r\n\r\nexport const Navbar = () => {\r\n  const { isOpen, onToggle } = useDisclosure();\r\n  const isAuth = useSelector((store) => store.authReducer.isAuth);\r\n  const token = useSelector((store) => store.authReducer.token);\r\n  const dispatch = useDispatch();\r\n  const toast = useToast();\r\n  const navigate = useNavigate();\r\n  const address = useLocation();\r\n\r\n  const bgColor = useColorModeValue(\"white\", \"gray.800\");\r\n  const textColor = useColorModeValue(\"primary-500\", \"white\");\r\n  const borderColor = useColorModeValue(\"gray.200\", \"gray.900\");\r\n  const signInColor = useColorModeValue(\"gray.600\", \"gray.200\");\r\n\r\n  const logoutHandler = () => {\r\n    dispatch(logoutUser(token, toast, navigate));\r\n  };\r\n\r\n  if(address.pathname === \"/admin\") {\r\n    return \r\n  }\r\n\r\n  return (\r\n    <Box w=\"min(100%,80rem)\" mx=\"auto\">\r\n      <Flex\r\n        bg={bgColor}\r\n        color={textColor}\r\n        py={{ base: 2 }}\r\n        px={{ base: 4 }}\r\n        align={\"center\"}\r\n      >\r\n        <Flex\r\n          flex={{ base: 1, md: \"auto\" }}\r\n          ml={{ base: -2 }}\r\n          display={{ base: \"flex\", md: \"none\" }}\r\n        >\r\n          <IconButton\r\n            onClick={onToggle}\r\n            icon={\r\n              isOpen ? <CloseIcon w={3} h={3} /> : <HamburgerIcon w={5} h={5} />\r\n            }\r\n            variant={\"ghost\"}\r\n            aria-label={\"Toggle Navigation\"}\r\n          />\r\n        </Flex>\r\n        <Flex\r\n          flex={{ base: 1 }}\r\n          justifyContent={{\r\n            lg: \"space-between\",\r\n            md: \"space-between\",\r\n            base: \"flex-end\",\r\n          }}\r\n        >\r\n          <Text\r\n            as={Link}\r\n            to=\"/\"\r\n            fontSize=\"2xl\"\r\n            fontWeight=\"bold\"\r\n            letterSpacing={\"1px\"}\r\n            fontFamily={\"Kaushan Script\"}\r\n            color={textColor}\r\n          >\r\n            Recipe\r\n            <Text display=\"inline\" color=\"primary.500\">\r\n              Hub\r\n            </Text>\r\n          </Text>\r\n\r\n          <Flex\r\n            display={{ base: \"none\", md: \"flex\" }}\r\n            ml={{ lg: 8, md: 4, base: 2 }}\r\n          >\r\n            <DesktopNav />\r\n          </Flex>\r\n        </Flex>\r\n      </Flex>\r\n      <Collapse in={isOpen} animateOpacity>\r\n        <MobileNav />\r\n        <Stack\r\n          flex={{ base: 1, md: 0 }}\r\n          justify={\"flex-start\"}\r\n          direction={\"row\"}\r\n          spacing={{ base: 2, md: 4 }}\r\n          p={4}\r\n        >\r\n          {isAuth ? (\r\n            <>\r\n              <Popover trigger={\"hover\"} placement={\"top-end\"}>\r\n                <PopoverTrigger>\r\n                  <IconButton\r\n                    variant=\"link\"\r\n                    color={textColor}\r\n                    _hover={{\r\n                      opacity: 1,\r\n                      color: \"primary.500\",\r\n                    }}\r\n                    aria-label=\"Notifications\"\r\n                  >\r\n                    <FaBell size={24} />\r\n                  </IconButton>\r\n                </PopoverTrigger>\r\n                <PopoverContent zIndex={100000} position=\"relative\">\r\n                  <Box p=\"4\">\r\n                    <Notifications />\r\n                  </Box>\r\n                </PopoverContent>\r\n              </Popover>\r\n              <Button\r\n                onClick={logoutHandler}\r\n                size={{ lg: \"lg\", md: \"md\", base: \"sm\" }}\r\n              >\r\n                Logout\r\n              </Button>\r\n            </>\r\n          ) : (\r\n            <>\r\n              <Button\r\n                as={Link}\r\n                to=\"/login\"\r\n                variant={\"outline\"}\r\n                size={{ lg: \"lg\", md: \"md\", base: \"sm\" }}\r\n              >\r\n                Login\r\n              </Button>\r\n              <Button\r\n                size={{ lg: \"lg\", md: \"md\", base: \"sm\" }}\r\n                as={Link}\r\n                to=\"/signup\"\r\n              >\r\n                SignUp\r\n              </Button>\r\n            </>\r\n          )}\r\n        </Stack>\r\n      </Collapse>\r\n    </Box>\r\n  );\r\n};\r\n\r\nconst DesktopNav = () => {\r\n  const linkColor = useColorModeValue(\"text\", \"white\");\r\n  const linkHoverColor = useColorModeValue(\"primary.500\", \"teal.500\");\r\n  const isAuth = useSelector((store) => store.authReducer.isAuth);\r\n  const token = useSelector((store) => store.authReducer.token);\r\n  const dispatch = useDispatch();\r\n  const toast = useToast();\r\n  const navigate = useNavigate();\r\n\r\n  const bgColor = useColorModeValue(\"white\", \"gray.800\");\r\n  const textColor = useColorModeValue(\"primary-500\", \"white\");\r\n  const borderColor = useColorModeValue(\"gray.200\", \"gray.900\");\r\n  const signInColor = useColorModeValue(\"gray.600\", \"gray.200\");\r\n\r\n  const logoutHandler = () => {\r\n    dispatch(logoutUser(token, toast, navigate));\r\n  };\r\n  return (\r\n    <Flex gap=\"1rem\" alignItems={\"center\"}>\r\n      <Flex gap=\"1rem\" alignItems={\"center\"}>\r\n        <Text\r\n          as={Link}\r\n          to=\"/explore\"\r\n          color={linkColor}\r\n          _hover={{\r\n            textDecoration: \"none\",\r\n            color: linkHoverColor,\r\n          }}\r\n        >\r\n          Explore\r\n        </Text>\r\n        <Text\r\n          as={Link}\r\n          to=\"/feed\"\r\n          color={linkColor}\r\n          _hover={{\r\n            textDecoration: \"none\",\r\n            color: linkHoverColor,\r\n          }}\r\n        >\r\n          Feed\r\n        </Text>\r\n        <Text\r\n          as={Link}\r\n          to=\"/account\"\r\n          color={linkColor}\r\n          _hover={{\r\n            textDecoration: \"none\",\r\n            color: linkHoverColor,\r\n          }}\r\n        >\r\n          Account\r\n        </Text>\r\n      </Flex>\r\n      <Stack\r\n        flex={{ base: 1, md: 0 }}\r\n        justify={\"flex-end\"}\r\n        direction={\"row\"}\r\n        spacing={{ base: 2, md: 4 }}\r\n      >\r\n        {isAuth ? (\r\n          <>\r\n            <Popover trigger={\"hover\"} placement={\"top-end\"}>\r\n              <PopoverTrigger>\r\n                <IconButton\r\n                  variant=\"link\"\r\n                  color={textColor}\r\n                  _hover={{\r\n                    opacity: 1,\r\n                    color: \"primary.500\",\r\n                  }}\r\n                  aria-label=\"Notifications\"\r\n                >\r\n                  <FaBell size={24} />\r\n                </IconButton>\r\n              </PopoverTrigger>\r\n              <PopoverContent zIndex={100000} position=\"relative\">\r\n                <Box p=\"4\">\r\n                  <Notifications />\r\n                </Box>\r\n              </PopoverContent>\r\n            </Popover>\r\n            <Button\r\n              onClick={logoutHandler}\r\n              size={{ lg: \"lg\", md: \"md\", base: \"sm\" }}\r\n            >\r\n              Logout\r\n            </Button>\r\n          </>\r\n        ) : (\r\n          <>\r\n            <Button\r\n              as={Link}\r\n              to=\"/login\"\r\n              variant={\"outline\"}\r\n              size={{ lg: \"lg\", md: \"md\", base: \"sm\" }}\r\n            >\r\n              Login\r\n            </Button>\r\n            <Button\r\n              size={{ lg: \"lg\", md: \"md\", base: \"sm\" }}\r\n              as={Link}\r\n              to=\"/signup\"\r\n            >\r\n              SignUp\r\n            </Button>\r\n          </>\r\n        )}\r\n      </Stack>\r\n    </Flex>\r\n  );\r\n};\r\n\r\nconst MobileNav = () => {\r\n  const bgColor = useColorModeValue(\"white\", \"gray.800\");\r\n\r\n  return (\r\n    <Stack bg={bgColor} p={4} display={{ md: \"none\", base: \"flex\" }}>\r\n      <MobileNavItem label=\"Explore\" href={\"/explore\"} />\r\n      <MobileNavItem label=\"Feed\" href={\"/feed\"} />\r\n      <MobileNavItem label=\"Account\" href={\"/account\"} />\r\n    </Stack>\r\n  );\r\n};\r\n\r\nconst MobileNavItem = ({ label, href }) => {\r\n  return (\r\n    <Text\r\n      py={2}\r\n      as={Link}\r\n      to={href ?? \"#\"}\r\n      fontWeight={600}\r\n      color={useColorModeValue(\"primary.500\", \"white\")}\r\n      _hover={{\r\n        textDecoration: \"none\",\r\n      }}\r\n    >\r\n      {label}\r\n    </Text>\r\n  );\r\n};\r\n\r\nexport default Navbar;\r\n", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\redux\\recipeReducer\\reducer.js", ["280", "281", "282", "283", "284", "285", "286"], [], "import {\r\n  ADDRECIPE_ERROR,\r\n  ADDRECIPE_LOADING,\r\n  ADDRECIPE_SUCCESS,\r\n  GETRECIPE_ERROR,\r\n  GETRECIPE_LOADING,\r\n  GETRECIPE_SUCCESS,\r\n  GET_FEED_ERROR,\r\n  GET_FEED_LOADING,\r\n  GET_FEED_SUCCESS,\r\n  UPDATE_RECIPE_SUCCESS\r\n} from \"./actionTypes\";\r\n\r\nconst initState = {\r\n  isLoading: false,\r\n  isError: false,\r\n  recipes: [], //recipes of current user\r\n  friendRecipes: [], //recipes of friends of current user for the feed\r\n  feed:[]\r\n};\r\n\r\nexport const reducer = (state = initState, action) => {\r\n  switch (action.type) {\r\n    case ADDRECIPE_ERROR:\r\n      return {\r\n        ...state,\r\n        isLoading: false,\r\n        isError: true,\r\n      };\r\n    case ADDRECIPE_LOADING:\r\n      return {\r\n        ...state,\r\n        isLoading: true,\r\n        isError: false,\r\n      };\r\n    case ADDRECIPE_SUCCESS:\r\n      return {\r\n        ...state,\r\n        isLoading: false,\r\n        isError: false,\r\n        recipes: [...state.recipes, action.payload],\r\n      };\r\n    case GET_FEED_ERROR:\r\n      return {\r\n        ...state,\r\n        isLoading: false,\r\n        isError: true,\r\n      };\r\n    case GET_FEED_LOADING:\r\n      return {\r\n        ...state,\r\n        isLoading: true,\r\n        isError: false,\r\n      };\r\n    case GET_FEED_SUCCESS:\r\n      return {\r\n        ...state,\r\n        isLoading: false,\r\n        isError: false,\r\n        feed: [...action.payload],\r\n      };\r\n    case UPDATE_RECIPE_SUCCESS:\r\n      let newRecipes = [...state.recipes].map((rep)=>{\r\n        if(rep._id ==action.payload._id){\r\n          rep=action.payload;\r\n        }\r\n      })\r\n      let newFeed =[...state.feed].map((rep)=>{\r\n        if(rep._id ==action.payload._id){\r\n          rep=action.payload;\r\n        }\r\n      })\r\n      return {\r\n        ...state,\r\n        isLoading:false,\r\n        isError:false,\r\n        recipes: newRecipes,\r\n        feed:newFeed\r\n      }\r\n    default:\r\n      return state;\r\n  }\r\n};\r\n", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\redux\\authReducer\\reducer.js", ["287"], [], "import {\r\n  CREATE_USER_LOADING,\r\n  CREATE_USER_ERROR,\r\n  CREATE_USER_SUCCESS,\r\n  LOGIN_USER_LOADING,\r\n  LOGIN_USER_ERROR,\r\n  LOGIN_USER_SUCCESS,\r\n  POST_DISLIKE_SUCCESS,\r\n  POST_LIKE_SUCCESS,\r\n  RESET,\r\n  UPDATE_USER_DETAILS,\r\n} from \"./actionTypes\";\r\nimport {\r\n  GET_LOGGEDUSER_LOADING,\r\n  GET_LOGGEDUSER_SUCCESS,\r\n  GET_LOGGEDUSER_ERROR,\r\n} from \"./actionTypes\";\r\nconst initState = {\r\n  isLoading: false,\r\n  isError: false,\r\n  isAuth: false,\r\n  token: null,\r\n  loggedInUser: null,\r\n  recipes : null\r\n};\r\n\r\nexport const reducer = (state = initState, action) => {\r\n  switch (action.type) {\r\n    case CREATE_USER_LOADING:\r\n      return {\r\n        ...initState,\r\n        isLoading: true,\r\n      };\r\n    case CREATE_USER_ERROR:\r\n      return {\r\n        ...initState,\r\n        isError: true,\r\n      };\r\n    case CREATE_USER_SUCCESS:\r\n      return {\r\n        ...initState,\r\n      };\r\n    case LOGIN_USER_LOADING:\r\n      return {\r\n        ...initState,\r\n        isLoading: true,\r\n      };\r\n    case LOGIN_USER_ERROR:\r\n      return {\r\n        ...initState,\r\n        isError: true,\r\n      };\r\n    case LOGIN_USER_SUCCESS:\r\n      return {\r\n        ...initState,\r\n        isAuth: true,\r\n        token: action.payload,\r\n      };\r\n    case RESET:\r\n      return {\r\n        ...initState,\r\n      };\r\n    case GET_LOGGEDUSER_LOADING:\r\n      return {\r\n        ...state,\r\n        isLoading: true,\r\n      };\r\n    case GET_LOGGEDUSER_ERROR:\r\n      return {\r\n        ...state,\r\n        isLoading: false,\r\n        isError: true,\r\n      };\r\n    case GET_LOGGEDUSER_SUCCESS:\r\n      return {\r\n        ...state,\r\n        isLoading: false,\r\n        isError: false,\r\n        loggedInUser: action.payload,\r\n      };\r\n    case UPDATE_USER_DETAILS:\r\n      return {\r\n        ...state,\r\n        loggedInUser: action.payload,\r\n      };\r\n    case POST_LIKE_SUCCESS:\r\n      let newLikedRecipes = [...state.loggedInUser.likedRecipes];\r\n      if (!state.loggedInUser.likedRecipes.includes(action.payload)) {\r\n        newLikedRecipes.push(action.payload);\r\n      }\r\n      return {\r\n        ...state,\r\n        loggedInUser: { ...state.loggedInUser, likedRecipes: newLikedRecipes },\r\n      };\r\n    case POST_DISLIKE_SUCCESS:\r\n      let newLikedRecipes1 = [...state.loggedInUser.likedRecipes].filter(\r\n        (rec) => rec != action.payload\r\n      );\r\n      return {\r\n        ...state,\r\n        loggedInUser: { ...state.loggedInUser, likedRecipes: newLikedRecipes1 },\r\n      };\r\n    case \"GET_USER_RECIPES\" : {\r\n      return {\r\n      ...state,\r\n        recipes: action.payload,\r\n      };\r\n    }\r\n    default:\r\n      return state;\r\n  }\r\n};\r\n", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\redux\\userReducer\\reducer.js", ["288", "289", "290", "291", "292"], [], "import {\r\n  GET_NONFRIEND_LOADING,\r\n  GET_NONFRIEND_ERROR,\r\n  GET_NONFRIEND_SUCCESS,\r\n  POST_REQUEST_ERROR,\r\n  POST_REQUEST_LOADING,\r\n  POST_REQUEST_SUCCESS,\r\n  GET_REQUESTSUSER_ERROR,\r\n  GET_REQUESTSUSER_SUCCESS,\r\n  GET_REQUESTSUSER_LOADING,\r\n  GET_FRIENDS_ERROR,\r\n  GET_FRIENDS_SUCCESS,\r\n  GET_FRIENDS_LOADING,\r\n  POST_ACCEPTREQUEST_ERROR,\r\n  POST_ACCEPTREQUEST_LOADING,\r\n  POST_ACCEPTREQUEST_SUCCESS,\r\n  POST_REJECTREQUEST_ERROR,\r\n  POST_REJECTREQUEST_LOADING,\r\n  POST_REJECTREQUEST_SUCCESS,\r\n} from \"./actionTypes\";\r\n\r\nconst initState = {\r\n  isLoading: false,\r\n  isError: false,\r\n  nonFriends: [],\r\n  requests: [],\r\n  friends: [],\r\n};\r\n\r\nexport const reducer = (state = initState, action) => {\r\n  switch (action.type) {\r\n    case GET_NONFRIEND_LOADING:\r\n      return {\r\n        ...state,\r\n        isLoading: true,\r\n        isError: false,\r\n      };\r\n    case GET_NONFRIEND_ERROR:\r\n      return {\r\n        ...state,\r\n        isLoading: false,\r\n        isError: true,\r\n      };\r\n    case GET_NONFRIEND_SUCCESS:\r\n      return {\r\n        ...state,\r\n        isLoading: false,\r\n        isError: false,\r\n        nonFriends: [...action.payload],\r\n      };\r\n    case POST_REQUEST_ERROR:\r\n      return {\r\n        ...state,\r\n        isLoading: false,\r\n        isError: true,\r\n      };\r\n    case POST_REQUEST_LOADING:\r\n      return {\r\n        ...state,\r\n        isLoading: true,\r\n      };\r\n    case POST_REQUEST_SUCCESS:\r\n      let filteredNonFriends = [...state.nonFriends].filter(\r\n        (friend) => friend._id != action.payload\r\n      );\r\n      return {\r\n        ...state,\r\n        isLoading: false,\r\n        isError: false,\r\n        nonFriends: filteredNonFriends,\r\n      };\r\n    case GET_REQUESTSUSER_LOADING:\r\n      return {\r\n        ...state,\r\n        isLoading: true,\r\n        isError: false,\r\n      };\r\n    case GET_REQUESTSUSER_ERROR:\r\n      return {\r\n        ...state,\r\n        isLoading: false,\r\n        isError: true,\r\n      };\r\n    case GET_REQUESTSUSER_SUCCESS:\r\n      return {\r\n        ...state,\r\n        isLoading: false,\r\n        isError: false,\r\n        requests: [...action.payload],\r\n      };\r\n    case GET_FRIENDS_LOADING:\r\n      return {\r\n        ...state,\r\n        isLoading: true,\r\n        isError: false,\r\n      };\r\n    case GET_FRIENDS_ERROR:\r\n      return {\r\n        ...state,\r\n        isLoading: false,\r\n        isError: true,\r\n      };\r\n    case GET_FRIENDS_SUCCESS:\r\n      return {\r\n        ...state,\r\n        isLoading: false,\r\n        isError: false,\r\n        friends: [...action.payload],\r\n      };\r\n    case POST_ACCEPTREQUEST_LOADING:\r\n      return {\r\n        ...state,\r\n        isLoading: true,\r\n        isError: false,\r\n      };\r\n    case POST_ACCEPTREQUEST_ERROR:\r\n      return {\r\n        ...state,\r\n        isLoading: false,\r\n        isError: true,\r\n      };\r\n    case POST_ACCEPTREQUEST_SUCCESS:\r\n      console.log(action.payload);\r\n      let filteredRequests = [...state.requests].filter(\r\n        (req) => req._id != action.payload\r\n      );\r\n      console.log(filteredRequests);\r\n      let addedFriends = [...state.friends];\r\n      for (let i = 0; i < state.requests.length; i++) {\r\n        if (state.requests[i]._id == action.payload) {\r\n          addedFriends.push(state.requests[i]);\r\n        }\r\n      }\r\n      console.log(addedFriends);\r\n      return {\r\n        ...state,\r\n        isLoading: false,\r\n        isError: false,\r\n        requests: filteredRequests,\r\n        friends: addedFriends,\r\n      };\r\n    case POST_REJECTREQUEST_LOADING:\r\n      return {\r\n        ...state,\r\n        isLoading: true,\r\n        isError: false,\r\n      };\r\n    case POST_REJECTREQUEST_ERROR:\r\n      return {\r\n        ...state,\r\n        isLoading: false,\r\n        isError: true,\r\n      };\r\n    case POST_REJECTREQUEST_SUCCESS:\r\n      let newRequests = [...state.requests].filter(\r\n        (req) => req._id != action.payload\r\n      );\r\n      let newNonFriends = [...state.nonFriends];\r\n      for (let i = 0; i < state.requests.length; i++) {\r\n        if (state.requests[i]._id == action.payload) {\r\n          newNonFriends.push(state.requests[i]);\r\n        }\r\n      }\r\n      return {\r\n        ...state,\r\n        isLoading: false,\r\n        isError: false,\r\n        requests: newRequests,\r\n        nonFriends: newNonFriends,\r\n      };\r\n    default:\r\n      return state;\r\n  }\r\n};\r\n", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\redux\\authReducer\\actions.js", ["293", "294"], [], "import {\r\n  CREATE_USER_LOADING,\r\n  CREATE_USER_ERROR,\r\n  CREATE_USER_SUCCESS,\r\n  LOGIN_USER_LOADING,\r\n  LOGIN_USER_ERROR,\r\n  LOGIN_USER_SUCCESS,\r\n  POST_DISLIKE_SUCCESS,\r\n  POST_LIKE_SUCCESS,\r\n  RESET,\r\n  UPDATE_USER_DETAILS,\r\n} from \"./actionTypes\";\r\nimport {\r\n  GET_LOGGEDUSER_LOADING,\r\n  GET_LOGGEDUSER_SUCCESS,\r\n  GET_LOGGEDUSER_ERROR,\r\n} from \"./actionTypes\";\r\nimport axios from \"axios\";\r\nexport const createUser = (newUser, toast, navigate) => async (dispatch) => {\r\n  dispatch({ type: CREATE_USER_LOADING });\r\n  try {\r\n    const response = await axios.post(\r\n      `${process.env.REACT_APP_API_URL}/auth/signup`,\r\n      newUser,\r\n      {\r\n        headers: {\r\n          \"Content-Type\": \"multipart/form-data\",\r\n        },\r\n      }\r\n    );\r\n    // Handle the server response here\r\n    console.log(response);\r\n    dispatch({ type: CREATE_USER_SUCCESS });\r\n    toast({\r\n      title: \"SignUp successfull\",\r\n      description: `${response.data.message}`,\r\n      status: \"success\",\r\n      duration: 3000,\r\n      isClosable: true,\r\n    });\r\n    navigate(\"/login\");\r\n  } catch (error) {\r\n    console.log(error);\r\n    dispatch({ type: CREATE_USER_ERROR });\r\n    toast({\r\n      title: \"SignUp Failed\",\r\n      description: `${error.response.data.message}`,\r\n      status: \"error\",\r\n      duration: 9000,\r\n      isClosable: true,\r\n    });\r\n  }\r\n};\r\n\r\nexport const loginUser = (userObj, toast, navigate) => async (dispatch) => {\r\n  dispatch({ type: LOGIN_USER_LOADING });\r\n  try {\r\n    const response = await axios.post(\r\n      `${process.env.REACT_APP_API_URL}/auth/login`,\r\n      userObj,\r\n      {\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n      }\r\n    );\r\n\r\n    // Handle the server response here\r\n    // console.log(response);\r\n    if (response.data.token) {\r\n      localStorage.setItem(\"token\", response.data.token);\r\n      dispatch({ type: LOGIN_USER_SUCCESS, payload: response.data.token });\r\n      toast({\r\n        title: \"Login Successfull\",\r\n        description: `${response.data.message}`,\r\n        status: \"success\",\r\n        duration: 3000,\r\n        isClosable: true,\r\n      });\r\n      navigate(\"/\");\r\n    }\r\n  } catch (error) {\r\n    console.log(error);\r\n    dispatch({ type: LOGIN_USER_ERROR });\r\n    toast({\r\n      title: \"Login Failed\",\r\n      description: `${error.response.data.message}`,\r\n      status: \"error\",\r\n      duration: 3000,\r\n      isClosable: true,\r\n    });\r\n  }\r\n};\r\n\r\nexport const logoutUser = (token, toast, navigate) => async (dispatch) => {\r\n  const config = {\r\n    headers: {\r\n      Authorization: `Bearer ${token}`,\r\n    },\r\n  };\r\n\r\n  try {\r\n    const response = await axios.get(\r\n      `${process.env.REACT_APP_API_URL}/auth/logout`,\r\n      config\r\n    );\r\n    localStorage.removeItem(\"token\");\r\n    toast({\r\n      title: \"Logout Successfull\",\r\n      description: `${response.data.message}`,\r\n      status: \"success\",\r\n      duration: 3000,\r\n      isClosable: true,\r\n    });\r\n    navigate(\"/\");\r\n  } catch (error) {\r\n    console.log(\"Error whlie logging out:\", error);\r\n    toast({\r\n      title: \"Logout Failed\",\r\n      description: `${error.response.data.message}`,\r\n      status: \"error\",\r\n      duration: 3000,\r\n      isClosable: true,\r\n    });\r\n  }\r\n\r\n  dispatch({ type: RESET });\r\n};\r\n\r\n//get data of the loggedin user\r\nexport const getUserData = (token, toast) => async (dispatch) => {\r\n  dispatch({ type: GET_LOGGEDUSER_LOADING });\r\n  const config = {\r\n    headers: {\r\n      Authorization: `Bearer ${token}`,\r\n    },\r\n  };\r\n  try {\r\n    const response = await axios.get(\r\n      `${process.env.REACT_APP_API_URL}/users`,\r\n      config\r\n    );\r\n    console.log(response.data.user);\r\n    const userWithProfileImage = response.data.user;\r\n    userWithProfileImage.profileImage = `${process.env.REACT_APP_API_URL}/${userWithProfileImage.profileImage}`;\r\n    dispatch({ type: GET_LOGGEDUSER_SUCCESS, payload: userWithProfileImage });\r\n  } catch (error) {\r\n    console.log(\"Error fetching user data:\", error);\r\n    dispatch({ type: GET_LOGGEDUSER_ERROR });\r\n    toast({\r\n      title: \"Failed To Load User Details\",\r\n      description: `${error.response.data.message}`,\r\n      status: \"error\",\r\n      duration: 3000,\r\n      isClosable: true,\r\n    });\r\n  }\r\n};\r\n\r\n// update user details\r\nexport const updateUserDetails =\r\n  (id, newData, headers, toast) => (dispatch) => {\r\n    axios\r\n      .patch(`${process.env.REACT_APP_API_URL}/users/update/${id}`, newData, {\r\n        headers: headers,\r\n      })\r\n      .then((res) => {\r\n        console.log(res.data.updatedUser, \"data in action from backend\");\r\n        dispatch({\r\n          type: UPDATE_USER_DETAILS,\r\n          payload: res.data.updatedUser,\r\n        });\r\n        toast({\r\n          title: \"Your data was successfully updated\",\r\n          description: `${res.data.status}`,\r\n          status: \"success\",\r\n          duration: 3000,\r\n          isClosable: true,\r\n        });\r\n      })\r\n      .catch((err) => {\r\n        toast({\r\n          title: \"Your data was successfully updated\",\r\n          description: `${err.response.data.message}`,\r\n          status: \"error\",\r\n          duration: 3000,\r\n          isClosable: true,\r\n        });\r\n      });\r\n  };\r\n\r\n// Get user recipes\r\nexport const getUserRecipes = (id, token) => (dispatch) => {\r\n  const config = {\r\n    headers: {\r\n      Authorization: `Bearer ${token}`,\r\n    },\r\n  };\r\n  axios\r\n    .get(\r\n      `${process.env.REACT_APP_API_URL}/recipe/getMyRecipe?populate=recipes`,\r\n      config\r\n    )\r\n    .then((response) => {\r\n      console.log(response.data.recipes);\r\n      dispatch({\r\n        type: \"GET_USER_RECIPES\",\r\n        payload: response.data.recipes,\r\n      });\r\n    })\r\n    .catch((error) => {\r\n      console.error(\"Error fetching user recipes:\", error);\r\n    });\r\n};\r\n\r\nexport const getAllRecipes = (token) => {\r\n  const config = {\r\n    headers: {\r\n      Authorization: `Bearer ${token}`,\r\n    },\r\n  };\r\n  return axios\r\n    .get(`${process.env.REACT_APP_API_URL}/recipe/getAllRecipe`, config)\r\n    .then((res) => {\r\n      console.log(res.data);\r\n    })\r\n    .catch((err) => {\r\n      console.log(err);\r\n    });\r\n};\r\n\r\n// export const getUserDetailsForSingleRecipe = (token, id) => {\r\n//   const config = {\r\n//     headers: {\r\n//       Authorization: `Bearer ${token}`,\r\n//     },\r\n//   };\r\n\r\n//   return axios.get(`${process.env.REACT_APP_API_URL}/users/${id}`, config)\r\n//     .then((res) => {\r\n//       return res.data;\r\n//     })\r\n//     .catch((err) => {\r\n//       console.log(err);\r\n//     });\r\n// }\r\n", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\pages\\Feed.jsx", ["295", "296", "297", "298"], [], "import {\r\n  Box,\r\n  Flex,\r\n  Heading,\r\n  Input,\r\n  InputGroup,\r\n  InputLeftElement,\r\n  Stack,\r\n  Divider,\r\n  useToast,\r\n} from \"@chakra-ui/react\";\r\nimport React, { useEffect } from \"react\";\r\nimport { UserFeed } from \"../components/Feed/UserFeed\";\r\nimport { FriendCard, MiniCard_Chef } from \"../components/Feed/MiniCard\";\r\nimport styled from \"@emotion/styled\";\r\nimport { BsSearch } from \"react-icons/bs\";\r\nimport { NonFriends } from \"../components/Feed/NonFriends\";\r\nimport { Requests } from \"../components/Feed/Requests\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { getFriends } from \"../redux/userReducer/actions\";\r\n\r\nexport const Feed = () => {\r\n  const dispatch = useDispatch();\r\n  const friends = useSelector((store) => store.userReducer.friends);\r\n  const token =\r\n    useSelector((store) => store.authReducer.token) ||\r\n    localStorage.getItem(\"token\");\r\n  useEffect(() => {\r\n    if (token) {\r\n      dispatch(getFriends(token));\r\n    }\r\n  }, []);\r\n  return (\r\n    <DIV>\r\n      <Flex spacing={8} direction=\"row\">\r\n        <Box\r\n          p={5}\r\n          w=\"26%\"\r\n          h=\"90vh\"\r\n          overflowY=\"scroll\"\r\n          className=\"scroll\"\r\n          backgroundColor={\"white\"}\r\n          boxShadow=\"rgba(0, 0, 0, 0.05) 0px 0px 0px 1px\"\r\n        >\r\n          <Heading size={\"md\"} mb={\"2rem\"} textTransform=\"uppercase\">\r\n            People Who Want To Know You\r\n          </Heading>\r\n          <Requests></Requests>\r\n          <Divider my={5} />\r\n          <Heading size={\"md\"} mb=\"2rem\" textTransform=\"uppercase\">\r\n            Your Friends\r\n          </Heading>\r\n          <InputGroup mb=\"10px\">\r\n            <InputLeftElement pointerEvents=\"none\">\r\n              <BsSearch color=\"gray.300\" />\r\n            </InputLeftElement>\r\n            <Input type=\"search\" placeholder=\"Search\" />\r\n          </InputGroup>\r\n          {friends.map((friend, index) => {\r\n            return <FriendCard friend={friend} key={index} />;\r\n          })}\r\n        </Box>\r\n        <UserFeed />\r\n        <Box\r\n          p={5}\r\n          spacing=\"10px\"\r\n          overflowY=\"scroll\"\r\n          className=\"scroll\"\r\n          // bg=\"white\"\r\n          height=\"auto\"\r\n        >\r\n          <Heading size={\"md\"} mb={\"1rem\"} textTransform=\"uppercase\">\r\n            Find New Friends\r\n          </Heading>\r\n          <NonFriends></NonFriends>\r\n        </Box>\r\n      </Flex>\r\n    </DIV>\r\n  );\r\n};\r\n\r\nconst DIV = styled.div`\r\n  background-color: #f7fbfc;\r\n  .scroll::-webkit-scrollbar {\r\n    display: none;\r\n  }\r\n`;\r\n", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\pages\\Home.jsx", ["299", "300", "301", "302"], [], "import React, { useEffect, useState } from \"react\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport {\r\n  Box,\r\n  Button,\r\n  Divider,\r\n  Flex,\r\n  Grid,\r\n  Heading,\r\n  SimpleGrid,\r\n  Text,\r\n  useToast,\r\n} from \"@chakra-ui/react\";\r\nimport styled from \"styled-components\";\r\nimport { getUserData, getUserRecipes } from \"../redux/authReducer/actions\";\r\nimport { Homecard } from \"../components/home/<USER>\";\r\nimport InfoCard from \"../components/home/<USER>\";\r\nimport { RecipeCard } from \"../components/home/<USER>\";\r\nimport ImageGrid from \"../components/home/<USER>\";\r\nimport { Reveal } from \"../components/common/Reveal\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nconst recipes = [\r\n  {\r\n    name: \"Spicy Mango Salsa\",\r\n    image:\r\n      \"https://dev-to-uploads.s3.amazonaws.com/uploads/articles/9pbikyfivldmvf8rivfg.jpg\",\r\n  },\r\n  {\r\n    name: \"Creamy Mushroom Risotto\",\r\n    image:\r\n      \"https://dev-to-uploads.s3.amazonaws.com/uploads/articles/32jo3oxq7sfneppw8hrw.jpg\",\r\n  },\r\n  {\r\n    name: \"Citrus Glazed Salmon\",\r\n    image:\r\n      \"https://dev-to-uploads.s3.amazonaws.com/uploads/articles/69s4bbsn6v44cvllukdj.jpg\",\r\n  },\r\n  {\r\n    name: \"Garlic Herb Roast\",\r\n    image:\r\n      \"https://dev-to-uploads.s3.amazonaws.com/uploads/articles/tb83glu72e9guv4uy8n8.jpg\",\r\n  },\r\n  {\r\n    name: \"Zesty Avocado Salad\",\r\n    image:\r\n      \"https://dev-to-uploads.s3.amazonaws.com/uploads/articles/acxhjjxsu75xk6fwqazn.jpg\",\r\n  },\r\n  {\r\n    name: \"Blueberry Pancakes\",\r\n    image:\r\n      \"https://dev-to-uploads.s3.amazonaws.com/uploads/articles/qc6mrixtyl6ma9x9f47k.jpg\",\r\n  },\r\n];\r\nlet imageUrls = [\r\n  \"https://dev-to-uploads.s3.amazonaws.com/uploads/articles/okl0xaxft6552fjjtz2v.jpg\",\r\n  \"https://dev-to-uploads.s3.amazonaws.com/uploads/articles/tont20fs91ztyywbv1w3.jpg\",\r\n  \"https://dev-to-uploads.s3.amazonaws.com/uploads/articles/58q9ssssox2malve6cey.jpg\",\r\n  \"https://dev-to-uploads.s3.amazonaws.com/uploads/articles/jep6d8153aqns3lmxkzz.jpg\",\r\n];\r\nexport const Home = () => {\r\n  const dispatch = useDispatch();\r\n  const toast = useToast();\r\n  const token = localStorage.getItem(\"token\") || \"\";\r\n  const user = useSelector((store) => store.authReducer.loggedInUser);\r\n\r\n  useEffect(() => {\r\n    if (!user && token) {\r\n      dispatch(getUserData(token, toast));\r\n    }\r\n  }, []);\r\n\r\n  const [screenSize, setScreenSize] = useState(getScreenSize());\r\n\r\n  function getScreenSize() {\r\n    return window.innerWidth > 768\r\n      ? \"lg\"\r\n      : window.innerWidth > 480\r\n      ? \"md\"\r\n      : \"base\";\r\n  }\r\n\r\n  useEffect(() => {\r\n    function handleResize() {\r\n      setScreenSize(getScreenSize());\r\n    }\r\n\r\n    window.addEventListener(\"resize\", handleResize);\r\n\r\n    return () => {\r\n      window.removeEventListener(\"resize\", handleResize);\r\n    };\r\n  }, []);\r\n\r\n  const getRecipesToDisplay = () => {\r\n    const recipesToShow = {\r\n      lg: 6,\r\n      md: 2,\r\n      base: 1,\r\n    };\r\n    console.log(screenSize);\r\n    return recipes.slice(0, recipesToShow[screenSize]);\r\n  };\r\n\r\n  return (\r\n    <DIV>\r\n      <Reveal>\r\n        <Box className=\"cover\">\r\n          <img\r\n            src=\"https://images.pexels.com/photos/1640773/pexels-photo-1640773.jpeg\"\r\n            alt=\"Hero Background\"\r\n          />\r\n          <div className=\"hero-content\" style={{ paddingInline: \"1rem\" }}>\r\n            <Heading\r\n              as=\"h1\"\r\n              fontSize={{ lg: \"3rem\", md: \"2rem\", base: \"1.5rem\" }}\r\n              fontWeight={{ lg: \"800\", md: \"700\", base: \"600\" }}\r\n              textTransform=\"uppercase\"\r\n              textAlign=\"center\"\r\n              noOfLines={2}\r\n              mb=\"1rem\"\r\n              textShadow=\"3px 3px 4px white\"\r\n            >\r\n              Healthy Cooking Recipes <br />\r\n              and the right Nutrition.\r\n            </Heading>\r\n            <Text textAlign=\"center\" mb=\"2rem\">\r\n              Browse Through Over 6500 Tasty Recipes\r\n            </Text>\r\n            <Button>MORE RECIPES</Button>\r\n            <Grid\r\n              mt=\"3rem\"\r\n              width={{ xl: \"100%\", lg: \"80%\", md: \"60%\", base: \"60%\" }}\r\n              templateColumns={{\r\n                lg: \"repeat(3, 1fr)\",\r\n                md: \"repeat(2,1fr)\",\r\n                base: \"1fr\",\r\n              }}\r\n              mx={{ base: \"auto\" }}\r\n              justifyContent=\"center\"\r\n              alignItems={\"center\"}\r\n              gap={{ lg: \"3rem\", md: \"2rem\", base: \"1rem\" }}\r\n            >\r\n              {getRecipesToDisplay().map((el, i) => {\r\n                return (\r\n                  <Reveal key={i} delay={1 + (i + 1) * 0.25}>\r\n                    <Homecard {...el} />\r\n                  </Reveal>\r\n                );\r\n              })}\r\n            </Grid>\r\n          </div>\r\n        </Box>\r\n      </Reveal>\r\n      <Box py={{ lg: 20, md: 16, base: 10 }}>\r\n        <Reveal>\r\n          <InfoCard\r\n            img={\r\n              \"https://dev-to-uploads.s3.amazonaws.com/uploads/articles/keyqnkmsp4cfr2d4qvtn.jpg\"\r\n            }\r\n            title={\"HEALTHY AND QUALITY WITH A NEW FEEL\"}\r\n            direction={\"row\"}\r\n            screenSize={screenSize}\r\n            mb={\"15rem\"}\r\n          />\r\n        </Reveal>\r\n        <Reveal>\r\n          <InfoCard\r\n            img={\r\n              \"https://dev-to-uploads.s3.amazonaws.com/uploads/articles/yd6q7vj4fhvteleq0065.jpg\"\r\n            }\r\n            screenSize={screenSize}\r\n            title={\"TASTE THE FUTURE OF GOOD FOOD\"}\r\n            direction={\"row-reverse\"}\r\n          />\r\n        </Reveal>\r\n      </Box>\r\n      <Reveal>\r\n        <Box textAlign=\"center\" py={{ lg: 20, md: 16, base: 10 }}>\r\n          <Heading\r\n            fontFamily={\"Kaushan Script, sans-serif\"}\r\n            size={{ lg: \"lg\", md: \"md\", base: \"sm\" }}\r\n            color=\"primary.500\"\r\n            mb=\"0.5rem\"\r\n          >\r\n            More\r\n          </Heading>\r\n          <Heading\r\n            fontWeight=\"800\"\r\n            lineHeight={1.15}\r\n            mb=\"1rem\"\r\n            noOfLines={2}\r\n            color=\"text\"\r\n            maxW=\"500px\"\r\n            mx=\"auto\"\r\n            size={{ lg: \"xl\", md: \"lg\", base: \"md\" }}\r\n          >\r\n            {\" \"}\r\n            MOST POPULAR ITEM{\" \"}\r\n          </Heading>\r\n          <Text mb={\"2rem\"}>\r\n            {\" \"}\r\n            Lorem ipsum, dolor sit amet consectetur adipisicing elit. <br />\r\n            Odit id et est eveniet officiis.{\" \"}\r\n          </Text>\r\n          <Button mb={\"4rem\"}>Explore More</Button>\r\n          <SimpleGrid\r\n            columns={{ lg: 4, md: 2, base: 1 }}\r\n            spacing={4}\r\n            width=\"min(80rem,100%)\"\r\n            mx=\"auto\"\r\n            px={{ lg: 4, base: 8 }}\r\n          >\r\n            {imageUrls.map((el, i) => {\r\n              return <RecipeCard key={i} img={el} />;\r\n            })}\r\n          </SimpleGrid>\r\n        </Box>\r\n      </Reveal>\r\n      <Reveal>\r\n        <Flex\r\n          mx=\"auto\"\r\n          // border=\"1px solid black\"\r\n          direction={{ lg: \"row\", md: \"row\", base: \"column\" }}\r\n          paddingBlock={{ lg: \"8rem 10rem\", md: \"5rem\", base: \"4rem\" }}\r\n          px={4}\r\n          width=\"min(80rem,100%)\"\r\n          alignItems=\"center\"\r\n          justifyContent={\"space-between\"}\r\n          gap=\"2rem\"\r\n        >\r\n          <ImageGrid />\r\n          <Box\r\n            width={{ lg: \"50%\", base: \"100%\" }}\r\n            textAlign={{ lg: \"right\", base: \"center\" }}\r\n          >\r\n            <Heading\r\n              fontFamily={\"Kaushan Script, sans-serif\"}\r\n              size={{ lg: \"lg\", md: \"md\", base: \"sm\" }}\r\n              color=\"primary.500\"\r\n              mb=\"0.5rem\"\r\n            >\r\n              About\r\n            </Heading>\r\n            <Heading\r\n              fontWeight=\"800\"\r\n              lineHeight={1.15}\r\n              mb=\"1rem\"\r\n              noOfLines={{ lg: 2 }}\r\n              color=\"text\"\r\n              maxW={{ lg: \"500px\" }}\r\n              ml=\"auto\"\r\n              size={{ lg: \"xl\", md: \"lg\", base: \"md\" }}\r\n            >\r\n              THAT'S WHAT OUR <br /> SAY CLIENT\r\n            </Heading>\r\n            <Text mb=\"2rem\">\r\n              This dish combines fresh, high-quality ingredients to create a\r\n              wholesome and nutritious meal that your body will thank you for.\r\n              With a unique twist and a burst of exciting flavors, it's the\r\n              perfect choice for a healthy and satisfying dining experience. Get\r\n              ready to impress your family and friends with this remarkable\r\n              recipe.\r\n            </Text>\r\n            <Button>Explore More</Button>\r\n          </Box>\r\n        </Flex>\r\n      </Reveal>\r\n    </DIV>\r\n  );\r\n};\r\n\r\nconst DIV = styled.div`\r\n  .cover {\r\n    width: 100%;\r\n    height: 90vh;\r\n    text-align: center;\r\n    position: relative;\r\n    overflow: hidden;\r\n    img {\r\n      width: 100%;\r\n      object-fit: cover;\r\n      object-position: top;\r\n    }\r\n    .hero-content {\r\n      position: absolute;\r\n      width: min(80rem, 100%);\r\n      top: 50%;\r\n      left: 50%;\r\n      transform: translate(-50%, -50%);\r\n      z-index: 11;\r\n    }\r\n    &::before {\r\n      content: \"\";\r\n      position: absolute;\r\n      inset: 0;\r\n      background-color: #fff4;\r\n      z-index: 1;\r\n    }\r\n  }\r\n  @media screen and (max-width: 768px) {\r\n    .cover {\r\n      height: 70vh;\r\n      img {\r\n        width: 100%;\r\n        min-height: 100%;\r\n      }\r\n    }\r\n  }\r\n`;\r\n", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\pages\\Explore.jsx", ["303", "304", "305", "306", "307", "308"], [], "import {\r\n  Box,\r\n  Button,\r\n  Grid,\r\n  HStack,\r\n  Heading,\r\n  Image,\r\n  Input,\r\n  InputGroup,\r\n  InputLeftElement,\r\n  Modal,\r\n  ModalBody,\r\n  ModalCloseButton,\r\n  ModalContent,\r\n  Modal<PERSON>ooter,\r\n  ModalHeader,\r\n  ModalOverlay,\r\n  Radio,\r\n  RadioGroup,\r\n  Select,\r\n  Stack,\r\n  Divider,\r\n  Tag,\r\n  TagCloseButton,\r\n  Text,\r\n  VStack,\r\n  useDisclosure,\r\n  Flex,\r\n  CardFooter,\r\n  Card,\r\n  CardHeader,\r\n  Spinner,\r\n} from \"@chakra-ui/react\";\r\nimport { SearchIcon } from \"@chakra-ui/icons\";\r\nimport React, { useEffect, useState } from \"react\";\r\nimport { getAllRecipes } from \"../redux/authReducer/actions\";\r\nimport { useSelector } from \"react-redux\";\r\nimport axios from \"axios\";\r\nimport FeedCard from \"../components/Feed/FeedCard\";\r\nimport { BiLike, BiShare } from \"react-icons/bi\";\r\nimport styled from \"@emotion/styled\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { Carousel } from \"../components/Feed/SingleRecipeCarousel\";\r\n\r\nexport const Explore = () => {\r\n  const { isOpen, onOpen, onClose } = useDisclosure();\r\n  const [selectedCuisines, setSelectedCuisines] = useState([]);\r\n  const [recipe, setRecipe] = useState([]);\r\n  const [filter, setFilter] = useState(false);\r\n  const [impression, setImpression] = useState(null);\r\n  const navigate = useNavigate();\r\n  const [selectedOption, setSelectedOption] = useState(null);\r\n  const handleImpressionChange = (event) => {\r\n    setImpression(event.target.value);\r\n  };\r\n  const token =\r\n    useSelector((store) => store.authReducer.token) ||\r\n    localStorage.getItem(\"token\");\r\n\r\n  const handleFilter = () => {\r\n    setFilter(!filter);\r\n    onClose();\r\n  };\r\n\r\n  // Function to set cuisine multiple option\r\n  const handleCuisineChange = (e) => {\r\n    const data = e.target.value;\r\n    const newCuisines = [...selectedCuisines];\r\n    if (newCuisines.includes(data)) {\r\n      const index = newCuisines.indexOf(data);\r\n      newCuisines.splice(index, 1);\r\n    } else {\r\n      newCuisines.push(data);\r\n    }\r\n    setSelectedCuisines(newCuisines);\r\n  };\r\n\r\n  // Function to remove cuisine\r\n  const handleCuisineRemove = (cuisine) => {\r\n    const updatedCuisines = selectedCuisines.filter((item) => item !== cuisine);\r\n    setSelectedCuisines(updatedCuisines);\r\n  };\r\n\r\n  useEffect(() => {\r\n    const config = {\r\n      headers: {\r\n        Authorization: `Bearer ${token}`,\r\n      },\r\n    };\r\n\r\n    axios\r\n      .get(`${process.env.REACT_APP_API_URL}/recipe/getAllRecipe`, {\r\n        params: {\r\n          impression: impression,\r\n          veg: selectedOption,\r\n        },\r\n        headers: config.headers,\r\n      })\r\n      .then((res) => {\r\n        let filteredRecipes = res.data; // Initialize filteredRecipes with all recipes\r\n\r\n        if (selectedCuisines.length > 0) {\r\n          filteredRecipes = res.data.filter((recipe) => {\r\n            // Check if at least one cuisine in the selectedCuisines array matches the cuisines of the recipe\r\n            return selectedCuisines.some((selectedCuisine) =>\r\n              recipe.cuisine.includes(selectedCuisine)\r\n            );\r\n          });\r\n        }\r\n\r\n        setRecipe(filteredRecipes); // Set the state with the filtered or unfiltered recipes\r\n        setSelectedOption(null);\r\n      })\r\n      .catch((err) => {\r\n        console.log(err);\r\n      });\r\n  }, [filter]);\r\n\r\n  console.log(recipe, \"recipe\");\r\n\r\n  // All types of cuisine\r\n  const cuisines = [\r\n    \"Mexican\",\r\n    \"Italian\",\r\n    \"Chinese\",\r\n    \"Indian\",\r\n    \"German\",\r\n    \"Greek\",\r\n    \"Filipino\",\r\n    \"Japanese\",\r\n  ];\r\n\r\n  return (\r\n    <>\r\n      <Box>\r\n        {/* Hero section image with heading and a button */}\r\n        <Box h=\"45vh\" position=\"relative\">\r\n          <Image\r\n            src=\"https://images.unsplash.com/photo-1495546968767-f0573cca821e?auto=format&fit=crop&q=80&w=2831&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D\"\r\n            alt=\"Hero image\"\r\n            w=\"100%\"\r\n            h=\"100%\"\r\n            objectFit=\"cover\"\r\n          />\r\n\r\n          <VStack\r\n            position=\"absolute\"\r\n            top=\"0\"\r\n            left=\"0\"\r\n            right=\"0\"\r\n            bottom=\"0\"\r\n            alignItems=\"center\"\r\n            justifyContent=\"space-around\"\r\n            paddingX=\"7\"\r\n          >\r\n            <Heading\r\n              as=\"h3\"\r\n              size=\"2xl\"\r\n              color=\"white\"\r\n              textShadow=\"1px 1px 2px black\"\r\n              textAlign={\"center\"}\r\n            >\r\n              Find the best recipes in a few step!\r\n            </Heading>\r\n\r\n            <Button>Search now</Button>\r\n          </VStack>\r\n        </Box>\r\n        {/* Search bar and advance search option */}\r\n        <Box boxShadow=\"0 4px 10px #0002\" padding=\"4\">\r\n          <HStack spacing={5} width=\"min(80rem,100%)\" mx=\"auto\">\r\n            {/* <Input variant=\"flushed\" placeholder=\"Flushed\" width='30%' /> */}\r\n            <InputGroup width=\"30%\">\r\n              <InputLeftElement pointerEvents=\"none\">\r\n                <SearchIcon color=\"text\" />\r\n              </InputLeftElement>\r\n              <Input\r\n                placeholder=\"Search for a recipe\"\r\n                border=\"1px solid\"\r\n                outline=\"none\"\r\n                borderColor=\"text\"\r\n                _focus={{ borderColor: \"primary.500\" }}\r\n              />\r\n            </InputGroup>\r\n            <Heading as=\"h5\" size=\"md\" color=\"text\">\r\n              Advanced Search\r\n            </Heading>\r\n            {/* This is the icon of filter */}\r\n            <svg\r\n              onClick={onOpen}\r\n              width=\"30\"\r\n              height=\"30\"\r\n              viewBox=\"0 0 24 24\"\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              cursor=\"pointer\"\r\n            >\r\n              <path\r\n                fill=\"#F58332\"\r\n                d=\"M9 5a1 1 0 1 0 0 2a1 1 0 0 0 0-2zM6.17 5a3.001 3.001 0 0 1 5.66 0H19a1 1 0 1 1 0 2h-7.17a3.001 3.001 0 0 1-5.66 0H5a1 1 0 0 1 0-2h1.17zM15 11a1 1 0 1 0 0 2a1 1 0 0 0 0-2zm-2.83 0a3.001 3.001 0 0 1 5.66 0H19a1 1 0 1 1 0 2h-1.17a3.001 3.001 0 0 1-5.66 0H5a1 1 0 1 1 0-2h7.17zM9 17a1 1 0 1 0 0 2a1 1 0 0 0 0-2zm-2.83 0a3.001 3.001 0 0 1 5.66 0H19a1 1 0 1 1 0 2h-7.17a3.001 3.001 0 0 1-5.66 0H5a1 1 0 1 1 0-2h1.17z\"\r\n              />\r\n            </svg>\r\n          </HStack>\r\n          {/* This model will open when selected advance search feature */}\r\n          <Modal isOpen={isOpen} onClose={onClose}>\r\n            <ModalOverlay />\r\n            <ModalContent>\r\n              <ModalHeader>Modal Title</ModalHeader>\r\n              <ModalCloseButton />\r\n              <ModalBody>\r\n                {/* Options for rating */}\r\n                <Select\r\n                  value={impression}\r\n                  onChange={handleImpressionChange}\r\n                  placeholder=\"Recipes with Impressions\"\r\n                >\r\n                  <option value=\"asc\">Highest</option>\r\n                  <option value=\"desc\">Lowest</option>\r\n                </Select>\r\n                {/* Options for selecting veg / non-veg recipe */}\r\n                <RadioGroup\r\n                  marginY=\"5\"\r\n                  value={selectedOption}\r\n                  onChange={setSelectedOption}\r\n                >\r\n                  <Stack spacing={5} direction=\"row\">\r\n                    <Radio colorScheme=\"green\" value=\"veg\">\r\n                      Only Veg\r\n                    </Radio>\r\n                    <Radio colorScheme=\"red\" value=\"non-veg\">\r\n                      Only Non-veg\r\n                    </Radio>\r\n                  </Stack>\r\n                </RadioGroup>\r\n                {/* Select option for single/multiple cuisines */}\r\n                <Select\r\n                  placeholder=\"Select cuisines\"\r\n                  value={cuisines}\r\n                  onChange={handleCuisineChange}\r\n                >\r\n                  {cuisines.map((cuisine) => (\r\n                    <option key={cuisine + Date.now()} value={cuisine}>\r\n                      {cuisine}\r\n                    </option>\r\n                  ))}\r\n                </Select>\r\n                {/* This will show the selected cuisines */}\r\n                <HStack\r\n                  display={\"flex\"}\r\n                  flexWrap={\"wrap\"}\r\n                  paddingY=\"2\"\r\n                  spacing=\"2\"\r\n                >\r\n                  {selectedCuisines.map((cuisine) => (\r\n                    <Tag key={cuisine} size=\"md\">\r\n                      {cuisine}\r\n                      <TagCloseButton\r\n                        onClick={() => handleCuisineRemove(cuisine)}\r\n                      />\r\n                    </Tag>\r\n                  ))}\r\n                </HStack>\r\n              </ModalBody>\r\n              <ModalFooter>\r\n                <Button\r\n                  border={\"1px solid\"}\r\n                  borderColor={\"secondary\"}\r\n                  color=\"secondary\"\r\n                  variant=\"outline\"\r\n                  mr=\"1rem\"\r\n                  onClick={onClose}\r\n                >\r\n                  Close\r\n                </Button>\r\n                <Button variant=\"solid\" onClick={handleFilter}>\r\n                  Apply\r\n                </Button>\r\n              </ModalFooter>\r\n            </ModalContent>\r\n          </Modal>\r\n        </Box>\r\n        {/* Mapping all recipe */}\r\n        <DIV>\r\n          {recipe.length == 0 ? (\r\n            <Spinner\r\n              mx=\"auto\"\r\n              thickness=\"4px\"\r\n              speed=\"0.65s\"\r\n              emptyColor=\"gray.200\"\r\n              color=\"primary.500\"\r\n              size=\"xl\"\r\n            />\r\n          ) : (\r\n            recipe?.length > 0 &&\r\n            recipe.map((ele, index) => (\r\n              <Card\r\n                textAlign={\"left\"}\r\n                key={index}\r\n                boxShadow={\"lg\"}\r\n                borderRadius=\"1rem\"\r\n                transition=\"0.2s ease-in\"\r\n                _hover={{ boxShadow: \"xl\", transform: \"scale(1.01)\" }}\r\n              >\r\n                <Box borderWidth=\"0\" borderRadius=\"md\" overflow=\"hidden\">\r\n                  <CardHeader>\r\n                    {/* <Image\r\n                      width=\"100%\"\r\n                      src={`${process.env.REACT_APP_API_URL}/${ele.images[0]}`}\r\n                      alt=\"Card\"\r\n                    /> */}\r\n                    <Carousel height={\"300px\"} images={ele?.images} />\r\n                  </CardHeader>\r\n                  <Divider w=\"90%\" mx=\"auto\"></Divider>\r\n                  <Box p=\"1rem\">\r\n                    <Heading\r\n                      fontSize=\"lg\"\r\n                      m={0}\r\n                      lineHeight={1.1}\r\n                      textTransform=\"uppercase\"\r\n                      fontWeight=\"700\"\r\n                    >\r\n                      {ele.title}\r\n                    </Heading>\r\n                    <Flex\r\n                      align=\"center\"\r\n                      justifyContent=\"space-between\"\r\n                      py={1}\r\n                      mb=\"0.5rem\"\r\n                    >\r\n                      <Text\r\n                        my={3}\r\n                        fontFamily={\"Kaushan Script\"}\r\n                        fontSize=\"md\"\r\n                        fontWeight=\"bold\"\r\n                        color=\"primary.500\"\r\n                      >\r\n                        {ele?.cuisine[0]}\r\n                      </Text>\r\n                      <Flex mt={3} flexWrap=\"wrap\" gap={3}>\r\n                        {ele?.tags?.length > 0 &&\r\n                          ele?.tags.map((e, index) => (\r\n                            <Tag key={index}>{e}</Tag>\r\n                          ))}\r\n                      </Flex>\r\n                    </Flex>\r\n                    <Text fontSize=\"sm\" mb=\"1rem\">\r\n                      {ele.description}\r\n                    </Text>\r\n                    <CardFooter\r\n                      p=\"0\"\r\n                      justify=\"flex-start\"\r\n                      gap=\"1rem\"\r\n                      flexWrap=\"wrap\"\r\n                      sx={{\r\n                        \"& > button\": {\r\n                          minW: \"136px\",\r\n                        },\r\n                      }}\r\n                    >\r\n                      <Button\r\n                        flex={{ base: \"1\", md: \"0.25\" }}\r\n                        variant=\"outline\"\r\n                        leftIcon={<BiShare />}\r\n                        border={\"1px solid\"}\r\n                        borderColor={\"secondary\"}\r\n                        color=\"secondary\"\r\n                        onClick={() => navigate(`/recipe/${ele._id}`)}\r\n                      >\r\n                        Details\r\n                      </Button>\r\n                    </CardFooter>\r\n                  </Box>\r\n                </Box>\r\n              </Card>\r\n            ))\r\n          )}\r\n        </DIV>\r\n      </Box>\r\n    </>\r\n  );\r\n};\r\n\r\nconst DIV = styled.div`\r\n  text-align: center;\r\n  min-height: 20vh;\r\n  display: grid;\r\n  grid-template-columns: repeat(3, 1fr);\r\n  gap: 20px;\r\n  width: min(80rem, 100%);\r\n  margin-inline: auto;\r\n  padding-block: 5rem;\r\n  @media (max-width: 768px) {\r\n    grid-template-columns: repeat(\r\n      1,\r\n      1fr\r\n    ); /* 1 column on screens up to 768px wide */\r\n  }\r\n\r\n  @media (min-width: 769px) and (max-width: 1024px) {\r\n    grid-template-columns: repeat(\r\n      2,\r\n      1fr\r\n    ); /* 2 columns on screens between 769px and 1024px wide */\r\n  }\r\n\r\n  @media (min-width: 1025px) {\r\n    grid-template-columns: repeat(\r\n      3,\r\n      1fr\r\n    ); /* 3 columns on screens wider than 1024px */\r\n  }\r\n`;\r\n", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\pages\\SignUp.jsx", [], [], "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\pages\\Login.jsx", [], [], "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\pages\\Account.jsx", ["309", "310", "311", "312"], [], "import {\r\n  Box,\r\n  Button,\r\n  Center,\r\n  Container,\r\n  Flex,\r\n  Image,\r\n  Text,\r\n  UnorderedList,\r\n  ListItem,\r\n  Grid,\r\n  HStack,\r\n  Tabs,\r\n  TabList,\r\n  Tab,\r\n  TabPanel,\r\n  TabPanels,\r\n  Tooltip,\r\n  useDisclosure,\r\n  Modal,\r\n  ModalOverlay,\r\n  ModalContent,\r\n  ModalHeader,\r\n  ModalFooter,\r\n  ModalBody,\r\n  ModalCloseButton,\r\n  Editable,\r\n  EditablePreview,\r\n  useEditableControls,\r\n  ButtonGroup,\r\n  IconButton,\r\n  EditableInput,\r\n  Textarea,\r\n  Heading,\r\n  Divider,\r\n} from \"@chakra-ui/react\";\r\nimport { CheckIcon, CloseIcon, EditIcon } from \"@chakra-ui/icons\";\r\nimport React, { useEffect, useState } from \"react\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { getUserData, updateUserDetails } from \"../redux/authReducer/actions\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { useToast } from \"@chakra-ui/react\";\r\nimport axios from \"axios\";\r\n\r\nexport const Account = () => {\r\n  const toast = useToast();\r\n  const dispatch = useDispatch();\r\n  const { isOpen, onOpen, onClose } = useDisclosure();\r\n  const navigate = useNavigate();\r\n  const [showRecipe, setShowRecipe] = useState(\"recipes\");\r\n  const token =\r\n    useSelector((store) => store.authReducer.token) ||\r\n    localStorage.getItem(\"token\");\r\n  // console.log(token)\r\n  const user = useSelector((store) => store.authReducer.loggedInUser);\r\n  console.log(\"user\", user);\r\n  // const recipes = useSelector((store) => store.authReducer.recipes);\r\n  const [recipes, setRecipes] = useState([]);\r\n  const [likedRecipes, setLikedRecipes] = useState([]);\r\n  const [savedRecipes, setSavedRecipes] = useState([]);\r\n  const [userName, setUserName] = useState(user?.name);\r\n  const [userBio, setUserBio] = useState(user?.bio);\r\n  const [userCity, setUserCity] = useState(user?.city);\r\n\r\n  // Function to edit profile\r\n  const handleEditProfile = () => {\r\n    const newUserName = userName || user?.name;\r\n    const newUserBio = userBio || user?.bio;\r\n    const newUserCity = userCity || user?.city;\r\n\r\n    const data = {\r\n      name: newUserName,\r\n      bio: newUserBio,\r\n      city: newUserCity,\r\n    };\r\n    console.log(\"Data that i wanter to get updated\", data);\r\n    const headers = {\r\n      Authorization: `Bearer ${token}`,\r\n    };\r\n    dispatch(updateUserDetails(user?._id, data, headers, toast));\r\n    navigate(\"/\");\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (token) {\r\n      dispatch(getUserData(token, toast));\r\n    }\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    const config = {\r\n      headers: {\r\n        Authorization: `Bearer ${token}`,\r\n      },\r\n    };\r\n    axios\r\n      .get(\r\n        `${process.env.REACT_APP_API_URL}/recipe/getMyRecipe?populate=${showRecipe}&`,\r\n        config\r\n      )\r\n      .then((response) => {\r\n        setRecipes(response.data.recipes);\r\n        setLikedRecipes(response.data.likedRecipes);\r\n        setSavedRecipes(response.data.savedRecipes);\r\n      })\r\n      .catch((error) => {\r\n        console.error(\"Error fetching user recipes:\", error);\r\n      });\r\n  }, [showRecipe]);\r\n\r\n  return (\r\n    <Container\r\n      bgColor={\"#EEF2F7\"}\r\n      maxW=\"full\"\r\n      height={\"100vh\"}\r\n      p={0}\r\n      paddingBlock={\"3rem\"}\r\n    >\r\n      {/* Modal for editting profile */}\r\n      <Modal isOpen={isOpen} onClose={onClose}>\r\n        <ModalOverlay />\r\n        <ModalContent>\r\n          <ModalHeader textTransform={\"uppercase\"} fontSize={\"2xl\"}>\r\n            Edit profile\r\n          </ModalHeader>\r\n          <ModalCloseButton />\r\n          <ModalBody>\r\n            {/* For name */}\r\n            <Center>\r\n              <Text fontWeight={\"bold\"} textTransform={\"uppercase\"}>\r\n                Name\r\n              </Text>\r\n            </Center>\r\n            <Editable\r\n              mb=\"1rem\"\r\n              textAlign=\"center\"\r\n              defaultValue={user?.name}\r\n              fontSize=\"md\"\r\n              isPreviewFocusable={false}\r\n              onChange={(newUserName) => setUserName(newUserName)}\r\n            >\r\n              <EditablePreview />\r\n              {/* Here is the custom input */}\r\n              <Textarea as={EditableInput} my=\"0.5rem\" />\r\n              <EditableControls />\r\n            </Editable>\r\n            <Divider mb=\"1rem\"></Divider>\r\n\r\n            {/* For city */}\r\n            <Center>\r\n              <Text fontWeight={\"bold\"} textTransform={\"uppercase\"}>\r\n                City\r\n              </Text>\r\n            </Center>\r\n            <Editable\r\n              mb=\"1rem\"\r\n              textAlign=\"center\"\r\n              defaultValue={user?.city}\r\n              fontSize=\"md\"\r\n              isPreviewFocusable={false}\r\n              onChange={(newUserCity) => setUserCity(newUserCity)}\r\n            >\r\n              <EditablePreview />\r\n              {/* Here is the custom input */}\r\n              <Textarea as={EditableInput} my=\"0.5rem\" />\r\n              <EditableControls />\r\n            </Editable>\r\n            <Divider mb=\"1rem\"></Divider>\r\n\r\n            {/* For bio */}\r\n            <Center>\r\n              <Text fontWeight={\"bold\"} textTransform=\"uppercase\">\r\n                Biography\r\n              </Text>\r\n            </Center>\r\n            <Editable\r\n              mb=\"1rem\"\r\n              textAlign=\"center\"\r\n              defaultValue={user?.bio}\r\n              fontSize=\"md\"\r\n              isPreviewFocusable={false}\r\n              onChange={(newUserBio) => setUserBio(newUserBio)}\r\n            >\r\n              <EditablePreview />\r\n              {/* Here is the custom input */}\r\n              <Textarea as={EditableInput} my=\"0.5rem\" />\r\n              <EditableControls />\r\n            </Editable>\r\n            <Divider mb=\"1rem\"></Divider>\r\n          </ModalBody>\r\n          <ModalFooter>\r\n            <Button variant=\"outline\" mr={\"1rem\"} onClick={onClose}>\r\n              Close\r\n            </Button>\r\n            <Button onClick={handleEditProfile}>Edit</Button>\r\n          </ModalFooter>\r\n        </ModalContent>\r\n      </Modal>\r\n      <Flex flexDir={{ base: \"column\" }} bg=\"gray.100\" p={4}>\r\n        {/* User detail section */}\r\n        <Box\r\n          w={\"min(80rem,100%)\"}\r\n          mx={\"auto\"}\r\n          display={\"flex\"}\r\n          flexDirection={{ base: \"column\", md: \"row\", lg: \"row\" }}\r\n          justifyContent={\"space-between\"}\r\n          gap=\"1rem\"\r\n        >\r\n          {/* User Profile Info */}\r\n          <Box\r\n            p={2}\r\n            border=\"1px solid\"\r\n            borderColor={\"accent\"}\r\n            borderRadius={\"50%\"}\r\n            overflow=\"hidden\"\r\n            aspectRatio={1}\r\n            w={{ md: \"35%\", base: \"50%\", lg: \"35%\" }}\r\n            mx={{ base: \"auto\" }}\r\n          >\r\n            <Image\r\n              aspectRatio={1}\r\n              w=\"100%\"\r\n              borderRadius=\"50%\"\r\n              objectFit=\"cover\"\r\n              src={user?.profileImage}\r\n              alt=\"Profile picture\"\r\n            />\r\n          </Box>\r\n          <Center>\r\n            <Divider\r\n              orientation=\"vertical\"\r\n              borderColor=\"secondary\"\r\n              opacity={0.3}\r\n            />\r\n          </Center>\r\n          <Box\r\n            display=\"flex\"\r\n            flexDir={{ base: \"column\" }}\r\n            alignItems={\"stretch\"}\r\n            justifyContent={\"space-around\"}\r\n          >\r\n            <Flex alignItems=\"center\">\r\n              <Text\r\n                fontWeight=\"700\"\r\n                fontSize=\"lg\"\r\n                textTransform={\"uppercase\"}\r\n                mr=\"1rem\"\r\n              >\r\n                {user?.name}\r\n              </Text>\r\n              <Text>{user?.city}</Text>\r\n              <Button\r\n                size=\"sm\"\r\n                colorScheme=\"primary\"\r\n                variant={\"outline\"}\r\n                onClick={onOpen}\r\n                ml=\"auto\"\r\n              >\r\n                Edit Profile\r\n              </Button>\r\n            </Flex>\r\n\r\n            <Text p={0} m={0}>\r\n              {user?.bio}\r\n            </Text>\r\n            <UnorderedList\r\n              listStyleType=\"none\"\r\n              display=\"flex\"\r\n              justifyContent=\"space-between\"\r\n              m=\"0\"\r\n              textAlign={\"center\"}\r\n            >\r\n              <ListItem>\r\n                <Text fontWeight=\"bold\" textTransform=\"uppercase\">\r\n                  Posts\r\n                </Text>\r\n                <Text>{user?.recipes.length}</Text>\r\n              </ListItem>\r\n              <ListItem>\r\n                <Text fontWeight=\"bold\" textTransform=\"uppercase\">\r\n                  Friends\r\n                </Text>\r\n                <Text>{user?.friends.length}</Text>\r\n              </ListItem>\r\n              <ListItem>\r\n                <Text fontWeight=\"bold\" textTransform=\"uppercase\">\r\n                  Saved Recipes\r\n                </Text>\r\n                <Text>{user?.savedRecipes.length}</Text>\r\n              </ListItem>\r\n              <ListItem>\r\n                <Text fontWeight=\"bold\" textTransform=\"uppercase\">\r\n                  Liked Recipes\r\n                </Text>\r\n                <Text>{user?.likedRecipes.length}</Text>\r\n              </ListItem>\r\n            </UnorderedList>\r\n          </Box>\r\n        </Box>\r\n\r\n        {/* User Posts and others */}\r\n        <Box w={\"min(80rem,100%)\"} m={\"auto\"} mt=\"1rem\">\r\n          {/* Grid View of Images */}\r\n          <Tabs colorScheme=\"primary\" isFitted>\r\n            <TabList>\r\n              <Tab onClick={() => setShowRecipe(\"recipes\")}>Posts</Tab>\r\n              <Tab onClick={() => setShowRecipe(\"savedRecipes\")}>\r\n                Saved Recipes\r\n              </Tab>\r\n              <Tab onClick={() => setShowRecipe(\"likedRecipes\")}>\r\n                Recent Likes\r\n              </Tab>\r\n            </TabList>\r\n            <TabPanels>\r\n              <TabPanel>\r\n                <Grid templateColumns=\"repeat(3, 1fr)\" gap={2}>\r\n                  {recipes?.length > 0 &&\r\n                    recipes.map((ele, index) => (\r\n                      <Tooltip\r\n                        bg=\"accent\"\r\n                        px={4}\r\n                        py={2}\r\n                        label={`Likes: ${ele?.likes?.length}, Comments: ${ele?.comments?.length}`}\r\n                        key={index}\r\n                      >\r\n                        <div>\r\n                          <Image\r\n                            src={`${process.env.REACT_APP_API_URL}/${ele.images[0]}`}\r\n                            alt=\"Recipe Image\"\r\n                            boxSize=\"100%\"\r\n                            objectFit=\"cover\"\r\n                            onClick={() => navigate(`/recipe/${ele._id}`)}\r\n                          />\r\n                        </div>\r\n                      </Tooltip>\r\n                    ))}\r\n                </Grid>\r\n              </TabPanel>\r\n              <TabPanel>\r\n                <Grid templateColumns=\"repeat(3, 1fr)\" gap={2}>\r\n                  {savedRecipes?.length > 0 &&\r\n                    savedRecipes.map((ele, index) => (\r\n                      <Tooltip\r\n                        bg=\"accent\"\r\n                        px={4}\r\n                        py={2}\r\n                        label={`Likes: ${ele?.likes?.length}, Comments: ${ele?.comments?.length}`}\r\n                        key={index}\r\n                      >\r\n                        <div>\r\n                          <Image\r\n                            src={`${process.env.REACT_APP_API_URL}/${ele.images[0]}`}\r\n                            alt=\"Recipe Image\"\r\n                            boxSize=\"100%\"\r\n                            objectFit=\"cover\"\r\n                            onClick={() => navigate(`/recipe/${ele._id}`)}\r\n                          />\r\n                        </div>\r\n                      </Tooltip>\r\n                    ))}\r\n                </Grid>\r\n              </TabPanel>\r\n              <TabPanel>\r\n                <Grid templateColumns=\"repeat(3, 1fr)\" gap={2}>\r\n                  {likedRecipes?.length > 0 &&\r\n                    likedRecipes.map((ele, index) => (\r\n                      <Tooltip\r\n                        bg=\"accent\"\r\n                        px={4}\r\n                        py={2}\r\n                        label={`Likes: ${ele?.likes?.length}, Comments: ${ele?.comments?.length}`}\r\n                        key={index}\r\n                      >\r\n                        <div>\r\n                          <Image\r\n                            src={`${process.env.REACT_APP_API_URL}/${ele.images[0]}`}\r\n                            alt=\"Recipe Image\"\r\n                            boxSize=\"100%\"\r\n                            objectFit=\"cover\"\r\n                            onClick={() => navigate(`/recipe/${ele._id}`)}\r\n                          />\r\n                        </div>\r\n                      </Tooltip>\r\n                    ))}\r\n                </Grid>\r\n              </TabPanel>\r\n            </TabPanels>\r\n          </Tabs>\r\n        </Box>\r\n      </Flex>\r\n    </Container>\r\n  );\r\n};\r\n\r\nfunction EditableControls() {\r\n  const {\r\n    isEditing,\r\n    getSubmitButtonProps,\r\n    getCancelButtonProps,\r\n    getEditButtonProps,\r\n  } = useEditableControls();\r\n\r\n  return isEditing ? (\r\n    <ButtonGroup justifyContent=\"center\" size=\"sm\">\r\n      <IconButton icon={<CheckIcon />} {...getSubmitButtonProps()} />\r\n      <IconButton icon={<CloseIcon />} {...getCancelButtonProps()} />\r\n    </ButtonGroup>\r\n  ) : (\r\n    <Flex justifyContent=\"center\">\r\n      <IconButton size=\"sm\" icon={<EditIcon />} {...getEditButtonProps()} />\r\n    </Flex>\r\n  );\r\n}\r\n", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\redux\\recipeReducer\\actionTypes.js", [], [], "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\redux\\authReducer\\actionTypes.js", [], [], "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\routes\\PrivateRoute.jsx", [], [], "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\pages\\SingleRecipe.jsx", ["313", "314", "315", "316", "317"], [], "import styled from \"@emotion/styled\";\r\nimport { useEffect, useState } from \"react\";\r\nimport { Carousel } from \"../components/Feed/SingleRecipeCarousel\";\r\nimport { useParams } from \"react-router-dom\";\r\nimport { CheckIcon } from \"@chakra-ui/icons\";\r\nimport {\r\n  Box,\r\n  Checkbox,\r\n  Flex,\r\n  Heading,\r\n  Step,\r\n  StepIcon,\r\n  StepIndicator,\r\n  StepNumber,\r\n  StepSeparator,\r\n  StepStatus,\r\n  StepTitle,\r\n  Stepper,\r\n  Tag,\r\n  Text,\r\n  SimpleGrid,\r\n  List,\r\n  ListItem,\r\n  VStack,\r\n  Divider,\r\n  Avatar,\r\n} from \"@chakra-ui/react\";\r\nimport { useSelector } from \"react-redux\";\r\nimport { getUserDetailsForSingleRecipe } from \"../redux/authReducer/actions\";\r\nimport { getSingleRecipe } from \"../redux/recipeReducer/actions\";\r\nimport axios from \"axios\";\r\n\r\nfunction SingleRecipe() {\r\n  const { postId } = useParams();\r\n  const [owner, setOwner] = useState({});\r\n  const [recipe, setRecipe] = useState({});\r\n\r\n  const token =\r\n    useSelector((store) => store.authReducer.token) ||\r\n    localStorage.getItem(\"token\");\r\n\r\n  useEffect(() => {\r\n    const config = {\r\n      headers: {\r\n        Authorization: `Bearer ${token}`,\r\n      },\r\n    };\r\n\r\n    axios\r\n      .get(\r\n        `${process.env.REACT_APP_API_URL}/recipe/getSingleRecipe/${postId}`,\r\n        config\r\n      )\r\n      .then((res) => {\r\n        // console.log(res.data)\r\n        setRecipe(res?.data);\r\n        setOwner(res.data.userId);\r\n        return res.data;\r\n      })\r\n      .catch((err) => {\r\n        console.log(err);\r\n      });\r\n  }, []);\r\n\r\n  console.log(\"Recipe\", recipe);\r\n  console.log(\"Owner\", owner);\r\n\r\n  if (!recipe.title) {\r\n    return <h1>Loading..</h1>;\r\n  }\r\n\r\n  return (\r\n    <>\r\n      <DIV>\r\n        <Flex gap=\"1rem\" justifyContent=\"space-between\" mb=\"2rem\">\r\n          <Box width={\"55%\"}>\r\n            <Carousel height=\"100%\" images={recipe?.images} />\r\n            <Divider mt=\"2rem\" />\r\n            <Box>\r\n              <Heading textTransform=\"uppercase\" size=\"lg\" my=\"2rem\">\r\n                Ingredients\r\n              </Heading>\r\n              <VStack textAlign=\"left\" align=\"start\">\r\n                <List\r\n                  w=\"100%\"\r\n                  display=\"grid\"\r\n                  gridTemplateColumns=\"repeat(auto-fill, minmax(200px, 1fr))\"\r\n                  gap={4}\r\n                >\r\n                  {recipe?.ingredients.length > 0 &&\r\n                    recipe.ingredients.map((ele, ind) => (\r\n                      <ListItem key={ind} display=\"flex\" alignItems=\"center\">\r\n                        <Box\r\n                          as={CheckIcon}\r\n                          w={5}\r\n                          h={5}\r\n                          color=\"green.500\"\r\n                          mr={2}\r\n                        />\r\n                        <Text fontSize=\"xs\">{ele}</Text>\r\n                      </ListItem>\r\n                    ))}\r\n                </List>\r\n              </VStack>\r\n            </Box>\r\n          </Box>\r\n          <Box\r\n            width={\"45%\"}\r\n            p={\"1rem\"}\r\n            display=\"flex\"\r\n            gap=\"1rem\"\r\n            flexDirection={\"column\"}\r\n          >\r\n            {/* User details image, name, city */}\r\n            <Flex\r\n              size=\"2xl\"\r\n              gap={\"1rem\"}\r\n              alignItems={\"center\"}\r\n              justifyContent={\"flex-start\"}\r\n            >\r\n              <Avatar\r\n                size=\"2xl\"\r\n                src={`${process.env.REACT_APP_API_URL}/${owner.profileImage}`}\r\n              />\r\n              <Box>\r\n                <Text\r\n                  fontSize={\"xl\"}\r\n                  fontWeight={\"bolder\"}\r\n                  textTransform={\"uppercase\"}\r\n                >\r\n                  {owner?.name}\r\n                </Text>\r\n                <Flex alignItems={\"flex-end\"}>\r\n                  <svg\r\n                    width=\"40\"\r\n                    height=\"40\"\r\n                    viewBox=\"0 0 24 24\"\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                  >\r\n                    <path\r\n                      fill=\"#fb8500\"\r\n                      d=\"M12 11.5A2.5 2.5 0 0 1 9.5 9A2.5 2.5 0 0 1 12 6.5A2.5 2.5 0 0 1 14.5 9a2.5 2.5 0 0 1-2.5 2.5M12 2a7 7 0 0 0-7 7c0 5.25 7 13 7 13s7-7.75 7-13a7 7 0 0 0-7-7Z\"\r\n                    />\r\n                  </svg>\r\n                  <Text fontSize={\"lg\"} textTransform={\"uppercase\"}>\r\n                    {owner?.city}\r\n                  </Text>\r\n                </Flex>\r\n              </Box>\r\n            </Flex>\r\n\r\n            {/* Recipe Information */}\r\n            <Flex flexDir={\"column\"} mt={5} gap={3}>\r\n              <Flex gap={\"1rem\"} alignItems={\"center\"}>\r\n                <Heading textTransform={\"uppercase\"} fontSize={\"2xl\"}>\r\n                  {recipe?.title}\r\n                </Heading>\r\n                <svg\r\n                  width=\"30\"\r\n                  height=\"30\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                >\r\n                  <path\r\n                    fill={recipe?.veg ? \"#10b981\" : \"#ea580c\"}\r\n                    d=\"M20 4v16H4V4h16m2-2H2v20h20V2M12 6c-3.31 0-6 2.69-6 6s2.69 6 6 6s6-2.69 6-6s-2.69-6-6-6Z\"\r\n                  />\r\n                </svg>\r\n              </Flex>\r\n              <Flex alighItems=\"center\" gap={3}>\r\n                <svg\r\n                  width=\"33\"\r\n                  height=\"33\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                >\r\n                  <path\r\n                    fill=\"#000000\"\r\n                    d=\"m20 15l2-2v5a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h13l-2 2H4v12h16v-3zm2.44-8.56l-.88-.88a1.5 1.5 0 0 0-2.12 0L12 13v2H6v2h9v-1l7.44-7.44a1.5 1.5 0 0 0 0-2.12z\"\r\n                  />\r\n                </svg>\r\n                <Text>{recipe?.description}</Text>\r\n              </Flex>\r\n              <Flex alighItems=\"center\" gap={3}>\r\n                <svg\r\n                  width=\"23\"\r\n                  height=\"23\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                >\r\n                  <path\r\n                    fill=\"#000000\"\r\n                    d=\"M21 15c0-4.625-3.507-8.441-8-8.941V4h-2v2.059c-4.493.5-8 4.316-8 8.941v2h18v-2zM2 18h20v2H2z\"\r\n                  />\r\n                </svg>{\" \"}\r\n                <Text>{recipe.cuisine[0]}</Text>\r\n              </Flex>\r\n            </Flex>\r\n            <Flex gap={4} my={3}>\r\n              {recipe?.tags?.length > 0 &&\r\n                recipe?.tags?.map((ele, index) => (\r\n                  <Tag key={index} size=\"xl\" p=\"1rem\">\r\n                    {ele}\r\n                  </Tag>\r\n                ))}\r\n            </Flex>\r\n            <Divider />\r\n            <Flex height=\"max-content\" flexGrow={1} direction={\"column\"}>\r\n              <Heading textTransform={\"uppercase\"} mb=\"2rem\">\r\n                Instructions\r\n              </Heading>\r\n              <Stepper orientation=\"vertical\" h=\"100%\">\r\n                {recipe?.instructions.map((step, index) => (\r\n                  <Step key={index}>\r\n                    <StepIndicator>\r\n                      <StepStatus\r\n                        complete={<StepIcon />}\r\n                        incomplete={<StepNumber />}\r\n                        active={<StepNumber />}\r\n                      />\r\n                    </StepIndicator>\r\n                    <StepSeparator />\r\n                    <Box>\r\n                      <StepTitle>{step}</StepTitle>\r\n                    </Box>\r\n                  </Step>\r\n                ))}\r\n              </Stepper>\r\n            </Flex>\r\n          </Box>\r\n        </Flex>\r\n      </DIV>\r\n      {/* Recipe Ingredients */}\r\n    </>\r\n  );\r\n}\r\n\r\nexport default SingleRecipe;\r\n\r\nconst DIV = styled.div`\r\n  width: 90%;\r\n  margin: 5rem auto 10rem auto;\r\n  @media screen and (max-width: 768px) {\r\n    /* flex-direction: column; */\r\n  }\r\n`;\r\n", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\redux\\userReducer\\actionTypes.js", [], [], "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\pages\\SingleUser.jsx", ["318", "319", "320", "321", "322", "323", "324", "325", "326", "327", "328", "329", "330", "331", "332", "333", "334", "335"], [], "import {\r\n    Box,\r\n    Button,\r\n    Center,\r\n    Container,\r\n    Flex,\r\n    Image,\r\n    Text,\r\n    UnorderedList,\r\n    ListItem,\r\n    Grid,\r\n    HStack,\r\n    Tabs,\r\n    TabList,\r\n    Tab,\r\n    TabPanel,\r\n    TabPanels,\r\n    Tooltip,\r\n    useDisclosure,\r\n    Modal,\r\n    ModalOverlay,\r\n    ModalContent,\r\n    ModalHeader,\r\n    ModalFooter,\r\n    ModalBody,\r\n    ModalCloseButton,\r\n    Editable,\r\n    EditablePreview,\r\n    useEditableControls,\r\n    ButtonGroup,\r\n    IconButton,\r\n    EditableInput,\r\n    Textarea,\r\n    Heading,\r\n    Divider,\r\n  } from \"@chakra-ui/react\";\r\n  import { CheckIcon, CloseIcon, EditIcon } from \"@chakra-ui/icons\";\r\n  import React, { useEffect, useState } from \"react\";\r\n  import { useNavigate, useParams } from \"react-router-dom\";\r\n  import axios from \"axios\";\r\n  \r\n  export const SingleUser = () => {\r\n    const { userId } = useParams();\r\n    const navigate = useNavigate();\r\n    const [recipes, setRecipes] = useState([]);\r\n    const [user, setUser] = useState({})\r\n  \r\n    // Function to edit profile\r\nconsole.log(user)\r\n\r\n  \r\n    useEffect(() => {\r\n      const config = {\r\n        headers: {\r\n          Authorization: `Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI2NTQyNjk4N2FlODJkZDg4M2VmZGNmMTAiLCJpYXQiOjE2OTkwMDU5NzF9.2BbpIwnPrvYyP2BY48EDBEVgdq8WaebKYtaXZ0KHgh0`,\r\n        },\r\n      };\r\n  \r\n      axios\r\n        .get(`${process.env.REACT_APP_API_URL}/users/getAllUsers/admin`, config)\r\n        .then((res) => {\r\n          let user = res.data.filter((id) => {\r\n            return id._id === userId\r\n          })\r\n          console.log(user[0])\r\n          setUser(user[0])\r\n          setRecipes(user[0]?.recipes)\r\n        })\r\n        .catch((err) => {\r\n          console.log(err);\r\n        });\r\n    }, []);\r\n\r\n    if(user.name == undefined) {\r\n      return <h1>Loading...</h1>\r\n    }\r\n  \r\n    return (\r\n      <Container\r\n        bgColor={\"#EEF2F7\"}\r\n        maxW=\"full\"\r\n        height={\"100vh\"}\r\n        p={0}\r\n        paddingBlock={\"3rem\"}\r\n      >\r\n        <Flex flexDir={{ base: \"column\" }} bg=\"gray.100\" p={4}>\r\n          {/* User detail section */}\r\n          <Box\r\n            w={\"min(80rem,100%)\"}\r\n            mx={\"auto\"}\r\n            display={\"flex\"}\r\n            justifyContent={\"space-between\"}\r\n            gap=\"1rem\"\r\n           \r\n          >\r\n            {/* User Profile Info */}\r\n            <Box\r\n              p={2}\r\n              border=\"1px solid\"\r\n              borderColor={\"accent\"}\r\n              borderRadius={\"50%\"}\r\n              overflow=\"hidden\"\r\n              w={{md:'25%',base:\"100%\"}}\r\n            >\r\n              <Image\r\n                aspectRatio={1}\r\n                w={{base:\"100%\"}}\r\n                maxH=\"20rem\"\r\n                borderRadius=\"50%\"\r\n                objectFit=\"cover\"\r\n                src={`${process.env.REACT_APP_API_URL}/${user?.profileImage}`}\r\n                alt=\"Profile picture\"\r\n              />\r\n            </Box>\r\n            <Center>\r\n              <Divider\r\n                orientation=\"vertical\"\r\n                borderColor=\"secondary\"\r\n                opacity={0.3}\r\n              />\r\n            </Center>\r\n            <Box\r\n              display=\"flex\"\r\n              flexDir={{ base: \"column\" }}\r\n              alignItems={\"stretch\"}\r\n              justifyContent={\"space-around\"}\r\n            >\r\n              <Flex alignItems=\"center\">\r\n                <Text\r\n                  fontWeight=\"700\"\r\n                  fontSize=\"lg\"\r\n                  textTransform={\"uppercase\"}\r\n                  mr=\"1rem\"\r\n                >\r\n                  {user?.name}\r\n                </Text>\r\n                <Text>{user?.city}</Text>\r\n              </Flex>\r\n  \r\n              <Text p={0} m={0}>\r\n                {user?.bio}\r\n              </Text>\r\n              <UnorderedList\r\n                listStyleType=\"none\"\r\n                display=\"flex\"\r\n                justifyContent=\"space-around\"\r\n                m=\"0\"\r\n                textAlign={\"center\"}\r\n              >\r\n                <ListItem>\r\n                  <Text fontWeight=\"bold\" textTransform=\"uppercase\">\r\n                    Posts\r\n                  </Text>\r\n                  <Text>{user?.recipes.length}</Text>\r\n                </ListItem>\r\n              </UnorderedList>\r\n            </Box>\r\n          </Box>\r\n  \r\n          {/* User Posts and others */}\r\n          <Box w={\"min(80rem,100%)\"} m={\"auto\"} mt=\"1rem\">\r\n            {/* Grid View of Images */}\r\n            <Tabs colorScheme=\"primary\" isFitted>\r\n              <TabList>\r\n                <Tab>All Posts</Tab>\r\n              </TabList>\r\n              <TabPanels>\r\n                <TabPanel>\r\n                  <Grid templateColumns=\"repeat(3, 1fr)\" gap={2}>\r\n                    {recipes?.length > 0 &&\r\n                      recipes.map((ele, index) => (\r\n                        <Tooltip\r\n                          bg=\"accent\"\r\n                          px={4}\r\n                          py={2}\r\n                          label={`Likes: ${ele?.likes?.length}, Comments: ${ele?.comments?.length}`}\r\n                          key={index}\r\n                        >\r\n                          <div>\r\n                            <Image\r\n                              src={`${process.env.REACT_APP_API_URL}/${ele.images[0]}`}\r\n                              alt=\"Recipe Image\"\r\n                              boxSize=\"100%\"\r\n                              objectFit=\"cover\"\r\n                              onClick={() => navigate(`/recipe/${ele._id}`)}\r\n                            />\r\n                          </div>\r\n                        </Tooltip>\r\n                      ))}\r\n                  </Grid>\r\n                </TabPanel>\r\n              </TabPanels>\r\n            </Tabs>\r\n          </Box>\r\n        </Flex>\r\n      </Container>\r\n    );\r\n  };\r\n  \r\n  function EditableControls() {\r\n    const {\r\n      isEditing,\r\n      getSubmitButtonProps,\r\n      getCancelButtonProps,\r\n      getEditButtonProps,\r\n    } = useEditableControls();\r\n  \r\n    return isEditing ? (\r\n      <ButtonGroup justifyContent=\"center\" size=\"sm\">\r\n        <IconButton icon={<CheckIcon />} {...getSubmitButtonProps()} />\r\n        <IconButton icon={<CloseIcon />} {...getCancelButtonProps()} />\r\n      </ButtonGroup>\r\n    ) : (\r\n      <Flex justifyContent=\"center\">\r\n        <IconButton size=\"sm\" icon={<EditIcon />} {...getEditButtonProps()} />\r\n      </Flex>\r\n    );\r\n  }\r\n  ", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\pages\\Admin.jsx", ["336", "337", "338", "339", "340", "341", "342"], [], "import {\r\n  AlertDialog,\r\n  AlertDialogBody,\r\n  AlertDialogContent,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogOverlay,\r\n  Avatar,\r\n  Box,\r\n  Button,\r\n  ButtonGroup,\r\n  Card,\r\n  CardBody,\r\n  CardFooter,\r\n  CardHeader,\r\n  Center,\r\n  Divider,\r\n  Flex,\r\n  HStack,\r\n  Heading,\r\n  Image,\r\n  Stack,\r\n  Tag,\r\n  Text,\r\n  useDisclosure,\r\n} from \"@chakra-ui/react\";\r\nimport {\r\n  Accordion,\r\n  AccordionItem,\r\n  AccordionButton,\r\n  AccordionPanel,\r\n  AccordionIcon,\r\n} from \"@chakra-ui/react\";\r\nimport axios from \"axios\";\r\nimport React, { useEffect, useReducer, useState } from \"react\";\r\nimport { useSelector } from \"react-redux\";\r\nimport { styled } from \"styled-components\";\r\nimport { Carousel } from \"../components/Feed/SingleRecipeCarousel\";\r\nimport { BiShare } from \"react-icons/bi\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport Contribution from \"../components/Charts/Contribution\";\r\nimport MostLikes from \"../components/Charts/MostLikes\";\r\nimport VegNonVegChart from \"../components/Charts/VegNonVeg\";\r\nimport Cuisines from \"../components/Charts/Cusines\";\r\nimport { Reveal } from \"../components/common/Reveal\";\r\n\r\nconst initialData = {\r\n  tabOne: true,\r\n  tabTwo: false,\r\n  tabThree: false,\r\n  tabFour: false,\r\n};\r\n\r\nconst reducer = (state, action) => {\r\n  switch (action.type) {\r\n    case \"tabOne\":\r\n      return {\r\n        ...state,\r\n        tabOne: true,\r\n        tabTwo: false,\r\n        tabThree: false,\r\n        tabFour: false,\r\n      };\r\n    case \"tabTwo\":\r\n      return {\r\n        ...state,\r\n        tabOne: false,\r\n        tabTwo: true,\r\n        tabThree: false,\r\n        tabFour: false,\r\n      };\r\n    case \"tabThree\":\r\n      return {\r\n        ...state,\r\n        tabOne: false,\r\n        tabTwo: false,\r\n        tabThree: true,\r\n        tabFour: false,\r\n      };\r\n    case \"tabFour\":\r\n      return {\r\n        ...state,\r\n        tabOne: false,\r\n        tabTwo: false,\r\n        tabThree: false,\r\n        tabFour: true,\r\n      };\r\n    default:\r\n      return state;\r\n  }\r\n};\r\n\r\nconst Admin = () => {\r\n  // const [loading, setLoading] = useState(false)\r\n  const [user, setUser] = useState([]);\r\n  const [recipe, setRecipe] = useState([]);\r\n  const [state, dispatch] = useReducer(reducer, initialData);\r\n  const hanldeTab = (tab) => {\r\n    dispatch({ type: tab });\r\n  };\r\n  const { isOpen, onOpen, onClose } = useDisclosure();\r\n  const cancelRef = React.useRef();\r\n  const token =\r\n    useSelector((store) => store.authReducer.token) ||\r\n    localStorage.getItem(\"token\");\r\n  const navigate = useNavigate();\r\n\r\n  console.log(recipe);\r\n\r\n  const [nav, setNav] = useState(\"expanded\");\r\n\r\n  const handleNavbarToggle = () => {\r\n    console.log(nav);\r\n    setNav((prevState) =>\r\n      prevState === \"expanded\" ? \"minimized\" : \"expanded\"\r\n    );\r\n  };\r\n\r\n  const handleDeleteRecipe = (id) => {\r\n    console.log(id);\r\n  };\r\n\r\n  // Get all users\r\n  useEffect(() => {\r\n    // setLoading(true)\r\n    const config = {\r\n      headers: {\r\n        Authorization: `Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI2NTQyNjk4N2FlODJkZDg4M2VmZGNmMTAiLCJpYXQiOjE2OTkwMDU5NzF9.2BbpIwnPrvYyP2BY48EDBEVgdq8WaebKYtaXZ0KHgh0`,\r\n      },\r\n    };\r\n\r\n    axios\r\n      .get(`${process.env.REACT_APP_API_URL}/users/getAllUsers/admin`, config)\r\n      .then((res) => {\r\n        setUser(res.data);\r\n        // setLoading(false)\r\n      })\r\n      .catch((err) => {\r\n        console.log(err);\r\n      });\r\n  }, []);\r\n\r\n  // Get all recipes\r\n  useEffect(() => {\r\n    const config = {\r\n      headers: {\r\n        Authorization: `Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI2NTQyNjk4N2FlODJkZDg4M2VmZGNmMTAiLCJpYXQiOjE2OTkwMDU5NzF9.2BbpIwnPrvYyP2BY48EDBEVgdq8WaebKYtaXZ0KHgh0`,\r\n      },\r\n    };\r\n\r\n    axios\r\n      .get(`${process.env.REACT_APP_API_URL}/recipe/getAllRecipe`, config)\r\n      .then((res) => {\r\n        setRecipe(res.data);\r\n      })\r\n      .catch((err) => {\r\n        console.log(err);\r\n      });\r\n  }, []);\r\n\r\n\r\n\r\n  return (\r\n    <>\r\n      <SidebarContainer\r\n        style={{ marginLeft: nav === \"expanded\" ? \"0px\" : \"-180px\" }}\r\n      >\r\n        <SidebarNav>\r\n          <SidebarNavItem>\r\n            <NavbarToggle id=\"navbar-toggle\">\r\n              <Text\r\n                fontFamily={\"Kaushan Script\"}\r\n                to=\"/\"\r\n                fontSize=\"2xl\"\r\n                fontWeight=\"bold\"\r\n                mr=\"1rem\"\r\n              >\r\n                Recipe\r\n                <Text display=\"inline\" color=\"primary.500\">\r\n                  Hub\r\n                </Text>\r\n              </Text>\r\n            </NavbarToggle>\r\n            <Hamburger navbar={handleNavbarToggle} />\r\n          </SidebarNavItem>\r\n          <SidebarNavItem onClick={() => hanldeTab(\"tabOne\")}>\r\n            <a>Dashboard </a>\r\n            <svg\r\n              width=\"30\"\r\n              height=\"30\"\r\n              viewBox=\"0 0 24 24\"\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n            >\r\n              <path\r\n                fill=\"#fff\"\r\n                d=\"M13 9V3h8v6h-8ZM3 13V3h8v10H3Zm10 8V11h8v10h-8ZM3 21v-6h8v6H3Z\"\r\n              />\r\n            </svg>\r\n          </SidebarNavItem>\r\n          <SidebarNavItem onClick={() => hanldeTab(\"tabTwo\")}>\r\n            <a className=\"\">All Users </a>\r\n            <svg\r\n              width=\"30\"\r\n              height=\"30\"\r\n              viewBox=\"0 0 15 15\"\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n            >\r\n              <path\r\n                fill=\"#fff\"\r\n                d=\"M5.5 0a3.499 3.499 0 1 0 0 6.996A3.499 3.499 0 1 0 5.5 0Zm-2 8.994a3.5 3.5 0 0 0-3.5 3.5v2.497h11v-2.497a3.5 3.5 0 0 0-3.5-3.5h-4Zm9 1.006H12v5h3v-2.5a2.5 2.5 0 0 0-2.5-2.5Z\"\r\n              />\r\n              <path\r\n                fill=\"#fff\"\r\n                d=\"M11.5 4a2.5 2.5 0 1 0 0 5a2.5 2.5 0 0 0 0-5Z\"\r\n              />\r\n            </svg>\r\n          </SidebarNavItem>\r\n          <SidebarNavItem onClick={() => hanldeTab(\"tabThree\")}>\r\n            <a>All Recipes </a>\r\n            <svg\r\n              width=\"30\"\r\n              height=\"30\"\r\n              viewBox=\"0 0 16 16\"\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n            >\r\n              <path\r\n                fill=\"#fff\"\r\n                d=\"M4 1.5a.5.5 0 0 0-1 0v3a2.5 2.5 0 0 0 2 2.45v7.55a.5.5 0 0 0 1 0V6.95A2.5 2.5 0 0 0 8 4.5v-3a.5.5 0 0 0-1 0v3a1.5 1.5 0 0 1-1 1.415V1.5a.5.5 0 0 0-1 0v4.415A1.5 1.5 0 0 1 4 4.5v-3Zm7 13V8H9.5a.5.5 0 0 1-.5-.5v-4c0-.663.326-1.283.771-1.729C10.217 1.326 10.837 1 11.5 1a.5.5 0 0 1 .5.5v13a.5.5 0 0 1-1 0Z\"\r\n              />\r\n            </svg>\r\n          </SidebarNavItem>\r\n          <SidebarNavItem onClick={() => navigate(\"/\")}>\r\n            <a>Log out </a>\r\n            <svg\r\n              width=\"30\"\r\n              height=\"30\"\r\n              viewBox=\"0 0 24 24\"\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n            >\r\n              <path\r\n                fill=\"#fff\"\r\n                d=\"M5 21q-.825 0-1.413-.588T3 19V5q0-.825.588-1.413T5 3h7v2H5v14h7v2H5Zm11-4l-1.375-1.45l2.55-2.55H9v-2h8.175l-2.55-2.55L16 7l5 5l-5 5Z\"\r\n              />\r\n            </svg>\r\n          </SidebarNavItem>\r\n        </SidebarNav>\r\n      </SidebarContainer>\r\n      <ADMIN>\r\n        <div className=\"tabs\">\r\n          <div className=\"tab-content\">\r\n            {/* Dashboard */}\r\n            <div\r\n              style={{ width: \"100%\" }}\r\n              className={state.tabOne ? \"active\" : \"noShow\"}\r\n            >\r\n              <Center>\r\n                <Box p={7} textAlign=\"center\">\r\n                  <Box\r\n                    maxW=\"xl\"\r\n                    mx=\"auto\"\r\n                    p={10}\r\n                    borderWidth=\"1px\"\r\n                    borderRadius=\"lg\"\r\n                    boxShadow=\"md\"\r\n                  >\r\n                    <Heading as=\"h1\" size=\"lg\">\r\n                      Welcome to Dashboard\r\n                    </Heading>\r\n                    <Text mb={0}>Hello Admin, welcome to your dashboard!</Text>\r\n                  </Box>\r\n                </Box>\r\n              </Center>\r\n              <Flex flexWrap=\"wrap\" justifyContent=\"space-between\">\r\n                <Box\r\n                  flex=\"1\"\r\n                  maxW=\"lg\"\r\n                  bg=\"red.500\"\r\n                  p=\"3\"\r\n                  m=\"4\"\r\n                  borderRadius=\"md\"\r\n                  textAlign=\"center\"\r\n                >\r\n                  <svg\r\n                    width=\"30\"\r\n                    height=\"30\"\r\n                    viewBox=\"0 0 24 24\"\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                  >\r\n                    <path\r\n                      fill=\"#ffffff\"\r\n                      fill-rule=\"evenodd\"\r\n                      d=\"M8 7a4 4 0 1 1 8 0a4 4 0 0 1-8 0Zm0 6a5 5 0 0 0-5 5a3 3 0 0 0 3 3h12a3 3 0 0 0 3-3a5 5 0 0 0-5-5H8Z\"\r\n                      clip-rule=\"evenodd\"\r\n                    />\r\n                  </svg>\r\n                  <Text fontSize=\"2xl\" fontWeight=\"bold\" color=\"white\">\r\n                    {user?.length}\r\n                  </Text>\r\n                  <Text fontSize=\"lg\" color=\"white\">\r\n                    User registered\r\n                  </Text>\r\n                </Box>\r\n                <Box\r\n                  flex=\"1\"\r\n                  maxW=\"lg\"\r\n                  bg=\"yellow.500\"\r\n                  p=\"3\"\r\n                  m=\"4\"\r\n                  borderRadius=\"md\"\r\n                  textAlign=\"center\"\r\n                >\r\n                  <svg\r\n                    width=\"30\"\r\n                    height=\"30\"\r\n                    viewBox=\"0 0 512 512\"\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                  >\r\n                    <path\r\n                      fill=\"#ffffff\"\r\n                      d=\"M0 192c0-35.3 28.7-64 64-64h1.6C73 91.5 105.3 64 144 64c15 0 29 4.1 40.9 11.2C198.2 49.6 225.1 32 256 32s57.8 17.6 71.1 43.2C339 68.1 353 64 368 64c38.7 0 71 27.5 78.4 64h1.6c35.3 0 64 28.7 64 64c0 11.7-3.1 22.6-8.6 32H8.6C3.1 214.6 0 203.7 0 192zm0 91.4C0 268.3 12.3 256 27.4 256h457.2c15.1 0 27.4 12.3 27.4 27.4c0 70.5-44.4 130.7-106.7 154.1l-1.8 14.5c-2 16-15.6 28-31.8 28H140.2c-16.1 0-29.8-12-31.8-28l-1.8-14.4C44.4 414.1 0 353.9 0 283.4z\"\r\n                    />\r\n                  </svg>\r\n                  <Text fontSize=\"2xl\" fontWeight=\"bold\" color=\"white\">\r\n                    {recipe?.length}\r\n                  </Text>\r\n                  <Text fontSize=\"lg\" color=\"white\">\r\n                    Total Recipes\r\n                  </Text>\r\n                </Box>\r\n              </Flex>\r\n              <Box borderWidth=\"1px\" borderRadius=\"lg\" boxShadow=\"md\" m={4}>\r\n                <Contribution user={user} />\r\n              </Box>\r\n              <Box borderWidth=\"1px\" borderRadius=\"lg\" boxShadow=\"md\" m={4}>\r\n                <MostLikes recipe={recipe} />\r\n              </Box>\r\n              <Flex borderWidth=\"1px\" borderRadius=\"lg\" boxShadow=\"md\" m={4}>\r\n                <VegNonVegChart recipeData={recipe} />\r\n                <Cuisines recipes={recipe} />\r\n              </Flex>\r\n            </div>\r\n            {/* All Users */}\r\n            <div className={state.tabTwo ? \"active\" : \"noShow\"}>\r\n              {user?.length > 0 &&\r\n                user.map((ele, indx) => (\r\n                  <Card m={5} maxW=\"\" key={indx + Date.now()}>\r\n                    <CardHeader>\r\n                      <Flex my={0} gap={3}>\r\n                        <Avatar\r\n                          size=\"lg\"\r\n                          name={ele?.name}\r\n                          src={`${process.env.REACT_APP_API_URL}/${ele?.profileImage}`}\r\n                        />\r\n                        <Flex flexDir={\"column\"} textAlign={\"left\"}>\r\n                          <Heading size={\"md\"} fontWeight={\"bold\"}>\r\n                            {ele?.name}\r\n                          </Heading>\r\n                          <Text size={\"sm\"}>City: {ele?.city}</Text>\r\n                          <Text size={\"sm\"}>Email: {ele?.email}</Text>\r\n                        </Flex>\r\n                      </Flex>\r\n                    </CardHeader>\r\n                    <CardBody>\r\n                      <Stack textAlign={\"left\"} mt=\"1\" spacing=\"3\">\r\n                        <Heading size=\"md\">Bio: </Heading>\r\n                        <Text>{ele?.bio}</Text>\r\n                        <Accordion allowMultiple>\r\n                          <AccordionItem>\r\n                            <h2>\r\n                              <AccordionButton>\r\n                                <Box\r\n                                  as=\"span\"\r\n                                  fontWeight={\"bold\"}\r\n                                  flex=\"1\"\r\n                                  textAlign=\"left\"\r\n                                  p={\"5\"}\r\n                                >\r\n                                  <Heading size=\"md\">See friends:</Heading>\r\n                                </Box>\r\n                                <AccordionIcon />\r\n                              </AccordionButton>\r\n                            </h2>\r\n                            <AccordionPanel pb={4}>\r\n                              {ele?.friends.length === 0 ? (\r\n                                <Text>No friends!</Text>\r\n                              ) : (\r\n                                ele?.friends.map((e, index) => (\r\n                                  <Box mx={9} my={2} key={index + Date.now()}>\r\n                                    <Flex my={0} gap={3}>\r\n                                      <Avatar\r\n                                        size=\"lg\"\r\n                                        name={e?.name}\r\n                                        src={`${process.env.REACT_APP_API_URL}/${e?.profileImage}`}\r\n                                      />\r\n                                      <Flex\r\n                                        flexDir={\"column\"}\r\n                                        textAlign={\"left\"}\r\n                                      >\r\n                                        <Text fontWeight={\"bold\"}>\r\n                                          {e?.name}\r\n                                        </Text>\r\n                                        <Text>{e?.city}</Text>\r\n                                      </Flex>\r\n                                    </Flex>\r\n                                  </Box>\r\n                                ))\r\n                              )}\r\n                            </AccordionPanel>\r\n                          </AccordionItem>\r\n                        </Accordion>\r\n                        <Heading size=\"md\">Recipes posted by user: </Heading>\r\n                        <HStack overflowX={\"scroll\"}>\r\n                          {ele?.recipes?.length === 0 ? (\r\n                            <Text>No posts yet!</Text>\r\n                          ) : (\r\n                            ele?.recipes.map((e, index) => (\r\n                              <Box key={index + Date.now()}>\r\n                                <Image\r\n                                  height={\"200px\"}\r\n                                  width={\"200px\"}\r\n                                  src={`${process.env.REACT_APP_API_URL}/${e?.images[0]}`}\r\n                                />\r\n                                <Text fontWeight={\"bold\"}>{e?.title}</Text>\r\n                              </Box>\r\n                            ))\r\n                          )}\r\n                        </HStack>\r\n                      </Stack>\r\n                    </CardBody>\r\n                    <Divider />\r\n                    <CardFooter>\r\n                      <ButtonGroup spacing=\"2\">\r\n                        <Button\r\n                          variant=\"solid\"\r\n                          colorScheme=\"blue\"\r\n                          onClick={() => navigate(`/user/${ele._id}`)}\r\n                        >\r\n                          View User\r\n                        </Button>\r\n                        <Button variant=\"ghost\" colorScheme=\"red\">\r\n                          Delete User\r\n                        </Button>\r\n                      </ButtonGroup>\r\n                    </CardFooter>\r\n                  </Card>\r\n                ))}\r\n            </div>\r\n            {/* All Recipes */}\r\n            <div className={state.tabThree ? \"active\" : \"noShow\"}>\r\n              <DIV>\r\n                {recipe?.length > 0 &&\r\n                  recipe.map((ele, index) => (\r\n                    <Card key={index}>\r\n                      <Box borderWidth=\"0\" borderRadius=\"md\" overflow=\"hidden\">\r\n                        <CardHeader>\r\n                          <Flex my={3} gap={3}>\r\n                            <Avatar\r\n                              size=\"lg\"\r\n                              name={ele?.userId?.name}\r\n                              src={`${process.env.REACT_APP_API_URL}/${ele?.userId?.profileImage}`}\r\n                            />\r\n                            <Flex flexDir={\"column\"} textAlign={\"left\"}>\r\n                              <Text fontWeight={\"bold\"}>\r\n                                {ele?.userId?.name}\r\n                              </Text>\r\n                              <Text>{ele?.userId?.city}</Text>\r\n                            </Flex>\r\n                          </Flex>\r\n                          <Carousel height={\"300px\"} images={ele?.images} />\r\n                        </CardHeader>\r\n                        <Box p=\"1rem\">\r\n                          <Heading fontSize=\"2xl\" fontWeight=\"bold\">\r\n                            {ele.title}\r\n                          </Heading>\r\n                          <Tag my={3}>{ele?.cuisine[0]}</Tag>\r\n                          <Text fontSize=\"md\">{ele.description}</Text>\r\n                          <Flex mt={3} flexWrap=\"wrap\" gap={3}>\r\n                            {ele?.tags?.length > 0 &&\r\n                              ele?.tags.map((e, index) => (\r\n                                <Tag key={index}>{e}</Tag>\r\n                              ))}\r\n                          </Flex>\r\n                          <CardFooter\r\n                            px=\"0\"\r\n                            justify=\"flex-start\"\r\n                            gap=\"1rem\"\r\n                            flexWrap=\"wrap\"\r\n                            sx={{\r\n                              \"& > button\": {\r\n                                minW: \"136px\",\r\n                              },\r\n                            }}\r\n                          >\r\n                            <Button\r\n                              flex={{ base: \"1\", md: \"0.25\" }}\r\n                              variant=\"outline\"\r\n                              leftIcon={<BiShare />}\r\n                              onClick={() => navigate(`/recipe/${ele._id}`)}\r\n                            >\r\n                              View Recipe\r\n                            </Button>\r\n                            <Button\r\n                              colorScheme=\"red\"\r\n                              onClick={() => onOpen(ele._id)} // Pass the recipe ID to onOpen\r\n                            >\r\n                              Delete Recipe\r\n                            </Button>\r\n                            {/* Deleting Alert message */}\r\n                            <AlertDialog\r\n                              isOpen={isOpen}\r\n                              leastDestructiveRef={cancelRef}\r\n                              onClose={onClose}\r\n                            >\r\n                              <AlertDialogOverlay>\r\n                                <AlertDialogContent>\r\n                                  <AlertDialogHeader\r\n                                    fontSize=\"lg\"\r\n                                    fontWeight=\"bold\"\r\n                                  >\r\n                                    Delete Recipe\r\n                                  </AlertDialogHeader>\r\n\r\n                                  <AlertDialogBody>\r\n                                    Are you sure? You can't undo this action\r\n                                    afterwards.\r\n                                  </AlertDialogBody>\r\n\r\n                                  <AlertDialogFooter>\r\n                                    <Button ref={cancelRef} onClick={onClose}>\r\n                                      Cancel\r\n                                    </Button>\r\n                                    <Button\r\n                                      colorScheme=\"red\"\r\n                                      onClick={(id) => handleDeleteRecipe(id)}\r\n                                      ml={3}\r\n                                    >\r\n                                      Delete\r\n                                    </Button>\r\n                                  </AlertDialogFooter>\r\n                                </AlertDialogContent>\r\n                              </AlertDialogOverlay>\r\n                            </AlertDialog>\r\n                          </CardFooter>\r\n                        </Box>\r\n                      </Box>\r\n                    </Card>\r\n                  ))}\r\n              </DIV>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </ADMIN>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default Admin;\r\n\r\nconst DIV = styled.div`\r\n  display: grid;\r\n  grid-template-columns: repeat(3, 1fr);\r\n  gap: 20px;\r\n  margin: 30px;\r\n  @media (max-width: 768px) {\r\n    grid-template-columns: repeat(\r\n      1,\r\n      1fr\r\n    ); /* 1 column on screens up to 768px wide */\r\n  }\r\n\r\n  @media (min-width: 769px) and (max-width: 1024px) {\r\n    grid-template-columns: repeat(\r\n      2,\r\n      1fr\r\n    ); /* 2 columns on screens between 769px and 1024px wide */\r\n  }\r\n\r\n  @media (min-width: 1025px) {\r\n    grid-template-columns: repeat(\r\n      3,\r\n      1fr\r\n    ); /* 3 columns on screens wider than 1024px */\r\n  }\r\n`;\r\n\r\nconst ADMIN = styled.div`\r\n  @import url(\"https://fonts.googleapis.com/css2?family=Raleway:wght@400;800&display=swap\");\r\n\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  height: 100vh;\r\n  /* width: 100vw; */\r\n  padding: 0 10%;\r\n  font-family: \"Raleway\", sans-serif;\r\n  font-size: 20px;\r\n\r\n  .noShow {\r\n    display: none;\r\n  }\r\n\r\n  .tabs {\r\n    position: absolute;\r\n    top: 50%;\r\n    left: 50%;\r\n    transform: translate(-50%, -50%);\r\n    width: 100%;\r\n    height: 100%;\r\n    padding: 10px 30px 30px 17%;\r\n    background: #fff;\r\n    /* overflow: hidden; */\r\n  }\r\n  .tabs .tab-header {\r\n    float: left;\r\n    width: 180px;\r\n    height: 100%;\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 50px;\r\n    border-right: 1px solid #ccc;\r\n    padding: 50px 0px;\r\n  }\r\n`;\r\n\r\nconst SidebarContainer = styled.div`\r\n  color: #fff;\r\n  background: #2b2b2d;\r\n  width: 250px;\r\n  max-width: 250px;\r\n  height: 100%;\r\n  float: left;\r\n  position: fixed;\r\n  z-index: 1000;\r\n  display: block;\r\n  transition: margin 1s;\r\n  flex: 1;\r\n  top: 0;\r\n`;\r\n\r\nconst NavbarToggle = styled.a`\r\n  /* background-color: blue; */\r\n  cursor: pointer;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  /* padding: 0 20px; */\r\n  align-items: center;\r\n`;\r\n\r\nconst SidebarNav = styled.ul`\r\n  display: block;\r\n  float: left;\r\n  width: 100%;\r\n  list-style: none;\r\n  margin: 0;\r\n  padding: 0;\r\n`;\r\n\r\nconst SidebarNavItem = styled.li`\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  padding: 0 20px;\r\n  a {\r\n    /* padding-left: 20px; */\r\n    font-size: 16px;\r\n    text-decoration: none;\r\n    color: #fff;\r\n    float: left;\r\n    text-decoration: none;\r\n    width: 100%;\r\n    height: 70px;\r\n    line-height: 25px;\r\n    padding: 20px;\r\n\r\n    /* vertical-align: center; */\r\n  }\r\n  &:hover {\r\n    background: #121213;\r\n    transition: background 0.5s;\r\n  }\r\n`;\r\n\r\nconst Hamburger = ({ navbar }) => {\r\n  return (\r\n    <HAMBURGER>\r\n      <input onChange={navbar} id=\"checkbox\" type=\"checkbox\" />\r\n      <label class=\"toggle\" for=\"checkbox\">\r\n        <div id=\"bar1\" class=\"bars\"></div>\r\n        <div id=\"bar2\" class=\"bars\"></div>\r\n        <div id=\"bar3\" class=\"bars\"></div>\r\n      </label>\r\n    </HAMBURGER>\r\n  );\r\n};\r\n\r\nconst HAMBURGER = styled.div`\r\n  #checkbox {\r\n    display: none;\r\n  }\r\n\r\n  .toggle {\r\n    position: relative;\r\n    width: 30px;\r\n    height: 30px;\r\n    cursor: pointer;\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    gap: 5px;\r\n    transition-duration: 0.3s;\r\n  }\r\n\r\n  .bars {\r\n    width: 90%;\r\n    height: 4px;\r\n    background-color: rgb(253, 255, 243);\r\n    border-radius: 5px;\r\n    transition-duration: 0.3s;\r\n  }\r\n\r\n  /* #checkbox:checked + .toggle .bars {\r\n  margin-left: 13px;\r\n} */\r\n\r\n  #checkbox:checked + .toggle #bar2 {\r\n    transform: translateY(14px) rotate(60deg);\r\n    margin-left: 0;\r\n    transform-origin: right;\r\n    transition-duration: 0.3s;\r\n    z-index: 2;\r\n  }\r\n\r\n  #checkbox:checked + .toggle {\r\n    gap: 10px;\r\n  }\r\n\r\n  #checkbox:checked + .toggle #bar1 {\r\n    transform: translateY(28px) rotate(-60deg);\r\n    transition-duration: 0.3s;\r\n    transform-origin: left;\r\n    z-index: 1;\r\n  }\r\n\r\n  #checkbox:checked + .toggle {\r\n    transform: rotate(-90deg);\r\n  }\r\n  /* #checkbox:checked + .toggle #bar3 {\r\n  transform: rotate(90deg);\r\n  transition-duration: .3s;\r\n  transform-origin:right;\r\n} */\r\n`;\r\n\r\n\r\nconst LOADER = styled.div`\r\n  position: fixed;\r\n  top: 50%;\r\n  left : 50%;\r\n  transform: translate(-50%, -50%);\r\n  border: 4px solid rgba(0, 0, 0, 1);\r\n  border-left-color: transparent;\r\n  border-radius: 50%;\r\n  margin-bottom : 50px;\r\n\r\n\r\n\r\n  border: 4px solid rgba(0, 0, 0, 1);\r\n  border-left-color: transparent;\r\n  width: 36px;\r\n  height: 36px;\r\n\r\n\r\n\r\n  border: 4px solid rgba(0, 0, 0, 1);\r\n  border-left-color: transparent;\r\n  width: 36px;\r\n  height: 36px;\r\n  animation: spin89345 1s linear infinite;\r\n\r\n\r\n@keyframes spin89345 {\r\n  0% {\r\n    transform: rotate(0deg);\r\n  }\r\n\r\n  100% {\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n`", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\pages\\AdminNew.jsx", ["343", "344", "345", "346", "347", "348", "349", "350", "351", "352", "353", "354", "355", "356"], [], "import {\r\n  Avatar,\r\n  Box,\r\n  Flex,\r\n  Icon,\r\n  Text,\r\n  Stack,\r\n  Image,\r\n  Button,\r\n  Heading,\r\n  AlertDialog,\r\n  AlertDialogBody,\r\n  AlertDialogContent,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogOverlay,\r\n  ButtonGroup,\r\n  Card,\r\n  CardBody,\r\n  CardFooter,\r\n  CardHeader,\r\n  Center,\r\n  Divider,\r\n  HStack,\r\n  Tag,\r\n  Drawer,\r\n  DrawerContent,\r\n  IconButton,\r\n  useDisclosure,\r\n  DrawerOverlay,\r\n  useColorModeValue,\r\n  Grid,\r\n} from \"@chakra-ui/react\";\r\nimport {\r\n  Accordion,\r\n  AccordionItem,\r\n  AccordionButton,\r\n  AccordionPanel,\r\n  AccordionIcon,\r\n} from \"@chakra-ui/react\";\r\n// Here we have used react-icons package for the icons\r\nimport { FaBell } from \"react-icons/fa\";\r\nimport { AiOutlineTeam, AiOutlineHome } from \"react-icons/ai\";\r\nimport { BsFolder2, BsCalendarCheck } from \"react-icons/bs\";\r\nimport { FiMenu } from \"react-icons/fi\";\r\nimport { RiFlashlightFill } from \"react-icons/ri\";\r\nimport { Link, useNavigate } from \"react-router-dom\";\r\nimport { styled } from \"styled-components\";\r\nimport { useEffect, useReducer, useRef, useState } from \"react\";\r\nimport { useSelector } from \"react-redux\";\r\nimport axios from \"axios\";\r\nimport Contribution from \"../components/Charts/Contribution\";\r\nimport MostLikes from \"../components/Charts/MostLikes\";\r\nimport VegNonVegChart from \"../components/Charts/VegNonVeg\";\r\nimport Cuisines from \"../components/Charts/Cusines\";\r\nimport { Carousel } from \"../components/Feed/SingleRecipeCarousel\";\r\nimport { BiShare } from \"react-icons/bi\";\r\nimport { GiExitDoor } from \"react-icons/gi\";\r\nimport { IoFastFoodOutline } from \"react-icons/io5\";\r\n\r\nexport default function AdminNew() {\r\n  const { isOpen, onClose, onOpen } = useDisclosure();\r\n  const [user, setUser] = useState([]);\r\n  const [recipe, setRecipe] = useState([]);\r\n  const [section, setSection] = useState(1);\r\n  const navigate = useNavigate();\r\n\r\n  // Get all users\r\n  useEffect(() => {\r\n    // setLoading(true)\r\n    const config = {\r\n      headers: {\r\n        Authorization: `Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI2NTQyNjk4N2FlODJkZDg4M2VmZGNmMTAiLCJpYXQiOjE2OTkwMDU5NzF9.2BbpIwnPrvYyP2BY48EDBEVgdq8WaebKYtaXZ0KHgh0`,\r\n      },\r\n    };\r\n\r\n    axios\r\n      .get(`${process.env.REACT_APP_API_URL}/users/getAllUsers/admin`, config)\r\n      .then((res) => {\r\n        setUser(res.data);\r\n        // setLoading(false)\r\n      })\r\n      .catch((err) => {\r\n        console.log(err);\r\n      });\r\n  }, []);\r\n\r\n  // Get all recipes\r\n  useEffect(() => {\r\n    const config = {\r\n      headers: {\r\n        Authorization: `Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI2NTQyNjk4N2FlODJkZDg4M2VmZGNmMTAiLCJpYXQiOjE2OTkwMDU5NzF9.2BbpIwnPrvYyP2BY48EDBEVgdq8WaebKYtaXZ0KHgh0`,\r\n      },\r\n    };\r\n\r\n    axios\r\n      .get(`${process.env.REACT_APP_API_URL}/recipe/getAllRecipe`, config)\r\n      .then((res) => {\r\n        setRecipe(res.data);\r\n      })\r\n      .catch((err) => {\r\n        console.log(err);\r\n      });\r\n  }, []);\r\n\r\n  return (\r\n    <Box\r\n      as=\"section\"\r\n      bg={useColorModeValue(\"gray.50\", \"gray.700\")}\r\n      minH=\"100vh\"\r\n    >\r\n      <Box\r\n        display={{ base: \"none\", md: \"unset\" }}\r\n        as=\"nav\"\r\n        pos=\"fixed\"\r\n        top=\"0\"\r\n        left=\"0\"\r\n        zIndex=\"sticky\"\r\n        h=\"full\"\r\n        pb=\"10\"\r\n        overflowX=\"hidden\"\r\n        overflowY=\"auto\"\r\n        bg={useColorModeValue(\"white\", \"gray.800\")}\r\n        borderColor={useColorModeValue(\"inherit\", \"gray.700\")}\r\n        borderRightWidth=\"1px\"\r\n        w=\"60\"\r\n      >\r\n        <Flex px=\"4\" py=\"5\" align=\"center\">\r\n          <Text\r\n            as={Link}\r\n            to=\"/\"\r\n            fontSize=\"2xl\"\r\n            fontWeight=\"bold\"\r\n            letterSpacing={\"1px\"}\r\n            fontFamily={\"Kaushan Script\"}\r\n          >\r\n            Recipe\r\n            <Text display=\"inline\" color=\"primary.500\">\r\n              Hub\r\n            </Text>\r\n          </Text>\r\n        </Flex>\r\n        <Flex\r\n          direction=\"column\"\r\n          as=\"nav\"\r\n          fontSize=\"md\"\r\n          color=\"gray.600\"\r\n          aria-label=\"Main Navigation\"\r\n        >\r\n          <NavItem icon={AiOutlineHome} onClick={() => setSection(1)}>\r\n            Dashboard\r\n          </NavItem>\r\n          <NavItem icon={AiOutlineTeam} onClick={() => setSection(2)}>\r\n            Users\r\n          </NavItem>\r\n          <NavItem icon={IoFastFoodOutline} onClick={() => setSection(3)}>\r\n            Recipes\r\n          </NavItem>\r\n          <NavItem icon={GiExitDoor} onClick={() => navigate(\"/\")}>\r\n            Logout\r\n          </NavItem>\r\n        </Flex>\r\n      </Box>\r\n      <Drawer isOpen={isOpen} onClose={onClose} placement=\"left\">\r\n        <DrawerOverlay />\r\n        <DrawerContent>\r\n          {/* <SidebarContent w=\"full\" borderRight=\"none\" /> */}\r\n          <Box\r\n            w=\"full\"\r\n            borderRight=\"none\"\r\n            as=\"nav\"\r\n            pos=\"fixed\"\r\n            top=\"0\"\r\n            left=\"0\"\r\n            zIndex=\"sticky\"\r\n            h=\"full\"\r\n            pb=\"10\"\r\n            overflowX=\"hidden\"\r\n            overflowY=\"auto\"\r\n            bg={useColorModeValue(\"white\", \"gray.800\")}\r\n            borderColor={useColorModeValue(\"inherit\", \"gray.700\")}\r\n            borderRightWidth=\"1px\"\r\n          >\r\n            <Flex px=\"4\" py=\"5\" align=\"center\">\r\n              <Text\r\n                as={Link}\r\n                to=\"/\"\r\n                fontSize=\"2xl\"\r\n                fontWeight=\"bold\"\r\n                letterSpacing={\"1px\"}\r\n                fontFamily={\"Kaushan Script\"}\r\n              >\r\n                Recipe\r\n                <Text display=\"inline\" color=\"primary.500\">\r\n                  Hub\r\n                </Text>\r\n              </Text>\r\n            </Flex>\r\n            <Flex\r\n              direction=\"column\"\r\n              as=\"nav\"\r\n              fontSize=\"md\"\r\n              color=\"gray.600\"\r\n              aria-label=\"Main Navigation\"\r\n            >\r\n              <NavItem icon={AiOutlineHome} onClick={() => setSection(1)}>\r\n                Dashboard\r\n              </NavItem>\r\n              <NavItem icon={AiOutlineTeam} onClick={() => setSection(2)}>\r\n                Users\r\n              </NavItem>\r\n              <NavItem icon={IoFastFoodOutline} onClick={() => setSection(3)}>\r\n                Recipes\r\n              </NavItem>\r\n              <NavItem icon={GiExitDoor} onClick={() => navigate(\"/\")}>\r\n                Logout\r\n              </NavItem>\r\n            </Flex>\r\n          </Box>\r\n        </DrawerContent>\r\n      </Drawer>\r\n      <Box ml={{ base: 0, md: 60 }} transition=\".3s ease\">\r\n        <Flex\r\n          as=\"header\"\r\n          align=\"center\"\r\n          justifyContent={{ base: \"space-between\", md: \"flex-end\" }}\r\n          w=\"full\"\r\n          px=\"4\"\r\n          borderBottomWidth=\"1px\"\r\n          borderColor={useColorModeValue(\"inherit\", \"gray.700\")}\r\n          bg={useColorModeValue(\"white\", \"gray.800\")}\r\n          boxShadow=\"sm\"\r\n          h=\"14\"\r\n        >\r\n          <IconButton\r\n            aria-label=\"Menu\"\r\n            display={{ base: \"inline-flex\", md: \"none\" }}\r\n            onClick={onOpen}\r\n            icon={<FiMenu />}\r\n            size=\"md\"\r\n          />\r\n\r\n          <Flex align=\"center\">\r\n            <Avatar\r\n              ml=\"4\"\r\n              size=\"sm\"\r\n              name=\"Ahmad\"\r\n              src=\"https://avatars2.githubusercontent.com/u/37842853?v=4\"\r\n              cursor=\"pointer\"\r\n            />\r\n          </Flex>\r\n        </Flex>\r\n\r\n        <Box\r\n          as=\"main\"\r\n          p={14}\r\n          minH=\"25rem\"\r\n          bg={useColorModeValue(\"auto\", \"gray.800\")}\r\n        >\r\n          <div>\r\n            <div className=\"tabs\">\r\n              <div className=\"tab-content\">\r\n                {section === 1 ? (\r\n                  <div>\r\n                    <Center>\r\n                      <Box p={7} textAlign=\"center\">\r\n                        <Box\r\n                          maxW=\"xl\"\r\n                          mx=\"auto\"\r\n                          p={10}\r\n                          borderWidth=\"1px\"\r\n                          borderRadius=\"lg\"\r\n                          boxShadow=\"md\"\r\n                        >\r\n                          <Heading as=\"h1\" size=\"lg\">\r\n                            Welcome to Dashboard\r\n                          </Heading>\r\n                          <Text mb={0}>\r\n                            Hello Admin, welcome to your dashboard!\r\n                          </Text>\r\n                        </Box>\r\n                      </Box>\r\n                    </Center>\r\n                    <Flex flexWrap=\"wrap\" justifyContent=\"space-between\">\r\n                      <Box\r\n                        flex=\"1\"\r\n                        maxW=\"lg\"\r\n                        bg=\"red.500\"\r\n                        p=\"3\"\r\n                        m=\"4\"\r\n                        borderRadius=\"md\"\r\n                        textAlign=\"center\"\r\n                      >\r\n                        <svg\r\n                          width=\"30\"\r\n                          height=\"30\"\r\n                          viewBox=\"0 0 24 24\"\r\n                          xmlns=\"http://www.w3.org/2000/svg\"\r\n                        >\r\n                          <path\r\n                            fill=\"#ffffff\"\r\n                            fill-rule=\"evenodd\"\r\n                            d=\"M8 7a4 4 0 1 1 8 0a4 4 0 0 1-8 0Zm0 6a5 5 0 0 0-5 5a3 3 0 0 0 3 3h12a3 3 0 0 0 3-3a5 5 0 0 0-5-5H8Z\"\r\n                            clip-rule=\"evenodd\"\r\n                          />\r\n                        </svg>\r\n                        <Text fontSize=\"2xl\" fontWeight=\"bold\" color=\"white\">\r\n                          {user?.length}\r\n                        </Text>\r\n                        <Text fontSize=\"lg\" color=\"white\">\r\n                          User registered\r\n                        </Text>\r\n                      </Box>\r\n                      <Box\r\n                        flex=\"1\"\r\n                        maxW=\"lg\"\r\n                        bg=\"yellow.500\"\r\n                        p=\"3\"\r\n                        m=\"4\"\r\n                        borderRadius=\"md\"\r\n                        textAlign=\"center\"\r\n                      >\r\n                        <svg\r\n                          width=\"30\"\r\n                          height=\"30\"\r\n                          viewBox=\"0 0 512 512\"\r\n                          xmlns=\"http://www.w3.org/2000/svg\"\r\n                        >\r\n                          <path\r\n                            fill=\"#ffffff\"\r\n                            d=\"M0 192c0-35.3 28.7-64 64-64h1.6C73 91.5 105.3 64 144 64c15 0 29 4.1 40.9 11.2C198.2 49.6 225.1 32 256 32s57.8 17.6 71.1 43.2C339 68.1 353 64 368 64c38.7 0 71 27.5 78.4 64h1.6c35.3 0 64 28.7 64 64c0 11.7-3.1 22.6-8.6 32H8.6C3.1 214.6 0 203.7 0 192zm0 91.4C0 268.3 12.3 256 27.4 256h457.2c15.1 0 27.4 12.3 27.4 27.4c0 70.5-44.4 130.7-106.7 154.1l-1.8 14.5c-2 16-15.6 28-31.8 28H140.2c-16.1 0-29.8-12-31.8-28l-1.8-14.4C44.4 414.1 0 353.9 0 283.4z\"\r\n                          />\r\n                        </svg>\r\n                        <Text fontSize=\"2xl\" fontWeight=\"bold\" color=\"white\">\r\n                          {recipe?.length}\r\n                        </Text>\r\n                        <Text fontSize=\"lg\" color=\"white\">\r\n                          Total Recipes\r\n                        </Text>\r\n                      </Box>\r\n                    </Flex>\r\n                    <Box\r\n                      borderWidth=\"1px\"\r\n                      borderRadius=\"lg\"\r\n                      boxShadow=\"md\"\r\n                      m={4}\r\n                    >\r\n                      <Contribution user={user} />\r\n                    </Box>\r\n                    <Box\r\n                      borderWidth=\"1px\"\r\n                      borderRadius=\"lg\"\r\n                      boxShadow=\"md\"\r\n                      m={4}\r\n                    >\r\n                      <MostLikes recipe={recipe} />\r\n                    </Box>\r\n                    <Flex\r\n                      borderWidth=\"1px\"\r\n                      borderRadius=\"lg\"\r\n                      boxShadow=\"md\"\r\n                      m={4}\r\n                    >\r\n                      <VegNonVegChart recipeData={recipe} />\r\n                      <Cuisines recipes={recipe} />\r\n                    </Flex>\r\n                  </div>\r\n                ) : section === 2 ? (\r\n                  <div>\r\n                    {user?.length > 0 &&\r\n                      user.map((ele, indx) => (\r\n                        <Card m={5} maxW=\"\" key={indx + Date.now()}>\r\n                          <CardHeader>\r\n                            <Flex my={0} gap={3}>\r\n                              <Avatar\r\n                                size=\"lg\"\r\n                                name={ele?.name}\r\n                                src={`${process.env.REACT_APP_API_URL}/${ele?.profileImage}`}\r\n                              />\r\n                              <Flex flexDir={\"column\"} textAlign={\"left\"}>\r\n                                <Heading size={\"md\"} fontWeight={\"bold\"}>\r\n                                  {ele?.name}\r\n                                </Heading>\r\n                                <Text size={\"sm\"}>City: {ele?.city}</Text>\r\n                                <Text size={\"sm\"}>Email: {ele?.email}</Text>\r\n                              </Flex>\r\n                            </Flex>\r\n                          </CardHeader>\r\n                          <CardBody>\r\n                            <Stack textAlign={\"left\"} mt=\"1\" spacing=\"3\">\r\n                              <Heading size=\"md\">Bio: </Heading>\r\n                              <Text>{ele?.bio}</Text>\r\n                              <Accordion allowMultiple>\r\n                                <AccordionItem>\r\n                                  <h2>\r\n                                    <AccordionButton>\r\n                                      <Box\r\n                                        as=\"span\"\r\n                                        fontWeight={\"bold\"}\r\n                                        flex=\"1\"\r\n                                        textAlign=\"left\"\r\n                                        p={\"5\"}\r\n                                      >\r\n                                        <Heading size=\"md\">\r\n                                          See friends:\r\n                                        </Heading>\r\n                                      </Box>\r\n                                      <AccordionIcon />\r\n                                    </AccordionButton>\r\n                                  </h2>\r\n                                  <AccordionPanel pb={4}>\r\n                                    {ele?.friends.length === 0 ? (\r\n                                      <Text>No friends!</Text>\r\n                                    ) : (\r\n                                      ele?.friends.map((e, index) => (\r\n                                        <Box\r\n                                          mx={9}\r\n                                          my={2}\r\n                                          key={index + Date.now()}\r\n                                        >\r\n                                          <Flex my={0} gap={3}>\r\n                                            <Avatar\r\n                                              size=\"lg\"\r\n                                              name={e?.name}\r\n                                              src={`${process.env.REACT_APP_API_URL}/${e?.profileImage}`}\r\n                                            />\r\n                                            <Flex\r\n                                              flexDir={\"column\"}\r\n                                              textAlign={\"left\"}\r\n                                            >\r\n                                              <Text fontWeight={\"bold\"}>\r\n                                                {e?.name}\r\n                                              </Text>\r\n                                              <Text>{e?.city}</Text>\r\n                                            </Flex>\r\n                                          </Flex>\r\n                                        </Box>\r\n                                      ))\r\n                                    )}\r\n                                  </AccordionPanel>\r\n                                </AccordionItem>\r\n                              </Accordion>\r\n                              <Heading size=\"md\">\r\n                                Recipes posted by user:{\" \"}\r\n                              </Heading>\r\n                              <HStack overflowX={\"scroll\"}>\r\n                                {ele?.recipes?.length === 0 ? (\r\n                                  <Text>No posts yet!</Text>\r\n                                ) : (\r\n                                  ele?.recipes.map((e, index) => (\r\n                                    <Box key={index + Date.now()}>\r\n                                      <Image\r\n                                        height={\"200px\"}\r\n                                        width={\"200px\"}\r\n                                        src={`${process.env.REACT_APP_API_URL}/${e?.images[0]}`}\r\n                                      />\r\n                                      <Text fontWeight={\"bold\"}>\r\n                                        {e?.title}\r\n                                      </Text>\r\n                                    </Box>\r\n                                  ))\r\n                                )}\r\n                              </HStack>\r\n                            </Stack>\r\n                          </CardBody>\r\n                          <Divider />\r\n                          <CardFooter>\r\n                            <ButtonGroup spacing=\"2\">\r\n                              <Button\r\n                                variant=\"solid\"\r\n                                colorScheme=\"blue\"\r\n                                onClick={() => navigate(`/user/${ele._id}`)}\r\n                              >\r\n                                View User\r\n                              </Button>\r\n                              <Button variant=\"ghost\" colorScheme=\"red\">\r\n                                Delete User\r\n                              </Button>\r\n                            </ButtonGroup>\r\n                          </CardFooter>\r\n                        </Card>\r\n                      ))}\r\n                  </div>\r\n                ) : (\r\n                  <div>\r\n                    <Grid\r\n                      gridTemplateColumns={{\r\n                        base: \"repeat(1, 1fr)\", // 1 column on small screens\r\n                        md: \"repeat(2, 1fr)\", // 2 columns on medium screens\r\n                        lg: \"repeat(3, 1fr)\", // 3 columns on large screens\r\n                      }}\r\n                      gap=\"30px\"\r\n                    >\r\n                      {recipe?.length > 0 &&\r\n                        recipe.map((ele, index) => (\r\n                          <Card key={index}>\r\n                            <Box\r\n                              borderWidth=\"0\"\r\n                              borderRadius=\"md\"\r\n                              overflow=\"hidden\"\r\n                            >\r\n                              <CardHeader>\r\n                                <Flex my={3} gap={3}>\r\n                                  <Avatar\r\n                                    size=\"lg\"\r\n                                    name={ele?.userId?.name}\r\n                                    src={`${process.env.REACT_APP_API_URL}/${ele?.userId?.profileImage}`}\r\n                                  />\r\n                                  <Flex flexDir={\"column\"} textAlign={\"left\"}>\r\n                                    <Text fontWeight={\"bold\"}>\r\n                                      {ele?.userId?.name}\r\n                                    </Text>\r\n                                    <Text>{ele?.userId?.city}</Text>\r\n                                  </Flex>\r\n                                </Flex>\r\n                                <Carousel\r\n                                  height={\"300px\"}\r\n                                  images={ele?.images}\r\n                                />\r\n                              </CardHeader>\r\n                              <Box p=\"1rem\">\r\n                                <Heading fontSize=\"2xl\" fontWeight=\"bold\">\r\n                                  {ele.title}\r\n                                </Heading>\r\n                                <Tag my={3}>{ele?.cuisine[0]}</Tag>\r\n                                <Text fontSize=\"md\">{ele.description}</Text>\r\n                                <Flex mt={3} flexWrap=\"wrap\" gap={3}>\r\n                                  {ele?.tags?.length > 0 &&\r\n                                    ele?.tags.map((e, index) => (\r\n                                      <Tag key={index}>{e}</Tag>\r\n                                    ))}\r\n                                </Flex>\r\n                                <CardFooter\r\n                                  px=\"0\"\r\n                                  justify=\"flex-start\"\r\n                                  gap=\"1rem\"\r\n                                  flexWrap=\"wrap\"\r\n                                  sx={{\r\n                                    \"& > button\": {\r\n                                      minW: \"136px\",\r\n                                    },\r\n                                  }}\r\n                                >\r\n                                  <Button\r\n                                    flex={{ base: \"1\", md: \"0.25\" }}\r\n                                    variant=\"outline\"\r\n                                    leftIcon={<BiShare />}\r\n                                    onClick={() =>\r\n                                      navigate(`/recipe/${ele._id}`)\r\n                                    }\r\n                                  >\r\n                                    View Recipe\r\n                                  </Button>\r\n                                  <Button\r\n                                    colorScheme=\"red\"\r\n                                    onClick={() => onOpen(ele._id)} // Pass the recipe ID to onOpen\r\n                                  >\r\n                                    Delete Recipe\r\n                                  </Button>\r\n                                </CardFooter>\r\n                              </Box>\r\n                            </Box>\r\n                          </Card>\r\n                        ))}\r\n                    </Grid>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </Box>\r\n      </Box>\r\n    </Box>\r\n  );\r\n}\r\n\r\nconst NavItem = (props) => {\r\n  const { icon, children, onClick } = props;\r\n  const color = useColorModeValue(\"gray.600\", \"gray.300\");\r\n\r\n  return (\r\n    <Flex\r\n      align=\"center\"\r\n      px=\"4\"\r\n      py=\"3\"\r\n      cursor=\"pointer\"\r\n      role=\"group\"\r\n      fontWeight=\"semibold\"\r\n      transition=\".15s ease\"\r\n      color={useColorModeValue(\"inherit\", \"gray.400\")}\r\n      _hover={{\r\n        bg: useColorModeValue(\"gray.100\", \"gray.900\"),\r\n        color: useColorModeValue(\"gray.900\", \"gray.200\"),\r\n      }}\r\n      onClick={onClick} // Pass the onClick prop to the Flex component\r\n    >\r\n      {icon && (\r\n        <Icon\r\n          mx=\"2\"\r\n          boxSize=\"4\"\r\n          _groupHover={{\r\n            color: color,\r\n          }}\r\n          as={icon}\r\n        />\r\n      )}\r\n      {children}\r\n    </Flex>\r\n  );\r\n};\r\n\r\n// const DIV = styled.div`\r\n//   display: grid;\r\n//   grid-template-columns: repeat(3, 1fr);\r\n//   gap: 20px;\r\n//   margin: 30px;\r\n//   @media (max-width: 768px) {\r\n//     grid-template-columns: repeat(\r\n//       1,\r\n//       1fr\r\n//     ); /* 1 column on screens up to 768px wide */\r\n//   }\r\n\r\n//   @media (min-width: 769px) and (max-width: 1024px) {\r\n//     grid-template-columns: repeat(\r\n//       2,\r\n//       1fr\r\n//     ); /* 2 columns on screens between 769px and 1024px wide */\r\n//   }\r\n\r\n//   @media (min-width: 1025px) {\r\n//     grid-template-columns: repeat(\r\n//       3,\r\n//       1fr\r\n//     ); /* 3 columns on screens wider than 1024px */\r\n//   }\r\n// `;\r\n\r\n// const ADMIN = styled.div`\r\n//   @import url(\"https://fonts.googleapis.com/css2?family=Raleway:wght@400;800&display=swap\");\r\n\r\n//   display: flex;\r\n//   flex-direction: column;\r\n//   align-items: center;\r\n//   justify-content: center;\r\n//   height: 100vh;\r\n//   /* width: 100vw; */\r\n//   padding: 0 10%;\r\n//   font-family: \"Raleway\", sans-serif;\r\n//   font-size: 20px;\r\n\r\n//   .noShow {\r\n//     display: none;\r\n//   }\r\n\r\n//   .tabs {\r\n//     position: absolute;\r\n//     top: 50%;\r\n//     left: 50%;\r\n//     transform: translate(-50%, -50%);\r\n//     width: 100%;\r\n//     height: 100%;\r\n//     padding: 10px 30px 30px 17%;\r\n//     background: #fff;\r\n//     /* overflow: hidden; */\r\n//   }\r\n//   .tabs .tab-header {\r\n//     float: left;\r\n//     width: 180px;\r\n//     height: 100%;\r\n//     display: flex;\r\n//     flex-direction: column;\r\n//     gap: 50px;\r\n//     border-right: 1px solid #ccc;\r\n//     padding: 50px 0px;\r\n//   }\r\n// `;\r\n", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\components\\forms\\AddRecipeForm.jsx", ["357", "358", "359", "360", "361", "362"], [], "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\components\\common\\Notifications.jsx", ["363", "364", "365", "366", "367", "368"], [], "import React, { useEffect, useState } from \"react\";\r\nimport { useSelector } from \"react-redux\";\r\nimport { useNavigate, Link } from \"react-router-dom\";\r\nimport {\r\n  useToast,\r\n  Box,\r\n  Text,\r\n  Button,\r\n  Flex,\r\n  Avatar,\r\n  Divider,\r\n  Center,\r\n  Icon,\r\n  AvatarBadge,\r\n} from \"@chakra-ui/react\";\r\nimport { FaComment } from \"react-icons/fa\";\r\nimport { EditIcon } from \"@chakra-ui/icons\";\r\nimport { CheckCircleIcon } from \"@chakra-ui/icons\";\r\n\r\nimport axios from \"axios\";\r\n\r\nexport const Notifications = () => {\r\n  const token = useSelector((store) => store.authReducer.token);\r\n  const navigate = useNavigate();\r\n  const toast = useToast();\r\n  const [notifications, setNotifications] = useState([]);\r\n  const reversedNoti = notifications?.slice().reverse().slice(0, 5);\r\n  const getNotificationIcon = (type) => {\r\n    switch (type) {\r\n      case \"comment\":\r\n        return <FaComment color=\"#fb8600ca\" />; // Chakra UI Comment icon\r\n      case \"like\":\r\n        return <CheckCircleIcon color=\"#fb8500ca\" />; // Chakra UI CheckCircle icon\r\n      case \"post\":\r\n        return <EditIcon color=\"#fb8500ca\" />; // Chakra UI Edit icon\r\n      default:\r\n        return null;\r\n    }\r\n  };\r\n\r\n  const fetchNotifications = () => {\r\n    axios\r\n      .get(`${process.env.REACT_APP_API_URL}/notification`, {\r\n        headers: {\r\n          Authorization: `Bearer ${token}`,\r\n        },\r\n      })\r\n      .then((response) => {\r\n        console.log(response.data);\r\n        setNotifications(response.data.notifications);\r\n      })\r\n      .catch((error) => {\r\n        // Handle errors\r\n        toast({\r\n          title: \"Error\",\r\n          description: \"Failed to fetch notifications\",\r\n          status: \"error\",\r\n          duration: 5000,\r\n          isClosable: true,\r\n        });\r\n        console.log(error);\r\n      });\r\n  };\r\n\r\n  useEffect(() => {\r\n    // Fetch notifications when the component mounts\r\n    fetchNotifications();\r\n\r\n    // Set up an interval to fetch new notifications every 5 seconds\r\n    const intervalId = setInterval(() => {\r\n      fetchNotifications();\r\n    }, 5000);\r\n\r\n    // Cleanup the interval when the component unmounts\r\n    return () => {\r\n      clearInterval(intervalId);\r\n    };\r\n  }, [token, toast]);\r\n\r\n  return (\r\n    <Box>\r\n      {reversedNoti.length > 0 ? (\r\n        reversedNoti.map((notification, index) => (\r\n          <>\r\n            <Flex gap=\"0.5rem\" alignItems=\"center\">\r\n              <Avatar\r\n                size=\"sm\"\r\n                src={`${process.env.REACT_APP_API_URL}/${notification.senderImage}`}\r\n              ></Avatar>\r\n              <Center height=\"50px\">\r\n                <Divider orientation=\"vertical\" />\r\n              </Center>\r\n              <Text key={index} fontSize=\"0.9rem\">\r\n                {getNotificationIcon(notification.type)} {notification.message}{\" \"}\r\n                at {notification.time}\r\n              </Text>\r\n            </Flex>\r\n            <Divider my={2} />\r\n          </>\r\n        ))\r\n      ) : (\r\n        <Text>Loading...</Text>\r\n      )}\r\n    </Box>\r\n  );\r\n};\r\n", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\redux\\userReducer\\actions.js", ["369", "370", "371", "372", "373", "374", "375", "376", "377"], [], "import {\r\n  GET_NONFRIEND_LOADING,\r\n  GET_NONFRIEND_ERROR,\r\n  GET_NONFRIEND_SUCCESS,\r\n  POST_REQUEST_ERROR,\r\n  POST_REQUEST_LOADING,\r\n  POST_REQUEST_SUCCESS,\r\n  GET_REQUESTSUSER_ERROR,\r\n  GET_REQUESTSUSER_SUCCESS,\r\n  GET_REQUESTSUSER_LOADING,\r\n  GET_FRIENDS_ERROR,\r\n  GET_FRIENDS_SUCCESS,\r\n  GET_FRIENDS_LOADING,\r\n  POST_ACCEPTREQUEST_ERROR,\r\n  POST_ACCEPTREQUEST_LOADING,\r\n  POST_ACCEPTREQUEST_SUCCESS,\r\n  POST_REJECTREQUEST_ERROR,\r\n  POST_REJECTREQUEST_LOADING,\r\n  POST_REJECTREQUEST_SUCCESS,\r\n} from \"./actionTypes\";\r\nimport {\r\n  POST_DISLIKE_SUCCESS,\r\n  POST_LIKE_SUCCESS,\r\n} from \"../authReducer/actionTypes\";\r\nimport axios from \"axios\";\r\nimport { getUserRecipes } from \"../authReducer/actions\";\r\n\r\nexport const updateUser =\r\n  (id, userObj, token, toast, type, id2) => async (dispatch) => {\r\n    try {\r\n      dispatch({ type: POST_REQUEST_LOADING });\r\n      // Make a patch request using Axios to update the user\r\n      const response = await axios.patch(\r\n        `${process.env.REACT_APP_API_URL}/users/update/${id}`,\r\n        userObj,\r\n        {\r\n          headers: {\r\n            Authorization: `Bearer ${token}`,\r\n            \"X-Action-Type\": type,\r\n          },\r\n        }\r\n      );\r\n      console.log(response.data);\r\n      if (type == \"request\") {\r\n        dispatch({ type: POST_REQUEST_SUCCESS, payload: id });\r\n        toast({\r\n          title: \"Friend Request Sent\",\r\n          status: \"success\",\r\n          duration: 3000,\r\n          isClosable: true,\r\n        });\r\n      } else if (type == \"accept\") {\r\n        dispatch({ type: POST_ACCEPTREQUEST_SUCCESS, payload: id2 });\r\n        toast({\r\n          title: \"Friend Request Accepted\",\r\n          status: \"success\",\r\n          duration: 3000,\r\n          isClosable: true,\r\n        });\r\n      } else if (type === \"reject\") {\r\n        dispatch({ type: POST_REJECTREQUEST_SUCCESS, payload: id2 });\r\n        toast({\r\n          title: \"Friend Request Rejected\",\r\n          status: \"success\",\r\n          duration: 3000,\r\n          isClosable: true,\r\n        });\r\n      } else if (type === \"like\") {\r\n        toast({\r\n          title: \"Post Liked\",\r\n          status: \"success\",\r\n          duration: 500,\r\n          isClosable: true,\r\n        });\r\n        dispatch(getUserRecipes(id, token));\r\n      } else if (type === \"dislike\") {\r\n        toast({\r\n          title: \"Post Liked\",\r\n          status: \"success\",\r\n          duration: 500,\r\n          isClosable: true,\r\n        });\r\n        dispatch(getUserRecipes(id, token));\r\n      } else if (type === \"save\") {\r\n        toast({\r\n          title: \"Recipe Saved\",\r\n          status: \"success\",\r\n          duration: 500,\r\n          isClosable: true,\r\n        });\r\n      } else if (type === \"unsave\") {\r\n        toast({\r\n          title: \"Recipe Unsaved\",\r\n          status: \"success\",\r\n          duration: 500,\r\n          isClosable: true,\r\n        });\r\n      }\r\n    } catch (error) {\r\n      dispatch({ type: POST_REQUEST_ERROR });\r\n      console.log(error);\r\n      // Handle any errors that occur during the request\r\n      // For example, dispatch an action to handle the error\r\n      // dispatch(updateUserFailure(error));\r\n    }\r\n  };\r\n\r\nexport const addToFriend = (id, userId, token) => async (dispatch) => {\r\n  try {\r\n    dispatch({ type: POST_REQUEST_LOADING });\r\n    // Make a patch request using Axios to update the user\r\n    const response = await axios.patch(\r\n      `${process.env.REACT_APP_API_URL}/users/addFriend/${id}`,\r\n      userId,\r\n      {\r\n        headers: {\r\n          Authorization: `Bearer ${token}`,\r\n        },\r\n      }\r\n    );\r\n    // console.log(response);\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\nexport const getAllNonFriends = (token) => async (dispatch) => {\r\n  dispatch({ type: GET_NONFRIEND_LOADING });\r\n  const config = {\r\n    headers: {\r\n      Authorization: `Bearer ${token}`,\r\n    },\r\n  };\r\n  try {\r\n    const response = await axios.get(\r\n      `${process.env.REACT_APP_API_URL}/users/notfriends`,\r\n      config\r\n    );\r\n    // console.log(response.data);\r\n\r\n    const users = response.data.notFriends;\r\n    users.forEach((user) => {\r\n      user.profileImage = `${process.env.REACT_APP_API_URL}/${user.profileImage}`;\r\n    });\r\n    // console.log(users);\r\n    dispatch({ type: GET_NONFRIEND_SUCCESS, payload: users });\r\n  } catch (error) {\r\n    console.log(\"Error fetching user data:\", error);\r\n    dispatch({ type: GET_NONFRIEND_ERROR });\r\n  }\r\n};\r\n\r\nexport const getRequestsUsers = (token) => async (dispatch) => {\r\n  dispatch({ type: GET_REQUESTSUSER_LOADING });\r\n  const config = {\r\n    headers: {\r\n      Authorization: `Bearer ${token}`,\r\n    },\r\n  };\r\n  try {\r\n    const response = await axios.get(\r\n      `${process.env.REACT_APP_API_URL}/users/requests`,\r\n      config\r\n    );\r\n    // console.log(response.data);\r\n\r\n    const users = response.data.requestUsers;\r\n    users.forEach((user) => {\r\n      user.profileImage = `${process.env.REACT_APP_API_URL}/${user.profileImage}`;\r\n    });\r\n    // console.log(users);\r\n    dispatch({ type: GET_REQUESTSUSER_SUCCESS, payload: users });\r\n  } catch (error) {\r\n    console.log(\"Error fetching user data:\", error);\r\n    dispatch({ type: GET_REQUESTSUSER_ERROR });\r\n  }\r\n};\r\n\r\nexport const getFriends = (token) => async (dispatch) => {\r\n  dispatch({ type: GET_FRIENDS_LOADING });\r\n  const config = {\r\n    headers: {\r\n      Authorization: `Bearer ${token}`,\r\n    },\r\n  };\r\n  try {\r\n    const response = await axios.get(\r\n      `${process.env.REACT_APP_API_URL}/users/friends`,\r\n      config\r\n    );\r\n    // console.log(response.data);\r\n\r\n    const users = response.data.friends;\r\n    users.forEach((user) => {\r\n      user.profileImage = `${process.env.REACT_APP_API_URL}/${user.profileImage}`;\r\n    });\r\n    // console.log(users);\r\n    dispatch({ type: GET_FRIENDS_SUCCESS, payload: users });\r\n  } catch (error) {\r\n    console.log(\"Error fetching user data:\", error);\r\n    dispatch({ type: GET_FRIENDS_ERROR });\r\n  }\r\n};\r\n", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\redux\\recipeReducer\\actions.js", ["378", "379", "380", "381"], [], "import {\r\n  ADDRECIPE_ERROR,\r\n  ADDRECIPE_LOADING,\r\n  ADDR<PERSON>IPE_SUCCESS,\r\n  GETRECIPE_ERROR,\r\n  GETRECIPE_LOADING,\r\n  GETRECIPE_SUCCESS,\r\n  GET_FEED_ERROR,\r\n  GET_FEED_LOADING,\r\n  GET_FEED_SUCCESS,\r\n  UPDATE_RECIPE_SUCCESS,\r\n} from \"./actionTypes\";\r\n\r\nimport axios from \"axios\";\r\n\r\nexport const addNewRecipe =\r\n  (token, recipe, toast, navigate, closeModal) => async (dispatch) => {\r\n    dispatch({ type: ADDRECIPE_LOADING });\r\n    console.log(recipe);\r\n    try {\r\n      const response = await axios.post(\r\n        `${process.env.REACT_APP_API_URL}/recipe/add`,\r\n        recipe,\r\n        {\r\n          headers: {\r\n            \"Content-Type\": \"multipart/form-data\",\r\n            Authorization: `Bearer ${token}`,\r\n          },\r\n        }\r\n      );\r\n      console.log(response);\r\n      dispatch({ type: ADDRECIPE_SUCCESS, payload: response.data.recipe }); //add payload after successful post\r\n      toast({\r\n        title: \"Recipe Created Successfully\",\r\n        description: `${response.data.message}`,\r\n        status: \"success\",\r\n        duration: 3000,\r\n        isClosable: true,\r\n      });\r\n      closeModal();\r\n      navigate(\"/feed\");\r\n    } catch (err) {\r\n      console.log(err);\r\n      dispatch({ type: ADDRECIPE_ERROR });\r\n      toast({\r\n        title: \"Failed To Add Recipe\",\r\n        description: `${err.response.data.message}`,\r\n        status: \"error\",\r\n        duration: 3000,\r\n        isClosable: true,\r\n      });\r\n    }\r\n  };\r\n\r\nexport const getFeed = (token) => async (dispatch) => {\r\n  dispatch({ type: GET_FEED_LOADING });\r\n  const config = {\r\n    headers: {\r\n      Authorization: `Bearer ${token}`,\r\n    },\r\n  };\r\n  try {\r\n    const response = await axios.get(\r\n      `${process.env.REACT_APP_API_URL}/recipe/feed`,\r\n      config\r\n    );\r\n    // console.log(response.data);\r\n\r\n    const recipes = response.data.feed;\r\n\r\n    for (let recipe of recipes) {\r\n      recipe.images = recipe.images.map((image) => {\r\n        return `${process.env.REACT_APP_API_URL}/${image}`;\r\n      });\r\n\r\n      // Update profileImage URL for the user in the recipe\r\n      recipe.userId.profileImage = `${process.env.REACT_APP_API_URL}/${recipe.userId.profileImage}`;\r\n    }\r\n\r\n    // console.log(recipes);\r\n    dispatch({ type: GET_FEED_SUCCESS, payload: recipes });\r\n  } catch (error) {\r\n    console.log(\"Error fetching user data:\", error);\r\n    dispatch({ type: GET_FEED_ERROR });\r\n  }\r\n};\r\n\r\nexport const updateRecipe =\r\n  (id, recipe, token, toast, type) => async (dispatch) => {\r\n    dispatch({ type: ADDRECIPE_LOADING });\r\n    try {\r\n      const response = await axios.patch(\r\n        `${process.env.REACT_APP_API_URL}/recipe/update/${id}`,\r\n        recipe,\r\n        {\r\n          headers: {\r\n            Authorization: `Bearer ${token}`,\r\n          },\r\n        }\r\n      );\r\n      console.log(response.data);\r\n      // dispatch({\r\n      //   type: UPDATE_RECIPE_SUCCESS,\r\n      //   payload: response.data.updatedRecipe,\r\n      // });\r\n      dispatch(getFeed(token));\r\n    } catch (err) {\r\n      console.log(\"failed to update recipe\", err);\r\n      dispatch({ type: ADDRECIPE_ERROR });\r\n    }\r\n  };\r\n\r\nexport const getSingleRecipe = (token, id) => {\r\n  const config = {\r\n    headers: {\r\n      Authorization: `Bearer ${token}`,\r\n    },\r\n  };\r\n\r\n  return axios\r\n    .get(\r\n      `${process.env.REACT_APP_API_URL}/recipe/getSingleRecipe/${id}`,\r\n      config\r\n    )\r\n    .then((res) => {\r\n      // console.log(res.data)\r\n      return res.data;\r\n    })\r\n    .catch((err) => {\r\n      console.log(err);\r\n    });\r\n};\r\n", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\components\\forms\\LoginForm.jsx", ["382"], [], "import React, { useState } from \"react\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { Image, useToast } from \"@chakra-ui/react\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { loginUser } from \"../../redux/authReducer/actions\";\r\nimport { ViewIcon, ViewOffIcon } from \"@chakra-ui/icons\";\r\n\r\nimport {\r\n  Box,\r\n  Button,\r\n  Input,\r\n  InputGroup,\r\n  InputRightElement,\r\n  FormControl,\r\n  FormLabel,\r\n  Stack,\r\n  Heading,\r\n} from \"@chakra-ui/react\";\r\n\r\nexport const LoginForm = () => {\r\n  const toast = useToast();\r\n  const navigate = useNavigate();\r\n  const dispatch = useDispatch();\r\n  const [email, setEmail] = useState(\"\");\r\n  const [password, setPassword] = useState(\"\");\r\n  const [showPassword, setShowPassword] = useState(false);\r\n\r\n  const handlePasswordVisibility = () => {\r\n    setShowPassword(!showPassword);\r\n  };\r\n\r\n  const handleSubmit = async (e) => {\r\n    if(email === \"<EMAIL>\" && password === \"admin\")  {\r\n      navigate(\"/admin\");\r\n      return\r\n    }\r\n    e.preventDefault();\r\n    // Handle form submission logic here\r\n    if (email && password) {\r\n      let userObj = { email, password };\r\n      dispatch(loginUser(userObj, toast, navigate));\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Box p={4}>\r\n      <Box></Box>\r\n      <Box>\r\n        <Heading size=\"2xl\" textTransform={\"uppercase\"} mb={\"2rem\"}>\r\n          Login\r\n        </Heading>\r\n        <form onSubmit={handleSubmit}>\r\n          <Stack spacing={4}>\r\n            <FormControl>\r\n              <FormLabel>Email</FormLabel>\r\n              <Input\r\n                type=\"email\"\r\n                placeholder=\"Enter your email\"\r\n                value={email}\r\n                onChange={(e) => setEmail(e.target.value)}\r\n              />\r\n            </FormControl>\r\n\r\n            <FormControl>\r\n              <FormLabel>Password</FormLabel>\r\n              <InputGroup>\r\n                <Input\r\n                  type={showPassword ? \"text\" : \"password\"}\r\n                  placeholder=\"Enter your password\"\r\n                  value={password}\r\n                  onChange={(e) => setPassword(e.target.value)}\r\n                />\r\n                <InputRightElement px={2}>\r\n                  <Button\r\n                    alignSelf=\"center\"\r\n                    variant=\"outline\"\r\n                    size={\"md\"}\r\n                    onClick={handlePasswordVisibility}\r\n                  >\r\n                    {showPassword ? <ViewIcon /> : <ViewOffIcon />}\r\n                  </Button>\r\n                </InputRightElement>\r\n              </InputGroup>\r\n            </FormControl>\r\n\r\n            <Button type=\"submit\" colorScheme=\"blue\" width=\"min-content\">\r\n              Login\r\n            </Button>\r\n          </Stack>\r\n        </form>\r\n      </Box>\r\n    </Box>\r\n  );\r\n};\r\n", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\components\\forms\\SignUpForm.jsx", [], [], "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\components\\common\\Reveal.jsx", ["383"], [], "import React from \"react\";\r\nimport styled from \"styled-components\";\r\nimport { motion, useInView, useAnimation } from \"framer-motion\";\r\n\r\nexport const Reveal = ({\r\n  children,\r\n  width = \"auto\",\r\n  delay = 1,\r\n  className = \"\",\r\n}) => {\r\n  const ref = React.useRef(null);\r\n  const isInView = useInView(ref, { once: true });\r\n\r\n  const mainControls = useAnimation();\r\n  const slideControls = useAnimation();\r\n\r\n  React.useEffect(() => {\r\n    if (isInView) {\r\n      mainControls.start(\"visible\");\r\n      slideControls.start(\"visible\");\r\n    }\r\n  }, [isInView]);\r\n\r\n  return (\r\n    <REVEAL ref={ref} width={width} className={className}>\r\n      <motion.div\r\n        variants={{\r\n          hidden: {\r\n            opacity: 0,\r\n            x: 150,\r\n            y: 150,\r\n          },\r\n          visible: {\r\n            opacity: 1,\r\n            x: 0,\r\n            y: 0,\r\n          },\r\n        }}\r\n        initial=\"hidden\"\r\n        animate={mainControls}\r\n        transition={{\r\n          duration: 0.5,\r\n          delay: delay,\r\n        }}\r\n      >\r\n        {children}\r\n      </motion.div>\r\n    </REVEAL>\r\n  );\r\n};\r\n\r\nconst REVEAL = styled.div`\r\n  position: relative;\r\n  width: ${(props) => (props.width ? props.width : \"100%\")};\r\n  /* overflow: hidden; */\r\n`;\r\n", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\components\\Feed\\MiniCard.jsx", ["384", "385", "386", "387", "388", "389", "390"], [], "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\components\\Feed\\UserFeed.jsx", ["391", "392", "393", "394", "395", "396", "397"], [], "import React, { useEffect } from \"react\";\r\nimport {\r\n  Box,\r\n  Flex,\r\n  Heading,\r\n  Input,\r\n  InputGroup,\r\n  InputLeftElement,\r\n  Stack,\r\n  Divider,\r\n  useToast,\r\n  Spinner,\r\n} from \"@chakra-ui/react\";\r\nimport FeedCard from \"./FeedCard\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { getFeed } from \"../../redux/recipeReducer/actions\";\r\n\r\nexport const UserFeed = () => {\r\n  const dispatch = useDispatch();\r\n  const feed = useSelector((store) => store.recipeReducer.feed);\r\n  const token =\r\n    useSelector((store) => store.authReducer.token) ||\r\n    localStorage.getItem(\"token\");\r\n  const isLoading = useSelector((store) => store.recipeReducer.isLoading);\r\n\r\n  useEffect(() => {\r\n    if (token) {\r\n      dispatch(getFeed(token));\r\n    }\r\n  }, []);\r\n\r\n  return (\r\n    <Box p={5} w=\"50%\" h=\"90vh\" overflowY=\"scroll\" className=\"scroll\">\r\n      {isLoading ? (\r\n        // Display loading state if the data is still being fetched\r\n        <Flex alignItems=\"center\" justifyContent={\"center\"} minH={\"50vh\"}>\r\n          <Spinner\r\n            w=\"6rem\"\r\n            h=\"6rem\"\r\n            mx=\"auto\"\r\n            thickness=\"4px\"\r\n            speed=\"0.65s\"\r\n            emptyColor=\"gray.200\"\r\n            color=\"primary.500\"\r\n            size=\"2xl\"\r\n          />\r\n        </Flex>\r\n      ) : feed.length > 0 ? (\r\n        feed.map((recipe, index) => {\r\n          return <FeedCard key={index} recipe={recipe} />;\r\n        })\r\n      ) : (\r\n        <Heading as=\"h2\">Nothing In Feed</Heading>\r\n      )}\r\n    </Box>\r\n  );\r\n};\r\n", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\components\\Feed\\NonFriends.jsx", ["398", "399"], [], "import React, { useEffect } from \"react\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { useToast } from \"@chakra-ui/react\";\r\nimport { getAllNonFriends, updateUser } from \"../../redux/userReducer/actions\";\r\nimport { MiniCard_Friends } from \"./MiniCard\";\r\n\r\nexport const NonFriends = () => {\r\n  const toast = useToast();\r\n  const dispatch = useDispatch();\r\n  const loggedInUser = useSelector((store) => store.authReducer.loggedInUser);\r\n  const nonFriends = useSelector((store) => store.userReducer.nonFriends);\r\n  const token =\r\n    useSelector((store) => store.authReducer.token) ||\r\n    localStorage.getItem(\"token\");\r\n\r\n  // console.log(token, nonFriends);\r\n  useEffect(() => {\r\n    if (token) {\r\n      dispatch(getAllNonFriends(token));\r\n    }\r\n  }, []);\r\n\r\n  function addRequestHandler(id, receiversRequest) {\r\n    // console.log(id, receiversRequest);\r\n    if (token) {\r\n      if (!receiversRequest.includes(loggedInUser._id)) {\r\n        receiversRequest.push(loggedInUser._id);\r\n        dispatch(updateUser(id, { requests: receiversRequest }, token, toast,'request'));\r\n      }\r\n    }\r\n  }\r\n\r\n  return (\r\n    <>\r\n      {nonFriends.length > 0 ? (\r\n        nonFriends.map((friend, i) => {\r\n          return (\r\n            <MiniCard_Friends\r\n              key={i}\r\n              friend={friend}\r\n              addRequestHandler={addRequestHandler}\r\n              userId={loggedInUser._id}\r\n            />\r\n          );\r\n        })\r\n      ) : (\r\n        <h3>Failed To Load Users</h3>\r\n      )}\r\n    </>\r\n  );\r\n};\r\n", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\components\\Feed\\SingleRecipeCarousel.jsx", [], [], "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\components\\Feed\\FeedCard.jsx", ["400", "401", "402", "403", "404"], [], "import React, { useState } from \"react\";\r\nimport { BsBookmark, BsBookmarkFill } from \"react-icons/bs\";\r\nimport { BiLike, BiChat, BiShare } from \"react-icons/bi\";\r\nimport { Carousel } from \"./Carousel\";\r\nimport { useSelector, useDispatch } from \"react-redux\";\r\nimport {\r\n  Card,\r\n  CardHeader,\r\n  CardBody,\r\n  CardFooter,\r\n  Flex,\r\n  Avatar,\r\n  Box,\r\n  Heading,\r\n  Tag,\r\n  Text,\r\n  IconButton,\r\n  Button,\r\n  Divider,\r\n  Center,\r\n  WrapItem,\r\n  Image,\r\n  Editable,\r\n  EditablePreview,\r\n  EditableInput,\r\n  Input,\r\n  useToast,\r\n  Spinner,\r\n} from \"@chakra-ui/react\";\r\nimport { updateRecipe } from \"../../redux/recipeReducer/actions\";\r\nimport { updateUser } from \"../../redux/userReducer/actions\";\r\nimport axios from \"axios\";\r\nimport { getFeed } from \"../../redux/recipeReducer/actions\";\r\nimport { useNavigate } from \"react-router-dom\";\r\n\r\nexport default function FeedCard({ recipe }) {\r\n  const toast = useToast();\r\n  const navigate = useNavigate();\r\n  const dispatch = useDispatch();\r\n  const { loggedInUser, token } = useSelector((store) => store.authReducer);\r\n  const [liked, setLiked] = useState(recipe.likes.includes(loggedInUser._id));\r\n  const [saved, setSaved] = useState(\r\n    loggedInUser.savedRecipes.includes(recipe._id)\r\n  );\r\n\r\n  const addLikeHandler = () => {\r\n    // Check if the loggedInUser has not already liked the recipe\r\n    if (!liked) {\r\n      // Perform the like operation here, e.g., send a request to your API\r\n      // Update the local state or Redux state accordingly\r\n      let newLikes = [...recipe.likes, loggedInUser._id];\r\n      let newLikedRecipes = [...loggedInUser.likedRecipes];\r\n      if (!newLikedRecipes.includes(recipe._id)) {\r\n        newLikedRecipes.push(recipe._id);\r\n      }\r\n      console.log(newLikes, 1, newLikedRecipes, 2);\r\n\r\n      dispatch(\r\n        updateRecipe(recipe._id, { likes: newLikes }, token, toast, \"like\")\r\n      );\r\n      dispatch(\r\n        updateUser(\r\n          loggedInUser._id,\r\n          { likedRecipes: newLikedRecipes },\r\n          token,\r\n          toast,\r\n          \"like\",\r\n          recipe._id\r\n        )\r\n      );\r\n    }\r\n  };\r\n\r\n  const removeLikeHandler = () => {\r\n    // Check if the loggedInUser has already liked the recipe\r\n    if (liked) {\r\n      let newLikes = [...recipe.likes].filter(\r\n        (like) => like != loggedInUser._id\r\n      );\r\n      let newLikedRecipes = [...loggedInUser.likedRecipes].filter(\r\n        (recipeId) => recipeId != recipe._id\r\n      );\r\n\r\n      console.log(newLikes, 1, newLikedRecipes, 2);\r\n      // Perform the unlike operation here, e.g., send a request to your API\r\n      dispatch(\r\n        updateRecipe(recipe._id, { likes: newLikes }, token, toast, \"dislike\")\r\n      );\r\n      // Update the local state or Redux state accordingly\r\n      dispatch(\r\n        updateUser(\r\n          loggedInUser._id,\r\n          { likedRecipes: newLikedRecipes },\r\n          token,\r\n          toast,\r\n          \"dislike\",\r\n          recipe._id\r\n        )\r\n      );\r\n    }\r\n  };\r\n\r\n  const saveRecipeHandler = () => {\r\n    // Check if the recipe is not already saved\r\n    if (!saved) {\r\n      let newSavedRecipes = [...loggedInUser.savedRecipes, recipe._id];\r\n\r\n      // Perform the save operation here, e.g., send a request to your API\r\n      // Update the local state or Redux state accordingly\r\n      dispatch(\r\n        updateUser(\r\n          loggedInUser._id,\r\n          { savedRecipes: newSavedRecipes },\r\n          token,\r\n          toast,\r\n          \"save\",\r\n          recipe._id\r\n        )\r\n      );\r\n\r\n      setSaved(true);\r\n    }\r\n  };\r\n\r\n  const unsaveRecipeHandler = () => {\r\n    // Check if the recipe is already saved\r\n    if (saved) {\r\n      let newSavedRecipes = loggedInUser.savedRecipes.filter(\r\n        (savedRecipeId) => savedRecipeId !== recipe._id\r\n      );\r\n\r\n      // Perform the unsave operation here, e.g., send a request to your API\r\n      // Update the local state or Redux state accordingly\r\n      dispatch(\r\n        updateUser(\r\n          loggedInUser._id,\r\n          { savedRecipes: newSavedRecipes },\r\n          token,\r\n          toast,\r\n          \"unsave\",\r\n          recipe._id\r\n        )\r\n      );\r\n\r\n      setSaved(false);\r\n    }\r\n  };\r\n\r\n  const [comments, setComments] = useState(recipe.comments);\r\n  const [newComment, setNewComment] = useState(\"\");\r\n  const [editingComment, setEditingComment] = useState(null);\r\n  const reversedComments = comments.slice().reverse().slice(0, 3);\r\n\r\n  const addCommentHandler = async () => {\r\n    if (newComment.trim() === \"\") {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // Send a request to add a new comment\r\n      const response = await axios.post(\r\n        `${process.env.REACT_APP_API_URL}/comment/add`,\r\n        {\r\n          text: newComment,\r\n          userId: loggedInUser._id,\r\n          recipeId: recipe._id,\r\n        },\r\n        {\r\n          headers: {\r\n            Authorization: `Bearer ${token}`,\r\n          },\r\n        }\r\n      );\r\n      toast({\r\n        title: \"Comment Added Successfully\",\r\n        status: \"success\",\r\n        duration: 1000,\r\n        isClosable: true,\r\n      });\r\n      setComments([...comments, response.data.comment]);\r\n      setNewComment(\"\");\r\n      dispatch(getFeed(token));\r\n    } catch (error) {\r\n      // Handle errors and display a toast message\r\n      toast({\r\n        title: \"Couldn't add comment\",\r\n        status: \"error\",\r\n        duration: 1000,\r\n        isClosable: true,\r\n      });\r\n    }\r\n  };\r\n\r\n  const updateCommentHandler = async () => {\r\n    if (newComment.trim() === \"\") return;\r\n\r\n    try {\r\n      // Send a request to update the comment\r\n      const response = await axios.patch(\r\n        `${process.env.REACT_APP_API_URL}/comment/update/${editingComment}`,\r\n        { text: newComment },\r\n        {\r\n          headers: {\r\n            Authorization: `Bearer ${token}`,\r\n          },\r\n        }\r\n      );\r\n\r\n      // Update the local state to reflect the updated comment\r\n      const updatedComments = comments.map((comment) => {\r\n        if (comment._id === editingComment) {\r\n          return response.data.comment;\r\n        }\r\n        return comment;\r\n      });\r\n      toast({\r\n        title: \"Comment Updated Successfully\",\r\n        status: \"success\",\r\n        duration: 1000,\r\n        isClosable: true,\r\n      });\r\n      setComments(updatedComments);\r\n      setEditingComment(null);\r\n      setNewComment(\"\");\r\n      dispatch(getFeed(token));\r\n    } catch (error) {\r\n      // Handle errors and display a toast message\r\n      toast({\r\n        title: \"Couldn't update comment\",\r\n        status: \"error\",\r\n        duration: 1000,\r\n        isClosable: true,\r\n      });\r\n    }\r\n  };\r\n\r\n  const deleteCommentHandler = async (commentId) => {\r\n    try {\r\n      // Send a request to delete the comment\r\n      await axios.delete(\r\n        `${process.env.REACT_APP_API_URL}/comment/delete/${commentId}`,\r\n        {\r\n          headers: {\r\n            Authorization: `Bearer ${token}`,\r\n          },\r\n        }\r\n      );\r\n      toast({\r\n        title: \"Comment Deleted Successfully\",\r\n        status: \"sucess\",\r\n        duration: 1000,\r\n        isClosable: true,\r\n      });\r\n      // Update the local state to remove the deleted comment\r\n      setComments((prevComments) =>\r\n        prevComments.filter((comment) => comment._id !== commentId)\r\n      );\r\n      dispatch(getFeed(token));\r\n    } catch (error) {\r\n      // Handle errors and display a toast message\r\n      toast({\r\n        title: \"Couldn't delete comment\",\r\n        status: \"error\",\r\n        duration: 1000,\r\n        isClosable: true,\r\n      });\r\n    }\r\n  };\r\n\r\n  if (!recipe) {\r\n    return (\r\n      <>\r\n        <Spinner\r\n          thickness=\"4px\"\r\n          speed=\"0.65s\"\r\n          emptyColor=\"gray.200\"\r\n          color=\"blue.500\"\r\n          size=\"2xl\"\r\n        />\r\n      </>\r\n    ); \r\n  }\r\n  return (\r\n    <div style={{ marginBlock: \"0 2rem\" }}>\r\n      <Card\r\n        w=\"100%\"\r\n        mb=\"10px\"\r\n        p=\"10px\"\r\n        transition=\"0.2s ease-in\"\r\n        boxShadow=\"md\"\r\n        _hover={{ boxShadow: \"lg\" }}\r\n      >\r\n        <CardHeader>\r\n          <Flex spacing=\"4\">\r\n            <Flex flex=\"1\" gap=\"4\" alignItems=\"center\" flexWrap=\"wrap\">\r\n              <Avatar size=\"lg\" src={recipe.userId.profileImage} />\r\n              <Center height=\"50px\">\r\n                <Divider orientation=\"vertical\" />\r\n              </Center>\r\n              <Box>\r\n                <Heading size=\"lg\" mb={2}>\r\n                  {recipe.userId.name}\r\n                </Heading>\r\n                <Text>{recipe.caption}</Text>\r\n              </Box>\r\n            </Flex>\r\n            <IconButton\r\n              variant=\"ghost\"\r\n              icon={\r\n                saved ? (\r\n                  <BsBookmarkFill color=\"#fb8500\" size={32} />\r\n                ) : (\r\n                  <BsBookmark color=\"#8c8c8c\" size={32} />\r\n                )\r\n              } // Use filled or unfilled bookmark icon based on saved state\r\n              onClick={() => {\r\n                if (saved) {\r\n                  unsaveRecipeHandler();\r\n                } else {\r\n                  saveRecipeHandler();\r\n                }\r\n              }}\r\n            />\r\n          </Flex>\r\n        </CardHeader>\r\n        <CardBody w=\"100%\" mx=\"auto\">\r\n          <Divider width=\"100%\" mx=\"auto\" mb=\"10\" />\r\n          <Carousel images={recipe.images}></Carousel>\r\n\r\n          <CardFooter\r\n            px=\"0\"\r\n            justify=\"flex-start\"\r\n            gap=\"1rem\"\r\n            flexWrap=\"wrap\"\r\n            sx={{\r\n              \"& > button\": {\r\n                minW: \"136px\",\r\n              },\r\n            }}\r\n          >\r\n            <Button\r\n              flex=\"0.25\"\r\n              bg={liked ? \"primary.500\" : \"#fff\"}\r\n              color={liked ? \"#fff\" : \"secondary\"}\r\n              variant=\"outline\"\r\n              onClick={() => {\r\n                setLiked((prev) => !prev);\r\n                if (liked) {\r\n                  removeLikeHandler();\r\n                } else {\r\n                  addLikeHandler();\r\n                }\r\n              }}\r\n            >\r\n              <BiLike color={liked ? \"#fff\" : \"text\"} />\r\n              <Text ml=\"4px\">{recipe.likes.length}</Text>\r\n            </Button>\r\n            <Button\r\n              flex=\"0.25\"\r\n              onClick={() => navigate(`/recipe/${recipe._id}`)}\r\n              variant=\"outline\"\r\n              color=\"secondary\"\r\n              leftIcon={<BiShare />}\r\n            >\r\n              View Recipe\r\n            </Button>\r\n          </CardFooter>\r\n          <Divider width=\"100%\" mx=\"auto\" mb=\"5\" />\r\n          <Flex alignItems={\"center\"} justifyContent={\"space-between\"}>\r\n            <Heading as=\"h3\" mb={\"0.5rem\"}>\r\n              {recipe.title}\r\n            </Heading>\r\n            <Image\r\n              w=\"2rem\"\r\n              h=\"2rem\"\r\n              objectFit=\"contain\"\r\n              src={`/images/${\r\n                recipe.veg ? \"veg-icon.png\" : \"non-veg-icon.png\"\r\n              }`}\r\n            ></Image>\r\n          </Flex>\r\n          <Flex\r\n            align=\"center\"\r\n            justifyContent=\"space-between\"\r\n            py={1}\r\n            mb=\"0.5rem\"\r\n          >\r\n            <Text\r\n              my={3}\r\n              fontFamily={\"Kaushan Script\"}\r\n              fontSize=\"xl\"\r\n              fontWeight=\"bold\"\r\n              color=\"primary.500\"\r\n            >\r\n              {recipe.cuisine}\r\n            </Text>\r\n            <Flex mt={3} flexWrap=\"wrap\" gap={4}>\r\n              {recipe?.tags?.length > 0 &&\r\n                recipe?.tags.map((e, index) => <Tag key={index}>{e}</Tag>)}\r\n            </Flex>\r\n          </Flex>\r\n          <Text mb=\"0.5rem\">{recipe.description}</Text>\r\n          <Text as=\"strong\">\r\n            {comments.length} {comments.length === 1 ? \"comment\" : \"comments\"}\r\n          </Text>\r\n\r\n          {/* Render existing comments */}\r\n          {reversedComments?.map((comment) => {\r\n            // console.log(comment.userId, loggedInUser, \"aaaaaaaaaaaaa\");\r\n            return comment.userId._id == loggedInUser._id ? (\r\n              <>\r\n                <div key={comment._id}>\r\n                  <Flex gap=\"3rem\" alignItems={\"center\"} my={4}>\r\n                    <WrapItem display=\"flex\" alignItems={\"center\"} width=\"100%\">\r\n                      <div\r\n                        style={{\r\n                          width: \"30%\",\r\n                          display: \"flex\",\r\n                          alignItems: \"center\",\r\n                          gap: \"1rem\",\r\n                          paddingInline: \"0.5rem\",\r\n                          marginRight: \"1rem\",\r\n                          borderRight: \"1px solid #3337\",\r\n                        }}\r\n                      >\r\n                        <Avatar\r\n                          size=\"sm\"\r\n                          name=\"Kent Dodds\"\r\n                          src={`${process.env.REACT_APP_API_URL}/${comment.userId.profileImage}`}\r\n                        />\r\n                        <Text\r\n                          as=\"p\"\r\n                          justifySelf=\"flex-start\"\r\n                          ml=\"8px\"\r\n                          fontWeight={\"500\"}\r\n                        >\r\n                          {comment.userId.name}\r\n                        </Text>\r\n                      </div>\r\n                      {/* <Text letterSpacing={\"1px\"}>{comment.text}</Text> */}\r\n\r\n                      <Editable\r\n                        defaultValue={comment.text}\r\n                        position=\"relative\"\r\n                        flexGrow={1}\r\n                        isPreviewFocusable={true}\r\n                        onFocus={() => setEditingComment(comment._id)}\r\n                      >\r\n                        <EditablePreview />\r\n                        <EditableInput\r\n                          p={2}\r\n                          value={comment.text}\r\n                          onChange={(e) => setNewComment(e.target.value)}\r\n                        ></EditableInput>\r\n                        <Flex\r\n                          alignItems={\"center\"}\r\n                          gap=\"0.5rem\"\r\n                          position=\"absolute\"\r\n                          right=\"2px\"\r\n                          top=\"50%\"\r\n                          transform=\"translateY(-50%)\"\r\n                        >\r\n                          <Button\r\n                            variant=\"outline\"\r\n                            size=\"sm\"\r\n                            onClick={() => {\r\n                              updateCommentHandler();\r\n                              setEditingComment(null);\r\n                            }}\r\n                          >\r\n                            Edit\r\n                          </Button>\r\n                          <Button\r\n                            variant=\"outline\"\r\n                            disabled={true}\r\n                            size=\"sm\"\r\n                            // onClick={() => {\r\n                            //   deleteCommentHandler(comment._id);\r\n                            //   setEditingComment(null);\r\n                            // }}\r\n                          >\r\n                            Delete\r\n                          </Button>\r\n                        </Flex>\r\n                      </Editable>\r\n                    </WrapItem>\r\n                  </Flex>\r\n                </div>\r\n              </>\r\n            ) : (\r\n              <div key={comment._id}>\r\n                <Flex gap=\"3rem\" alignItems={\"center\"} my={4}>\r\n                  <WrapItem display=\"flex\" alignItems={\"center\"} width=\"100%\">\r\n                    <div\r\n                      style={{\r\n                        width: \"30%\",\r\n                        display: \"flex\",\r\n                        gap: \"1rem\",\r\n                        paddingInline: \"0.5rem\",\r\n                        marginRight: \"1rem\",\r\n                        borderRight: \"1px solid #3337\",\r\n                      }}\r\n                    >\r\n                      <Avatar\r\n                        size=\"sm\"\r\n                        name=\"Kent Dodds\"\r\n                        src={`${process.env.REACT_APP_API_URL}/${comment.userId.profileImage}`}\r\n                      />\r\n                      <Text\r\n                        as=\"p\"\r\n                        justifySelf=\"flex-start\"\r\n                        ml=\"8px\"\r\n                        fontWeight={\"500\"}\r\n                      >\r\n                        {comment.userId.name}\r\n                      </Text>\r\n                    </div>\r\n                    <Text letterSpacing={\"1px\"}>{comment.text}</Text>\r\n                  </WrapItem>\r\n                </Flex>\r\n              </div>\r\n            );\r\n          })}\r\n\r\n          {/* Add a new comment */}\r\n          <Input\r\n            onChange={(e) => setNewComment(e.target.value)}\r\n            focusBorderColor=\"transparent\"\r\n            _focus={{ boxShadow: \"none\" }}\r\n            border=\"none\"\r\n            type=\"text\"\r\n            placeholder=\"Add a comment...\"\r\n          />\r\n          <Button onClick={addCommentHandler}>{\"Add Comment\"}</Button>\r\n        </CardBody>\r\n      </Card>\r\n    </div>\r\n  );\r\n}\r\n", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\components\\home\\HomeCard.jsx", ["405"], [], "import {\r\n  Card,\r\n  Box,\r\n  CardBody,\r\n  Heading,\r\n  Image,\r\n  Stack,\r\n  Text,\r\n} from \"@chakra-ui/react\";\r\n\r\nexport const Homecard = ({ image, name, key }) => {\r\n  return (\r\n    <Card\r\n      // w=\"350px\"\r\n      h={{ lg: \"180px\", base: \"auto\" }}\r\n      textAlign=\"left\"\r\n      direction={{ base: \"column-reverse\", sm: \"row\" }}\r\n      overflow=\"hidden\"\r\n      boxShadow=\"lg\"\r\n    >\r\n      <CardBody width={{ lg: \"50%\", md: \"70%\", base: \"100%\" }} p={4}>\r\n        <Heading mb={2} size=\"sm\" textTransform=\"uppercase\">\r\n          {name}\r\n        </Heading>\r\n\r\n        <Text>{Math.floor(Math.random() * (30 - 15 + 15))} Min</Text>\r\n        <Text>{Math.floor(Math.random() * (5 - 1 + 1)) + 1} Serving</Text>\r\n        <Text>{Math.floor(Math.random() * (20 - 10 + 1)) + 10} Calories</Text>\r\n      </CardBody>\r\n      <Box width={{ lg: \"50%\", md: \"70%\", base: \"100%\" }}>\r\n        <Image\r\n          objectFit=\"cover\"\r\n          width={\"100%\"}\r\n          height={\"100%\"}\r\n          minH={\"100px\"}\r\n          src={image}\r\n          // src=\"https://images.immediate.co.uk/production/volatile/sites/30/2020/08/chorizo-mozarella-gnocchi-bake-cropped-9ab73a3.jpg?quality=90&resize=556,505\"\r\n        />\r\n      </Box>\r\n    </Card>\r\n  );\r\n};\r\n", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\components\\Feed\\Requests.jsx", ["406", "407", "408"], [], "import React, { useEffect } from \"react\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { useToast } from \"@chakra-ui/react\";\r\nimport { Heading } from \"@chakra-ui/react\";\r\nimport { MiniCard_Request } from \"./MiniCard\";\r\nimport { addToFriend, getRequestsUsers, updateUser } from \"../../redux/userReducer/actions\";\r\n\r\nexport const Requests = () => {\r\n  const toast = useToast();\r\n  const dispatch = useDispatch();\r\n  const requests = useSelector((store) => store.userReducer.requests);\r\n  const friends = useSelector((store) => store.userReducer.friends);\r\n  const nonFriends = useSelector((store) => store.userReducer.nonFriends);\r\n\r\n  const loggedInUser = useSelector((store) => store.authReducer.loggedInUser);\r\n\r\n  const token =\r\n    useSelector((store) => store.authReducer.token) ||\r\n    localStorage.getItem(\"token\");\r\n\r\n  useEffect(() => {\r\n    dispatch(getRequestsUsers(token));\r\n  }, []);\r\n  // console.log(requests, token);\r\n\r\n  function acceptRequestHandler(id) {\r\n    console.log(id);\r\n    const requestIds = requests.map((request) => request._id);\r\n    const friendIds = friends.map((friend) => friend._id);\r\n    const updatedRequests = requestIds.filter((requestId) => requestId !== id);\r\n    const updatedFriends = [...friendIds,id];\r\n    console.log(requestIds,friendIds);\r\n    console.log(updatedRequests,updatedFriends);\r\n    dispatch(\r\n      updateUser(\r\n        loggedInUser._id,\r\n        { requests: updatedRequests, friends: updatedFriends },\r\n        token,\r\n        toast,\r\n        \"accept\"\r\n        ,id\r\n      )\r\n    );\r\n    dispatch(\r\n      addToFriend(\r\n        id,\r\n        loggedInUser._id,\r\n        token,\r\n      )\r\n    );\r\n  }\r\n\r\n  function rejectRequestHandler(id) {\r\n    const requestIds = requests.map((request) => request._id);\r\n    const updatedRequests = requestIds.filter((requestId) => requestId !== id);\r\n    dispatch(\r\n      updateUser(loggedInUser._id,{ requests: updatedRequests }, token, toast, \"reject\",id)\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div>\r\n      {requests.length > 0 ? (\r\n        requests.map((req, i) => {\r\n          return (\r\n            <MiniCard_Request\r\n              friend={req}\r\n              key={i}\r\n              acceptRequestHandler={acceptRequestHandler}\r\n              rejectRequestHandler={rejectRequestHandler}\r\n            />\r\n          );\r\n        })\r\n      ) : (\r\n        <Heading size='sm'>You Have No Pending Requests</Heading>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\components\\home\\Card.jsx", ["409", "410", "411", "412", "413"], [], "import React from \"react\";\r\nimport { Box, Image, Text, Flex, But<PERSON>, Heading } from \"@chakra-ui/react\";\r\n\r\nconst InfoCard = ({ title, img, direction, mb, screenSize }) => {\r\n  return (\r\n    <Flex\r\n      margin=\"auto\"\r\n      // border=\"1px solid black\"\r\n      direction={screenSize == \"lg\" ? direction : \"column\"}\r\n      width=\"min(80rem,100%)\"\r\n      alignItems=\"center\"\r\n      gap={{ lg: \"2rem\", base: \"4rem\" }}\r\n      px={4}\r\n      mb={screenSize == \"lg\" ? mb : \"4rem\"}\r\n    >\r\n      <Box\r\n        width={{ lg: \"65%\", base: \"75%\" }}\r\n        position=\"relative\"\r\n        mb={screenSize == \"lg\" ? \"2rem\" : \"0\"}\r\n      >\r\n        <img\r\n          src=\"/images/Pattern.png\"\r\n          alt=\"Pattern\"\r\n          style={{\r\n            width: \"40%\",\r\n            position: \"absolute\",\r\n            right: direction == \"row-reverse\" ? \"auto\" : \"76%\",\r\n            left: direction == \"row-reverse\" ? \"76%\" : \"auto\",\r\n            top: \"70%\",\r\n          }}\r\n        />\r\n        <Image\r\n          src={img}\r\n          w={\"100%\"}\r\n          alt=\"Sample Image\"\r\n          objectFit=\"cover\"\r\n          borderRadius={\"2rem 0 2rem 0\"}\r\n          position=\"relative\"\r\n          zIndex={1}\r\n          mr={4}\r\n          boxShadow={\"xl\"}\r\n        />\r\n      </Box>\r\n      <Box\r\n        width=\"100%\"\r\n        textAlign={{\r\n          lg: direction === \"row-reverse\" ? \"left\" : \"right\",\r\n          md: \"left\",\r\n          base: \"center\",\r\n        }}\r\n      >\r\n        <Heading\r\n          fontFamily={\"Kaushan Script, sans-serif\"}\r\n          size={{ lg: \"lg\", md: \"md\", base: \"sm\" }}\r\n          color=\"primary.500\"\r\n          mb=\"0.5rem\"\r\n        >\r\n          About\r\n        </Heading>\r\n        <Heading\r\n          fontWeight=\"800\"\r\n          lineHeight={1.15}\r\n          mb=\"1rem\"\r\n          noOfLines={2}\r\n          color=\"text\"\r\n          maxW={{ lg: \"500px\", md: \"100%\", base: \"100%\" }}\r\n          mx={{ base: \"auto\", md: \"none\", lg: \"none\" }}\r\n          size={{ lg: \"lg\", md: \"md\", base: \"sm\" }}\r\n          ml={{ lg: direction !== \"row-reverse\" ? \"auto\" : \"none\" }}\r\n        >\r\n          {title}\r\n        </Heading>\r\n        <Text\r\n          maxW={{ lg: \"650px\", md: \"100%\", base: \"100%\" }}\r\n          ml={{ lg: direction !== \"row-reverse\" ? \"auto\" : \"none\" }}\r\n          mb=\"2rem\"\r\n        >\r\n          Indulge in a culinary adventure with our delightful recipe. Savor the\r\n          perfect blend of flavors, textures, and aromas that make every bite an\r\n          unforgettable experience. Whether you're a seasoned chef or a novice\r\n          in the kitchen, this dish is easy to prepare and promises a delectable\r\n          outcome.\r\n        </Text>\r\n        <Button>Explore More</Button>\r\n      </Box>\r\n    </Flex>\r\n  );\r\n};\r\n\r\nexport default InfoCard;\r\n", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\components\\home\\ImageGrid.jsx", ["414", "415"], [], "import { Grid, GridItem, Image, Box, flexbox } from \"@chakra-ui/react\";\r\nimport React from \"react\";\r\n\r\nconst ImageGrid = () => {\r\n  return (\r\n    <Grid\r\n      templateColumns=\"repeat(2, 1fr)\"\r\n      alignItems=\"center\"\r\n      justifyContent=\"center\"\r\n    >\r\n      <Box\r\n        display=\"flex\"\r\n        alignItems=\"center\"\r\n        flexDirection=\"column\"\r\n        mr={\"10px\"}\r\n      >\r\n        <Image\r\n          src=\"https://dev-to-uploads.s3.amazonaws.com/uploads/articles/r5zc0u7fz74lhg7l09bt.jpg\"\r\n          alt=\"\"\r\n          width=\"100%\"\r\n          marginTop=\"50px\"\r\n          mb={\"10px\"}\r\n        />\r\n        <Image\r\n          src=\"https://dev-to-uploads.s3.amazonaws.com/uploads/articles/a0yv0qjk4433b9072j79.jpg\"\r\n          alt=\"\"\r\n          width=\"100%\"\r\n        />\r\n      </Box>\r\n      <Box display=\"flex\" alignItems=\"center\" flexDirection=\"column\">\r\n        <Image\r\n          src=\"https://dev-to-uploads.s3.amazonaws.com/uploads/articles/4id2tsqd9504crods063.jpg\"\r\n          alt=\"\"\r\n          width=\"100%\"\r\n          mb={\"10px\"}\r\n        />\r\n        <Image\r\n          src=\"https://dev-to-uploads.s3.amazonaws.com/uploads/articles/zbuqqc91vsxxr7xhsa5f.jpg\"\r\n          alt=\"\"\r\n          width=\"100%\"\r\n          marginBottom=\"50px\"\r\n        />\r\n      </Box>\r\n    </Grid>\r\n  );\r\n};\r\n\r\nexport default ImageGrid;\r\n", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\components\\home\\RecipeCard.jsx", [], [], "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\components\\Charts\\VegNonVeg.jsx", [], [], "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\components\\Charts\\MostLikes.jsx", ["416"], [], "import React from \"react\";\r\nimport { Bar } from \"react-chartjs-2\";\r\nimport { Chart as ChartJS } from \"chart.js/auto\";\r\n\r\nconst MostLikes = ({ recipe }) => {\r\n\r\n    const chartData = recipe.map((recipe) => ({\r\n        recipeName: recipe.title,\r\n        likes: recipe.likes.length,\r\n        comments: recipe.comments.length,\r\n      }));\r\n    \r\n      const labels = chartData.map((data) => data.recipeName);\r\n      const likesData = chartData.map((data) => data.likes);\r\n      const commentsData = chartData.map((data) => data.comments);\r\n    \r\n      const chartConfig = {\r\n        labels: labels,\r\n        datasets: [\r\n          {\r\n            label: \"Likes Count\",\r\n            data: likesData,\r\n            backgroundColor: \"rgba(214, 114, 13, 0.2)\",\r\n            borderColor: \"#4e3502\",\r\n            borderWidth: 1,\r\n          },\r\n          {\r\n            label: \"Comments Count\",\r\n            data: commentsData,\r\n            backgroundColor: \"rgba(12, 87, 208, 0.2)\",\r\n            borderColor: \"#051b6b\",\r\n            borderWidth: 1,\r\n          },\r\n        ],\r\n      };\r\n\r\n  return (\r\n    <div style={{ width: \"75%\", margin: \"auto\" }}>\r\n      <h1 style={{ fontWeight: \"bold\", fontSize : \"25px\", margin : \"30px 0\" }}>Recipes with Interactions</h1>\r\n      <Bar data={chartConfig} />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default MostLikes;\r\n", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\components\\Charts\\Cusines.jsx", [], [], "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\components\\Charts\\Contribution.jsx", ["417", "418"], [], "import React from \"react\";\r\nimport { Bar } from \"react-chartjs-2\";\r\nimport { Chart as ChartJS } from \"chart.js/auto\";\r\nimport { Text } from \"@chakra-ui/react\";\r\n\r\nconst Contribution = ({ user }) => {\r\n  const userData = user.map((user) => ({\r\n    username: user.name,\r\n    totalPosts: user.recipes.length,\r\n  }));\r\n\r\n  const labels = userData.map((user) => user.username);\r\n  const data = userData.map((user) => user.totalPosts);\r\n\r\n  const chartData = {\r\n    labels: labels,\r\n    datasets: [\r\n      {\r\n        label: \"Total Posts\",\r\n        data: data,\r\n        backgroundColor: \"rgba(75, 192, 192, 0.2)\",\r\n        borderColor: \"rgba(75, 192, 192, 1)\",\r\n        borderWidth: 1,\r\n      },\r\n    ],\r\n  };\r\n  return (\r\n    <div style={{ width: \"75%\", margin: \"auto\" }}>\r\n      <h1 style={{ fontWeight: \"bold\", fontSize : \"25px\", margin : \"30px 0\" }}>Contributions by all users</h1>\r\n      <Bar data={chartData} />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Contribution;", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\src\\components\\Feed\\Carousel.jsx", [], [], {"ruleId": "419", "replacedBy": "420"}, {"ruleId": "421", "severity": 1, "message": "422", "line": 11, "column": 3, "nodeType": "423", "messageId": "424", "endLine": 11, "endColumn": 14}, {"ruleId": "421", "severity": 1, "message": "425", "line": 11, "column": 8, "nodeType": "423", "messageId": "424", "endLine": 11, "endColumn": 13}, {"ruleId": "421", "severity": 1, "message": "426", "line": 35, "column": 9, "nodeType": "423", "messageId": "424", "endLine": 35, "endColumn": 20}, {"ruleId": "421", "severity": 1, "message": "427", "line": 36, "column": 9, "nodeType": "423", "messageId": "424", "endLine": 36, "endColumn": 20}, {"ruleId": "421", "severity": 1, "message": "428", "line": 172, "column": 9, "nodeType": "423", "messageId": "424", "endLine": 172, "endColumn": 16}, {"ruleId": "421", "severity": 1, "message": "426", "line": 174, "column": 9, "nodeType": "423", "messageId": "424", "endLine": 174, "endColumn": 20}, {"ruleId": "421", "severity": 1, "message": "427", "line": 175, "column": 9, "nodeType": "423", "messageId": "424", "endLine": 175, "endColumn": 20}, {"ruleId": "421", "severity": 1, "message": "429", "line": 5, "column": 3, "nodeType": "423", "messageId": "424", "endLine": 5, "endColumn": 18}, {"ruleId": "421", "severity": 1, "message": "430", "line": 6, "column": 3, "nodeType": "423", "messageId": "424", "endLine": 6, "endColumn": 20}, {"ruleId": "421", "severity": 1, "message": "431", "line": 7, "column": 3, "nodeType": "423", "messageId": "424", "endLine": 7, "endColumn": 20}, {"ruleId": "432", "severity": 1, "message": "433", "line": 63, "column": 52, "nodeType": "434", "messageId": "435", "endLine": 63, "endColumn": 54}, {"ruleId": "436", "severity": 1, "message": "437", "line": 64, "column": 20, "nodeType": "438", "messageId": "439", "endLine": 64, "endColumn": 22}, {"ruleId": "432", "severity": 1, "message": "433", "line": 68, "column": 45, "nodeType": "434", "messageId": "435", "endLine": 68, "endColumn": 47}, {"ruleId": "436", "severity": 1, "message": "437", "line": 69, "column": 20, "nodeType": "438", "messageId": "439", "endLine": 69, "endColumn": 22}, {"ruleId": "436", "severity": 1, "message": "440", "line": 97, "column": 22, "nodeType": "438", "messageId": "439", "endLine": 97, "endColumn": 24}, {"ruleId": "436", "severity": 1, "message": "440", "line": 64, "column": 32, "nodeType": "438", "messageId": "439", "endLine": 64, "endColumn": 34}, {"ruleId": "436", "severity": 1, "message": "440", "line": 125, "column": 26, "nodeType": "438", "messageId": "439", "endLine": 125, "endColumn": 28}, {"ruleId": "436", "severity": 1, "message": "437", "line": 130, "column": 35, "nodeType": "438", "messageId": "439", "endLine": 130, "endColumn": 37}, {"ruleId": "436", "severity": 1, "message": "440", "line": 156, "column": 26, "nodeType": "438", "messageId": "439", "endLine": 156, "endColumn": 28}, {"ruleId": "436", "severity": 1, "message": "437", "line": 160, "column": 35, "nodeType": "438", "messageId": "439", "endLine": 160, "endColumn": 37}, {"ruleId": "421", "severity": 1, "message": "441", "line": 8, "column": 3, "nodeType": "423", "messageId": "424", "endLine": 8, "endColumn": 23}, {"ruleId": "421", "severity": 1, "message": "442", "line": 9, "column": 3, "nodeType": "423", "messageId": "424", "endLine": 9, "endColumn": 20}, {"ruleId": "421", "severity": 1, "message": "443", "line": 8, "column": 3, "nodeType": "423", "messageId": "424", "endLine": 8, "endColumn": 8}, {"ruleId": "421", "severity": 1, "message": "444", "line": 10, "column": 3, "nodeType": "423", "messageId": "424", "endLine": 10, "endColumn": 11}, {"ruleId": "421", "severity": 1, "message": "445", "line": 14, "column": 22, "nodeType": "423", "messageId": "424", "endLine": 14, "endColumn": 35}, {"ruleId": "446", "severity": 1, "message": "447", "line": 32, "column": 6, "nodeType": "448", "endLine": 32, "endColumn": 8, "suggestions": "449"}, {"ruleId": "421", "severity": 1, "message": "450", "line": 6, "column": 3, "nodeType": "423", "messageId": "424", "endLine": 6, "endColumn": 10}, {"ruleId": "421", "severity": 1, "message": "451", "line": 15, "column": 23, "nodeType": "423", "messageId": "424", "endLine": 15, "endColumn": 37}, {"ruleId": "421", "severity": 1, "message": "452", "line": 21, "column": 10, "nodeType": "423", "messageId": "424", "endLine": 21, "endColumn": 21}, {"ruleId": "446", "severity": 1, "message": "453", "line": 70, "column": 6, "nodeType": "448", "endLine": 70, "endColumn": 8, "suggestions": "454"}, {"ruleId": "421", "severity": 1, "message": "455", "line": 4, "column": 3, "nodeType": "423", "messageId": "424", "endLine": 4, "endColumn": 7}, {"ruleId": "421", "severity": 1, "message": "456", "line": 36, "column": 10, "nodeType": "423", "messageId": "424", "endLine": 36, "endColumn": 23}, {"ruleId": "421", "severity": 1, "message": "457", "line": 39, "column": 8, "nodeType": "423", "messageId": "424", "endLine": 39, "endColumn": 16}, {"ruleId": "421", "severity": 1, "message": "458", "line": 40, "column": 10, "nodeType": "423", "messageId": "424", "endLine": 40, "endColumn": 16}, {"ruleId": "446", "severity": 1, "message": "459", "line": 117, "column": 6, "nodeType": "448", "endLine": 117, "endColumn": 14, "suggestions": "460"}, {"ruleId": "436", "severity": 1, "message": "437", "line": 283, "column": 26, "nodeType": "438", "messageId": "439", "endLine": 283, "endColumn": 28}, {"ruleId": "421", "severity": 1, "message": "461", "line": 12, "column": 3, "nodeType": "423", "messageId": "424", "endLine": 12, "endColumn": 9}, {"ruleId": "421", "severity": 1, "message": "462", "line": 34, "column": 3, "nodeType": "423", "messageId": "424", "endLine": 34, "endColumn": 10}, {"ruleId": "446", "severity": 1, "message": "463", "line": 88, "column": 6, "nodeType": "448", "endLine": 88, "endColumn": 8, "suggestions": "464"}, {"ruleId": "446", "severity": 1, "message": "465", "line": 109, "column": 6, "nodeType": "448", "endLine": 109, "endColumn": 18, "suggestions": "466"}, {"ruleId": "421", "severity": 1, "message": "467", "line": 8, "column": 3, "nodeType": "423", "messageId": "424", "endLine": 8, "endColumn": 11}, {"ruleId": "421", "severity": 1, "message": "468", "line": 21, "column": 3, "nodeType": "423", "messageId": "424", "endLine": 21, "endColumn": 13}, {"ruleId": "421", "severity": 1, "message": "469", "line": 29, "column": 10, "nodeType": "423", "messageId": "424", "endLine": 29, "endColumn": 39}, {"ruleId": "421", "severity": 1, "message": "470", "line": 30, "column": 10, "nodeType": "423", "messageId": "424", "endLine": 30, "endColumn": 25}, {"ruleId": "446", "severity": 1, "message": "471", "line": 63, "column": 6, "nodeType": "448", "endLine": 63, "endColumn": 8, "suggestions": "472"}, {"ruleId": "421", "severity": 1, "message": "473", "line": 3, "column": 5, "nodeType": "423", "messageId": "424", "endLine": 3, "endColumn": 11}, {"ruleId": "421", "severity": 1, "message": "461", "line": 12, "column": 5, "nodeType": "423", "messageId": "424", "endLine": 12, "endColumn": 11}, {"ruleId": "421", "severity": 1, "message": "474", "line": 19, "column": 5, "nodeType": "423", "messageId": "424", "endLine": 19, "endColumn": 18}, {"ruleId": "421", "severity": 1, "message": "475", "line": 20, "column": 5, "nodeType": "423", "messageId": "424", "endLine": 20, "endColumn": 10}, {"ruleId": "421", "severity": 1, "message": "476", "line": 21, "column": 5, "nodeType": "423", "messageId": "424", "endLine": 21, "endColumn": 17}, {"ruleId": "421", "severity": 1, "message": "477", "line": 22, "column": 5, "nodeType": "423", "messageId": "424", "endLine": 22, "endColumn": 17}, {"ruleId": "421", "severity": 1, "message": "478", "line": 23, "column": 5, "nodeType": "423", "messageId": "424", "endLine": 23, "endColumn": 16}, {"ruleId": "421", "severity": 1, "message": "422", "line": 24, "column": 5, "nodeType": "423", "messageId": "424", "endLine": 24, "endColumn": 16}, {"ruleId": "421", "severity": 1, "message": "479", "line": 25, "column": 5, "nodeType": "423", "messageId": "424", "endLine": 25, "endColumn": 14}, {"ruleId": "421", "severity": 1, "message": "480", "line": 26, "column": 5, "nodeType": "423", "messageId": "424", "endLine": 26, "endColumn": 21}, {"ruleId": "421", "severity": 1, "message": "481", "line": 27, "column": 5, "nodeType": "423", "messageId": "424", "endLine": 27, "endColumn": 13}, {"ruleId": "421", "severity": 1, "message": "482", "line": 28, "column": 5, "nodeType": "423", "messageId": "424", "endLine": 28, "endColumn": 20}, {"ruleId": "421", "severity": 1, "message": "483", "line": 32, "column": 5, "nodeType": "423", "messageId": "424", "endLine": 32, "endColumn": 18}, {"ruleId": "421", "severity": 1, "message": "484", "line": 33, "column": 5, "nodeType": "423", "messageId": "424", "endLine": 33, "endColumn": 13}, {"ruleId": "421", "severity": 1, "message": "462", "line": 34, "column": 5, "nodeType": "423", "messageId": "424", "endLine": 34, "endColumn": 12}, {"ruleId": "446", "severity": 1, "message": "485", "line": 72, "column": 8, "nodeType": "448", "endLine": 72, "endColumn": 10, "suggestions": "486"}, {"ruleId": "436", "severity": 1, "message": "437", "line": 74, "column": 18, "nodeType": "438", "messageId": "439", "endLine": 74, "endColumn": 20}, {"ruleId": "421", "severity": 1, "message": "487", "line": 200, "column": 12, "nodeType": "423", "messageId": "424", "endLine": 200, "endColumn": 28}, {"ruleId": "421", "severity": 1, "message": "488", "line": 45, "column": 10, "nodeType": "423", "messageId": "424", "endLine": 45, "endColumn": 16}, {"ruleId": "421", "severity": 1, "message": "489", "line": 103, "column": 9, "nodeType": "423", "messageId": "424", "endLine": 103, "endColumn": 14}, {"ruleId": "490", "severity": 1, "message": "491", "line": 187, "column": 13, "nodeType": "492", "endLine": 187, "endColumn": 16}, {"ruleId": "490", "severity": 1, "message": "491", "line": 201, "column": 13, "nodeType": "492", "endLine": 201, "endColumn": 29}, {"ruleId": "490", "severity": 1, "message": "491", "line": 219, "column": 13, "nodeType": "492", "endLine": 219, "endColumn": 16}, {"ruleId": "490", "severity": 1, "message": "491", "line": 233, "column": 13, "nodeType": "492", "endLine": 233, "endColumn": 16}, {"ruleId": "421", "severity": 1, "message": "493", "line": 757, "column": 7, "nodeType": "423", "messageId": "424", "endLine": 757, "endColumn": 13}, {"ruleId": "421", "severity": 1, "message": "494", "line": 11, "column": 3, "nodeType": "423", "messageId": "424", "endLine": 11, "endColumn": 14}, {"ruleId": "421", "severity": 1, "message": "495", "line": 12, "column": 3, "nodeType": "423", "messageId": "424", "endLine": 12, "endColumn": 18}, {"ruleId": "421", "severity": 1, "message": "496", "line": 13, "column": 3, "nodeType": "423", "messageId": "424", "endLine": 13, "endColumn": 21}, {"ruleId": "421", "severity": 1, "message": "497", "line": 14, "column": 3, "nodeType": "423", "messageId": "424", "endLine": 14, "endColumn": 20}, {"ruleId": "421", "severity": 1, "message": "498", "line": 15, "column": 3, "nodeType": "423", "messageId": "424", "endLine": 15, "endColumn": 20}, {"ruleId": "421", "severity": 1, "message": "499", "line": 16, "column": 3, "nodeType": "423", "messageId": "424", "endLine": 16, "endColumn": 21}, {"ruleId": "421", "severity": 1, "message": "500", "line": 42, "column": 10, "nodeType": "423", "messageId": "424", "endLine": 42, "endColumn": 16}, {"ruleId": "421", "severity": 1, "message": "501", "line": 44, "column": 10, "nodeType": "423", "messageId": "424", "endLine": 44, "endColumn": 19}, {"ruleId": "421", "severity": 1, "message": "502", "line": 44, "column": 21, "nodeType": "423", "messageId": "424", "endLine": 44, "endColumn": 36}, {"ruleId": "421", "severity": 1, "message": "503", "line": 46, "column": 10, "nodeType": "423", "messageId": "424", "endLine": 46, "endColumn": 26}, {"ruleId": "421", "severity": 1, "message": "504", "line": 48, "column": 10, "nodeType": "423", "messageId": "424", "endLine": 48, "endColumn": 16}, {"ruleId": "421", "severity": 1, "message": "505", "line": 49, "column": 21, "nodeType": "423", "messageId": "424", "endLine": 49, "endColumn": 31}, {"ruleId": "421", "severity": 1, "message": "506", "line": 49, "column": 33, "nodeType": "423", "messageId": "424", "endLine": 49, "endColumn": 39}, {"ruleId": "421", "severity": 1, "message": "507", "line": 50, "column": 10, "nodeType": "423", "messageId": "424", "endLine": 50, "endColumn": 21}, {"ruleId": "421", "severity": 1, "message": "508", "line": 26, "column": 3, "nodeType": "423", "messageId": "424", "endLine": 26, "endColumn": 18}, {"ruleId": "421", "severity": 1, "message": "509", "line": 29, "column": 3, "nodeType": "423", "messageId": "424", "endLine": 29, "endColumn": 13}, {"ruleId": "421", "severity": 1, "message": "510", "line": 32, "column": 3, "nodeType": "423", "messageId": "424", "endLine": 32, "endColumn": 12}, {"ruleId": "421", "severity": 1, "message": "511", "line": 34, "column": 3, "nodeType": "423", "messageId": "424", "endLine": 34, "endColumn": 11}, {"ruleId": "421", "severity": 1, "message": "512", "line": 105, "column": 9, "nodeType": "423", "messageId": "424", "endLine": 105, "endColumn": 30}, {"ruleId": "421", "severity": 1, "message": "513", "line": 114, "column": 9, "nodeType": "423", "messageId": "424", "endLine": 114, "endColumn": 27}, {"ruleId": "421", "severity": 1, "message": "514", "line": 3, "column": 23, "nodeType": "423", "messageId": "424", "endLine": 3, "endColumn": 27}, {"ruleId": "421", "severity": 1, "message": "473", "line": 8, "column": 3, "nodeType": "423", "messageId": "424", "endLine": 8, "endColumn": 9}, {"ruleId": "421", "severity": 1, "message": "515", "line": 13, "column": 3, "nodeType": "423", "messageId": "424", "endLine": 13, "endColumn": 7}, {"ruleId": "421", "severity": 1, "message": "516", "line": 14, "column": 3, "nodeType": "423", "messageId": "424", "endLine": 14, "endColumn": 14}, {"ruleId": "421", "severity": 1, "message": "517", "line": 24, "column": 9, "nodeType": "423", "messageId": "424", "endLine": 24, "endColumn": 17}, {"ruleId": "446", "severity": 1, "message": "518", "line": 78, "column": 6, "nodeType": "448", "endLine": 78, "endColumn": 20, "suggestions": "519"}, {"ruleId": "421", "severity": 1, "message": "520", "line": 14, "column": 3, "nodeType": "423", "messageId": "424", "endLine": 14, "endColumn": 27}, {"ruleId": "421", "severity": 1, "message": "521", "line": 15, "column": 3, "nodeType": "423", "messageId": "424", "endLine": 15, "endColumn": 29}, {"ruleId": "421", "severity": 1, "message": "522", "line": 17, "column": 3, "nodeType": "423", "messageId": "424", "endLine": 17, "endColumn": 27}, {"ruleId": "421", "severity": 1, "message": "523", "line": 18, "column": 3, "nodeType": "423", "messageId": "424", "endLine": 18, "endColumn": 29}, {"ruleId": "421", "severity": 1, "message": "441", "line": 22, "column": 3, "nodeType": "423", "messageId": "424", "endLine": 22, "endColumn": 23}, {"ruleId": "421", "severity": 1, "message": "442", "line": 23, "column": 3, "nodeType": "423", "messageId": "424", "endLine": 23, "endColumn": 20}, {"ruleId": "436", "severity": 1, "message": "437", "line": 44, "column": 16, "nodeType": "438", "messageId": "439", "endLine": 44, "endColumn": 18}, {"ruleId": "436", "severity": 1, "message": "437", "line": 52, "column": 23, "nodeType": "438", "messageId": "439", "endLine": 52, "endColumn": 25}, {"ruleId": "421", "severity": 1, "message": "524", "line": 112, "column": 11, "nodeType": "423", "messageId": "424", "endLine": 112, "endColumn": 19}, {"ruleId": "421", "severity": 1, "message": "429", "line": 5, "column": 3, "nodeType": "423", "messageId": "424", "endLine": 5, "endColumn": 18}, {"ruleId": "421", "severity": 1, "message": "430", "line": 6, "column": 3, "nodeType": "423", "messageId": "424", "endLine": 6, "endColumn": 20}, {"ruleId": "421", "severity": 1, "message": "431", "line": 7, "column": 3, "nodeType": "423", "messageId": "424", "endLine": 7, "endColumn": 20}, {"ruleId": "421", "severity": 1, "message": "525", "line": 11, "column": 3, "nodeType": "423", "messageId": "424", "endLine": 11, "endColumn": 24}, {"ruleId": "421", "severity": 1, "message": "526", "line": 3, "column": 10, "nodeType": "423", "messageId": "424", "endLine": 3, "endColumn": 15}, {"ruleId": "446", "severity": 1, "message": "527", "line": 22, "column": 6, "nodeType": "448", "endLine": 22, "endColumn": 16, "suggestions": "528"}, {"ruleId": "421", "severity": 1, "message": "461", "line": 11, "column": 3, "nodeType": "423", "messageId": "424", "endLine": 11, "endColumn": 9}, {"ruleId": "421", "severity": 1, "message": "529", "line": 21, "column": 3, "nodeType": "423", "messageId": "424", "endLine": 21, "endColumn": 9}, {"ruleId": "421", "severity": 1, "message": "516", "line": 25, "column": 18, "nodeType": "423", "messageId": "424", "endLine": 25, "endColumn": 29}, {"ruleId": "421", "severity": 1, "message": "530", "line": 25, "column": 31, "nodeType": "423", "messageId": "424", "endLine": 25, "endColumn": 42}, {"ruleId": "421", "severity": 1, "message": "531", "line": 28, "column": 10, "nodeType": "423", "messageId": "424", "endLine": 28, "endColumn": 23}, {"ruleId": "446", "severity": 1, "message": "532", "line": 192, "column": 6, "nodeType": "448", "endLine": 192, "endColumn": 15, "suggestions": "533"}, {"ruleId": "446", "severity": 1, "message": "534", "line": 203, "column": 6, "nodeType": "448", "endLine": 203, "endColumn": 14, "suggestions": "535"}, {"ruleId": "421", "severity": 1, "message": "536", "line": 6, "column": 3, "nodeType": "423", "messageId": "424", "endLine": 6, "endColumn": 8}, {"ruleId": "421", "severity": 1, "message": "537", "line": 7, "column": 3, "nodeType": "423", "messageId": "424", "endLine": 7, "endColumn": 13}, {"ruleId": "421", "severity": 1, "message": "538", "line": 8, "column": 3, "nodeType": "423", "messageId": "424", "endLine": 8, "endColumn": 19}, {"ruleId": "421", "severity": 1, "message": "443", "line": 9, "column": 3, "nodeType": "423", "messageId": "424", "endLine": 9, "endColumn": 8}, {"ruleId": "421", "severity": 1, "message": "450", "line": 10, "column": 3, "nodeType": "423", "messageId": "424", "endLine": 10, "endColumn": 10}, {"ruleId": "421", "severity": 1, "message": "444", "line": 11, "column": 3, "nodeType": "423", "messageId": "424", "endLine": 11, "endColumn": 11}, {"ruleId": "446", "severity": 1, "message": "447", "line": 30, "column": 6, "nodeType": "448", "endLine": 30, "endColumn": 8, "suggestions": "539"}, {"ruleId": "446", "severity": 1, "message": "447", "line": 21, "column": 6, "nodeType": "448", "endLine": 21, "endColumn": 8, "suggestions": "540"}, {"ruleId": "541", "severity": 1, "message": "542", "line": 38, "column": 13, "nodeType": "492", "messageId": "543", "endLine": 43, "endColumn": 15}, {"ruleId": "421", "severity": 1, "message": "544", "line": 3, "column": 18, "nodeType": "423", "messageId": "424", "endLine": 3, "endColumn": 24}, {"ruleId": "436", "severity": 1, "message": "440", "line": 78, "column": 24, "nodeType": "438", "messageId": "439", "endLine": 78, "endColumn": 26}, {"ruleId": "436", "severity": 1, "message": "440", "line": 81, "column": 32, "nodeType": "438", "messageId": "439", "endLine": 81, "endColumn": 34}, {"ruleId": "421", "severity": 1, "message": "545", "line": 237, "column": 9, "nodeType": "423", "messageId": "424", "endLine": 237, "endColumn": 29}, {"ruleId": "436", "severity": 1, "message": "437", "line": 410, "column": 39, "nodeType": "438", "messageId": "439", "endLine": 410, "endColumn": 41}, {"ruleId": "421", "severity": 1, "message": "443", "line": 7, "column": 3, "nodeType": "423", "messageId": "424", "endLine": 7, "endColumn": 8}, {"ruleId": "421", "severity": 1, "message": "546", "line": 13, "column": 9, "nodeType": "423", "messageId": "424", "endLine": 13, "endColumn": 19}, {"ruleId": "446", "severity": 1, "message": "447", "line": 23, "column": 6, "nodeType": "448", "endLine": 23, "endColumn": 8, "suggestions": "547"}, {"ruleId": "541", "severity": 1, "message": "548", "line": 66, "column": 13, "nodeType": "492", "messageId": "543", "endLine": 71, "endColumn": 15}, {"ruleId": "436", "severity": 1, "message": "437", "line": 9, "column": 29, "nodeType": "438", "messageId": "439", "endLine": 9, "endColumn": 31}, {"ruleId": "436", "severity": 1, "message": "437", "line": 14, "column": 22, "nodeType": "438", "messageId": "439", "endLine": 14, "endColumn": 24}, {"ruleId": "436", "severity": 1, "message": "437", "line": 19, "column": 24, "nodeType": "438", "messageId": "439", "endLine": 19, "endColumn": 26}, {"ruleId": "436", "severity": 1, "message": "437", "line": 27, "column": 30, "nodeType": "438", "messageId": "439", "endLine": 27, "endColumn": 32}, {"ruleId": "436", "severity": 1, "message": "437", "line": 28, "column": 29, "nodeType": "438", "messageId": "439", "endLine": 28, "endColumn": 31}, {"ruleId": "421", "severity": 1, "message": "549", "line": 1, "column": 16, "nodeType": "423", "messageId": "424", "endLine": 1, "endColumn": 24}, {"ruleId": "421", "severity": 1, "message": "550", "line": 1, "column": 38, "nodeType": "423", "messageId": "424", "endLine": 1, "endColumn": 45}, {"ruleId": "421", "severity": 1, "message": "551", "line": 3, "column": 19, "nodeType": "423", "messageId": "424", "endLine": 3, "endColumn": 26}, {"ruleId": "421", "severity": 1, "message": "551", "line": 3, "column": 19, "nodeType": "423", "messageId": "424", "endLine": 3, "endColumn": 26}, {"ruleId": "421", "severity": 1, "message": "552", "line": 4, "column": 10, "nodeType": "423", "messageId": "424", "endLine": 4, "endColumn": 14}, "no-new-object", ["553"], "no-unused-vars", "'ModalFooter' is defined but never used.", "Identifier", "unusedVar", "'Admin' is defined but never used.", "'borderColor' is assigned a value but never used.", "'signInColor' is assigned a value but never used.", "'bgColor' is assigned a value but never used.", "'GETRECIPE_ERROR' is defined but never used.", "'GETRECIPE_LOADING' is defined but never used.", "'GETRECIPE_SUCCESS' is defined but never used.", "array-callback-return", "Array.prototype.map() expects a return value from arrow function.", "ArrowFunctionExpression", "expectedInside", "eqeqeq", "Expected '===' and instead saw '=='.", "BinaryExpression", "unexpected", "Expected '!==' and instead saw '!='.", "'POST_DISLIKE_SUCCESS' is defined but never used.", "'POST_LIKE_SUCCESS' is defined but never used.", "'Stack' is defined but never used.", "'useToast' is defined but never used.", "'MiniCard_Chef' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'dispatch' and 'token'. Either include them or remove the dependency array.", "ArrayExpression", ["554"], "'Divider' is defined but never used.", "'getUserRecipes' is defined but never used.", "'useNavigate' is defined but never used.", "React Hook useEffect has missing dependencies: 'dispatch', 'toast', 'token', and 'user'. Either include them or remove the dependency array.", ["555"], "'Grid' is defined but never used.", "'getAllRecipes' is defined but never used.", "'FeedCard' is defined but never used.", "'BiLike' is defined but never used.", "React Hook useEffect has missing dependencies: 'impression', 'selectedCuisines', 'selectedOption', and 'token'. Either include them or remove the dependency array.", ["556"], "'HStack' is defined but never used.", "'Heading' is defined but never used.", "React Hook useEffect has missing dependencies: 'dispatch', 'toast', and 'token'. Either include them or remove the dependency array.", ["557"], "React Hook useEffect has a missing dependency: 'token'. Either include it or remove the dependency array.", ["558"], "'Checkbox' is defined but never used.", "'SimpleGrid' is defined but never used.", "'getUserDetailsForSingleRecipe' is defined but never used.", "'getSingleRecipe' is defined but never used.", "React Hook useEffect has missing dependencies: 'postId' and 'token'. Either include them or remove the dependency array.", ["559"], "'Button' is defined but never used.", "'useDisclosure' is defined but never used.", "'Modal' is defined but never used.", "'ModalOverlay' is defined but never used.", "'ModalContent' is defined but never used.", "'ModalHeader' is defined but never used.", "'ModalBody' is defined but never used.", "'ModalCloseButton' is defined but never used.", "'Editable' is defined but never used.", "'EditablePreview' is defined but never used.", "'EditableInput' is defined but never used.", "'Textarea' is defined but never used.", "React Hook useEffect has a missing dependency: 'userId'. Either include it or remove the dependency array.", ["560"], "'EditableControls' is defined but never used.", "'Reveal' is defined but never used.", "'token' is assigned a value but never used.", "jsx-a11y/anchor-is-valid", "The href attribute is required for an anchor to be keyboard accessible. Provide a valid, navigable address as the href value. If you cannot provide an href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "'LOADER' is assigned a value but never used.", "'AlertDialog' is defined but never used.", "'AlertDialogBody' is defined but never used.", "'AlertDialogContent' is defined but never used.", "'AlertDialogFooter' is defined but never used.", "'AlertDialogHeader' is defined but never used.", "'AlertDialogOverlay' is defined but never used.", "'FaBell' is defined but never used.", "'BsFolder2' is defined but never used.", "'BsCalendarCheck' is defined but never used.", "'RiFlashlightFill' is defined but never used.", "'styled' is defined but never used.", "'useReducer' is defined but never used.", "'useRef' is defined but never used.", "'useSelector' is defined but never used.", "'StepDescription' is defined but never used.", "'StepNumber' is defined but never used.", "'StepTitle' is defined but never used.", "'useSteps' is defined but never used.", "'handleArrayItemChange' is assigned a value but never used.", "'handleAddArrayItem' is assigned a value but never used.", "'Link' is defined but never used.", "'Icon' is defined but never used.", "'AvatarBadge' is defined but never used.", "'navigate' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchNotifications'. Either include it or remove the dependency array.", ["561"], "'POST_ACCEPTREQUEST_ERROR' is defined but never used.", "'POST_ACCEPTREQUEST_LOADING' is defined but never used.", "'POST_REJECTREQUEST_ERROR' is defined but never used.", "'POST_REJECTREQUEST_LOADING' is defined but never used.", "'response' is assigned a value but never used.", "'UPDATE_RECIPE_SUCCESS' is defined but never used.", "'Image' is defined but never used.", "React Hook React.useEffect has missing dependencies: 'mainControls' and 'slideControls'. Either include them or remove the dependency array.", ["562"], "'VStack' is defined but never used.", "'AvatarGroup' is defined but never used.", "'AiOutlineUser' is defined but never used.", "React Hook useEffect has missing dependencies: 'friend._id' and 'user._id'. Either include them or remove the dependency array.", ["563"], "React Hook useEffect has missing dependencies: 'friend._id' and 'user._id'. Either include them or remove the dependency array. Outer scope values like 'socket' aren't valid dependencies because mutating them doesn't re-render the component.", ["564"], "'Input' is defined but never used.", "'InputGroup' is defined but never used.", "'InputLeftElement' is defined but never used.", ["565"], ["566"], "react/jsx-pascal-case", "Imported JSX component MiniCard_Friends must be in PascalCase or SCREAMING_SNAKE_CASE", "usePascalOrSnakeCase", "'BiChat' is defined but never used.", "'deleteCommentHandler' is assigned a value but never used.", "'nonFriends' is assigned a value but never used.", ["567"], "Imported JSX component MiniCard_Request must be in PascalCase or SCREAMING_SNAKE_CASE", "'GridItem' is defined but never used.", "'flexbox' is defined but never used.", "'ChartJS' is defined but never used.", "'Text' is defined but never used.", "no-object-constructor", {"desc": "568", "fix": "569"}, {"desc": "570", "fix": "571"}, {"desc": "572", "fix": "573"}, {"desc": "574", "fix": "575"}, {"desc": "576", "fix": "577"}, {"desc": "578", "fix": "579"}, {"desc": "580", "fix": "581"}, {"desc": "582", "fix": "583"}, {"desc": "584", "fix": "585"}, {"desc": "586", "fix": "587"}, {"desc": "588", "fix": "589"}, {"desc": "568", "fix": "590"}, {"desc": "568", "fix": "591"}, {"desc": "568", "fix": "592"}, "Update the dependencies array to be: [dispatch, token]", {"range": "593", "text": "594"}, "Update the dependencies array to be: [dispatch, toast, token, user]", {"range": "595", "text": "596"}, "Update the dependencies array to be: [filter, impression, selectedCuisines, selectedOption, token]", {"range": "597", "text": "598"}, "Update the dependencies array to be: [dispatch, toast, token]", {"range": "599", "text": "600"}, "Update the dependencies array to be: [showRecipe, token]", {"range": "601", "text": "602"}, "Update the dependencies array to be: [postId, token]", {"range": "603", "text": "604"}, "Update the dependencies array to be: [userId]", {"range": "605", "text": "606"}, "Update the dependencies array to be: [token, toast, fetchNotifications]", {"range": "607", "text": "608"}, "Update the dependencies array to be: [isInView, mainControls, slideControls]", {"range": "609", "text": "610"}, "Update the dependencies array to be: [friend._id, refresh, user._id]", {"range": "611", "text": "612"}, "Update the dependencies array to be: [friend._id, user._id]", {"range": "613", "text": "614"}, {"range": "615", "text": "594"}, {"range": "616", "text": "594"}, {"range": "617", "text": "594"}, [972, 974], "[dispatch, token]", [2307, 2309], "[dispatch, toast, token, user]", [3269, 3277], "[filter, impression, selectedCuisines, selectedOption, token]", [2412, 2414], "[dispatch, toast, token]", [2976, 2988], "[show<PERSON><PERSON><PERSON><PERSON>, token]", [1493, 1495], "[postId, token]", [1748, 1750], "[userId]", [2201, 2215], "[token, toast, fetchNotifications]", [546, 556], "[isInView, mainControls, slideControls]", [5096, 5105], "[friend._id, refresh, user._id]", [5427, 5435], "[friend._id, user._id]", [765, 767], [774, 776], [911, 913]]