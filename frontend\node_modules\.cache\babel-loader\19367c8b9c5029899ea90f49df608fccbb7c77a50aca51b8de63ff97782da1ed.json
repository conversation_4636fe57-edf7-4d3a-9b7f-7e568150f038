{"ast": null, "code": "'use strict';\n\nvar uncurryThis = require('../internals/function-uncurry-this');\nmodule.exports = uncurryThis({}.isPrototypeOf);", "map": {"version": 3, "names": ["uncurryThis", "require", "module", "exports", "isPrototypeOf"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/core-js-pure/internals/object-is-prototype-of.js"], "sourcesContent": ["'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this');\n\nmodule.exports = uncurryThis({}.isPrototypeOf);\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,WAAW,GAAGC,OAAO,CAAC,oCAAoC,CAAC;AAE/DC,MAAM,CAACC,OAAO,GAAGH,WAAW,CAAC,CAAC,CAAC,CAACI,aAAa,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}