{"ast": null, "code": "import { assignRef } from './assignRef';\nimport { useCallbackRef } from './useRef';\n/**\n * Create a _lense_ on Ref, making it possible to transform ref value\n * @param {ReactRef} ref\n * @param {Function} transformer. 👉 Ref would be __NOT updated__ on `transformer` update.\n * @returns {RefObject}\n *\n * @see https://github.com/theKashey/use-callback-ref#usetransformref-to-replace-reactuseimperativehandle\n * @example\n *\n * const ResizableWithRef = forwardRef((props, ref) =>\n *  <Resizable {...props} ref={useTransformRef(ref, i => i ? i.resizable : null)}/>\n * );\n */\nexport function useTransformRef(ref, transformer) {\n  return useCallbackRef(null, function (value) {\n    return assignRef(ref, transformer(value));\n  });\n}", "map": {"version": 3, "names": ["assignRef", "useCallbackRef", "useTransformRef", "ref", "transformer", "value"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/use-callback-ref/dist/es2015/useTransformRef.js"], "sourcesContent": ["import { assignRef } from './assignRef';\nimport { useCallbackRef } from './useRef';\n/**\n * Create a _lense_ on Ref, making it possible to transform ref value\n * @param {ReactRef} ref\n * @param {Function} transformer. 👉 Ref would be __NOT updated__ on `transformer` update.\n * @returns {RefObject}\n *\n * @see https://github.com/theKashey/use-callback-ref#usetransformref-to-replace-reactuseimperativehandle\n * @example\n *\n * const ResizableWithRef = forwardRef((props, ref) =>\n *  <Resizable {...props} ref={useTransformRef(ref, i => i ? i.resizable : null)}/>\n * );\n */\nexport function useTransformRef(ref, transformer) {\n    return useCallbackRef(null, function (value) { return assignRef(ref, transformer(value)); });\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,aAAa;AACvC,SAASC,cAAc,QAAQ,UAAU;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,eAAeA,CAACC,GAAG,EAAEC,WAAW,EAAE;EAC9C,OAAOH,cAAc,CAAC,IAAI,EAAE,UAAUI,KAAK,EAAE;IAAE,OAAOL,SAAS,CAACG,GAAG,EAAEC,WAAW,CAACC,KAAK,CAAC,CAAC;EAAE,CAAC,CAAC;AAChG"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}