{"ast": null, "code": "export var focusOn = function (target, focusOptions) {\n  if ('focus' in target) {\n    target.focus(focusOptions);\n  }\n  if ('contentWindow' in target && target.contentWindow) {\n    target.contentWindow.focus();\n  }\n};", "map": {"version": 3, "names": ["focusOn", "target", "focusOptions", "focus", "contentWindow"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/focus-lock/dist/es2015/commands.js"], "sourcesContent": ["export var focusOn = function (target, focusOptions) {\n    if ('focus' in target) {\n        target.focus(focusOptions);\n    }\n    if ('contentWindow' in target && target.contentWindow) {\n        target.contentWindow.focus();\n    }\n};\n"], "mappings": "AAAA,OAAO,IAAIA,OAAO,GAAG,SAAAA,CAAUC,MAAM,EAAEC,YAAY,EAAE;EACjD,IAAI,OAAO,IAAID,MAAM,EAAE;IACnBA,MAAM,CAACE,KAAK,CAACD,YAAY,CAAC;EAC9B;EACA,IAAI,eAAe,IAAID,MAAM,IAAIA,MAAM,CAACG,aAAa,EAAE;IACnDH,MAAM,CAACG,aAAa,CAACD,KAAK,CAAC,CAAC;EAChC;AACJ,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}