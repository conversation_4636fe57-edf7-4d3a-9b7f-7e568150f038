{"ast": null, "code": "import { invariant } from '../../utils/errors.mjs';\nimport * as React from 'react';\nimport { forwardRef, useRef, useEffect } from 'react';\nimport { ReorderContext } from '../../context/ReorderContext.mjs';\nimport { motion } from '../../render/dom/motion.mjs';\nimport { useConstant } from '../../utils/use-constant.mjs';\nimport { checkReorder } from './utils/check-reorder.mjs';\nfunction ReorderGroup({\n  children,\n  as = \"ul\",\n  axis = \"y\",\n  onReorder,\n  values,\n  ...props\n}, externalRef) {\n  const Component = useConstant(() => motion(as));\n  const order = [];\n  const isReordering = useRef(false);\n  invariant(Boolean(values), \"Reorder.Group must be provided a values prop\");\n  const context = {\n    axis,\n    registerItem: (value, layout) => {\n      /**\n       * Ensure entries can't add themselves more than once\n       */\n      if (layout && order.findIndex(entry => value === entry.value) === -1) {\n        order.push({\n          value,\n          layout: layout[axis]\n        });\n        order.sort(compareMin);\n      }\n    },\n    updateOrder: (id, offset, velocity) => {\n      if (isReordering.current) return;\n      const newOrder = checkReorder(order, id, offset, velocity);\n      if (order !== newOrder) {\n        isReordering.current = true;\n        onReorder(newOrder.map(getValue).filter(value => values.indexOf(value) !== -1));\n      }\n    }\n  };\n  useEffect(() => {\n    isReordering.current = false;\n  });\n  return React.createElement(Component, {\n    ...props,\n    ref: externalRef,\n    ignoreStrict: true\n  }, React.createElement(ReorderContext.Provider, {\n    value: context\n  }, children));\n}\nconst Group = forwardRef(ReorderGroup);\nfunction getValue(item) {\n  return item.value;\n}\nfunction compareMin(a, b) {\n  return a.layout.min - b.layout.min;\n}\nexport { Group, ReorderGroup };", "map": {"version": 3, "names": ["invariant", "React", "forwardRef", "useRef", "useEffect", "ReorderContext", "motion", "useConstant", "check<PERSON>eor<PERSON>", "ReorderGroup", "children", "as", "axis", "onReorder", "values", "props", "externalRef", "Component", "order", "isReordering", "Boolean", "context", "registerItem", "value", "layout", "findIndex", "entry", "push", "sort", "compareMin", "updateOrder", "id", "offset", "velocity", "current", "newOrder", "map", "getValue", "filter", "indexOf", "createElement", "ref", "ignoreStrict", "Provider", "Group", "item", "a", "b", "min"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/framer-motion/dist/es/components/Reorder/Group.mjs"], "sourcesContent": ["import { invariant } from '../../utils/errors.mjs';\nimport * as React from 'react';\nimport { forwardRef, useRef, useEffect } from 'react';\nimport { ReorderContext } from '../../context/ReorderContext.mjs';\nimport { motion } from '../../render/dom/motion.mjs';\nimport { useConstant } from '../../utils/use-constant.mjs';\nimport { checkReorder } from './utils/check-reorder.mjs';\n\nfunction ReorderGroup({ children, as = \"ul\", axis = \"y\", onReorder, values, ...props }, externalRef) {\n    const Component = useConstant(() => motion(as));\n    const order = [];\n    const isReordering = useRef(false);\n    invariant(Boolean(values), \"Reorder.Group must be provided a values prop\");\n    const context = {\n        axis,\n        registerItem: (value, layout) => {\n            /**\n             * Ensure entries can't add themselves more than once\n             */\n            if (layout &&\n                order.findIndex((entry) => value === entry.value) === -1) {\n                order.push({ value, layout: layout[axis] });\n                order.sort(compareMin);\n            }\n        },\n        updateOrder: (id, offset, velocity) => {\n            if (isReordering.current)\n                return;\n            const newOrder = checkReorder(order, id, offset, velocity);\n            if (order !== newOrder) {\n                isReordering.current = true;\n                onReorder(newOrder\n                    .map(getValue)\n                    .filter((value) => values.indexOf(value) !== -1));\n            }\n        },\n    };\n    useEffect(() => {\n        isReordering.current = false;\n    });\n    return (React.createElement(Component, { ...props, ref: externalRef, ignoreStrict: true },\n        React.createElement(ReorderContext.Provider, { value: context }, children)));\n}\nconst Group = forwardRef(ReorderGroup);\nfunction getValue(item) {\n    return item.value;\n}\nfunction compareMin(a, b) {\n    return a.layout.min - b.layout.min;\n}\n\nexport { Group, ReorderGroup };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,wBAAwB;AAClD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AACrD,SAASC,cAAc,QAAQ,kCAAkC;AACjE,SAASC,MAAM,QAAQ,6BAA6B;AACpD,SAASC,WAAW,QAAQ,8BAA8B;AAC1D,SAASC,YAAY,QAAQ,2BAA2B;AAExD,SAASC,YAAYA,CAAC;EAAEC,QAAQ;EAAEC,EAAE,GAAG,IAAI;EAAEC,IAAI,GAAG,GAAG;EAAEC,SAAS;EAAEC,MAAM;EAAE,GAAGC;AAAM,CAAC,EAAEC,WAAW,EAAE;EACjG,MAAMC,SAAS,GAAGV,WAAW,CAAC,MAAMD,MAAM,CAACK,EAAE,CAAC,CAAC;EAC/C,MAAMO,KAAK,GAAG,EAAE;EAChB,MAAMC,YAAY,GAAGhB,MAAM,CAAC,KAAK,CAAC;EAClCH,SAAS,CAACoB,OAAO,CAACN,MAAM,CAAC,EAAE,8CAA8C,CAAC;EAC1E,MAAMO,OAAO,GAAG;IACZT,IAAI;IACJU,YAAY,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;MAC7B;AACZ;AACA;MACY,IAAIA,MAAM,IACNN,KAAK,CAACO,SAAS,CAAEC,KAAK,IAAKH,KAAK,KAAKG,KAAK,CAACH,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;QAC1DL,KAAK,CAACS,IAAI,CAAC;UAAEJ,KAAK;UAAEC,MAAM,EAAEA,MAAM,CAACZ,IAAI;QAAE,CAAC,CAAC;QAC3CM,KAAK,CAACU,IAAI,CAACC,UAAU,CAAC;MAC1B;IACJ,CAAC;IACDC,WAAW,EAAEA,CAACC,EAAE,EAAEC,MAAM,EAAEC,QAAQ,KAAK;MACnC,IAAId,YAAY,CAACe,OAAO,EACpB;MACJ,MAAMC,QAAQ,GAAG3B,YAAY,CAACU,KAAK,EAAEa,EAAE,EAAEC,MAAM,EAAEC,QAAQ,CAAC;MAC1D,IAAIf,KAAK,KAAKiB,QAAQ,EAAE;QACpBhB,YAAY,CAACe,OAAO,GAAG,IAAI;QAC3BrB,SAAS,CAACsB,QAAQ,CACbC,GAAG,CAACC,QAAQ,CAAC,CACbC,MAAM,CAAEf,KAAK,IAAKT,MAAM,CAACyB,OAAO,CAAChB,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;MACzD;IACJ;EACJ,CAAC;EACDnB,SAAS,CAAC,MAAM;IACZe,YAAY,CAACe,OAAO,GAAG,KAAK;EAChC,CAAC,CAAC;EACF,OAAQjC,KAAK,CAACuC,aAAa,CAACvB,SAAS,EAAE;IAAE,GAAGF,KAAK;IAAE0B,GAAG,EAAEzB,WAAW;IAAE0B,YAAY,EAAE;EAAK,CAAC,EACrFzC,KAAK,CAACuC,aAAa,CAACnC,cAAc,CAACsC,QAAQ,EAAE;IAAEpB,KAAK,EAAEF;EAAQ,CAAC,EAAEX,QAAQ,CAAC,CAAC;AACnF;AACA,MAAMkC,KAAK,GAAG1C,UAAU,CAACO,YAAY,CAAC;AACtC,SAAS4B,QAAQA,CAACQ,IAAI,EAAE;EACpB,OAAOA,IAAI,CAACtB,KAAK;AACrB;AACA,SAASM,UAAUA,CAACiB,CAAC,EAAEC,CAAC,EAAE;EACtB,OAAOD,CAAC,CAACtB,MAAM,CAACwB,GAAG,GAAGD,CAAC,CAACvB,MAAM,CAACwB,GAAG;AACtC;AAEA,SAASJ,KAAK,EAAEnC,YAAY"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}