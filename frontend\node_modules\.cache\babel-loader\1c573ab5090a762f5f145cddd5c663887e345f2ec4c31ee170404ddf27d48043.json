{"ast": null, "code": "// src/assertion.ts\nfunction isMouseEvent(event) {\n  const win = getEventWindow(event);\n  if (typeof win.PointerEvent !== \"undefined\" && event instanceof win.PointerEvent) {\n    return !!(event.pointerType === \"mouse\");\n  }\n  return event instanceof win.MouseEvent;\n}\nfunction isTouchEvent(event) {\n  const hasTouches = !!event.touches;\n  return hasTouches;\n}\nfunction isMultiTouchEvent(event) {\n  return isTouchEvent(event) && event.touches.length > 1;\n}\nfunction getEventWindow(event) {\n  var _a;\n  return (_a = event.view) != null ? _a : window;\n}\nexport { isMouseEvent, isTouchEvent, isMultiTouchEvent, getEventWindow };", "map": {"version": 3, "names": ["isMouseEvent", "event", "win", "getEventWindow", "PointerEvent", "pointerType", "MouseEvent", "isTouchEvent", "hasTouches", "touches", "isMultiTouchEvent", "length", "_a", "view", "window"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/@chakra-ui/event-utils/dist/chunk-B7KYFEHM.mjs"], "sourcesContent": ["// src/assertion.ts\nfunction isMouseEvent(event) {\n  const win = getEventWindow(event);\n  if (typeof win.PointerEvent !== \"undefined\" && event instanceof win.PointerEvent) {\n    return !!(event.pointerType === \"mouse\");\n  }\n  return event instanceof win.MouseEvent;\n}\nfunction isTouchEvent(event) {\n  const hasTouches = !!event.touches;\n  return hasTouches;\n}\nfunction isMultiTouchEvent(event) {\n  return isTouchEvent(event) && event.touches.length > 1;\n}\nfunction getEventWindow(event) {\n  var _a;\n  return (_a = event.view) != null ? _a : window;\n}\n\nexport {\n  isMouseEvent,\n  isTouchEvent,\n  isMultiTouchEvent,\n  getEventWindow\n};\n"], "mappings": "AAAA;AACA,SAASA,YAAYA,CAACC,KAAK,EAAE;EAC3B,MAAMC,GAAG,GAAGC,cAAc,CAACF,KAAK,CAAC;EACjC,IAAI,OAAOC,GAAG,CAACE,YAAY,KAAK,WAAW,IAAIH,KAAK,YAAYC,GAAG,CAACE,YAAY,EAAE;IAChF,OAAO,CAAC,EAAEH,KAAK,CAACI,WAAW,KAAK,OAAO,CAAC;EAC1C;EACA,OAAOJ,KAAK,YAAYC,GAAG,CAACI,UAAU;AACxC;AACA,SAASC,YAAYA,CAACN,KAAK,EAAE;EAC3B,MAAMO,UAAU,GAAG,CAAC,CAACP,KAAK,CAACQ,OAAO;EAClC,OAAOD,UAAU;AACnB;AACA,SAASE,iBAAiBA,CAACT,KAAK,EAAE;EAChC,OAAOM,YAAY,CAACN,KAAK,CAAC,IAAIA,KAAK,CAACQ,OAAO,CAACE,MAAM,GAAG,CAAC;AACxD;AACA,SAASR,cAAcA,CAACF,KAAK,EAAE;EAC7B,IAAIW,EAAE;EACN,OAAO,CAACA,EAAE,GAAGX,KAAK,CAACY,IAAI,KAAK,IAAI,GAAGD,EAAE,GAAGE,MAAM;AAChD;AAEA,SACEd,YAAY,EACZO,YAAY,EACZG,iBAAiB,EACjBP,cAAc"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}