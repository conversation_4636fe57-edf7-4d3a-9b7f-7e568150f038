{"ast": null, "code": "function trackElementSize(element, callback) {\n  if (!element) {\n    callback(void 0);\n    return;\n  }\n  callback({\n    width: element.offsetWidth,\n    height: element.offsetHeight\n  });\n  const win = element.ownerDocument.defaultView ?? window;\n  const observer = new win.ResizeObserver(entries => {\n    if (!Array.isArray(entries) || !entries.length) return;\n    const [entry] = entries;\n    let width;\n    let height;\n    if (\"borderBoxSize\" in entry) {\n      const borderSizeEntry = entry[\"borderBoxSize\"];\n      const borderSize = Array.isArray(borderSizeEntry) ? borderSizeEntry[0] : borderSizeEntry;\n      width = borderSize[\"inlineSize\"];\n      height = borderSize[\"blockSize\"];\n    } else {\n      width = element.offsetWidth;\n      height = element.offsetHeight;\n    }\n    callback({\n      width,\n      height\n    });\n  });\n  observer.observe(element, {\n    box: \"border-box\"\n  });\n  return () => observer.unobserve(element);\n}\nexport { trackElementSize };", "map": {"version": 3, "names": ["trackElementSize", "element", "callback", "width", "offsetWidth", "height", "offsetHeight", "win", "ownerDocument", "defaultView", "window", "observer", "ResizeObserver", "entries", "Array", "isArray", "length", "entry", "borderSizeEntry", "borderSize", "observe", "box", "unobserve"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/@zag-js/element-size/dist/track-size.mjs"], "sourcesContent": ["function trackElementSize(element, callback) {\n  if (!element) {\n    callback(void 0);\n    return;\n  }\n  callback({ width: element.offsetWidth, height: element.offsetHeight });\n  const win = element.ownerDocument.defaultView ?? window;\n  const observer = new win.ResizeObserver((entries) => {\n    if (!Array.isArray(entries) || !entries.length)\n      return;\n    const [entry] = entries;\n    let width;\n    let height;\n    if (\"borderBoxSize\" in entry) {\n      const borderSizeEntry = entry[\"borderBoxSize\"];\n      const borderSize = Array.isArray(borderSizeEntry) ? borderSizeEntry[0] : borderSizeEntry;\n      width = borderSize[\"inlineSize\"];\n      height = borderSize[\"blockSize\"];\n    } else {\n      width = element.offsetWidth;\n      height = element.offsetHeight;\n    }\n    callback({ width, height });\n  });\n  observer.observe(element, { box: \"border-box\" });\n  return () => observer.unobserve(element);\n}\n\nexport { trackElementSize };\n"], "mappings": "AAAA,SAASA,gBAAgBA,CAACC,OAAO,EAAEC,QAAQ,EAAE;EAC3C,IAAI,CAACD,OAAO,EAAE;IACZC,QAAQ,CAAC,KAAK,CAAC,CAAC;IAChB;EACF;EACAA,QAAQ,CAAC;IAAEC,KAAK,EAAEF,OAAO,CAACG,WAAW;IAAEC,MAAM,EAAEJ,OAAO,CAACK;EAAa,CAAC,CAAC;EACtE,MAAMC,GAAG,GAAGN,OAAO,CAACO,aAAa,CAACC,WAAW,IAAIC,MAAM;EACvD,MAAMC,QAAQ,GAAG,IAAIJ,GAAG,CAACK,cAAc,CAAEC,OAAO,IAAK;IACnD,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,OAAO,CAAC,IAAI,CAACA,OAAO,CAACG,MAAM,EAC5C;IACF,MAAM,CAACC,KAAK,CAAC,GAAGJ,OAAO;IACvB,IAAIV,KAAK;IACT,IAAIE,MAAM;IACV,IAAI,eAAe,IAAIY,KAAK,EAAE;MAC5B,MAAMC,eAAe,GAAGD,KAAK,CAAC,eAAe,CAAC;MAC9C,MAAME,UAAU,GAAGL,KAAK,CAACC,OAAO,CAACG,eAAe,CAAC,GAAGA,eAAe,CAAC,CAAC,CAAC,GAAGA,eAAe;MACxFf,KAAK,GAAGgB,UAAU,CAAC,YAAY,CAAC;MAChCd,MAAM,GAAGc,UAAU,CAAC,WAAW,CAAC;IAClC,CAAC,MAAM;MACLhB,KAAK,GAAGF,OAAO,CAACG,WAAW;MAC3BC,MAAM,GAAGJ,OAAO,CAACK,YAAY;IAC/B;IACAJ,QAAQ,CAAC;MAAEC,KAAK;MAAEE;IAAO,CAAC,CAAC;EAC7B,CAAC,CAAC;EACFM,QAAQ,CAACS,OAAO,CAACnB,OAAO,EAAE;IAAEoB,GAAG,EAAE;EAAa,CAAC,CAAC;EAChD,OAAO,MAAMV,QAAQ,CAACW,SAAS,CAACrB,OAAO,CAAC;AAC1C;AAEA,SAASD,gBAAgB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}