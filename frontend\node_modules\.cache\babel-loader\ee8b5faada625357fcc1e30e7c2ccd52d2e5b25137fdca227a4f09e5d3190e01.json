{"ast": null, "code": "import { isKeyframesTarget } from '../../../animation/utils/is-keyframes-target.mjs';\nimport { invariant } from '../../../utils/errors.mjs';\nimport { transformPropOrder } from '../../html/utils/transform.mjs';\nimport { findDimensionValueType } from '../value-types/dimensions.mjs';\nimport { isBrowser } from '../../../utils/is-browser.mjs';\nimport { number } from '../../../value/types/numbers/index.mjs';\nimport { px } from '../../../value/types/numbers/units.mjs';\nconst positionalKeys = new Set([\"width\", \"height\", \"top\", \"left\", \"right\", \"bottom\", \"x\", \"y\", \"translateX\", \"translateY\"]);\nconst isPositionalKey = key => positionalKeys.has(key);\nconst hasPositionalKey = target => {\n  return Object.keys(target).some(isPositionalKey);\n};\nconst isNumOrPxType = v => v === number || v === px;\nconst getPosFromMatrix = (matrix, pos) => parseFloat(matrix.split(\", \")[pos]);\nconst getTranslateFromMatrix = (pos2, pos3) => (_bbox, {\n  transform\n}) => {\n  if (transform === \"none\" || !transform) return 0;\n  const matrix3d = transform.match(/^matrix3d\\((.+)\\)$/);\n  if (matrix3d) {\n    return getPosFromMatrix(matrix3d[1], pos3);\n  } else {\n    const matrix = transform.match(/^matrix\\((.+)\\)$/);\n    if (matrix) {\n      return getPosFromMatrix(matrix[1], pos2);\n    } else {\n      return 0;\n    }\n  }\n};\nconst transformKeys = new Set([\"x\", \"y\", \"z\"]);\nconst nonTranslationalTransformKeys = transformPropOrder.filter(key => !transformKeys.has(key));\nfunction removeNonTranslationalTransform(visualElement) {\n  const removedTransforms = [];\n  nonTranslationalTransformKeys.forEach(key => {\n    const value = visualElement.getValue(key);\n    if (value !== undefined) {\n      removedTransforms.push([key, value.get()]);\n      value.set(key.startsWith(\"scale\") ? 1 : 0);\n    }\n  });\n  // Apply changes to element before measurement\n  if (removedTransforms.length) visualElement.render();\n  return removedTransforms;\n}\nconst positionalValues = {\n  // Dimensions\n  width: ({\n    x\n  }, {\n    paddingLeft = \"0\",\n    paddingRight = \"0\"\n  }) => x.max - x.min - parseFloat(paddingLeft) - parseFloat(paddingRight),\n  height: ({\n    y\n  }, {\n    paddingTop = \"0\",\n    paddingBottom = \"0\"\n  }) => y.max - y.min - parseFloat(paddingTop) - parseFloat(paddingBottom),\n  top: (_bbox, {\n    top\n  }) => parseFloat(top),\n  left: (_bbox, {\n    left\n  }) => parseFloat(left),\n  bottom: ({\n    y\n  }, {\n    top\n  }) => parseFloat(top) + (y.max - y.min),\n  right: ({\n    x\n  }, {\n    left\n  }) => parseFloat(left) + (x.max - x.min),\n  // Transform\n  x: getTranslateFromMatrix(4, 13),\n  y: getTranslateFromMatrix(5, 14)\n};\n// Alias translate longform names\npositionalValues.translateX = positionalValues.x;\npositionalValues.translateY = positionalValues.y;\nconst convertChangedValueTypes = (target, visualElement, changedKeys) => {\n  const originBbox = visualElement.measureViewportBox();\n  const element = visualElement.current;\n  const elementComputedStyle = getComputedStyle(element);\n  const {\n    display\n  } = elementComputedStyle;\n  const origin = {};\n  // If the element is currently set to display: \"none\", make it visible before\n  // measuring the target bounding box\n  if (display === \"none\") {\n    visualElement.setStaticValue(\"display\", target.display || \"block\");\n  }\n  /**\n   * Record origins before we render and update styles\n   */\n  changedKeys.forEach(key => {\n    origin[key] = positionalValues[key](originBbox, elementComputedStyle);\n  });\n  // Apply the latest values (as set in checkAndConvertChangedValueTypes)\n  visualElement.render();\n  const targetBbox = visualElement.measureViewportBox();\n  changedKeys.forEach(key => {\n    // Restore styles to their **calculated computed style**, not their actual\n    // originally set style. This allows us to animate between equivalent pixel units.\n    const value = visualElement.getValue(key);\n    value && value.jump(origin[key]);\n    target[key] = positionalValues[key](targetBbox, elementComputedStyle);\n  });\n  return target;\n};\nconst checkAndConvertChangedValueTypes = (visualElement, target, origin = {}, transitionEnd = {}) => {\n  target = {\n    ...target\n  };\n  transitionEnd = {\n    ...transitionEnd\n  };\n  const targetPositionalKeys = Object.keys(target).filter(isPositionalKey);\n  // We want to remove any transform values that could affect the element's bounding box before\n  // it's measured. We'll reapply these later.\n  let removedTransformValues = [];\n  let hasAttemptedToRemoveTransformValues = false;\n  const changedValueTypeKeys = [];\n  targetPositionalKeys.forEach(key => {\n    const value = visualElement.getValue(key);\n    if (!visualElement.hasValue(key)) return;\n    let from = origin[key];\n    let fromType = findDimensionValueType(from);\n    const to = target[key];\n    let toType;\n    // TODO: The current implementation of this basically throws an error\n    // if you try and do value conversion via keyframes. There's probably\n    // a way of doing this but the performance implications would need greater scrutiny,\n    // as it'd be doing multiple resize-remeasure operations.\n    if (isKeyframesTarget(to)) {\n      const numKeyframes = to.length;\n      const fromIndex = to[0] === null ? 1 : 0;\n      from = to[fromIndex];\n      fromType = findDimensionValueType(from);\n      for (let i = fromIndex; i < numKeyframes; i++) {\n        /**\n         * Don't allow wildcard keyframes to be used to detect\n         * a difference in value types.\n         */\n        if (to[i] === null) break;\n        if (!toType) {\n          toType = findDimensionValueType(to[i]);\n          invariant(toType === fromType || isNumOrPxType(fromType) && isNumOrPxType(toType), \"Keyframes must be of the same dimension as the current value\");\n        } else {\n          invariant(findDimensionValueType(to[i]) === toType, \"All keyframes must be of the same type\");\n        }\n      }\n    } else {\n      toType = findDimensionValueType(to);\n    }\n    if (fromType !== toType) {\n      // If they're both just number or px, convert them both to numbers rather than\n      // relying on resize/remeasure to convert (which is wasteful in this situation)\n      if (isNumOrPxType(fromType) && isNumOrPxType(toType)) {\n        const current = value.get();\n        if (typeof current === \"string\") {\n          value.set(parseFloat(current));\n        }\n        if (typeof to === \"string\") {\n          target[key] = parseFloat(to);\n        } else if (Array.isArray(to) && toType === px) {\n          target[key] = to.map(parseFloat);\n        }\n      } else if ((fromType === null || fromType === void 0 ? void 0 : fromType.transform) && (toType === null || toType === void 0 ? void 0 : toType.transform) && (from === 0 || to === 0)) {\n        // If one or the other value is 0, it's safe to coerce it to the\n        // type of the other without measurement\n        if (from === 0) {\n          value.set(toType.transform(from));\n        } else {\n          target[key] = fromType.transform(to);\n        }\n      } else {\n        // If we're going to do value conversion via DOM measurements, we first\n        // need to remove non-positional transform values that could affect the bbox measurements.\n        if (!hasAttemptedToRemoveTransformValues) {\n          removedTransformValues = removeNonTranslationalTransform(visualElement);\n          hasAttemptedToRemoveTransformValues = true;\n        }\n        changedValueTypeKeys.push(key);\n        transitionEnd[key] = transitionEnd[key] !== undefined ? transitionEnd[key] : target[key];\n        value.jump(to);\n      }\n    }\n  });\n  if (changedValueTypeKeys.length) {\n    const scrollY = changedValueTypeKeys.indexOf(\"height\") >= 0 ? window.pageYOffset : null;\n    const convertedTarget = convertChangedValueTypes(target, visualElement, changedValueTypeKeys);\n    // If we removed transform values, reapply them before the next render\n    if (removedTransformValues.length) {\n      removedTransformValues.forEach(([key, value]) => {\n        visualElement.getValue(key).set(value);\n      });\n    }\n    // Reapply original values\n    visualElement.render();\n    // Restore scroll position\n    if (isBrowser && scrollY !== null) {\n      window.scrollTo({\n        top: scrollY\n      });\n    }\n    return {\n      target: convertedTarget,\n      transitionEnd\n    };\n  } else {\n    return {\n      target,\n      transitionEnd\n    };\n  }\n};\n/**\n * Convert value types for x/y/width/height/top/left/bottom/right\n *\n * Allows animation between `'auto'` -> `'100%'` or `0` -> `'calc(50% - 10vw)'`\n *\n * @internal\n */\nfunction unitConversion(visualElement, target, origin, transitionEnd) {\n  return hasPositionalKey(target) ? checkAndConvertChangedValueTypes(visualElement, target, origin, transitionEnd) : {\n    target,\n    transitionEnd\n  };\n}\nexport { positionalValues, unitConversion };", "map": {"version": 3, "names": ["isKeyframesTarget", "invariant", "transformPropOrder", "findDimensionValueType", "<PERSON><PERSON><PERSON><PERSON>", "number", "px", "positional<PERSON>eys", "Set", "isPositionalKey", "key", "has", "hasPositionalKey", "target", "Object", "keys", "some", "isNumOrPxType", "v", "getPosFromMatrix", "matrix", "pos", "parseFloat", "split", "getTranslateFromMatrix", "pos2", "pos3", "_bbox", "transform", "matrix3d", "match", "transformKeys", "nonTranslationalTransformKeys", "filter", "removeNonTranslationalTransform", "visualElement", "removedTransforms", "for<PERSON>ach", "value", "getValue", "undefined", "push", "get", "set", "startsWith", "length", "render", "positionalV<PERSON>ues", "width", "x", "paddingLeft", "paddingRight", "max", "min", "height", "y", "paddingTop", "paddingBottom", "top", "left", "bottom", "right", "translateX", "translateY", "convertChangedValueTypes", "changed<PERSON><PERSON><PERSON>", "originBbox", "measureViewportBox", "element", "current", "elementComputedStyle", "getComputedStyle", "display", "origin", "setStaticValue", "targetBbox", "jump", "checkAndConvertChangedValueTypes", "transitionEnd", "targetPositionalKeys", "removedTransformValues", "hasAttemptedToRemoveTransformValues", "changedValueTypeKeys", "hasValue", "from", "fromType", "to", "toType", "numKeyframes", "fromIndex", "i", "Array", "isArray", "map", "scrollY", "indexOf", "window", "pageYOffset", "convertedTarget", "scrollTo", "unitConversion"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/framer-motion/dist/es/render/dom/utils/unit-conversion.mjs"], "sourcesContent": ["import { isKeyframesTarget } from '../../../animation/utils/is-keyframes-target.mjs';\nimport { invariant } from '../../../utils/errors.mjs';\nimport { transformPropOrder } from '../../html/utils/transform.mjs';\nimport { findDimensionValueType } from '../value-types/dimensions.mjs';\nimport { isBrowser } from '../../../utils/is-browser.mjs';\nimport { number } from '../../../value/types/numbers/index.mjs';\nimport { px } from '../../../value/types/numbers/units.mjs';\n\nconst positionalKeys = new Set([\n    \"width\",\n    \"height\",\n    \"top\",\n    \"left\",\n    \"right\",\n    \"bottom\",\n    \"x\",\n    \"y\",\n    \"translateX\",\n    \"translateY\",\n]);\nconst isPositionalKey = (key) => positionalKeys.has(key);\nconst hasPositionalKey = (target) => {\n    return Object.keys(target).some(isPositionalKey);\n};\nconst isNumOrPxType = (v) => v === number || v === px;\nconst getPosFromMatrix = (matrix, pos) => parseFloat(matrix.split(\", \")[pos]);\nconst getTranslateFromMatrix = (pos2, pos3) => (_bbox, { transform }) => {\n    if (transform === \"none\" || !transform)\n        return 0;\n    const matrix3d = transform.match(/^matrix3d\\((.+)\\)$/);\n    if (matrix3d) {\n        return getPosFromMatrix(matrix3d[1], pos3);\n    }\n    else {\n        const matrix = transform.match(/^matrix\\((.+)\\)$/);\n        if (matrix) {\n            return getPosFromMatrix(matrix[1], pos2);\n        }\n        else {\n            return 0;\n        }\n    }\n};\nconst transformKeys = new Set([\"x\", \"y\", \"z\"]);\nconst nonTranslationalTransformKeys = transformPropOrder.filter((key) => !transformKeys.has(key));\nfunction removeNonTranslationalTransform(visualElement) {\n    const removedTransforms = [];\n    nonTranslationalTransformKeys.forEach((key) => {\n        const value = visualElement.getValue(key);\n        if (value !== undefined) {\n            removedTransforms.push([key, value.get()]);\n            value.set(key.startsWith(\"scale\") ? 1 : 0);\n        }\n    });\n    // Apply changes to element before measurement\n    if (removedTransforms.length)\n        visualElement.render();\n    return removedTransforms;\n}\nconst positionalValues = {\n    // Dimensions\n    width: ({ x }, { paddingLeft = \"0\", paddingRight = \"0\" }) => x.max - x.min - parseFloat(paddingLeft) - parseFloat(paddingRight),\n    height: ({ y }, { paddingTop = \"0\", paddingBottom = \"0\" }) => y.max - y.min - parseFloat(paddingTop) - parseFloat(paddingBottom),\n    top: (_bbox, { top }) => parseFloat(top),\n    left: (_bbox, { left }) => parseFloat(left),\n    bottom: ({ y }, { top }) => parseFloat(top) + (y.max - y.min),\n    right: ({ x }, { left }) => parseFloat(left) + (x.max - x.min),\n    // Transform\n    x: getTranslateFromMatrix(4, 13),\n    y: getTranslateFromMatrix(5, 14),\n};\n// Alias translate longform names\npositionalValues.translateX = positionalValues.x;\npositionalValues.translateY = positionalValues.y;\nconst convertChangedValueTypes = (target, visualElement, changedKeys) => {\n    const originBbox = visualElement.measureViewportBox();\n    const element = visualElement.current;\n    const elementComputedStyle = getComputedStyle(element);\n    const { display } = elementComputedStyle;\n    const origin = {};\n    // If the element is currently set to display: \"none\", make it visible before\n    // measuring the target bounding box\n    if (display === \"none\") {\n        visualElement.setStaticValue(\"display\", target.display || \"block\");\n    }\n    /**\n     * Record origins before we render and update styles\n     */\n    changedKeys.forEach((key) => {\n        origin[key] = positionalValues[key](originBbox, elementComputedStyle);\n    });\n    // Apply the latest values (as set in checkAndConvertChangedValueTypes)\n    visualElement.render();\n    const targetBbox = visualElement.measureViewportBox();\n    changedKeys.forEach((key) => {\n        // Restore styles to their **calculated computed style**, not their actual\n        // originally set style. This allows us to animate between equivalent pixel units.\n        const value = visualElement.getValue(key);\n        value && value.jump(origin[key]);\n        target[key] = positionalValues[key](targetBbox, elementComputedStyle);\n    });\n    return target;\n};\nconst checkAndConvertChangedValueTypes = (visualElement, target, origin = {}, transitionEnd = {}) => {\n    target = { ...target };\n    transitionEnd = { ...transitionEnd };\n    const targetPositionalKeys = Object.keys(target).filter(isPositionalKey);\n    // We want to remove any transform values that could affect the element's bounding box before\n    // it's measured. We'll reapply these later.\n    let removedTransformValues = [];\n    let hasAttemptedToRemoveTransformValues = false;\n    const changedValueTypeKeys = [];\n    targetPositionalKeys.forEach((key) => {\n        const value = visualElement.getValue(key);\n        if (!visualElement.hasValue(key))\n            return;\n        let from = origin[key];\n        let fromType = findDimensionValueType(from);\n        const to = target[key];\n        let toType;\n        // TODO: The current implementation of this basically throws an error\n        // if you try and do value conversion via keyframes. There's probably\n        // a way of doing this but the performance implications would need greater scrutiny,\n        // as it'd be doing multiple resize-remeasure operations.\n        if (isKeyframesTarget(to)) {\n            const numKeyframes = to.length;\n            const fromIndex = to[0] === null ? 1 : 0;\n            from = to[fromIndex];\n            fromType = findDimensionValueType(from);\n            for (let i = fromIndex; i < numKeyframes; i++) {\n                /**\n                 * Don't allow wildcard keyframes to be used to detect\n                 * a difference in value types.\n                 */\n                if (to[i] === null)\n                    break;\n                if (!toType) {\n                    toType = findDimensionValueType(to[i]);\n                    invariant(toType === fromType ||\n                        (isNumOrPxType(fromType) && isNumOrPxType(toType)), \"Keyframes must be of the same dimension as the current value\");\n                }\n                else {\n                    invariant(findDimensionValueType(to[i]) === toType, \"All keyframes must be of the same type\");\n                }\n            }\n        }\n        else {\n            toType = findDimensionValueType(to);\n        }\n        if (fromType !== toType) {\n            // If they're both just number or px, convert them both to numbers rather than\n            // relying on resize/remeasure to convert (which is wasteful in this situation)\n            if (isNumOrPxType(fromType) && isNumOrPxType(toType)) {\n                const current = value.get();\n                if (typeof current === \"string\") {\n                    value.set(parseFloat(current));\n                }\n                if (typeof to === \"string\") {\n                    target[key] = parseFloat(to);\n                }\n                else if (Array.isArray(to) && toType === px) {\n                    target[key] = to.map(parseFloat);\n                }\n            }\n            else if ((fromType === null || fromType === void 0 ? void 0 : fromType.transform) &&\n                (toType === null || toType === void 0 ? void 0 : toType.transform) &&\n                (from === 0 || to === 0)) {\n                // If one or the other value is 0, it's safe to coerce it to the\n                // type of the other without measurement\n                if (from === 0) {\n                    value.set(toType.transform(from));\n                }\n                else {\n                    target[key] = fromType.transform(to);\n                }\n            }\n            else {\n                // If we're going to do value conversion via DOM measurements, we first\n                // need to remove non-positional transform values that could affect the bbox measurements.\n                if (!hasAttemptedToRemoveTransformValues) {\n                    removedTransformValues =\n                        removeNonTranslationalTransform(visualElement);\n                    hasAttemptedToRemoveTransformValues = true;\n                }\n                changedValueTypeKeys.push(key);\n                transitionEnd[key] =\n                    transitionEnd[key] !== undefined\n                        ? transitionEnd[key]\n                        : target[key];\n                value.jump(to);\n            }\n        }\n    });\n    if (changedValueTypeKeys.length) {\n        const scrollY = changedValueTypeKeys.indexOf(\"height\") >= 0\n            ? window.pageYOffset\n            : null;\n        const convertedTarget = convertChangedValueTypes(target, visualElement, changedValueTypeKeys);\n        // If we removed transform values, reapply them before the next render\n        if (removedTransformValues.length) {\n            removedTransformValues.forEach(([key, value]) => {\n                visualElement.getValue(key).set(value);\n            });\n        }\n        // Reapply original values\n        visualElement.render();\n        // Restore scroll position\n        if (isBrowser && scrollY !== null) {\n            window.scrollTo({ top: scrollY });\n        }\n        return { target: convertedTarget, transitionEnd };\n    }\n    else {\n        return { target, transitionEnd };\n    }\n};\n/**\n * Convert value types for x/y/width/height/top/left/bottom/right\n *\n * Allows animation between `'auto'` -> `'100%'` or `0` -> `'calc(50% - 10vw)'`\n *\n * @internal\n */\nfunction unitConversion(visualElement, target, origin, transitionEnd) {\n    return hasPositionalKey(target)\n        ? checkAndConvertChangedValueTypes(visualElement, target, origin, transitionEnd)\n        : { target, transitionEnd };\n}\n\nexport { positionalValues, unitConversion };\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,kDAAkD;AACpF,SAASC,SAAS,QAAQ,2BAA2B;AACrD,SAASC,kBAAkB,QAAQ,gCAAgC;AACnE,SAASC,sBAAsB,QAAQ,+BAA+B;AACtE,SAASC,SAAS,QAAQ,+BAA+B;AACzD,SAASC,MAAM,QAAQ,wCAAwC;AAC/D,SAASC,EAAE,QAAQ,wCAAwC;AAE3D,MAAMC,cAAc,GAAG,IAAIC,GAAG,CAAC,CAC3B,OAAO,EACP,QAAQ,EACR,KAAK,EACL,MAAM,EACN,OAAO,EACP,QAAQ,EACR,GAAG,EACH,GAAG,EACH,YAAY,EACZ,YAAY,CACf,CAAC;AACF,MAAMC,eAAe,GAAIC,GAAG,IAAKH,cAAc,CAACI,GAAG,CAACD,GAAG,CAAC;AACxD,MAAME,gBAAgB,GAAIC,MAAM,IAAK;EACjC,OAAOC,MAAM,CAACC,IAAI,CAACF,MAAM,CAAC,CAACG,IAAI,CAACP,eAAe,CAAC;AACpD,CAAC;AACD,MAAMQ,aAAa,GAAIC,CAAC,IAAKA,CAAC,KAAKb,MAAM,IAAIa,CAAC,KAAKZ,EAAE;AACrD,MAAMa,gBAAgB,GAAGA,CAACC,MAAM,EAAEC,GAAG,KAAKC,UAAU,CAACF,MAAM,CAACG,KAAK,CAAC,IAAI,CAAC,CAACF,GAAG,CAAC,CAAC;AAC7E,MAAMG,sBAAsB,GAAGA,CAACC,IAAI,EAAEC,IAAI,KAAK,CAACC,KAAK,EAAE;EAAEC;AAAU,CAAC,KAAK;EACrE,IAAIA,SAAS,KAAK,MAAM,IAAI,CAACA,SAAS,EAClC,OAAO,CAAC;EACZ,MAAMC,QAAQ,GAAGD,SAAS,CAACE,KAAK,CAAC,oBAAoB,CAAC;EACtD,IAAID,QAAQ,EAAE;IACV,OAAOV,gBAAgB,CAACU,QAAQ,CAAC,CAAC,CAAC,EAAEH,IAAI,CAAC;EAC9C,CAAC,MACI;IACD,MAAMN,MAAM,GAAGQ,SAAS,CAACE,KAAK,CAAC,kBAAkB,CAAC;IAClD,IAAIV,MAAM,EAAE;MACR,OAAOD,gBAAgB,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEK,IAAI,CAAC;IAC5C,CAAC,MACI;MACD,OAAO,CAAC;IACZ;EACJ;AACJ,CAAC;AACD,MAAMM,aAAa,GAAG,IAAIvB,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AAC9C,MAAMwB,6BAA6B,GAAG9B,kBAAkB,CAAC+B,MAAM,CAAEvB,GAAG,IAAK,CAACqB,aAAa,CAACpB,GAAG,CAACD,GAAG,CAAC,CAAC;AACjG,SAASwB,+BAA+BA,CAACC,aAAa,EAAE;EACpD,MAAMC,iBAAiB,GAAG,EAAE;EAC5BJ,6BAA6B,CAACK,OAAO,CAAE3B,GAAG,IAAK;IAC3C,MAAM4B,KAAK,GAAGH,aAAa,CAACI,QAAQ,CAAC7B,GAAG,CAAC;IACzC,IAAI4B,KAAK,KAAKE,SAAS,EAAE;MACrBJ,iBAAiB,CAACK,IAAI,CAAC,CAAC/B,GAAG,EAAE4B,KAAK,CAACI,GAAG,CAAC,CAAC,CAAC,CAAC;MAC1CJ,KAAK,CAACK,GAAG,CAACjC,GAAG,CAACkC,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAC9C;EACJ,CAAC,CAAC;EACF;EACA,IAAIR,iBAAiB,CAACS,MAAM,EACxBV,aAAa,CAACW,MAAM,CAAC,CAAC;EAC1B,OAAOV,iBAAiB;AAC5B;AACA,MAAMW,gBAAgB,GAAG;EACrB;EACAC,KAAK,EAAEA,CAAC;IAAEC;EAAE,CAAC,EAAE;IAAEC,WAAW,GAAG,GAAG;IAAEC,YAAY,GAAG;EAAI,CAAC,KAAKF,CAAC,CAACG,GAAG,GAAGH,CAAC,CAACI,GAAG,GAAG/B,UAAU,CAAC4B,WAAW,CAAC,GAAG5B,UAAU,CAAC6B,YAAY,CAAC;EAC/HG,MAAM,EAAEA,CAAC;IAAEC;EAAE,CAAC,EAAE;IAAEC,UAAU,GAAG,GAAG;IAAEC,aAAa,GAAG;EAAI,CAAC,KAAKF,CAAC,CAACH,GAAG,GAAGG,CAAC,CAACF,GAAG,GAAG/B,UAAU,CAACkC,UAAU,CAAC,GAAGlC,UAAU,CAACmC,aAAa,CAAC;EAChIC,GAAG,EAAEA,CAAC/B,KAAK,EAAE;IAAE+B;EAAI,CAAC,KAAKpC,UAAU,CAACoC,GAAG,CAAC;EACxCC,IAAI,EAAEA,CAAChC,KAAK,EAAE;IAAEgC;EAAK,CAAC,KAAKrC,UAAU,CAACqC,IAAI,CAAC;EAC3CC,MAAM,EAAEA,CAAC;IAAEL;EAAE,CAAC,EAAE;IAAEG;EAAI,CAAC,KAAKpC,UAAU,CAACoC,GAAG,CAAC,IAAIH,CAAC,CAACH,GAAG,GAAGG,CAAC,CAACF,GAAG,CAAC;EAC7DQ,KAAK,EAAEA,CAAC;IAAEZ;EAAE,CAAC,EAAE;IAAEU;EAAK,CAAC,KAAKrC,UAAU,CAACqC,IAAI,CAAC,IAAIV,CAAC,CAACG,GAAG,GAAGH,CAAC,CAACI,GAAG,CAAC;EAC9D;EACAJ,CAAC,EAAEzB,sBAAsB,CAAC,CAAC,EAAE,EAAE,CAAC;EAChC+B,CAAC,EAAE/B,sBAAsB,CAAC,CAAC,EAAE,EAAE;AACnC,CAAC;AACD;AACAuB,gBAAgB,CAACe,UAAU,GAAGf,gBAAgB,CAACE,CAAC;AAChDF,gBAAgB,CAACgB,UAAU,GAAGhB,gBAAgB,CAACQ,CAAC;AAChD,MAAMS,wBAAwB,GAAGA,CAACnD,MAAM,EAAEsB,aAAa,EAAE8B,WAAW,KAAK;EACrE,MAAMC,UAAU,GAAG/B,aAAa,CAACgC,kBAAkB,CAAC,CAAC;EACrD,MAAMC,OAAO,GAAGjC,aAAa,CAACkC,OAAO;EACrC,MAAMC,oBAAoB,GAAGC,gBAAgB,CAACH,OAAO,CAAC;EACtD,MAAM;IAAEI;EAAQ,CAAC,GAAGF,oBAAoB;EACxC,MAAMG,MAAM,GAAG,CAAC,CAAC;EACjB;EACA;EACA,IAAID,OAAO,KAAK,MAAM,EAAE;IACpBrC,aAAa,CAACuC,cAAc,CAAC,SAAS,EAAE7D,MAAM,CAAC2D,OAAO,IAAI,OAAO,CAAC;EACtE;EACA;AACJ;AACA;EACIP,WAAW,CAAC5B,OAAO,CAAE3B,GAAG,IAAK;IACzB+D,MAAM,CAAC/D,GAAG,CAAC,GAAGqC,gBAAgB,CAACrC,GAAG,CAAC,CAACwD,UAAU,EAAEI,oBAAoB,CAAC;EACzE,CAAC,CAAC;EACF;EACAnC,aAAa,CAACW,MAAM,CAAC,CAAC;EACtB,MAAM6B,UAAU,GAAGxC,aAAa,CAACgC,kBAAkB,CAAC,CAAC;EACrDF,WAAW,CAAC5B,OAAO,CAAE3B,GAAG,IAAK;IACzB;IACA;IACA,MAAM4B,KAAK,GAAGH,aAAa,CAACI,QAAQ,CAAC7B,GAAG,CAAC;IACzC4B,KAAK,IAAIA,KAAK,CAACsC,IAAI,CAACH,MAAM,CAAC/D,GAAG,CAAC,CAAC;IAChCG,MAAM,CAACH,GAAG,CAAC,GAAGqC,gBAAgB,CAACrC,GAAG,CAAC,CAACiE,UAAU,EAAEL,oBAAoB,CAAC;EACzE,CAAC,CAAC;EACF,OAAOzD,MAAM;AACjB,CAAC;AACD,MAAMgE,gCAAgC,GAAGA,CAAC1C,aAAa,EAAEtB,MAAM,EAAE4D,MAAM,GAAG,CAAC,CAAC,EAAEK,aAAa,GAAG,CAAC,CAAC,KAAK;EACjGjE,MAAM,GAAG;IAAE,GAAGA;EAAO,CAAC;EACtBiE,aAAa,GAAG;IAAE,GAAGA;EAAc,CAAC;EACpC,MAAMC,oBAAoB,GAAGjE,MAAM,CAACC,IAAI,CAACF,MAAM,CAAC,CAACoB,MAAM,CAACxB,eAAe,CAAC;EACxE;EACA;EACA,IAAIuE,sBAAsB,GAAG,EAAE;EAC/B,IAAIC,mCAAmC,GAAG,KAAK;EAC/C,MAAMC,oBAAoB,GAAG,EAAE;EAC/BH,oBAAoB,CAAC1C,OAAO,CAAE3B,GAAG,IAAK;IAClC,MAAM4B,KAAK,GAAGH,aAAa,CAACI,QAAQ,CAAC7B,GAAG,CAAC;IACzC,IAAI,CAACyB,aAAa,CAACgD,QAAQ,CAACzE,GAAG,CAAC,EAC5B;IACJ,IAAI0E,IAAI,GAAGX,MAAM,CAAC/D,GAAG,CAAC;IACtB,IAAI2E,QAAQ,GAAGlF,sBAAsB,CAACiF,IAAI,CAAC;IAC3C,MAAME,EAAE,GAAGzE,MAAM,CAACH,GAAG,CAAC;IACtB,IAAI6E,MAAM;IACV;IACA;IACA;IACA;IACA,IAAIvF,iBAAiB,CAACsF,EAAE,CAAC,EAAE;MACvB,MAAME,YAAY,GAAGF,EAAE,CAACzC,MAAM;MAC9B,MAAM4C,SAAS,GAAGH,EAAE,CAAC,CAAC,CAAC,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC;MACxCF,IAAI,GAAGE,EAAE,CAACG,SAAS,CAAC;MACpBJ,QAAQ,GAAGlF,sBAAsB,CAACiF,IAAI,CAAC;MACvC,KAAK,IAAIM,CAAC,GAAGD,SAAS,EAAEC,CAAC,GAAGF,YAAY,EAAEE,CAAC,EAAE,EAAE;QAC3C;AAChB;AACA;AACA;QACgB,IAAIJ,EAAE,CAACI,CAAC,CAAC,KAAK,IAAI,EACd;QACJ,IAAI,CAACH,MAAM,EAAE;UACTA,MAAM,GAAGpF,sBAAsB,CAACmF,EAAE,CAACI,CAAC,CAAC,CAAC;UACtCzF,SAAS,CAACsF,MAAM,KAAKF,QAAQ,IACxBpE,aAAa,CAACoE,QAAQ,CAAC,IAAIpE,aAAa,CAACsE,MAAM,CAAE,EAAE,8DAA8D,CAAC;QAC3H,CAAC,MACI;UACDtF,SAAS,CAACE,sBAAsB,CAACmF,EAAE,CAACI,CAAC,CAAC,CAAC,KAAKH,MAAM,EAAE,wCAAwC,CAAC;QACjG;MACJ;IACJ,CAAC,MACI;MACDA,MAAM,GAAGpF,sBAAsB,CAACmF,EAAE,CAAC;IACvC;IACA,IAAID,QAAQ,KAAKE,MAAM,EAAE;MACrB;MACA;MACA,IAAItE,aAAa,CAACoE,QAAQ,CAAC,IAAIpE,aAAa,CAACsE,MAAM,CAAC,EAAE;QAClD,MAAMlB,OAAO,GAAG/B,KAAK,CAACI,GAAG,CAAC,CAAC;QAC3B,IAAI,OAAO2B,OAAO,KAAK,QAAQ,EAAE;UAC7B/B,KAAK,CAACK,GAAG,CAACrB,UAAU,CAAC+C,OAAO,CAAC,CAAC;QAClC;QACA,IAAI,OAAOiB,EAAE,KAAK,QAAQ,EAAE;UACxBzE,MAAM,CAACH,GAAG,CAAC,GAAGY,UAAU,CAACgE,EAAE,CAAC;QAChC,CAAC,MACI,IAAIK,KAAK,CAACC,OAAO,CAACN,EAAE,CAAC,IAAIC,MAAM,KAAKjF,EAAE,EAAE;UACzCO,MAAM,CAACH,GAAG,CAAC,GAAG4E,EAAE,CAACO,GAAG,CAACvE,UAAU,CAAC;QACpC;MACJ,CAAC,MACI,IAAI,CAAC+D,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACzD,SAAS,MAC3E2D,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC3D,SAAS,CAAC,KACjEwD,IAAI,KAAK,CAAC,IAAIE,EAAE,KAAK,CAAC,CAAC,EAAE;QAC1B;QACA;QACA,IAAIF,IAAI,KAAK,CAAC,EAAE;UACZ9C,KAAK,CAACK,GAAG,CAAC4C,MAAM,CAAC3D,SAAS,CAACwD,IAAI,CAAC,CAAC;QACrC,CAAC,MACI;UACDvE,MAAM,CAACH,GAAG,CAAC,GAAG2E,QAAQ,CAACzD,SAAS,CAAC0D,EAAE,CAAC;QACxC;MACJ,CAAC,MACI;QACD;QACA;QACA,IAAI,CAACL,mCAAmC,EAAE;UACtCD,sBAAsB,GAClB9C,+BAA+B,CAACC,aAAa,CAAC;UAClD8C,mCAAmC,GAAG,IAAI;QAC9C;QACAC,oBAAoB,CAACzC,IAAI,CAAC/B,GAAG,CAAC;QAC9BoE,aAAa,CAACpE,GAAG,CAAC,GACdoE,aAAa,CAACpE,GAAG,CAAC,KAAK8B,SAAS,GAC1BsC,aAAa,CAACpE,GAAG,CAAC,GAClBG,MAAM,CAACH,GAAG,CAAC;QACrB4B,KAAK,CAACsC,IAAI,CAACU,EAAE,CAAC;MAClB;IACJ;EACJ,CAAC,CAAC;EACF,IAAIJ,oBAAoB,CAACrC,MAAM,EAAE;IAC7B,MAAMiD,OAAO,GAAGZ,oBAAoB,CAACa,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,GACrDC,MAAM,CAACC,WAAW,GAClB,IAAI;IACV,MAAMC,eAAe,GAAGlC,wBAAwB,CAACnD,MAAM,EAAEsB,aAAa,EAAE+C,oBAAoB,CAAC;IAC7F;IACA,IAAIF,sBAAsB,CAACnC,MAAM,EAAE;MAC/BmC,sBAAsB,CAAC3C,OAAO,CAAC,CAAC,CAAC3B,GAAG,EAAE4B,KAAK,CAAC,KAAK;QAC7CH,aAAa,CAACI,QAAQ,CAAC7B,GAAG,CAAC,CAACiC,GAAG,CAACL,KAAK,CAAC;MAC1C,CAAC,CAAC;IACN;IACA;IACAH,aAAa,CAACW,MAAM,CAAC,CAAC;IACtB;IACA,IAAI1C,SAAS,IAAI0F,OAAO,KAAK,IAAI,EAAE;MAC/BE,MAAM,CAACG,QAAQ,CAAC;QAAEzC,GAAG,EAAEoC;MAAQ,CAAC,CAAC;IACrC;IACA,OAAO;MAAEjF,MAAM,EAAEqF,eAAe;MAAEpB;IAAc,CAAC;EACrD,CAAC,MACI;IACD,OAAO;MAAEjE,MAAM;MAAEiE;IAAc,CAAC;EACpC;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASsB,cAAcA,CAACjE,aAAa,EAAEtB,MAAM,EAAE4D,MAAM,EAAEK,aAAa,EAAE;EAClE,OAAOlE,gBAAgB,CAACC,MAAM,CAAC,GACzBgE,gCAAgC,CAAC1C,aAAa,EAAEtB,MAAM,EAAE4D,MAAM,EAAEK,aAAa,CAAC,GAC9E;IAAEjE,MAAM;IAAEiE;EAAc,CAAC;AACnC;AAEA,SAAS/B,gBAAgB,EAAEqD,cAAc"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}