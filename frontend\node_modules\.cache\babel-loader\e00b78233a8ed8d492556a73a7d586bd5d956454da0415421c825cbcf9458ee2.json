{"ast": null, "code": "/**\n * Assigns a value for a given ref, no matter of the ref format\n * @param {RefObject} ref - a callback function or ref object\n * @param value - a new value\n *\n * @see https://github.com/theKashey/use-callback-ref#assignref\n * @example\n * const refObject = useRef();\n * const refFn = (ref) => {....}\n *\n * assignRef(refObject, \"refValue\");\n * assignRef(refFn, \"refValue\");\n */\nexport function assignRef(ref, value) {\n  if (typeof ref === 'function') {\n    ref(value);\n  } else if (ref) {\n    ref.current = value;\n  }\n  return ref;\n}", "map": {"version": 3, "names": ["assignRef", "ref", "value", "current"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/use-callback-ref/dist/es2015/assignRef.js"], "sourcesContent": ["/**\n * Assigns a value for a given ref, no matter of the ref format\n * @param {RefObject} ref - a callback function or ref object\n * @param value - a new value\n *\n * @see https://github.com/theKashey/use-callback-ref#assignref\n * @example\n * const refObject = useRef();\n * const refFn = (ref) => {....}\n *\n * assignRef(refObject, \"refValue\");\n * assignRef(refFn, \"refValue\");\n */\nexport function assignRef(ref, value) {\n    if (typeof ref === 'function') {\n        ref(value);\n    }\n    else if (ref) {\n        ref.current = value;\n    }\n    return ref;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,SAASA,CAACC,GAAG,EAAEC,KAAK,EAAE;EAClC,IAAI,OAAOD,GAAG,KAAK,UAAU,EAAE;IAC3BA,GAAG,CAACC,KAAK,CAAC;EACd,CAAC,MACI,IAAID,GAAG,EAAE;IACVA,GAAG,CAACE,OAAO,GAAGD,KAAK;EACvB;EACA,OAAOD,GAAG;AACd"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}