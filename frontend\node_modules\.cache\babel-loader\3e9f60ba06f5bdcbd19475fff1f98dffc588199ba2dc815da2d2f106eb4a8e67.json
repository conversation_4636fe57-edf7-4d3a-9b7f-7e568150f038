{"ast": null, "code": "/**\n * Implement a practical max duration for keyframe generation\n * to prevent infinite loops\n */\nconst maxGeneratorDuration = 20000;\nfunction calcGeneratorDuration(generator) {\n  let duration = 0;\n  const timeStep = 50;\n  let state = generator.next(duration);\n  while (!state.done && duration < maxGeneratorDuration) {\n    duration += timeStep;\n    state = generator.next(duration);\n  }\n  return duration >= maxGeneratorDuration ? Infinity : duration;\n}\nexport { calcGeneratorDuration, maxGeneratorDuration };", "map": {"version": 3, "names": ["maxGeneratorDuration", "calcGeneratorDuration", "generator", "duration", "timeStep", "state", "next", "done", "Infinity"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/framer-motion/dist/es/animation/generators/utils/calc-duration.mjs"], "sourcesContent": ["/**\n * Implement a practical max duration for keyframe generation\n * to prevent infinite loops\n */\nconst maxGeneratorDuration = 20000;\nfunction calcGeneratorDuration(generator) {\n    let duration = 0;\n    const timeStep = 50;\n    let state = generator.next(duration);\n    while (!state.done && duration < maxGeneratorDuration) {\n        duration += timeStep;\n        state = generator.next(duration);\n    }\n    return duration >= maxGeneratorDuration ? Infinity : duration;\n}\n\nexport { calcGeneratorDuration, maxGeneratorDuration };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,MAAMA,oBAAoB,GAAG,KAAK;AAClC,SAASC,qBAAqBA,CAACC,SAAS,EAAE;EACtC,IAAIC,QAAQ,GAAG,CAAC;EAChB,MAAMC,QAAQ,GAAG,EAAE;EACnB,IAAIC,KAAK,GAAGH,SAAS,CAACI,IAAI,CAACH,QAAQ,CAAC;EACpC,OAAO,CAACE,KAAK,CAACE,IAAI,IAAIJ,QAAQ,GAAGH,oBAAoB,EAAE;IACnDG,QAAQ,IAAIC,QAAQ;IACpBC,KAAK,GAAGH,SAAS,CAACI,IAAI,CAACH,QAAQ,CAAC;EACpC;EACA,OAAOA,QAAQ,IAAIH,oBAAoB,GAAGQ,QAAQ,GAAGL,QAAQ;AACjE;AAEA,SAASF,qBAAqB,EAAED,oBAAoB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}