{"ast": null, "code": "export var zeroRightClassName = 'right-scroll-bar-position';\nexport var fullWidthClassName = 'width-before-scroll-bar';\nexport var noScrollbarsClassName = 'with-scroll-bars-hidden';\n/**\n * Name of a CSS variable containing the amount of \"hidden\" scrollbar\n * ! might be undefined ! use will fallback!\n */\nexport var removedBarSizeVariable = '--removed-body-scroll-bar-size';", "map": {"version": 3, "names": ["zeroRightClassName", "fullWidthClassName", "noScrollbarsClassName", "removedBarSizeVariable"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/react-remove-scroll-bar/dist/es2015/constants.js"], "sourcesContent": ["export var zeroRightClassName = 'right-scroll-bar-position';\nexport var fullWidthClassName = 'width-before-scroll-bar';\nexport var noScrollbarsClassName = 'with-scroll-bars-hidden';\n/**\n * Name of a CSS variable containing the amount of \"hidden\" scrollbar\n * ! might be undefined ! use will fallback!\n */\nexport var removedBarSizeVariable = '--removed-body-scroll-bar-size';\n"], "mappings": "AAAA,OAAO,IAAIA,kBAAkB,GAAG,2BAA2B;AAC3D,OAAO,IAAIC,kBAAkB,GAAG,yBAAyB;AACzD,OAAO,IAAIC,qBAAqB,GAAG,yBAAyB;AAC5D;AACA;AACA;AACA;AACA,OAAO,IAAIC,sBAAsB,GAAG,gCAAgC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}