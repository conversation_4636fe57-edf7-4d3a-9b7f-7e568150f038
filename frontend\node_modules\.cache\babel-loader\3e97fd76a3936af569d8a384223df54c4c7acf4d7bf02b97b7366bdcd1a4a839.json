{"ast": null, "code": "/*\nIE11 support\n */\nexport var toArray = function (a) {\n  var ret = Array(a.length);\n  for (var i = 0; i < a.length; ++i) {\n    ret[i] = a[i];\n  }\n  return ret;\n};\nexport var asArray = function (a) {\n  return Array.isArray(a) ? a : [a];\n};\nexport var getFirst = function (a) {\n  return Array.isArray(a) ? a[0] : a;\n};", "map": {"version": 3, "names": ["toArray", "a", "ret", "Array", "length", "i", "asArray", "isArray", "get<PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/focus-lock/dist/es2015/utils/array.js"], "sourcesContent": ["/*\nIE11 support\n */\nexport var toArray = function (a) {\n    var ret = Array(a.length);\n    for (var i = 0; i < a.length; ++i) {\n        ret[i] = a[i];\n    }\n    return ret;\n};\nexport var asArray = function (a) { return (Array.isArray(a) ? a : [a]); };\nexport var getFirst = function (a) { return (Array.isArray(a) ? a[0] : a); };\n"], "mappings": "AAAA;AACA;AACA;AACA,OAAO,IAAIA,OAAO,GAAG,SAAAA,CAAUC,CAAC,EAAE;EAC9B,IAAIC,GAAG,GAAGC,KAAK,CAACF,CAAC,CAACG,MAAM,CAAC;EACzB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,CAAC,CAACG,MAAM,EAAE,EAAEC,CAAC,EAAE;IAC/BH,GAAG,CAACG,CAAC,CAAC,GAAGJ,CAAC,CAACI,CAAC,CAAC;EACjB;EACA,OAAOH,GAAG;AACd,CAAC;AACD,OAAO,IAAII,OAAO,GAAG,SAAAA,CAAUL,CAAC,EAAE;EAAE,OAAQE,KAAK,CAACI,OAAO,CAACN,CAAC,CAAC,GAAGA,CAAC,GAAG,CAACA,CAAC,CAAC;AAAG,CAAC;AAC1E,OAAO,IAAIO,QAAQ,GAAG,SAAAA,CAAUP,CAAC,EAAE;EAAE,OAAQE,KAAK,CAACI,OAAO,CAACN,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC;AAAG,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}