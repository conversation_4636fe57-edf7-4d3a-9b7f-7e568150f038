{"ast": null, "code": "import { __assign } from \"tslib\";\nimport * as React from 'react';\nimport { useState, useCallback, useEffect, useLayoutEffect } from 'react';\nexport function renderCar(WrappedComponent, defaults) {\n  function State(_a) {\n    var stateRef = _a.stateRef,\n      props = _a.props;\n    var renderTarget = useCallback(function SideTarget() {\n      var args = [];\n      for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n      }\n      useLayoutEffect(function () {\n        stateRef.current(args);\n      });\n      return null;\n    }, []);\n    // @ts-ignore\n    return React.createElement(WrappedComponent, __assign({}, props, {\n      children: renderTarget\n    }));\n  }\n  var Children = React.memo(function (_a) {\n    var stateRef = _a.stateRef,\n      defaultState = _a.defaultState,\n      children = _a.children;\n    var _b = useState(defaultState.current),\n      state = _b[0],\n      setState = _b[1];\n    useEffect(function () {\n      stateRef.current = setState;\n    }, []);\n    return children.apply(void 0, state);\n  }, function () {\n    return true;\n  });\n  return function Combiner(props) {\n    var defaultState = React.useRef(defaults(props));\n    var ref = React.useRef(function (state) {\n      return defaultState.current = state;\n    });\n    return React.createElement(React.Fragment, null, React.createElement(State, {\n      stateRef: ref,\n      props: props\n    }), React.createElement(Children, {\n      stateRef: ref,\n      defaultState: defaultState,\n      children: props.children\n    }));\n  };\n}", "map": {"version": 3, "names": ["__assign", "React", "useState", "useCallback", "useEffect", "useLayoutEffect", "renderCar", "WrappedComponent", "defaults", "State", "_a", "stateRef", "props", "renderTarget", "SideTarget", "args", "_i", "arguments", "length", "current", "createElement", "children", "Children", "memo", "defaultState", "_b", "state", "setState", "apply", "<PERSON><PERSON><PERSON>", "useRef", "ref", "Fragment"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/use-sidecar/dist/es2015/renderProp.js"], "sourcesContent": ["import { __assign } from \"tslib\";\nimport * as React from 'react';\nimport { useState, useCallback, useEffect, useLayoutEffect } from 'react';\nexport function renderCar(WrappedComponent, defaults) {\n    function State(_a) {\n        var stateRef = _a.stateRef, props = _a.props;\n        var renderTarget = useCallback(function SideTarget() {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            useLayoutEffect(function () {\n                stateRef.current(args);\n            });\n            return null;\n        }, []);\n        // @ts-ignore\n        return React.createElement(WrappedComponent, __assign({}, props, { children: renderTarget }));\n    }\n    var Children = React.memo(function (_a) {\n        var stateRef = _a.stateRef, defaultState = _a.defaultState, children = _a.children;\n        var _b = useState(defaultState.current), state = _b[0], setState = _b[1];\n        useEffect(function () {\n            stateRef.current = setState;\n        }, []);\n        return children.apply(void 0, state);\n    }, function () { return true; });\n    return function Combiner(props) {\n        var defaultState = React.useRef(defaults(props));\n        var ref = React.useRef(function (state) { return (defaultState.current = state); });\n        return (React.createElement(React.Fragment, null,\n            React.createElement(State, { stateRef: ref, props: props }),\n            React.createElement(Children, { stateRef: ref, defaultState: defaultState, children: props.children })));\n    };\n}\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,WAAW,EAAEC,SAAS,EAAEC,eAAe,QAAQ,OAAO;AACzE,OAAO,SAASC,SAASA,CAACC,gBAAgB,EAAEC,QAAQ,EAAE;EAClD,SAASC,KAAKA,CAACC,EAAE,EAAE;IACf,IAAIC,QAAQ,GAAGD,EAAE,CAACC,QAAQ;MAAEC,KAAK,GAAGF,EAAE,CAACE,KAAK;IAC5C,IAAIC,YAAY,GAAGV,WAAW,CAAC,SAASW,UAAUA,CAAA,EAAG;MACjD,IAAIC,IAAI,GAAG,EAAE;MACb,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;QAC1CD,IAAI,CAACC,EAAE,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;MAC5B;MACAX,eAAe,CAAC,YAAY;QACxBM,QAAQ,CAACQ,OAAO,CAACJ,IAAI,CAAC;MAC1B,CAAC,CAAC;MACF,OAAO,IAAI;IACf,CAAC,EAAE,EAAE,CAAC;IACN;IACA,OAAOd,KAAK,CAACmB,aAAa,CAACb,gBAAgB,EAAEP,QAAQ,CAAC,CAAC,CAAC,EAAEY,KAAK,EAAE;MAAES,QAAQ,EAAER;IAAa,CAAC,CAAC,CAAC;EACjG;EACA,IAAIS,QAAQ,GAAGrB,KAAK,CAACsB,IAAI,CAAC,UAAUb,EAAE,EAAE;IACpC,IAAIC,QAAQ,GAAGD,EAAE,CAACC,QAAQ;MAAEa,YAAY,GAAGd,EAAE,CAACc,YAAY;MAAEH,QAAQ,GAAGX,EAAE,CAACW,QAAQ;IAClF,IAAII,EAAE,GAAGvB,QAAQ,CAACsB,YAAY,CAACL,OAAO,CAAC;MAAEO,KAAK,GAAGD,EAAE,CAAC,CAAC,CAAC;MAAEE,QAAQ,GAAGF,EAAE,CAAC,CAAC,CAAC;IACxErB,SAAS,CAAC,YAAY;MAClBO,QAAQ,CAACQ,OAAO,GAAGQ,QAAQ;IAC/B,CAAC,EAAE,EAAE,CAAC;IACN,OAAON,QAAQ,CAACO,KAAK,CAAC,KAAK,CAAC,EAAEF,KAAK,CAAC;EACxC,CAAC,EAAE,YAAY;IAAE,OAAO,IAAI;EAAE,CAAC,CAAC;EAChC,OAAO,SAASG,QAAQA,CAACjB,KAAK,EAAE;IAC5B,IAAIY,YAAY,GAAGvB,KAAK,CAAC6B,MAAM,CAACtB,QAAQ,CAACI,KAAK,CAAC,CAAC;IAChD,IAAImB,GAAG,GAAG9B,KAAK,CAAC6B,MAAM,CAAC,UAAUJ,KAAK,EAAE;MAAE,OAAQF,YAAY,CAACL,OAAO,GAAGO,KAAK;IAAG,CAAC,CAAC;IACnF,OAAQzB,KAAK,CAACmB,aAAa,CAACnB,KAAK,CAAC+B,QAAQ,EAAE,IAAI,EAC5C/B,KAAK,CAACmB,aAAa,CAACX,KAAK,EAAE;MAAEE,QAAQ,EAAEoB,GAAG;MAAEnB,KAAK,EAAEA;IAAM,CAAC,CAAC,EAC3DX,KAAK,CAACmB,aAAa,CAACE,QAAQ,EAAE;MAAEX,QAAQ,EAAEoB,GAAG;MAAEP,YAAY,EAAEA,YAAY;MAAEH,QAAQ,EAAET,KAAK,CAACS;IAAS,CAAC,CAAC,CAAC;EAC/G,CAAC;AACL"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}