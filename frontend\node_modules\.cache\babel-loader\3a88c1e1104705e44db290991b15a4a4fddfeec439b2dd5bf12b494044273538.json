{"ast": null, "code": "import { FOCUS_AUTO } from '../constants';\nimport { toArray } from './array';\nimport { tabbables } from './tabbables';\nvar queryTabbables = tabbables.join(',');\nvar queryGuardTabbables = \"\".concat(queryTabbables, \", [data-focus-guard]\");\nvar getFocusablesWithShadowDom = function (parent, withGuards) {\n  return toArray((parent.shadowRoot || parent).children).reduce(function (acc, child) {\n    return acc.concat(child.matches(withGuards ? queryGuardTabbables : queryTabbables) ? [child] : [], getFocusablesWithShadowDom(child));\n  }, []);\n};\nvar getFocusablesWithIFrame = function (parent, withGuards) {\n  var _a;\n  // contentDocument of iframe will be null if current origin cannot access it\n  if (parent instanceof HTMLIFrameElement && ((_a = parent.contentDocument) === null || _a === void 0 ? void 0 : _a.body)) {\n    return getFocusables([parent.contentDocument.body], withGuards);\n  }\n  return [parent];\n};\nexport var getFocusables = function (parents, withGuards) {\n  return parents.reduce(function (acc, parent) {\n    var _a;\n    var focusableWithShadowDom = getFocusablesWithShadowDom(parent, withGuards);\n    var focusableWithIframes = (_a = []).concat.apply(_a, focusableWithShadowDom.map(function (node) {\n      return getFocusablesWithIFrame(node, withGuards);\n    }));\n    return acc.concat(\n    // add all tabbables inside and within shadow DOMs in DOM order\n    focusableWithIframes,\n    // add if node is tabbable itself\n    parent.parentNode ? toArray(parent.parentNode.querySelectorAll(queryTabbables)).filter(function (node) {\n      return node === parent;\n    }) : []);\n  }, []);\n};\n/**\n * return a list of focusable nodes within an area marked as \"auto-focusable\"\n * @param parent\n */\nexport var getParentAutofocusables = function (parent) {\n  var parentFocus = parent.querySelectorAll(\"[\".concat(FOCUS_AUTO, \"]\"));\n  return toArray(parentFocus).map(function (node) {\n    return getFocusables([node]);\n  }).reduce(function (acc, nodes) {\n    return acc.concat(nodes);\n  }, []);\n};", "map": {"version": 3, "names": ["FOCUS_AUTO", "toArray", "tabbables", "queryTabbables", "join", "queryGuard<PERSON>ab<PERSON>bles", "concat", "getFocusablesWithShadowDom", "parent", "<PERSON><PERSON><PERSON><PERSON>", "shadowRoot", "children", "reduce", "acc", "child", "matches", "getFocusablesWithIFrame", "_a", "HTMLIFrameElement", "contentDocument", "body", "getFocusables", "parents", "focusableWithShadowDom", "focusableWithIframes", "apply", "map", "node", "parentNode", "querySelectorAll", "filter", "getParentAutofocusables", "parentFocus", "nodes"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/focus-lock/dist/es2015/utils/tabUtils.js"], "sourcesContent": ["import { FOCUS_AUTO } from '../constants';\nimport { toArray } from './array';\nimport { tabbables } from './tabbables';\nvar queryTabbables = tabbables.join(',');\nvar queryGuardTabbables = \"\".concat(queryTabbables, \", [data-focus-guard]\");\nvar getFocusablesWithShadowDom = function (parent, withGuards) {\n    return toArray((parent.shadowRoot || parent).children).reduce(function (acc, child) {\n        return acc.concat(child.matches(withGuards ? queryGuardTabbables : queryTabbables) ? [child] : [], getFocusablesWithShadowDom(child));\n    }, []);\n};\nvar getFocusablesWithIFrame = function (parent, withGuards) {\n    var _a;\n    // contentDocument of iframe will be null if current origin cannot access it\n    if (parent instanceof HTMLIFrameElement && ((_a = parent.contentDocument) === null || _a === void 0 ? void 0 : _a.body)) {\n        return getFocusables([parent.contentDocument.body], withGuards);\n    }\n    return [parent];\n};\nexport var getFocusables = function (parents, withGuards) {\n    return parents.reduce(function (acc, parent) {\n        var _a;\n        var focusableWithShadowDom = getFocusablesWithShadowDom(parent, withGuards);\n        var focusableWithIframes = (_a = []).concat.apply(_a, focusableWithShadowDom.map(function (node) { return getFocusablesWithIFrame(node, withGuards); }));\n        return acc.concat(\n        // add all tabbables inside and within shadow DOMs in DOM order\n        focusableWithIframes, \n        // add if node is tabbable itself\n        parent.parentNode\n            ? toArray(parent.parentNode.querySelectorAll(queryTabbables)).filter(function (node) { return node === parent; })\n            : []);\n    }, []);\n};\n/**\n * return a list of focusable nodes within an area marked as \"auto-focusable\"\n * @param parent\n */\nexport var getParentAutofocusables = function (parent) {\n    var parentFocus = parent.querySelectorAll(\"[\".concat(FOCUS_AUTO, \"]\"));\n    return toArray(parentFocus)\n        .map(function (node) { return getFocusables([node]); })\n        .reduce(function (acc, nodes) { return acc.concat(nodes); }, []);\n};\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,cAAc;AACzC,SAASC,OAAO,QAAQ,SAAS;AACjC,SAASC,SAAS,QAAQ,aAAa;AACvC,IAAIC,cAAc,GAAGD,SAAS,CAACE,IAAI,CAAC,GAAG,CAAC;AACxC,IAAIC,mBAAmB,GAAG,EAAE,CAACC,MAAM,CAACH,cAAc,EAAE,sBAAsB,CAAC;AAC3E,IAAII,0BAA0B,GAAG,SAAAA,CAAUC,MAAM,EAAEC,UAAU,EAAE;EAC3D,OAAOR,OAAO,CAAC,CAACO,MAAM,CAACE,UAAU,IAAIF,MAAM,EAAEG,QAAQ,CAAC,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAEC,KAAK,EAAE;IAChF,OAAOD,GAAG,CAACP,MAAM,CAACQ,KAAK,CAACC,OAAO,CAACN,UAAU,GAAGJ,mBAAmB,GAAGF,cAAc,CAAC,GAAG,CAACW,KAAK,CAAC,GAAG,EAAE,EAAEP,0BAA0B,CAACO,KAAK,CAAC,CAAC;EACzI,CAAC,EAAE,EAAE,CAAC;AACV,CAAC;AACD,IAAIE,uBAAuB,GAAG,SAAAA,CAAUR,MAAM,EAAEC,UAAU,EAAE;EACxD,IAAIQ,EAAE;EACN;EACA,IAAIT,MAAM,YAAYU,iBAAiB,KAAK,CAACD,EAAE,GAAGT,MAAM,CAACW,eAAe,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,IAAI,CAAC,EAAE;IACrH,OAAOC,aAAa,CAAC,CAACb,MAAM,CAACW,eAAe,CAACC,IAAI,CAAC,EAAEX,UAAU,CAAC;EACnE;EACA,OAAO,CAACD,MAAM,CAAC;AACnB,CAAC;AACD,OAAO,IAAIa,aAAa,GAAG,SAAAA,CAAUC,OAAO,EAAEb,UAAU,EAAE;EACtD,OAAOa,OAAO,CAACV,MAAM,CAAC,UAAUC,GAAG,EAAEL,MAAM,EAAE;IACzC,IAAIS,EAAE;IACN,IAAIM,sBAAsB,GAAGhB,0BAA0B,CAACC,MAAM,EAAEC,UAAU,CAAC;IAC3E,IAAIe,oBAAoB,GAAG,CAACP,EAAE,GAAG,EAAE,EAAEX,MAAM,CAACmB,KAAK,CAACR,EAAE,EAAEM,sBAAsB,CAACG,GAAG,CAAC,UAAUC,IAAI,EAAE;MAAE,OAAOX,uBAAuB,CAACW,IAAI,EAAElB,UAAU,CAAC;IAAE,CAAC,CAAC,CAAC;IACxJ,OAAOI,GAAG,CAACP,MAAM;IACjB;IACAkB,oBAAoB;IACpB;IACAhB,MAAM,CAACoB,UAAU,GACX3B,OAAO,CAACO,MAAM,CAACoB,UAAU,CAACC,gBAAgB,CAAC1B,cAAc,CAAC,CAAC,CAAC2B,MAAM,CAAC,UAAUH,IAAI,EAAE;MAAE,OAAOA,IAAI,KAAKnB,MAAM;IAAE,CAAC,CAAC,GAC/G,EAAE,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;AACV,CAAC;AACD;AACA;AACA;AACA;AACA,OAAO,IAAIuB,uBAAuB,GAAG,SAAAA,CAAUvB,MAAM,EAAE;EACnD,IAAIwB,WAAW,GAAGxB,MAAM,CAACqB,gBAAgB,CAAC,GAAG,CAACvB,MAAM,CAACN,UAAU,EAAE,GAAG,CAAC,CAAC;EACtE,OAAOC,OAAO,CAAC+B,WAAW,CAAC,CACtBN,GAAG,CAAC,UAAUC,IAAI,EAAE;IAAE,OAAON,aAAa,CAAC,CAACM,IAAI,CAAC,CAAC;EAAE,CAAC,CAAC,CACtDf,MAAM,CAAC,UAAUC,GAAG,EAAEoB,KAAK,EAAE;IAAE,OAAOpB,GAAG,CAACP,MAAM,CAAC2B,KAAK,CAAC;EAAE,CAAC,EAAE,EAAE,CAAC;AACxE,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}