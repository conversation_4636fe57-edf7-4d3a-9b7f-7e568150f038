{"ast": null, "code": "import { correctNode } from './correctFocus';\nexport var pickFirstFocus = function (nodes) {\n  if (nodes[0] && nodes.length > 1) {\n    return correctNode(nodes[0], nodes);\n  }\n  return nodes[0];\n};\nexport var pickFocusable = function (nodes, index) {\n  if (nodes.length > 1) {\n    return nodes.indexOf(correctNode(nodes[index], nodes));\n  }\n  return index;\n};", "map": {"version": 3, "names": ["correctNode", "pickFirstFocus", "nodes", "length", "pickFocusable", "index", "indexOf"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/focus-lock/dist/es2015/utils/firstFocus.js"], "sourcesContent": ["import { correctNode } from './correctFocus';\nexport var pickFirstFocus = function (nodes) {\n    if (nodes[0] && nodes.length > 1) {\n        return correctNode(nodes[0], nodes);\n    }\n    return nodes[0];\n};\nexport var pickFocusable = function (nodes, index) {\n    if (nodes.length > 1) {\n        return nodes.indexOf(correctNode(nodes[index], nodes));\n    }\n    return index;\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,gBAAgB;AAC5C,OAAO,IAAIC,cAAc,GAAG,SAAAA,CAAUC,KAAK,EAAE;EACzC,IAAIA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;IAC9B,OAAOH,WAAW,CAACE,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC;EACvC;EACA,OAAOA,KAAK,CAAC,CAAC,CAAC;AACnB,CAAC;AACD,OAAO,IAAIE,aAAa,GAAG,SAAAA,CAAUF,KAAK,EAAEG,KAAK,EAAE;EAC/C,IAAIH,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;IAClB,OAAOD,KAAK,CAACI,OAAO,CAACN,WAAW,CAACE,KAAK,CAACG,KAAK,CAAC,EAAEH,KAAK,CAAC,CAAC;EAC1D;EACA,OAAOG,KAAK;AAChB,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}