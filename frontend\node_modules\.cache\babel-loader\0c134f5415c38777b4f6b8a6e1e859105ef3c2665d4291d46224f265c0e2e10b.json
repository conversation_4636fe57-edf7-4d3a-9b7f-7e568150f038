{"ast": null, "code": "import { createSidecarMedium } from 'use-sidecar';\nexport var effectCar = createSidecarMedium();", "map": {"version": 3, "names": ["createSidecarMedium", "effectCar"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/react-remove-scroll/dist/es2015/medium.js"], "sourcesContent": ["import { createSidecarMedium } from 'use-sidecar';\nexport var effectCar = createSidecarMedium();\n"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,aAAa;AACjD,OAAO,IAAIC,SAAS,GAAGD,mBAAmB,CAAC,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}