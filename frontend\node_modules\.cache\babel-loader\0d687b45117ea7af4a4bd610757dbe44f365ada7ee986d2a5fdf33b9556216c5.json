{"ast": null, "code": "import { isTouchEvent } from \"./chunk-B7KYFEHM.mjs\";\n\n// src/get-event-point.ts\nfunction pointFromTouch(e, type = \"page\") {\n  const point = e.touches[0] || e.changedTouches[0];\n  return {\n    x: point[`${type}X`],\n    y: point[`${type}Y`]\n  };\n}\nfunction pointFromMouse(point, type = \"page\") {\n  return {\n    x: point[`${type}X`],\n    y: point[`${type}Y`]\n  };\n}\nfunction getEventPoint(event, type = \"page\") {\n  return isTouchEvent(event) ? pointFromTouch(event, type) : pointFromMouse(event, type);\n}\nexport { getEventPoint };", "map": {"version": 3, "names": ["isTouchEvent", "pointFromTouch", "e", "type", "point", "touches", "changedTouches", "x", "y", "pointFromMouse", "getEventPoint", "event"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/@chakra-ui/event-utils/dist/chunk-6FBKF3LK.mjs"], "sourcesContent": ["import {\n  isTouchEvent\n} from \"./chunk-B7KYFEHM.mjs\";\n\n// src/get-event-point.ts\nfunction pointFromTouch(e, type = \"page\") {\n  const point = e.touches[0] || e.changedTouches[0];\n  return { x: point[`${type}X`], y: point[`${type}Y`] };\n}\nfunction pointFromMouse(point, type = \"page\") {\n  return {\n    x: point[`${type}X`],\n    y: point[`${type}Y`]\n  };\n}\nfunction getEventPoint(event, type = \"page\") {\n  return isTouchEvent(event) ? pointFromTouch(event, type) : pointFromMouse(event, type);\n}\n\nexport {\n  getEventPoint\n};\n"], "mappings": "AAAA,SACEA,YAAY,QACP,sBAAsB;;AAE7B;AACA,SAASC,cAAcA,CAACC,CAAC,EAAEC,IAAI,GAAG,MAAM,EAAE;EACxC,MAAMC,KAAK,GAAGF,CAAC,CAACG,OAAO,CAAC,CAAC,CAAC,IAAIH,CAAC,CAACI,cAAc,CAAC,CAAC,CAAC;EACjD,OAAO;IAAEC,CAAC,EAAEH,KAAK,CAAE,GAAED,IAAK,GAAE,CAAC;IAAEK,CAAC,EAAEJ,KAAK,CAAE,GAAED,IAAK,GAAE;EAAE,CAAC;AACvD;AACA,SAASM,cAAcA,CAACL,KAAK,EAAED,IAAI,GAAG,MAAM,EAAE;EAC5C,OAAO;IACLI,CAAC,EAAEH,KAAK,CAAE,GAAED,IAAK,GAAE,CAAC;IACpBK,CAAC,EAAEJ,KAAK,CAAE,GAAED,IAAK,GAAE;EACrB,CAAC;AACH;AACA,SAASO,aAAaA,CAACC,KAAK,EAAER,IAAI,GAAG,MAAM,EAAE;EAC3C,OAAOH,YAAY,CAACW,KAAK,CAAC,GAAGV,cAAc,CAACU,KAAK,EAAER,IAAI,CAAC,GAAGM,cAAc,CAACE,KAAK,EAAER,IAAI,CAAC;AACxF;AAEA,SACEO,aAAa"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}