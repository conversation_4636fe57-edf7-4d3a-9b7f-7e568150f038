{"ast": null, "code": "/**\n * defines a focus group\n */\nexport var FOCUS_GROUP = 'data-focus-lock';\n/**\n * disables element discovery inside a group marked by key\n */\nexport var FOCUS_DISABLED = 'data-focus-lock-disabled';\n/**\n * allows uncontrolled focus within the marked area, effectively disabling focus lock for it's content\n */\nexport var FOCUS_ALLOW = 'data-no-focus-lock';\n/**\n * instructs autofocus engine to pick default autofocus inside a given node\n * can be set on the element or container\n */\nexport var FOCUS_AUTO = 'data-autofocus-inside';\n/**\n * instructs autofocus to ignore elements within a given node\n * can be set on the element or container\n */\nexport var FOCUS_NO_AUTOFOCUS = 'data-no-autofocus';", "map": {"version": 3, "names": ["FOCUS_GROUP", "FOCUS_DISABLED", "FOCUS_ALLOW", "FOCUS_AUTO", "FOCUS_NO_AUTOFOCUS"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/focus-lock/dist/es2015/constants.js"], "sourcesContent": ["/**\n * defines a focus group\n */\nexport var FOCUS_GROUP = 'data-focus-lock';\n/**\n * disables element discovery inside a group marked by key\n */\nexport var FOCUS_DISABLED = 'data-focus-lock-disabled';\n/**\n * allows uncontrolled focus within the marked area, effectively disabling focus lock for it's content\n */\nexport var FOCUS_ALLOW = 'data-no-focus-lock';\n/**\n * instructs autofocus engine to pick default autofocus inside a given node\n * can be set on the element or container\n */\nexport var FOCUS_AUTO = 'data-autofocus-inside';\n/**\n * instructs autofocus to ignore elements within a given node\n * can be set on the element or container\n */\nexport var FOCUS_NO_AUTOFOCUS = 'data-no-autofocus';\n"], "mappings": "AAAA;AACA;AACA;AACA,OAAO,IAAIA,WAAW,GAAG,iBAAiB;AAC1C;AACA;AACA;AACA,OAAO,IAAIC,cAAc,GAAG,0BAA0B;AACtD;AACA;AACA;AACA,OAAO,IAAIC,WAAW,GAAG,oBAAoB;AAC7C;AACA;AACA;AACA;AACA,OAAO,IAAIC,UAAU,GAAG,uBAAuB;AAC/C;AACA;AACA;AACA;AACA,OAAO,IAAIC,kBAAkB,GAAG,mBAAmB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}