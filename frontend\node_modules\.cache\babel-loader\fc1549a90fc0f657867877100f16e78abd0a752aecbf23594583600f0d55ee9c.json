{"ast": null, "code": "import { anatomy } from \"./chunk-OA3DH5LS.mjs\";\n\n// src/components.ts\nvar accordionAnatomy = anatomy(\"accordion\").parts(\"root\", \"container\", \"button\", \"panel\").extend(\"icon\");\nvar alertAnatomy = anatomy(\"alert\").parts(\"title\", \"description\", \"container\").extend(\"icon\", \"spinner\");\nvar avatarAnatomy = anatomy(\"avatar\").parts(\"label\", \"badge\", \"container\").extend(\"excessLabel\", \"group\");\nvar breadcrumbAnatomy = anatomy(\"breadcrumb\").parts(\"link\", \"item\", \"container\").extend(\"separator\");\nvar buttonAnatomy = anatomy(\"button\").parts();\nvar checkboxAnatomy = anatomy(\"checkbox\").parts(\"control\", \"icon\", \"container\").extend(\"label\");\nvar circularProgressAnatomy = anatomy(\"progress\").parts(\"track\", \"filledTrack\").extend(\"label\");\nvar drawerAnatomy = anatomy(\"drawer\").parts(\"overlay\", \"dialogContainer\", \"dialog\").extend(\"header\", \"closeButton\", \"body\", \"footer\");\nvar editableAnatomy = anatomy(\"editable\").parts(\"preview\", \"input\", \"textarea\");\nvar formAnatomy = anatomy(\"form\").parts(\"container\", \"requiredIndicator\", \"helperText\");\nvar formErrorAnatomy = anatomy(\"formError\").parts(\"text\", \"icon\");\nvar inputAnatomy = anatomy(\"input\").parts(\"addon\", \"field\", \"element\", \"group\");\nvar listAnatomy = anatomy(\"list\").parts(\"container\", \"item\", \"icon\");\nvar menuAnatomy = anatomy(\"menu\").parts(\"button\", \"list\", \"item\").extend(\"groupTitle\", \"icon\", \"command\", \"divider\");\nvar modalAnatomy = anatomy(\"modal\").parts(\"overlay\", \"dialogContainer\", \"dialog\").extend(\"header\", \"closeButton\", \"body\", \"footer\");\nvar numberInputAnatomy = anatomy(\"numberinput\").parts(\"root\", \"field\", \"stepperGroup\", \"stepper\");\nvar pinInputAnatomy = anatomy(\"pininput\").parts(\"field\");\nvar popoverAnatomy = anatomy(\"popover\").parts(\"content\", \"header\", \"body\", \"footer\").extend(\"popper\", \"arrow\", \"closeButton\");\nvar progressAnatomy = anatomy(\"progress\").parts(\"label\", \"filledTrack\", \"track\");\nvar radioAnatomy = anatomy(\"radio\").parts(\"container\", \"control\", \"label\");\nvar selectAnatomy = anatomy(\"select\").parts(\"field\", \"icon\");\nvar sliderAnatomy = anatomy(\"slider\").parts(\"container\", \"track\", \"thumb\", \"filledTrack\", \"mark\");\nvar statAnatomy = anatomy(\"stat\").parts(\"container\", \"label\", \"helpText\", \"number\", \"icon\");\nvar switchAnatomy = anatomy(\"switch\").parts(\"container\", \"track\", \"thumb\");\nvar tableAnatomy = anatomy(\"table\").parts(\"table\", \"thead\", \"tbody\", \"tr\", \"th\", \"td\", \"tfoot\", \"caption\");\nvar tabsAnatomy = anatomy(\"tabs\").parts(\"root\", \"tab\", \"tablist\", \"tabpanel\", \"tabpanels\", \"indicator\");\nvar tagAnatomy = anatomy(\"tag\").parts(\"container\", \"label\", \"closeButton\");\nvar cardAnatomy = anatomy(\"card\").parts(\"container\", \"header\", \"body\", \"footer\");\nexport { accordionAnatomy, alertAnatomy, avatarAnatomy, breadcrumbAnatomy, buttonAnatomy, checkboxAnatomy, circularProgressAnatomy, drawerAnatomy, editableAnatomy, formAnatomy, formErrorAnatomy, inputAnatomy, listAnatomy, menuAnatomy, modalAnatomy, numberInputAnatomy, pinInputAnatomy, popoverAnatomy, progressAnatomy, radioAnatomy, selectAnatomy, sliderAnatomy, statAnatomy, switchAnatomy, tableAnatomy, tabsAnatomy, tagAnatomy, cardAnatomy };", "map": {"version": 3, "names": ["accordionAnatomy", "anatomy", "parts", "extend", "alertAnatomy", "avatarAnatomy", "breadcrumbAnatomy", "buttonAnatomy", "checkboxAnatomy", "circularProgressAnatomy", "drawerAnatomy", "editableAnatomy", "formAnatomy", "formErrorAnatomy", "inputAnatomy", "listAnatomy", "menuAnatomy", "modalAnatomy", "numberInputAnatomy", "pinInputAnatomy", "popoverAnatomy", "progressAnatomy", "radioAnatomy", "selectAnatomy", "sliderAnatomy", "statAnatomy", "switchAnatomy", "tableAnatomy", "tabsAnatomy", "tagAnatomy", "cardAnatomy"], "sources": ["C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\@chakra-ui\\anatomy\\src\\components.ts"], "sourcesContent": ["import { anatomy } from \"./anatomy\"\n\n/**\n * **Accordion anatomy**\n * - Root: the root container of the accordion\n * - Container: the accordion item contains the button and panel\n * - Button: the button is the trigger for the panel\n * - Panel: the panel is the content of the accordion item\n * - Icon: the expanded/collapsed icon\n */\nexport const accordionAnatomy = anatomy(\"accordion\")\n  .parts(\"root\", \"container\", \"button\", \"panel\")\n  .extend(\"icon\")\n\n/**\n * **Alert anatomy**\n * - Title: the alert's title\n * - Description: the alert's description\n * - Icon: the alert's icon\n */\nexport const alertAnatomy = anatomy(\"alert\")\n  .parts(\"title\", \"description\", \"container\")\n  .extend(\"icon\", \"spinner\")\n\n/**\n * **Avatar anatomy**\n * - Container: the container for the avatar\n * - Label: the avatar initials text\n * - Excess Label: the label or text that represents excess avatar count.\n * Typically used in avatar groups.\n * - Group: the container for the avatar group\n */\nexport const avatarAnatomy = anatomy(\"avatar\")\n  .parts(\"label\", \"badge\", \"container\")\n  .extend(\"excessLabel\", \"group\")\n\n/**\n * **Breadcrumb anatomy**\n * - Item: the container for a breadcrumb item\n * - Link: the element that represents the breadcrumb link\n * - Container: the container for the breadcrumb items\n * - Separator: the separator between breadcrumb items\n */\nexport const breadcrumbAnatomy = anatomy(\"breadcrumb\")\n  .parts(\"link\", \"item\", \"container\")\n  .extend(\"separator\")\n\nexport const buttonAnatomy = anatomy(\"button\").parts()\n\nexport const checkboxAnatomy = anatomy(\"checkbox\")\n  .parts(\"control\", \"icon\", \"container\")\n  .extend(\"label\")\n\nexport const circularProgressAnatomy = anatomy(\"progress\")\n  .parts(\"track\", \"filledTrack\")\n  .extend(\"label\")\n\nexport const drawerAnatomy = anatomy(\"drawer\")\n  .parts(\"overlay\", \"dialogContainer\", \"dialog\")\n  .extend(\"header\", \"closeButton\", \"body\", \"footer\")\n\nexport const editableAnatomy = anatomy(\"editable\").parts(\n  \"preview\",\n  \"input\",\n  \"textarea\",\n)\n\nexport const formAnatomy = anatomy(\"form\").parts(\n  \"container\",\n  \"requiredIndicator\",\n  \"helperText\",\n)\n\nexport const formErrorAnatomy = anatomy(\"formError\").parts(\"text\", \"icon\")\n\nexport const inputAnatomy = anatomy(\"input\").parts(\n  \"addon\",\n  \"field\",\n  \"element\",\n  \"group\",\n)\n\nexport const listAnatomy = anatomy(\"list\").parts(\"container\", \"item\", \"icon\")\n\nexport const menuAnatomy = anatomy(\"menu\")\n  .parts(\"button\", \"list\", \"item\")\n  .extend(\"groupTitle\", \"icon\", \"command\", \"divider\")\n\nexport const modalAnatomy = anatomy(\"modal\")\n  .parts(\"overlay\", \"dialogContainer\", \"dialog\")\n  .extend(\"header\", \"closeButton\", \"body\", \"footer\")\n\nexport const numberInputAnatomy = anatomy(\"numberinput\").parts(\n  \"root\",\n  \"field\",\n  \"stepperGroup\",\n  \"stepper\",\n)\n\nexport const pinInputAnatomy = anatomy(\"pininput\").parts(\"field\")\n\nexport const popoverAnatomy = anatomy(\"popover\")\n  .parts(\"content\", \"header\", \"body\", \"footer\")\n  .extend(\"popper\", \"arrow\", \"closeButton\")\n\nexport const progressAnatomy = anatomy(\"progress\").parts(\n  \"label\",\n  \"filledTrack\",\n  \"track\",\n)\n\nexport const radioAnatomy = anatomy(\"radio\").parts(\n  \"container\",\n  \"control\",\n  \"label\",\n)\n\nexport const selectAnatomy = anatomy(\"select\").parts(\"field\", \"icon\")\n\nexport const sliderAnatomy = anatomy(\"slider\").parts(\n  \"container\",\n  \"track\",\n  \"thumb\",\n  \"filledTrack\",\n  \"mark\",\n)\n\nexport const statAnatomy = anatomy(\"stat\").parts(\n  \"container\",\n  \"label\",\n  \"helpText\",\n  \"number\",\n  \"icon\",\n)\n\nexport const switchAnatomy = anatomy(\"switch\").parts(\n  \"container\",\n  \"track\",\n  \"thumb\",\n)\n\nexport const tableAnatomy = anatomy(\"table\").parts(\n  \"table\",\n  \"thead\",\n  \"tbody\",\n  \"tr\",\n  \"th\",\n  \"td\",\n  \"tfoot\",\n  \"caption\",\n)\n\nexport const tabsAnatomy = anatomy(\"tabs\").parts(\n  \"root\",\n  \"tab\",\n  \"tablist\",\n  \"tabpanel\",\n  \"tabpanels\",\n  \"indicator\",\n)\n\n/**\n * **Tag anatomy**\n * - Container: the container for the tag\n * - Label: the text content of the tag\n * - closeButton: the close button for the tag\n */\nexport const tagAnatomy = anatomy(\"tag\").parts(\n  \"container\",\n  \"label\",\n  \"closeButton\",\n)\n\nexport const cardAnatomy = anatomy(\"card\").parts(\n  \"container\",\n  \"header\",\n  \"body\",\n  \"footer\",\n)\n"], "mappings": ";;;AAUO,IAAMA,gBAAA,GAAmBC,OAAA,CAAQ,WAAW,EAChDC,KAAA,CAAM,QAAQ,aAAa,UAAU,OAAO,EAC5CC,MAAA,CAAO,MAAM;AAQT,IAAMC,YAAA,GAAeH,OAAA,CAAQ,OAAO,EACxCC,KAAA,CAAM,SAAS,eAAe,WAAW,EACzCC,MAAA,CAAO,QAAQ,SAAS;AAUpB,IAAME,aAAA,GAAgBJ,OAAA,CAAQ,QAAQ,EAC1CC,KAAA,CAAM,SAAS,SAAS,WAAW,EACnCC,MAAA,CAAO,eAAe,OAAO;AASzB,IAAMG,iBAAA,GAAoBL,OAAA,CAAQ,YAAY,EAClDC,KAAA,CAAM,QAAQ,QAAQ,WAAW,EACjCC,MAAA,CAAO,WAAW;AAEd,IAAMI,aAAA,GAAgBN,OAAA,CAAQ,QAAQ,EAAEC,KAAA,CAAM;AAE9C,IAAMM,eAAA,GAAkBP,OAAA,CAAQ,UAAU,EAC9CC,KAAA,CAAM,WAAW,QAAQ,WAAW,EACpCC,MAAA,CAAO,OAAO;AAEV,IAAMM,uBAAA,GAA0BR,OAAA,CAAQ,UAAU,EACtDC,KAAA,CAAM,SAAS,aAAa,EAC5BC,MAAA,CAAO,OAAO;AAEV,IAAMO,aAAA,GAAgBT,OAAA,CAAQ,QAAQ,EAC1CC,KAAA,CAAM,WAAW,mBAAmB,QAAQ,EAC5CC,MAAA,CAAO,UAAU,eAAe,QAAQ,QAAQ;AAE5C,IAAMQ,eAAA,GAAkBV,OAAA,CAAQ,UAAU,EAAEC,KAAA,CACjD,WACA,SACA,UACF;AAEO,IAAMU,WAAA,GAAcX,OAAA,CAAQ,MAAM,EAAEC,KAAA,CACzC,aACA,qBACA,YACF;AAEO,IAAMW,gBAAA,GAAmBZ,OAAA,CAAQ,WAAW,EAAEC,KAAA,CAAM,QAAQ,MAAM;AAElE,IAAMY,YAAA,GAAeb,OAAA,CAAQ,OAAO,EAAEC,KAAA,CAC3C,SACA,SACA,WACA,OACF;AAEO,IAAMa,WAAA,GAAcd,OAAA,CAAQ,MAAM,EAAEC,KAAA,CAAM,aAAa,QAAQ,MAAM;AAErE,IAAMc,WAAA,GAAcf,OAAA,CAAQ,MAAM,EACtCC,KAAA,CAAM,UAAU,QAAQ,MAAM,EAC9BC,MAAA,CAAO,cAAc,QAAQ,WAAW,SAAS;AAE7C,IAAMc,YAAA,GAAehB,OAAA,CAAQ,OAAO,EACxCC,KAAA,CAAM,WAAW,mBAAmB,QAAQ,EAC5CC,MAAA,CAAO,UAAU,eAAe,QAAQ,QAAQ;AAE5C,IAAMe,kBAAA,GAAqBjB,OAAA,CAAQ,aAAa,EAAEC,KAAA,CACvD,QACA,SACA,gBACA,SACF;AAEO,IAAMiB,eAAA,GAAkBlB,OAAA,CAAQ,UAAU,EAAEC,KAAA,CAAM,OAAO;AAEzD,IAAMkB,cAAA,GAAiBnB,OAAA,CAAQ,SAAS,EAC5CC,KAAA,CAAM,WAAW,UAAU,QAAQ,QAAQ,EAC3CC,MAAA,CAAO,UAAU,SAAS,aAAa;AAEnC,IAAMkB,eAAA,GAAkBpB,OAAA,CAAQ,UAAU,EAAEC,KAAA,CACjD,SACA,eACA,OACF;AAEO,IAAMoB,YAAA,GAAerB,OAAA,CAAQ,OAAO,EAAEC,KAAA,CAC3C,aACA,WACA,OACF;AAEO,IAAMqB,aAAA,GAAgBtB,OAAA,CAAQ,QAAQ,EAAEC,KAAA,CAAM,SAAS,MAAM;AAE7D,IAAMsB,aAAA,GAAgBvB,OAAA,CAAQ,QAAQ,EAAEC,KAAA,CAC7C,aACA,SACA,SACA,eACA,MACF;AAEO,IAAMuB,WAAA,GAAcxB,OAAA,CAAQ,MAAM,EAAEC,KAAA,CACzC,aACA,SACA,YACA,UACA,MACF;AAEO,IAAMwB,aAAA,GAAgBzB,OAAA,CAAQ,QAAQ,EAAEC,KAAA,CAC7C,aACA,SACA,OACF;AAEO,IAAMyB,YAAA,GAAe1B,OAAA,CAAQ,OAAO,EAAEC,KAAA,CAC3C,SACA,SACA,SACA,MACA,MACA,MACA,SACA,SACF;AAEO,IAAM0B,WAAA,GAAc3B,OAAA,CAAQ,MAAM,EAAEC,KAAA,CACzC,QACA,OACA,WACA,YACA,aACA,WACF;AAQO,IAAM2B,UAAA,GAAa5B,OAAA,CAAQ,KAAK,EAAEC,KAAA,CACvC,aACA,SACA,aACF;AAEO,IAAM4B,WAAA,GAAc7B,OAAA,CAAQ,MAAM,EAAEC,KAAA,CACzC,aACA,UACA,QACA,QACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}