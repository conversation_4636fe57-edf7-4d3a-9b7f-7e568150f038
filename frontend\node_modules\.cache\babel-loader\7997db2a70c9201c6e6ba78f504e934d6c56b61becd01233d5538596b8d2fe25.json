{"ast": null, "code": "import { createMedium, createSidecarMedium } from 'use-sidecar';\nexport var mediumFocus = createMedium({}, function (_ref) {\n  var target = _ref.target,\n    currentTarget = _ref.currentTarget;\n  return {\n    target: target,\n    currentTarget: currentTarget\n  };\n});\nexport var mediumBlur = createMedium();\nexport var mediumEffect = createMedium();\nexport var mediumSidecar = createSidecarMedium({\n  async: true // focus-lock sidecar is not required on the server\n  // however, it might be required for JSDOM tests\n  // ssr: true,\n});", "map": {"version": 3, "names": ["createMedium", "createSidecarMedium", "mediumFocus", "_ref", "target", "currentTarget", "mediumBlur", "mediumEffect", "mediumSidecar", "async"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/react-focus-lock/dist/es2015/medium.js"], "sourcesContent": ["import { createMedium, createSidecarMedium } from 'use-sidecar';\nexport var mediumFocus = createMedium({}, function (_ref) {\n  var target = _ref.target,\n      currentTarget = _ref.currentTarget;\n  return {\n    target: target,\n    currentTarget: currentTarget\n  };\n});\nexport var mediumBlur = createMedium();\nexport var mediumEffect = createMedium();\nexport var mediumSidecar = createSidecarMedium({\n  async: true // focus-lock sidecar is not required on the server\n  // however, it might be required for JSDOM tests\n  // ssr: true,\n\n});"], "mappings": "AAAA,SAASA,YAAY,EAAEC,mBAAmB,QAAQ,aAAa;AAC/D,OAAO,IAAIC,WAAW,GAAGF,YAAY,CAAC,CAAC,CAAC,EAAE,UAAUG,IAAI,EAAE;EACxD,IAAIC,MAAM,GAAGD,IAAI,CAACC,MAAM;IACpBC,aAAa,GAAGF,IAAI,CAACE,aAAa;EACtC,OAAO;IACLD,MAAM,EAAEA,MAAM;IACdC,aAAa,EAAEA;EACjB,CAAC;AACH,CAAC,CAAC;AACF,OAAO,IAAIC,UAAU,GAAGN,YAAY,CAAC,CAAC;AACtC,OAAO,IAAIO,YAAY,GAAGP,YAAY,CAAC,CAAC;AACxC,OAAO,IAAIQ,aAAa,GAAGP,mBAAmB,CAAC;EAC7CQ,KAAK,EAAE,IAAI,CAAC;EACZ;EACA;AAEF,CAAC,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}