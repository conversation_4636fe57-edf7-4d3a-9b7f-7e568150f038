{"ast": null, "code": "import { __spreadArray } from \"tslib\";\nimport * as React from 'react';\nimport { RemoveScrollBar } from 'react-remove-scroll-bar';\nimport { styleSingleton } from 'react-style-singleton';\nimport { nonPassive } from './aggresiveCapture';\nimport { handleScroll, locationCouldBeScrolled } from './handleScroll';\nexport var getTouchXY = function (event) {\n  return 'changedTouches' in event ? [event.changedTouches[0].clientX, event.changedTouches[0].clientY] : [0, 0];\n};\nexport var getDeltaXY = function (event) {\n  return [event.deltaX, event.deltaY];\n};\nvar extractRef = function (ref) {\n  return ref && 'current' in ref ? ref.current : ref;\n};\nvar deltaCompare = function (x, y) {\n  return x[0] === y[0] && x[1] === y[1];\n};\nvar generateStyle = function (id) {\n  return \"\\n  .block-interactivity-\".concat(id, \" {pointer-events: none;}\\n  .allow-interactivity-\").concat(id, \" {pointer-events: all;}\\n\");\n};\nvar idCounter = 0;\nvar lockStack = [];\nexport function RemoveScrollSideCar(props) {\n  var shouldPreventQueue = React.useRef([]);\n  var touchStartRef = React.useRef([0, 0]);\n  var activeAxis = React.useRef();\n  var id = React.useState(idCounter++)[0];\n  var Style = React.useState(styleSingleton)[0];\n  var lastProps = React.useRef(props);\n  React.useEffect(function () {\n    lastProps.current = props;\n  }, [props]);\n  React.useEffect(function () {\n    if (props.inert) {\n      document.body.classList.add(\"block-interactivity-\".concat(id));\n      var allow_1 = __spreadArray([props.lockRef.current], (props.shards || []).map(extractRef), true).filter(Boolean);\n      allow_1.forEach(function (el) {\n        return el.classList.add(\"allow-interactivity-\".concat(id));\n      });\n      return function () {\n        document.body.classList.remove(\"block-interactivity-\".concat(id));\n        allow_1.forEach(function (el) {\n          return el.classList.remove(\"allow-interactivity-\".concat(id));\n        });\n      };\n    }\n    return;\n  }, [props.inert, props.lockRef.current, props.shards]);\n  var shouldCancelEvent = React.useCallback(function (event, parent) {\n    if ('touches' in event && event.touches.length === 2) {\n      return !lastProps.current.allowPinchZoom;\n    }\n    var touch = getTouchXY(event);\n    var touchStart = touchStartRef.current;\n    var deltaX = 'deltaX' in event ? event.deltaX : touchStart[0] - touch[0];\n    var deltaY = 'deltaY' in event ? event.deltaY : touchStart[1] - touch[1];\n    var currentAxis;\n    var target = event.target;\n    var moveDirection = Math.abs(deltaX) > Math.abs(deltaY) ? 'h' : 'v';\n    // allow horizontal touch move on Range inputs. They will not cause any scroll\n    if ('touches' in event && moveDirection === 'h' && target.type === 'range') {\n      return false;\n    }\n    var canBeScrolledInMainDirection = locationCouldBeScrolled(moveDirection, target);\n    if (!canBeScrolledInMainDirection) {\n      return true;\n    }\n    if (canBeScrolledInMainDirection) {\n      currentAxis = moveDirection;\n    } else {\n      currentAxis = moveDirection === 'v' ? 'h' : 'v';\n      canBeScrolledInMainDirection = locationCouldBeScrolled(moveDirection, target);\n      // other axis might be not scrollable\n    }\n\n    if (!canBeScrolledInMainDirection) {\n      return false;\n    }\n    if (!activeAxis.current && 'changedTouches' in event && (deltaX || deltaY)) {\n      activeAxis.current = currentAxis;\n    }\n    if (!currentAxis) {\n      return true;\n    }\n    var cancelingAxis = activeAxis.current || currentAxis;\n    return handleScroll(cancelingAxis, parent, event, cancelingAxis === 'h' ? deltaX : deltaY, true);\n  }, []);\n  var shouldPrevent = React.useCallback(function (_event) {\n    var event = _event;\n    if (!lockStack.length || lockStack[lockStack.length - 1] !== Style) {\n      // not the last active\n      return;\n    }\n    var delta = 'deltaY' in event ? getDeltaXY(event) : getTouchXY(event);\n    var sourceEvent = shouldPreventQueue.current.filter(function (e) {\n      return e.name === event.type && (e.target === event.target || event.target === e.shadowParent) && deltaCompare(e.delta, delta);\n    })[0];\n    // self event, and should be canceled\n    if (sourceEvent && sourceEvent.should) {\n      if (event.cancelable) {\n        event.preventDefault();\n      }\n      return;\n    }\n    // outside or shard event\n    if (!sourceEvent) {\n      var shardNodes = (lastProps.current.shards || []).map(extractRef).filter(Boolean).filter(function (node) {\n        return node.contains(event.target);\n      });\n      var shouldStop = shardNodes.length > 0 ? shouldCancelEvent(event, shardNodes[0]) : !lastProps.current.noIsolation;\n      if (shouldStop) {\n        if (event.cancelable) {\n          event.preventDefault();\n        }\n      }\n    }\n  }, []);\n  var shouldCancel = React.useCallback(function (name, delta, target, should) {\n    var event = {\n      name: name,\n      delta: delta,\n      target: target,\n      should: should,\n      shadowParent: getOutermostShadowParent(target)\n    };\n    shouldPreventQueue.current.push(event);\n    setTimeout(function () {\n      shouldPreventQueue.current = shouldPreventQueue.current.filter(function (e) {\n        return e !== event;\n      });\n    }, 1);\n  }, []);\n  var scrollTouchStart = React.useCallback(function (event) {\n    touchStartRef.current = getTouchXY(event);\n    activeAxis.current = undefined;\n  }, []);\n  var scrollWheel = React.useCallback(function (event) {\n    shouldCancel(event.type, getDeltaXY(event), event.target, shouldCancelEvent(event, props.lockRef.current));\n  }, []);\n  var scrollTouchMove = React.useCallback(function (event) {\n    shouldCancel(event.type, getTouchXY(event), event.target, shouldCancelEvent(event, props.lockRef.current));\n  }, []);\n  React.useEffect(function () {\n    lockStack.push(Style);\n    props.setCallbacks({\n      onScrollCapture: scrollWheel,\n      onWheelCapture: scrollWheel,\n      onTouchMoveCapture: scrollTouchMove\n    });\n    document.addEventListener('wheel', shouldPrevent, nonPassive);\n    document.addEventListener('touchmove', shouldPrevent, nonPassive);\n    document.addEventListener('touchstart', scrollTouchStart, nonPassive);\n    return function () {\n      lockStack = lockStack.filter(function (inst) {\n        return inst !== Style;\n      });\n      document.removeEventListener('wheel', shouldPrevent, nonPassive);\n      document.removeEventListener('touchmove', shouldPrevent, nonPassive);\n      document.removeEventListener('touchstart', scrollTouchStart, nonPassive);\n    };\n  }, []);\n  var removeScrollBar = props.removeScrollBar,\n    inert = props.inert;\n  return React.createElement(React.Fragment, null, inert ? React.createElement(Style, {\n    styles: generateStyle(id)\n  }) : null, removeScrollBar ? React.createElement(RemoveScrollBar, {\n    gapMode: props.gapMode\n  }) : null);\n}\nfunction getOutermostShadowParent(node) {\n  var shadowParent = null;\n  while (node !== null) {\n    if (node instanceof ShadowRoot) {\n      shadowParent = node.host;\n      node = node.host;\n    }\n    node = node.parentNode;\n  }\n  return shadowParent;\n}", "map": {"version": 3, "names": ["__spread<PERSON><PERSON>y", "React", "RemoveScrollBar", "styleSingleton", "nonPassive", "handleScroll", "locationCouldBeScrolled", "getTouchXY", "event", "changedTouches", "clientX", "clientY", "getDeltaXY", "deltaX", "deltaY", "extractRef", "ref", "current", "deltaCompare", "x", "y", "generateStyle", "id", "concat", "idCounter", "lockStack", "RemoveScrollSideCar", "props", "shouldPreventQueue", "useRef", "touchStartRef", "activeAxis", "useState", "Style", "lastProps", "useEffect", "inert", "document", "body", "classList", "add", "allow_1", "lockRef", "shards", "map", "filter", "Boolean", "for<PERSON>ach", "el", "remove", "shouldCancelEvent", "useCallback", "parent", "touches", "length", "allowPinchZoom", "touch", "touchStart", "currentAxis", "target", "moveDirection", "Math", "abs", "type", "canBeScrolledInMainDirection", "cancelingAxis", "shouldPrevent", "_event", "delta", "sourceEvent", "e", "name", "shadowParent", "should", "cancelable", "preventDefault", "shardNodes", "node", "contains", "shouldStop", "noIsolation", "shouldCancel", "getOutermostShadowParent", "push", "setTimeout", "scrollTouchStart", "undefined", "scrollWheel", "scrollTouchMove", "setCallbacks", "onScrollCapture", "onWheelCapture", "onTouchMoveCapture", "addEventListener", "inst", "removeEventListener", "removeScrollBar", "createElement", "Fragment", "styles", "gapMode", "ShadowRoot", "host", "parentNode"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/react-remove-scroll/dist/es2015/SideEffect.js"], "sourcesContent": ["import { __spreadArray } from \"tslib\";\nimport * as React from 'react';\nimport { RemoveScrollBar } from 'react-remove-scroll-bar';\nimport { styleSingleton } from 'react-style-singleton';\nimport { nonPassive } from './aggresiveCapture';\nimport { handleScroll, locationCouldBeScrolled } from './handleScroll';\nexport var getTouchXY = function (event) {\n    return 'changedTouches' in event ? [event.changedTouches[0].clientX, event.changedTouches[0].clientY] : [0, 0];\n};\nexport var getDeltaXY = function (event) { return [event.deltaX, event.deltaY]; };\nvar extractRef = function (ref) {\n    return ref && 'current' in ref ? ref.current : ref;\n};\nvar deltaCompare = function (x, y) { return x[0] === y[0] && x[1] === y[1]; };\nvar generateStyle = function (id) { return \"\\n  .block-interactivity-\".concat(id, \" {pointer-events: none;}\\n  .allow-interactivity-\").concat(id, \" {pointer-events: all;}\\n\"); };\nvar idCounter = 0;\nvar lockStack = [];\nexport function RemoveScrollSideCar(props) {\n    var shouldPreventQueue = React.useRef([]);\n    var touchStartRef = React.useRef([0, 0]);\n    var activeAxis = React.useRef();\n    var id = React.useState(idCounter++)[0];\n    var Style = React.useState(styleSingleton)[0];\n    var lastProps = React.useRef(props);\n    React.useEffect(function () {\n        lastProps.current = props;\n    }, [props]);\n    React.useEffect(function () {\n        if (props.inert) {\n            document.body.classList.add(\"block-interactivity-\".concat(id));\n            var allow_1 = __spreadArray([props.lockRef.current], (props.shards || []).map(extractRef), true).filter(Boolean);\n            allow_1.forEach(function (el) { return el.classList.add(\"allow-interactivity-\".concat(id)); });\n            return function () {\n                document.body.classList.remove(\"block-interactivity-\".concat(id));\n                allow_1.forEach(function (el) { return el.classList.remove(\"allow-interactivity-\".concat(id)); });\n            };\n        }\n        return;\n    }, [props.inert, props.lockRef.current, props.shards]);\n    var shouldCancelEvent = React.useCallback(function (event, parent) {\n        if ('touches' in event && event.touches.length === 2) {\n            return !lastProps.current.allowPinchZoom;\n        }\n        var touch = getTouchXY(event);\n        var touchStart = touchStartRef.current;\n        var deltaX = 'deltaX' in event ? event.deltaX : touchStart[0] - touch[0];\n        var deltaY = 'deltaY' in event ? event.deltaY : touchStart[1] - touch[1];\n        var currentAxis;\n        var target = event.target;\n        var moveDirection = Math.abs(deltaX) > Math.abs(deltaY) ? 'h' : 'v';\n        // allow horizontal touch move on Range inputs. They will not cause any scroll\n        if ('touches' in event && moveDirection === 'h' && target.type === 'range') {\n            return false;\n        }\n        var canBeScrolledInMainDirection = locationCouldBeScrolled(moveDirection, target);\n        if (!canBeScrolledInMainDirection) {\n            return true;\n        }\n        if (canBeScrolledInMainDirection) {\n            currentAxis = moveDirection;\n        }\n        else {\n            currentAxis = moveDirection === 'v' ? 'h' : 'v';\n            canBeScrolledInMainDirection = locationCouldBeScrolled(moveDirection, target);\n            // other axis might be not scrollable\n        }\n        if (!canBeScrolledInMainDirection) {\n            return false;\n        }\n        if (!activeAxis.current && 'changedTouches' in event && (deltaX || deltaY)) {\n            activeAxis.current = currentAxis;\n        }\n        if (!currentAxis) {\n            return true;\n        }\n        var cancelingAxis = activeAxis.current || currentAxis;\n        return handleScroll(cancelingAxis, parent, event, cancelingAxis === 'h' ? deltaX : deltaY, true);\n    }, []);\n    var shouldPrevent = React.useCallback(function (_event) {\n        var event = _event;\n        if (!lockStack.length || lockStack[lockStack.length - 1] !== Style) {\n            // not the last active\n            return;\n        }\n        var delta = 'deltaY' in event ? getDeltaXY(event) : getTouchXY(event);\n        var sourceEvent = shouldPreventQueue.current.filter(function (e) { return e.name === event.type && (e.target === event.target || event.target === e.shadowParent) && deltaCompare(e.delta, delta); })[0];\n        // self event, and should be canceled\n        if (sourceEvent && sourceEvent.should) {\n            if (event.cancelable) {\n                event.preventDefault();\n            }\n            return;\n        }\n        // outside or shard event\n        if (!sourceEvent) {\n            var shardNodes = (lastProps.current.shards || [])\n                .map(extractRef)\n                .filter(Boolean)\n                .filter(function (node) { return node.contains(event.target); });\n            var shouldStop = shardNodes.length > 0 ? shouldCancelEvent(event, shardNodes[0]) : !lastProps.current.noIsolation;\n            if (shouldStop) {\n                if (event.cancelable) {\n                    event.preventDefault();\n                }\n            }\n        }\n    }, []);\n    var shouldCancel = React.useCallback(function (name, delta, target, should) {\n        var event = { name: name, delta: delta, target: target, should: should, shadowParent: getOutermostShadowParent(target) };\n        shouldPreventQueue.current.push(event);\n        setTimeout(function () {\n            shouldPreventQueue.current = shouldPreventQueue.current.filter(function (e) { return e !== event; });\n        }, 1);\n    }, []);\n    var scrollTouchStart = React.useCallback(function (event) {\n        touchStartRef.current = getTouchXY(event);\n        activeAxis.current = undefined;\n    }, []);\n    var scrollWheel = React.useCallback(function (event) {\n        shouldCancel(event.type, getDeltaXY(event), event.target, shouldCancelEvent(event, props.lockRef.current));\n    }, []);\n    var scrollTouchMove = React.useCallback(function (event) {\n        shouldCancel(event.type, getTouchXY(event), event.target, shouldCancelEvent(event, props.lockRef.current));\n    }, []);\n    React.useEffect(function () {\n        lockStack.push(Style);\n        props.setCallbacks({\n            onScrollCapture: scrollWheel,\n            onWheelCapture: scrollWheel,\n            onTouchMoveCapture: scrollTouchMove,\n        });\n        document.addEventListener('wheel', shouldPrevent, nonPassive);\n        document.addEventListener('touchmove', shouldPrevent, nonPassive);\n        document.addEventListener('touchstart', scrollTouchStart, nonPassive);\n        return function () {\n            lockStack = lockStack.filter(function (inst) { return inst !== Style; });\n            document.removeEventListener('wheel', shouldPrevent, nonPassive);\n            document.removeEventListener('touchmove', shouldPrevent, nonPassive);\n            document.removeEventListener('touchstart', scrollTouchStart, nonPassive);\n        };\n    }, []);\n    var removeScrollBar = props.removeScrollBar, inert = props.inert;\n    return (React.createElement(React.Fragment, null,\n        inert ? React.createElement(Style, { styles: generateStyle(id) }) : null,\n        removeScrollBar ? React.createElement(RemoveScrollBar, { gapMode: props.gapMode }) : null));\n}\nfunction getOutermostShadowParent(node) {\n    var shadowParent = null;\n    while (node !== null) {\n        if (node instanceof ShadowRoot) {\n            shadowParent = node.host;\n            node = node.host;\n        }\n        node = node.parentNode;\n    }\n    return shadowParent;\n}\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,OAAO;AACrC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,eAAe,QAAQ,yBAAyB;AACzD,SAASC,cAAc,QAAQ,uBAAuB;AACtD,SAASC,UAAU,QAAQ,oBAAoB;AAC/C,SAASC,YAAY,EAAEC,uBAAuB,QAAQ,gBAAgB;AACtE,OAAO,IAAIC,UAAU,GAAG,SAAAA,CAAUC,KAAK,EAAE;EACrC,OAAO,gBAAgB,IAAIA,KAAK,GAAG,CAACA,KAAK,CAACC,cAAc,CAAC,CAAC,CAAC,CAACC,OAAO,EAAEF,KAAK,CAACC,cAAc,CAAC,CAAC,CAAC,CAACE,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;AAClH,CAAC;AACD,OAAO,IAAIC,UAAU,GAAG,SAAAA,CAAUJ,KAAK,EAAE;EAAE,OAAO,CAACA,KAAK,CAACK,MAAM,EAAEL,KAAK,CAACM,MAAM,CAAC;AAAE,CAAC;AACjF,IAAIC,UAAU,GAAG,SAAAA,CAAUC,GAAG,EAAE;EAC5B,OAAOA,GAAG,IAAI,SAAS,IAAIA,GAAG,GAAGA,GAAG,CAACC,OAAO,GAAGD,GAAG;AACtD,CAAC;AACD,IAAIE,YAAY,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;EAAE,OAAOD,CAAC,CAAC,CAAC,CAAC,KAAKC,CAAC,CAAC,CAAC,CAAC,IAAID,CAAC,CAAC,CAAC,CAAC,KAAKC,CAAC,CAAC,CAAC,CAAC;AAAE,CAAC;AAC7E,IAAIC,aAAa,GAAG,SAAAA,CAAUC,EAAE,EAAE;EAAE,OAAO,2BAA2B,CAACC,MAAM,CAACD,EAAE,EAAE,mDAAmD,CAAC,CAACC,MAAM,CAACD,EAAE,EAAE,2BAA2B,CAAC;AAAE,CAAC;AACjL,IAAIE,SAAS,GAAG,CAAC;AACjB,IAAIC,SAAS,GAAG,EAAE;AAClB,OAAO,SAASC,mBAAmBA,CAACC,KAAK,EAAE;EACvC,IAAIC,kBAAkB,GAAG3B,KAAK,CAAC4B,MAAM,CAAC,EAAE,CAAC;EACzC,IAAIC,aAAa,GAAG7B,KAAK,CAAC4B,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACxC,IAAIE,UAAU,GAAG9B,KAAK,CAAC4B,MAAM,CAAC,CAAC;EAC/B,IAAIP,EAAE,GAAGrB,KAAK,CAAC+B,QAAQ,CAACR,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC;EACvC,IAAIS,KAAK,GAAGhC,KAAK,CAAC+B,QAAQ,CAAC7B,cAAc,CAAC,CAAC,CAAC,CAAC;EAC7C,IAAI+B,SAAS,GAAGjC,KAAK,CAAC4B,MAAM,CAACF,KAAK,CAAC;EACnC1B,KAAK,CAACkC,SAAS,CAAC,YAAY;IACxBD,SAAS,CAACjB,OAAO,GAAGU,KAAK;EAC7B,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;EACX1B,KAAK,CAACkC,SAAS,CAAC,YAAY;IACxB,IAAIR,KAAK,CAACS,KAAK,EAAE;MACbC,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,sBAAsB,CAACjB,MAAM,CAACD,EAAE,CAAC,CAAC;MAC9D,IAAImB,OAAO,GAAGzC,aAAa,CAAC,CAAC2B,KAAK,CAACe,OAAO,CAACzB,OAAO,CAAC,EAAE,CAACU,KAAK,CAACgB,MAAM,IAAI,EAAE,EAAEC,GAAG,CAAC7B,UAAU,CAAC,EAAE,IAAI,CAAC,CAAC8B,MAAM,CAACC,OAAO,CAAC;MAChHL,OAAO,CAACM,OAAO,CAAC,UAAUC,EAAE,EAAE;QAAE,OAAOA,EAAE,CAACT,SAAS,CAACC,GAAG,CAAC,sBAAsB,CAACjB,MAAM,CAACD,EAAE,CAAC,CAAC;MAAE,CAAC,CAAC;MAC9F,OAAO,YAAY;QACfe,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACU,MAAM,CAAC,sBAAsB,CAAC1B,MAAM,CAACD,EAAE,CAAC,CAAC;QACjEmB,OAAO,CAACM,OAAO,CAAC,UAAUC,EAAE,EAAE;UAAE,OAAOA,EAAE,CAACT,SAAS,CAACU,MAAM,CAAC,sBAAsB,CAAC1B,MAAM,CAACD,EAAE,CAAC,CAAC;QAAE,CAAC,CAAC;MACrG,CAAC;IACL;IACA;EACJ,CAAC,EAAE,CAACK,KAAK,CAACS,KAAK,EAAET,KAAK,CAACe,OAAO,CAACzB,OAAO,EAAEU,KAAK,CAACgB,MAAM,CAAC,CAAC;EACtD,IAAIO,iBAAiB,GAAGjD,KAAK,CAACkD,WAAW,CAAC,UAAU3C,KAAK,EAAE4C,MAAM,EAAE;IAC/D,IAAI,SAAS,IAAI5C,KAAK,IAAIA,KAAK,CAAC6C,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE;MAClD,OAAO,CAACpB,SAAS,CAACjB,OAAO,CAACsC,cAAc;IAC5C;IACA,IAAIC,KAAK,GAAGjD,UAAU,CAACC,KAAK,CAAC;IAC7B,IAAIiD,UAAU,GAAG3B,aAAa,CAACb,OAAO;IACtC,IAAIJ,MAAM,GAAG,QAAQ,IAAIL,KAAK,GAAGA,KAAK,CAACK,MAAM,GAAG4C,UAAU,CAAC,CAAC,CAAC,GAAGD,KAAK,CAAC,CAAC,CAAC;IACxE,IAAI1C,MAAM,GAAG,QAAQ,IAAIN,KAAK,GAAGA,KAAK,CAACM,MAAM,GAAG2C,UAAU,CAAC,CAAC,CAAC,GAAGD,KAAK,CAAC,CAAC,CAAC;IACxE,IAAIE,WAAW;IACf,IAAIC,MAAM,GAAGnD,KAAK,CAACmD,MAAM;IACzB,IAAIC,aAAa,GAAGC,IAAI,CAACC,GAAG,CAACjD,MAAM,CAAC,GAAGgD,IAAI,CAACC,GAAG,CAAChD,MAAM,CAAC,GAAG,GAAG,GAAG,GAAG;IACnE;IACA,IAAI,SAAS,IAAIN,KAAK,IAAIoD,aAAa,KAAK,GAAG,IAAID,MAAM,CAACI,IAAI,KAAK,OAAO,EAAE;MACxE,OAAO,KAAK;IAChB;IACA,IAAIC,4BAA4B,GAAG1D,uBAAuB,CAACsD,aAAa,EAAED,MAAM,CAAC;IACjF,IAAI,CAACK,4BAA4B,EAAE;MAC/B,OAAO,IAAI;IACf;IACA,IAAIA,4BAA4B,EAAE;MAC9BN,WAAW,GAAGE,aAAa;IAC/B,CAAC,MACI;MACDF,WAAW,GAAGE,aAAa,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG;MAC/CI,4BAA4B,GAAG1D,uBAAuB,CAACsD,aAAa,EAAED,MAAM,CAAC;MAC7E;IACJ;;IACA,IAAI,CAACK,4BAA4B,EAAE;MAC/B,OAAO,KAAK;IAChB;IACA,IAAI,CAACjC,UAAU,CAACd,OAAO,IAAI,gBAAgB,IAAIT,KAAK,KAAKK,MAAM,IAAIC,MAAM,CAAC,EAAE;MACxEiB,UAAU,CAACd,OAAO,GAAGyC,WAAW;IACpC;IACA,IAAI,CAACA,WAAW,EAAE;MACd,OAAO,IAAI;IACf;IACA,IAAIO,aAAa,GAAGlC,UAAU,CAACd,OAAO,IAAIyC,WAAW;IACrD,OAAOrD,YAAY,CAAC4D,aAAa,EAAEb,MAAM,EAAE5C,KAAK,EAAEyD,aAAa,KAAK,GAAG,GAAGpD,MAAM,GAAGC,MAAM,EAAE,IAAI,CAAC;EACpG,CAAC,EAAE,EAAE,CAAC;EACN,IAAIoD,aAAa,GAAGjE,KAAK,CAACkD,WAAW,CAAC,UAAUgB,MAAM,EAAE;IACpD,IAAI3D,KAAK,GAAG2D,MAAM;IAClB,IAAI,CAAC1C,SAAS,CAAC6B,MAAM,IAAI7B,SAAS,CAACA,SAAS,CAAC6B,MAAM,GAAG,CAAC,CAAC,KAAKrB,KAAK,EAAE;MAChE;MACA;IACJ;IACA,IAAImC,KAAK,GAAG,QAAQ,IAAI5D,KAAK,GAAGI,UAAU,CAACJ,KAAK,CAAC,GAAGD,UAAU,CAACC,KAAK,CAAC;IACrE,IAAI6D,WAAW,GAAGzC,kBAAkB,CAACX,OAAO,CAAC4B,MAAM,CAAC,UAAUyB,CAAC,EAAE;MAAE,OAAOA,CAAC,CAACC,IAAI,KAAK/D,KAAK,CAACuD,IAAI,KAAKO,CAAC,CAACX,MAAM,KAAKnD,KAAK,CAACmD,MAAM,IAAInD,KAAK,CAACmD,MAAM,KAAKW,CAAC,CAACE,YAAY,CAAC,IAAItD,YAAY,CAACoD,CAAC,CAACF,KAAK,EAAEA,KAAK,CAAC;IAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACxM;IACA,IAAIC,WAAW,IAAIA,WAAW,CAACI,MAAM,EAAE;MACnC,IAAIjE,KAAK,CAACkE,UAAU,EAAE;QAClBlE,KAAK,CAACmE,cAAc,CAAC,CAAC;MAC1B;MACA;IACJ;IACA;IACA,IAAI,CAACN,WAAW,EAAE;MACd,IAAIO,UAAU,GAAG,CAAC1C,SAAS,CAACjB,OAAO,CAAC0B,MAAM,IAAI,EAAE,EAC3CC,GAAG,CAAC7B,UAAU,CAAC,CACf8B,MAAM,CAACC,OAAO,CAAC,CACfD,MAAM,CAAC,UAAUgC,IAAI,EAAE;QAAE,OAAOA,IAAI,CAACC,QAAQ,CAACtE,KAAK,CAACmD,MAAM,CAAC;MAAE,CAAC,CAAC;MACpE,IAAIoB,UAAU,GAAGH,UAAU,CAACtB,MAAM,GAAG,CAAC,GAAGJ,iBAAiB,CAAC1C,KAAK,EAAEoE,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC1C,SAAS,CAACjB,OAAO,CAAC+D,WAAW;MACjH,IAAID,UAAU,EAAE;QACZ,IAAIvE,KAAK,CAACkE,UAAU,EAAE;UAClBlE,KAAK,CAACmE,cAAc,CAAC,CAAC;QAC1B;MACJ;IACJ;EACJ,CAAC,EAAE,EAAE,CAAC;EACN,IAAIM,YAAY,GAAGhF,KAAK,CAACkD,WAAW,CAAC,UAAUoB,IAAI,EAAEH,KAAK,EAAET,MAAM,EAAEc,MAAM,EAAE;IACxE,IAAIjE,KAAK,GAAG;MAAE+D,IAAI,EAAEA,IAAI;MAAEH,KAAK,EAAEA,KAAK;MAAET,MAAM,EAAEA,MAAM;MAAEc,MAAM,EAAEA,MAAM;MAAED,YAAY,EAAEU,wBAAwB,CAACvB,MAAM;IAAE,CAAC;IACxH/B,kBAAkB,CAACX,OAAO,CAACkE,IAAI,CAAC3E,KAAK,CAAC;IACtC4E,UAAU,CAAC,YAAY;MACnBxD,kBAAkB,CAACX,OAAO,GAAGW,kBAAkB,CAACX,OAAO,CAAC4B,MAAM,CAAC,UAAUyB,CAAC,EAAE;QAAE,OAAOA,CAAC,KAAK9D,KAAK;MAAE,CAAC,CAAC;IACxG,CAAC,EAAE,CAAC,CAAC;EACT,CAAC,EAAE,EAAE,CAAC;EACN,IAAI6E,gBAAgB,GAAGpF,KAAK,CAACkD,WAAW,CAAC,UAAU3C,KAAK,EAAE;IACtDsB,aAAa,CAACb,OAAO,GAAGV,UAAU,CAACC,KAAK,CAAC;IACzCuB,UAAU,CAACd,OAAO,GAAGqE,SAAS;EAClC,CAAC,EAAE,EAAE,CAAC;EACN,IAAIC,WAAW,GAAGtF,KAAK,CAACkD,WAAW,CAAC,UAAU3C,KAAK,EAAE;IACjDyE,YAAY,CAACzE,KAAK,CAACuD,IAAI,EAAEnD,UAAU,CAACJ,KAAK,CAAC,EAAEA,KAAK,CAACmD,MAAM,EAAET,iBAAiB,CAAC1C,KAAK,EAAEmB,KAAK,CAACe,OAAO,CAACzB,OAAO,CAAC,CAAC;EAC9G,CAAC,EAAE,EAAE,CAAC;EACN,IAAIuE,eAAe,GAAGvF,KAAK,CAACkD,WAAW,CAAC,UAAU3C,KAAK,EAAE;IACrDyE,YAAY,CAACzE,KAAK,CAACuD,IAAI,EAAExD,UAAU,CAACC,KAAK,CAAC,EAAEA,KAAK,CAACmD,MAAM,EAAET,iBAAiB,CAAC1C,KAAK,EAAEmB,KAAK,CAACe,OAAO,CAACzB,OAAO,CAAC,CAAC;EAC9G,CAAC,EAAE,EAAE,CAAC;EACNhB,KAAK,CAACkC,SAAS,CAAC,YAAY;IACxBV,SAAS,CAAC0D,IAAI,CAAClD,KAAK,CAAC;IACrBN,KAAK,CAAC8D,YAAY,CAAC;MACfC,eAAe,EAAEH,WAAW;MAC5BI,cAAc,EAAEJ,WAAW;MAC3BK,kBAAkB,EAAEJ;IACxB,CAAC,CAAC;IACFnD,QAAQ,CAACwD,gBAAgB,CAAC,OAAO,EAAE3B,aAAa,EAAE9D,UAAU,CAAC;IAC7DiC,QAAQ,CAACwD,gBAAgB,CAAC,WAAW,EAAE3B,aAAa,EAAE9D,UAAU,CAAC;IACjEiC,QAAQ,CAACwD,gBAAgB,CAAC,YAAY,EAAER,gBAAgB,EAAEjF,UAAU,CAAC;IACrE,OAAO,YAAY;MACfqB,SAAS,GAAGA,SAAS,CAACoB,MAAM,CAAC,UAAUiD,IAAI,EAAE;QAAE,OAAOA,IAAI,KAAK7D,KAAK;MAAE,CAAC,CAAC;MACxEI,QAAQ,CAAC0D,mBAAmB,CAAC,OAAO,EAAE7B,aAAa,EAAE9D,UAAU,CAAC;MAChEiC,QAAQ,CAAC0D,mBAAmB,CAAC,WAAW,EAAE7B,aAAa,EAAE9D,UAAU,CAAC;MACpEiC,QAAQ,CAAC0D,mBAAmB,CAAC,YAAY,EAAEV,gBAAgB,EAAEjF,UAAU,CAAC;IAC5E,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EACN,IAAI4F,eAAe,GAAGrE,KAAK,CAACqE,eAAe;IAAE5D,KAAK,GAAGT,KAAK,CAACS,KAAK;EAChE,OAAQnC,KAAK,CAACgG,aAAa,CAAChG,KAAK,CAACiG,QAAQ,EAAE,IAAI,EAC5C9D,KAAK,GAAGnC,KAAK,CAACgG,aAAa,CAAChE,KAAK,EAAE;IAAEkE,MAAM,EAAE9E,aAAa,CAACC,EAAE;EAAE,CAAC,CAAC,GAAG,IAAI,EACxE0E,eAAe,GAAG/F,KAAK,CAACgG,aAAa,CAAC/F,eAAe,EAAE;IAAEkG,OAAO,EAAEzE,KAAK,CAACyE;EAAQ,CAAC,CAAC,GAAG,IAAI,CAAC;AAClG;AACA,SAASlB,wBAAwBA,CAACL,IAAI,EAAE;EACpC,IAAIL,YAAY,GAAG,IAAI;EACvB,OAAOK,IAAI,KAAK,IAAI,EAAE;IAClB,IAAIA,IAAI,YAAYwB,UAAU,EAAE;MAC5B7B,YAAY,GAAGK,IAAI,CAACyB,IAAI;MACxBzB,IAAI,GAAGA,IAAI,CAACyB,IAAI;IACpB;IACAzB,IAAI,GAAGA,IAAI,CAAC0B,UAAU;EAC1B;EACA,OAAO/B,YAAY;AACvB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}