{"ast": null, "code": "export { styleSingleton } from './component';\nexport { stylesheetSingleton } from './singleton';\nexport { styleHookSingleton } from './hook';", "map": {"version": 3, "names": ["styleSingleton", "stylesheetSingleton", "styleHookSingleton"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/react-style-singleton/dist/es2015/index.js"], "sourcesContent": ["export { styleSingleton } from './component';\nexport { stylesheetSingleton } from './singleton';\nexport { styleHookSingleton } from './hook';\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,aAAa;AAC5C,SAASC,mBAAmB,QAAQ,aAAa;AACjD,SAASC,kBAAkB,QAAQ,QAAQ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}