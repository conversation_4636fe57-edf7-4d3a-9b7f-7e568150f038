{"ast": null, "code": "export function deferAction(action) {\n  setTimeout(action, 1);\n}\nexport var inlineProp = function inlineProp(name, value) {\n  var obj = {};\n  obj[name] = value;\n  return obj;\n};", "map": {"version": 3, "names": ["deferAction", "action", "setTimeout", "inlineProp", "name", "value", "obj"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/react-focus-lock/dist/es2015/util.js"], "sourcesContent": ["export function deferAction(action) {\n  setTimeout(action, 1);\n}\nexport var inlineProp = function inlineProp(name, value) {\n  var obj = {};\n  obj[name] = value;\n  return obj;\n};"], "mappings": "AAAA,OAAO,SAASA,WAAWA,CAACC,MAAM,EAAE;EAClCC,UAAU,CAACD,MAAM,EAAE,CAAC,CAAC;AACvB;AACA,OAAO,IAAIE,UAAU,GAAG,SAASA,UAAUA,CAACC,IAAI,EAAEC,KAAK,EAAE;EACvD,IAAIC,GAAG,GAAG,CAAC,CAAC;EACZA,GAAG,CAACF,IAAI,CAAC,GAAGC,KAAK;EACjB,OAAOC,GAAG;AACZ,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}