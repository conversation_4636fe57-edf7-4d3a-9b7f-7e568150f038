{"ast": null, "code": "import { FOCUS_DISABLED, FOCUS_GROUP } from '../constants';\nimport { asArray, toArray } from './array';\n/**\n * in case of multiple nodes nested inside each other\n * keeps only top ones\n * this is O(nlogn)\n * @param nodes\n * @returns {*}\n */\nvar filterNested = function (nodes) {\n  var contained = new Set();\n  var l = nodes.length;\n  for (var i = 0; i < l; i += 1) {\n    for (var j = i + 1; j < l; j += 1) {\n      var position = nodes[i].compareDocumentPosition(nodes[j]);\n      /* eslint-disable no-bitwise */\n      if ((position & Node.DOCUMENT_POSITION_CONTAINED_BY) > 0) {\n        contained.add(j);\n      }\n      if ((position & Node.DOCUMENT_POSITION_CONTAINS) > 0) {\n        contained.add(i);\n      }\n      /* eslint-enable */\n    }\n  }\n\n  return nodes.filter(function (_, index) {\n    return !contained.has(index);\n  });\n};\n/**\n * finds top most parent for a node\n * @param node\n * @returns {*}\n */\nvar getTopParent = function (node) {\n  return node.parentNode ? getTopParent(node.parentNode) : node;\n};\n/**\n * returns all \"focus containers\" inside a given node\n * @param node - node or nodes to look inside\n * @returns Element[]\n */\nexport var getAllAffectedNodes = function (node) {\n  var nodes = asArray(node);\n  return nodes.filter(Boolean).reduce(function (acc, currentNode) {\n    var group = currentNode.getAttribute(FOCUS_GROUP);\n    acc.push.apply(acc, group ? filterNested(toArray(getTopParent(currentNode).querySelectorAll(\"[\".concat(FOCUS_GROUP, \"=\\\"\").concat(group, \"\\\"]:not([\").concat(FOCUS_DISABLED, \"=\\\"disabled\\\"])\")))) : [currentNode]);\n    return acc;\n  }, []);\n};", "map": {"version": 3, "names": ["FOCUS_DISABLED", "FOCUS_GROUP", "asArray", "toArray", "filterNested", "nodes", "contained", "Set", "l", "length", "i", "j", "position", "compareDocumentPosition", "Node", "DOCUMENT_POSITION_CONTAINED_BY", "add", "DOCUMENT_POSITION_CONTAINS", "filter", "_", "index", "has", "getTopParent", "node", "parentNode", "getAllAffectedNodes", "Boolean", "reduce", "acc", "currentNode", "group", "getAttribute", "push", "apply", "querySelectorAll", "concat"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/focus-lock/dist/es2015/utils/all-affected.js"], "sourcesContent": ["import { FOCUS_DISABLED, FOCUS_GROUP } from '../constants';\nimport { asArray, toArray } from './array';\n/**\n * in case of multiple nodes nested inside each other\n * keeps only top ones\n * this is O(nlogn)\n * @param nodes\n * @returns {*}\n */\nvar filterNested = function (nodes) {\n    var contained = new Set();\n    var l = nodes.length;\n    for (var i = 0; i < l; i += 1) {\n        for (var j = i + 1; j < l; j += 1) {\n            var position = nodes[i].compareDocumentPosition(nodes[j]);\n            /* eslint-disable no-bitwise */\n            if ((position & Node.DOCUMENT_POSITION_CONTAINED_BY) > 0) {\n                contained.add(j);\n            }\n            if ((position & Node.DOCUMENT_POSITION_CONTAINS) > 0) {\n                contained.add(i);\n            }\n            /* eslint-enable */\n        }\n    }\n    return nodes.filter(function (_, index) { return !contained.has(index); });\n};\n/**\n * finds top most parent for a node\n * @param node\n * @returns {*}\n */\nvar getTopParent = function (node) {\n    return node.parentNode ? getTopParent(node.parentNode) : node;\n};\n/**\n * returns all \"focus containers\" inside a given node\n * @param node - node or nodes to look inside\n * @returns Element[]\n */\nexport var getAllAffectedNodes = function (node) {\n    var nodes = asArray(node);\n    return nodes.filter(Boolean).reduce(function (acc, currentNode) {\n        var group = currentNode.getAttribute(FOCUS_GROUP);\n        acc.push.apply(acc, (group\n            ? filterNested(toArray(getTopParent(currentNode).querySelectorAll(\"[\".concat(FOCUS_GROUP, \"=\\\"\").concat(group, \"\\\"]:not([\").concat(FOCUS_DISABLED, \"=\\\"disabled\\\"])\"))))\n            : [currentNode]));\n        return acc;\n    }, []);\n};\n"], "mappings": "AAAA,SAASA,cAAc,EAAEC,WAAW,QAAQ,cAAc;AAC1D,SAASC,OAAO,EAAEC,OAAO,QAAQ,SAAS;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,YAAY,GAAG,SAAAA,CAAUC,KAAK,EAAE;EAChC,IAAIC,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;EACzB,IAAIC,CAAC,GAAGH,KAAK,CAACI,MAAM;EACpB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,CAAC,EAAEE,CAAC,IAAI,CAAC,EAAE;IAC3B,KAAK,IAAIC,CAAC,GAAGD,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGH,CAAC,EAAEG,CAAC,IAAI,CAAC,EAAE;MAC/B,IAAIC,QAAQ,GAAGP,KAAK,CAACK,CAAC,CAAC,CAACG,uBAAuB,CAACR,KAAK,CAACM,CAAC,CAAC,CAAC;MACzD;MACA,IAAI,CAACC,QAAQ,GAAGE,IAAI,CAACC,8BAA8B,IAAI,CAAC,EAAE;QACtDT,SAAS,CAACU,GAAG,CAACL,CAAC,CAAC;MACpB;MACA,IAAI,CAACC,QAAQ,GAAGE,IAAI,CAACG,0BAA0B,IAAI,CAAC,EAAE;QAClDX,SAAS,CAACU,GAAG,CAACN,CAAC,CAAC;MACpB;MACA;IACJ;EACJ;;EACA,OAAOL,KAAK,CAACa,MAAM,CAAC,UAAUC,CAAC,EAAEC,KAAK,EAAE;IAAE,OAAO,CAACd,SAAS,CAACe,GAAG,CAACD,KAAK,CAAC;EAAE,CAAC,CAAC;AAC9E,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,IAAIE,YAAY,GAAG,SAAAA,CAAUC,IAAI,EAAE;EAC/B,OAAOA,IAAI,CAACC,UAAU,GAAGF,YAAY,CAACC,IAAI,CAACC,UAAU,CAAC,GAAGD,IAAI;AACjE,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIE,mBAAmB,GAAG,SAAAA,CAAUF,IAAI,EAAE;EAC7C,IAAIlB,KAAK,GAAGH,OAAO,CAACqB,IAAI,CAAC;EACzB,OAAOlB,KAAK,CAACa,MAAM,CAACQ,OAAO,CAAC,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAEC,WAAW,EAAE;IAC5D,IAAIC,KAAK,GAAGD,WAAW,CAACE,YAAY,CAAC9B,WAAW,CAAC;IACjD2B,GAAG,CAACI,IAAI,CAACC,KAAK,CAACL,GAAG,EAAGE,KAAK,GACpB1B,YAAY,CAACD,OAAO,CAACmB,YAAY,CAACO,WAAW,CAAC,CAACK,gBAAgB,CAAC,GAAG,CAACC,MAAM,CAAClC,WAAW,EAAE,KAAK,CAAC,CAACkC,MAAM,CAACL,KAAK,EAAE,WAAW,CAAC,CAACK,MAAM,CAACnC,cAAc,EAAE,iBAAiB,CAAC,CAAC,CAAC,CAAC,GACtK,CAAC6B,WAAW,CAAE,CAAC;IACrB,OAAOD,GAAG;EACd,CAAC,EAAE,EAAE,CAAC;AACV,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}