{"ast": null, "code": "import { PanSession } from './PanSession.mjs';\nimport { addPointerEvent } from '../../events/add-pointer-event.mjs';\nimport { Feature } from '../../motion/features/Feature.mjs';\nimport { noop } from '../../utils/noop.mjs';\nimport { frame } from '../../frameloop/frame.mjs';\nconst asyncHandler = handler => (event, info) => {\n  if (handler) {\n    frame.update(() => handler(event, info));\n  }\n};\nclass PanGesture extends Feature {\n  constructor() {\n    super(...arguments);\n    this.removePointerDownListener = noop;\n  }\n  onPointerDown(pointerDownEvent) {\n    this.session = new PanSession(pointerDownEvent, this.createPanHandlers(), {\n      transformPagePoint: this.node.getTransformPagePoint()\n    });\n  }\n  createPanHandlers() {\n    const {\n      onPanSessionStart,\n      onPanStart,\n      onPan,\n      onPanEnd\n    } = this.node.getProps();\n    return {\n      onSessionStart: asyncHandler(onPanSessionStart),\n      onStart: asyncHandler(onPanStart),\n      onMove: onPan,\n      onEnd: (event, info) => {\n        delete this.session;\n        if (onPanEnd) {\n          frame.update(() => onPanEnd(event, info));\n        }\n      }\n    };\n  }\n  mount() {\n    this.removePointerDownListener = addPointerEvent(this.node.current, \"pointerdown\", event => this.onPointerDown(event));\n  }\n  update() {\n    this.session && this.session.updateHandlers(this.createPanHandlers());\n  }\n  unmount() {\n    this.removePointerDownListener();\n    this.session && this.session.end();\n  }\n}\nexport { PanGesture };", "map": {"version": 3, "names": ["PanSession", "addPointerEvent", "Feature", "noop", "frame", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "handler", "event", "info", "update", "PanGesture", "constructor", "arguments", "removePointerDownListener", "onPointerDown", "pointerDownEvent", "session", "createPanHandlers", "transformPagePoint", "node", "getTransformPagePoint", "onPanSessionStart", "onPanStart", "onPan", "onPanEnd", "getProps", "onSessionStart", "onStart", "onMove", "onEnd", "mount", "current", "updateHandlers", "unmount", "end"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/framer-motion/dist/es/gestures/pan/index.mjs"], "sourcesContent": ["import { PanSession } from './PanSession.mjs';\nimport { addPointerEvent } from '../../events/add-pointer-event.mjs';\nimport { Feature } from '../../motion/features/Feature.mjs';\nimport { noop } from '../../utils/noop.mjs';\nimport { frame } from '../../frameloop/frame.mjs';\n\nconst asyncHandler = (handler) => (event, info) => {\n    if (handler) {\n        frame.update(() => handler(event, info));\n    }\n};\nclass PanGesture extends Feature {\n    constructor() {\n        super(...arguments);\n        this.removePointerDownListener = noop;\n    }\n    onPointerDown(pointerDownEvent) {\n        this.session = new PanSession(pointerDownEvent, this.createPanHandlers(), { transformPagePoint: this.node.getTransformPagePoint() });\n    }\n    createPanHandlers() {\n        const { onPanSessionStart, onPanStart, onPan, onPanEnd } = this.node.getProps();\n        return {\n            onSessionStart: asyncHandler(onPanSessionStart),\n            onStart: asyncHandler(onPanStart),\n            onMove: onPan,\n            onEnd: (event, info) => {\n                delete this.session;\n                if (onPanEnd) {\n                    frame.update(() => onPanEnd(event, info));\n                }\n            },\n        };\n    }\n    mount() {\n        this.removePointerDownListener = addPointerEvent(this.node.current, \"pointerdown\", (event) => this.onPointerDown(event));\n    }\n    update() {\n        this.session && this.session.updateHandlers(this.createPanHandlers());\n    }\n    unmount() {\n        this.removePointerDownListener();\n        this.session && this.session.end();\n    }\n}\n\nexport { PanGesture };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,kBAAkB;AAC7C,SAASC,eAAe,QAAQ,oCAAoC;AACpE,SAASC,OAAO,QAAQ,mCAAmC;AAC3D,SAASC,IAAI,QAAQ,sBAAsB;AAC3C,SAASC,KAAK,QAAQ,2BAA2B;AAEjD,MAAMC,YAAY,GAAIC,OAAO,IAAK,CAACC,KAAK,EAAEC,IAAI,KAAK;EAC/C,IAAIF,OAAO,EAAE;IACTF,KAAK,CAACK,MAAM,CAAC,MAAMH,OAAO,CAACC,KAAK,EAAEC,IAAI,CAAC,CAAC;EAC5C;AACJ,CAAC;AACD,MAAME,UAAU,SAASR,OAAO,CAAC;EAC7BS,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACC,yBAAyB,GAAGV,IAAI;EACzC;EACAW,aAAaA,CAACC,gBAAgB,EAAE;IAC5B,IAAI,CAACC,OAAO,GAAG,IAAIhB,UAAU,CAACe,gBAAgB,EAAE,IAAI,CAACE,iBAAiB,CAAC,CAAC,EAAE;MAAEC,kBAAkB,EAAE,IAAI,CAACC,IAAI,CAACC,qBAAqB,CAAC;IAAE,CAAC,CAAC;EACxI;EACAH,iBAAiBA,CAAA,EAAG;IAChB,MAAM;MAAEI,iBAAiB;MAAEC,UAAU;MAAEC,KAAK;MAAEC;IAAS,CAAC,GAAG,IAAI,CAACL,IAAI,CAACM,QAAQ,CAAC,CAAC;IAC/E,OAAO;MACHC,cAAc,EAAErB,YAAY,CAACgB,iBAAiB,CAAC;MAC/CM,OAAO,EAAEtB,YAAY,CAACiB,UAAU,CAAC;MACjCM,MAAM,EAAEL,KAAK;MACbM,KAAK,EAAEA,CAACtB,KAAK,EAAEC,IAAI,KAAK;QACpB,OAAO,IAAI,CAACQ,OAAO;QACnB,IAAIQ,QAAQ,EAAE;UACVpB,KAAK,CAACK,MAAM,CAAC,MAAMe,QAAQ,CAACjB,KAAK,EAAEC,IAAI,CAAC,CAAC;QAC7C;MACJ;IACJ,CAAC;EACL;EACAsB,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACjB,yBAAyB,GAAGZ,eAAe,CAAC,IAAI,CAACkB,IAAI,CAACY,OAAO,EAAE,aAAa,EAAGxB,KAAK,IAAK,IAAI,CAACO,aAAa,CAACP,KAAK,CAAC,CAAC;EAC5H;EACAE,MAAMA,CAAA,EAAG;IACL,IAAI,CAACO,OAAO,IAAI,IAAI,CAACA,OAAO,CAACgB,cAAc,CAAC,IAAI,CAACf,iBAAiB,CAAC,CAAC,CAAC;EACzE;EACAgB,OAAOA,CAAA,EAAG;IACN,IAAI,CAACpB,yBAAyB,CAAC,CAAC;IAChC,IAAI,CAACG,OAAO,IAAI,IAAI,CAACA,OAAO,CAACkB,GAAG,CAAC,CAAC;EACtC;AACJ;AAEA,SAASxB,UAAU"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}