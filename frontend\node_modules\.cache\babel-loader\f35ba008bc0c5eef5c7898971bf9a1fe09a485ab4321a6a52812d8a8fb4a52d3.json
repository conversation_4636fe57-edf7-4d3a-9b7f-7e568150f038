{"ast": null, "code": "/**\n * returns active element from document or from nested shadowdoms\n */\nimport { safeProbe } from './safe';\n/**\n * returns current active element. If the active element is a \"container\" itself(shadowRoot or iframe) returns active element inside it\n * @param [inDocument]\n */\nexport var getActiveElement = function (inDocument) {\n  if (inDocument === void 0) {\n    inDocument = document;\n  }\n  if (!inDocument || !inDocument.activeElement) {\n    return undefined;\n  }\n  var activeElement = inDocument.activeElement;\n  return activeElement.shadowRoot ? getActiveElement(activeElement.shadowRoot) : activeElement instanceof HTMLIFrameElement && safeProbe(function () {\n    return activeElement.contentWindow.document;\n  }) ? getActiveElement(activeElement.contentWindow.document) : activeElement;\n};", "map": {"version": 3, "names": ["safeProbe", "getActiveElement", "inDocument", "document", "activeElement", "undefined", "shadowRoot", "HTMLIFrameElement", "contentWindow"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/focus-lock/dist/es2015/utils/getActiveElement.js"], "sourcesContent": ["/**\n * returns active element from document or from nested shadowdoms\n */\nimport { safeProbe } from './safe';\n/**\n * returns current active element. If the active element is a \"container\" itself(shadowRoot or iframe) returns active element inside it\n * @param [inDocument]\n */\nexport var getActiveElement = function (inDocument) {\n    if (inDocument === void 0) { inDocument = document; }\n    if (!inDocument || !inDocument.activeElement) {\n        return undefined;\n    }\n    var activeElement = inDocument.activeElement;\n    return (activeElement.shadowRoot\n        ? getActiveElement(activeElement.shadowRoot)\n        : activeElement instanceof HTMLIFrameElement && safeProbe(function () { return activeElement.contentWindow.document; })\n            ? getActiveElement(activeElement.contentWindow.document)\n            : activeElement);\n};\n"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,QAAQ;AAClC;AACA;AACA;AACA;AACA,OAAO,IAAIC,gBAAgB,GAAG,SAAAA,CAAUC,UAAU,EAAE;EAChD,IAAIA,UAAU,KAAK,KAAK,CAAC,EAAE;IAAEA,UAAU,GAAGC,QAAQ;EAAE;EACpD,IAAI,CAACD,UAAU,IAAI,CAACA,UAAU,CAACE,aAAa,EAAE;IAC1C,OAAOC,SAAS;EACpB;EACA,IAAID,aAAa,GAAGF,UAAU,CAACE,aAAa;EAC5C,OAAQA,aAAa,CAACE,UAAU,GAC1BL,gBAAgB,CAACG,aAAa,CAACE,UAAU,CAAC,GAC1CF,aAAa,YAAYG,iBAAiB,IAAIP,SAAS,CAAC,YAAY;IAAE,OAAOI,aAAa,CAACI,aAAa,CAACL,QAAQ;EAAE,CAAC,CAAC,GACjHF,gBAAgB,CAACG,aAAa,CAACI,aAAa,CAACL,QAAQ,CAAC,GACtDC,aAAa;AAC3B,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}