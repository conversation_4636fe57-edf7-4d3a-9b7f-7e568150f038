{"ast": null, "code": "import { transformProps } from '../../render/html/utils/transform.mjs';\nimport { appearAnimationStore } from './store.mjs';\nimport { appearStoreId } from './store-id.mjs';\nfunction handoffOptimizedAppearAnimation(id, name,\n/**\n * Legacy argument. This function is inlined apart from framer-motion so\n * will co-ordinate with <PERSON><PERSON> with how best to remove this.\n */\n_value,\n/**\n * This function is loaded via window by startOptimisedAnimation.\n * By accepting `sync` as an argument, rather than using it via\n * import, it can be kept out of the first-load Framer bundle,\n * while also allowing this function to not be included in\n * Framer Motion bundles where it's not needed.\n */\nframe) {\n  const storeId = appearStoreId(id, transformProps.has(name) ? \"transform\" : name);\n  const appearAnimation = appearAnimationStore.get(storeId);\n  if (!appearAnimation) return 0;\n  const {\n    animation,\n    startTime\n  } = appearAnimation;\n  const cancelOptimisedAnimation = () => {\n    appearAnimationStore.delete(storeId);\n    /**\n     * Animation.cancel() throws so it needs to be wrapped in a try/catch\n     */\n    try {\n      animation.cancel();\n    } catch (e) {}\n  };\n  if (startTime !== null) {\n    /**\n     * We allow the animation to persist until the next frame:\n     *   1. So it continues to play until Framer Motion is ready to render\n     *      (avoiding a potential flash of the element's original state)\n     *   2. As all independent transforms share a single transform animation, stopping\n     *      it synchronously would prevent subsequent transforms from handing off.\n     */\n    frame.render(cancelOptimisedAnimation);\n    /**\n     * We use main thread timings vs those returned by Animation.currentTime as it\n     * can be the case, particularly in Firefox, that currentTime doesn't return\n     * an updated value for several frames, even as the animation plays smoothly via\n     * the GPU.\n     */\n    return performance.now() - startTime || 0;\n  } else {\n    cancelOptimisedAnimation();\n    return 0;\n  }\n}\nexport { handoffOptimizedAppearAnimation };", "map": {"version": 3, "names": ["transformProps", "appearAnimationStore", "appearStoreId", "handoffOptimizedAppearAnimation", "id", "name", "_value", "frame", "storeId", "has", "appearAnimation", "get", "animation", "startTime", "cancelOptimisedAnimation", "delete", "cancel", "e", "render", "performance", "now"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/framer-motion/dist/es/animation/optimized-appear/handoff.mjs"], "sourcesContent": ["import { transformProps } from '../../render/html/utils/transform.mjs';\nimport { appearAnimationStore } from './store.mjs';\nimport { appearStoreId } from './store-id.mjs';\n\nfunction handoffOptimizedAppearAnimation(id, name, \n/**\n * Legacy argument. This function is inlined apart from framer-motion so\n * will co-ordinate with <PERSON><PERSON> with how best to remove this.\n */\n_value, \n/**\n * This function is loaded via window by startOptimisedAnimation.\n * By accepting `sync` as an argument, rather than using it via\n * import, it can be kept out of the first-load Framer bundle,\n * while also allowing this function to not be included in\n * Framer Motion bundles where it's not needed.\n */\nframe) {\n    const storeId = appearStoreId(id, transformProps.has(name) ? \"transform\" : name);\n    const appearAnimation = appearAnimationStore.get(storeId);\n    if (!appearAnimation)\n        return 0;\n    const { animation, startTime } = appearAnimation;\n    const cancelOptimisedAnimation = () => {\n        appearAnimationStore.delete(storeId);\n        /**\n         * Animation.cancel() throws so it needs to be wrapped in a try/catch\n         */\n        try {\n            animation.cancel();\n        }\n        catch (e) { }\n    };\n    if (startTime !== null) {\n        /**\n         * We allow the animation to persist until the next frame:\n         *   1. So it continues to play until Framer Motion is ready to render\n         *      (avoiding a potential flash of the element's original state)\n         *   2. As all independent transforms share a single transform animation, stopping\n         *      it synchronously would prevent subsequent transforms from handing off.\n         */\n        frame.render(cancelOptimisedAnimation);\n        /**\n         * We use main thread timings vs those returned by Animation.currentTime as it\n         * can be the case, particularly in Firefox, that currentTime doesn't return\n         * an updated value for several frames, even as the animation plays smoothly via\n         * the GPU.\n         */\n        return performance.now() - startTime || 0;\n    }\n    else {\n        cancelOptimisedAnimation();\n        return 0;\n    }\n}\n\nexport { handoffOptimizedAppearAnimation };\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,uCAAuC;AACtE,SAASC,oBAAoB,QAAQ,aAAa;AAClD,SAASC,aAAa,QAAQ,gBAAgB;AAE9C,SAASC,+BAA+BA,CAACC,EAAE,EAAEC,IAAI;AACjD;AACA;AACA;AACA;AACAC,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACAC,KAAK,EAAE;EACH,MAAMC,OAAO,GAAGN,aAAa,CAACE,EAAE,EAAEJ,cAAc,CAACS,GAAG,CAACJ,IAAI,CAAC,GAAG,WAAW,GAAGA,IAAI,CAAC;EAChF,MAAMK,eAAe,GAAGT,oBAAoB,CAACU,GAAG,CAACH,OAAO,CAAC;EACzD,IAAI,CAACE,eAAe,EAChB,OAAO,CAAC;EACZ,MAAM;IAAEE,SAAS;IAAEC;EAAU,CAAC,GAAGH,eAAe;EAChD,MAAMI,wBAAwB,GAAGA,CAAA,KAAM;IACnCb,oBAAoB,CAACc,MAAM,CAACP,OAAO,CAAC;IACpC;AACR;AACA;IACQ,IAAI;MACAI,SAAS,CAACI,MAAM,CAAC,CAAC;IACtB,CAAC,CACD,OAAOC,CAAC,EAAE,CAAE;EAChB,CAAC;EACD,IAAIJ,SAAS,KAAK,IAAI,EAAE;IACpB;AACR;AACA;AACA;AACA;AACA;AACA;IACQN,KAAK,CAACW,MAAM,CAACJ,wBAAwB,CAAC;IACtC;AACR;AACA;AACA;AACA;AACA;IACQ,OAAOK,WAAW,CAACC,GAAG,CAAC,CAAC,GAAGP,SAAS,IAAI,CAAC;EAC7C,CAAC,MACI;IACDC,wBAAwB,CAAC,CAAC;IAC1B,OAAO,CAAC;EACZ;AACJ;AAEA,SAASX,+BAA+B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}