{"ast": null, "code": "import { animateStyle } from './index.mjs';\nimport { isWaapiSupportedEasing } from './easing.mjs';\nimport { getFinalKeyframe } from './utils/get-final-keyframe.mjs';\nimport { animateValue } from '../js/index.mjs';\nimport { millisecondsToSeconds, secondsToMilliseconds } from '../../../utils/time-conversion.mjs';\nimport { memo } from '../../../utils/memo.mjs';\nimport { noop } from '../../../utils/noop.mjs';\nimport { frameData, frame, cancelFrame } from '../../../frameloop/frame.mjs';\nconst supportsWaapi = memo(() => Object.hasOwnProperty.call(Element.prototype, \"animate\"));\n/**\n * A list of values that can be hardware-accelerated.\n */\nconst acceleratedValues = new Set([\"opacity\", \"clipPath\", \"filter\", \"transform\", \"backgroundColor\"]);\n/**\n * 10ms is chosen here as it strikes a balance between smooth\n * results (more than one keyframe per frame at 60fps) and\n * keyframe quantity.\n */\nconst sampleDelta = 10; //ms\n/**\n * Implement a practical max duration for keyframe generation\n * to prevent infinite loops\n */\nconst maxDuration = 20000;\nconst requiresPregeneratedKeyframes = (valueName, options) => options.type === \"spring\" || valueName === \"backgroundColor\" || !isWaapiSupportedEasing(options.ease);\nfunction createAcceleratedAnimation(value, valueName, {\n  onUpdate,\n  onComplete,\n  ...options\n}) {\n  const canAccelerateAnimation = supportsWaapi() && acceleratedValues.has(valueName) && !options.repeatDelay && options.repeatType !== \"mirror\" && options.damping !== 0 && options.type !== \"inertia\";\n  if (!canAccelerateAnimation) return false;\n  /**\n   * TODO: Unify with js/index\n   */\n  let hasStopped = false;\n  let resolveFinishedPromise;\n  let currentFinishedPromise;\n  /**\n   * Resolve the current Promise every time we enter the\n   * finished state. This is WAAPI-compatible behaviour.\n   */\n  const updateFinishedPromise = () => {\n    currentFinishedPromise = new Promise(resolve => {\n      resolveFinishedPromise = resolve;\n    });\n  };\n  // Create the first finished promise\n  updateFinishedPromise();\n  let {\n    keyframes,\n    duration = 300,\n    ease,\n    times\n  } = options;\n  /**\n   * If this animation needs pre-generated keyframes then generate.\n   */\n  if (requiresPregeneratedKeyframes(valueName, options)) {\n    const sampleAnimation = animateValue({\n      ...options,\n      repeat: 0,\n      delay: 0\n    });\n    let state = {\n      done: false,\n      value: keyframes[0]\n    };\n    const pregeneratedKeyframes = [];\n    /**\n     * Bail after 20 seconds of pre-generated keyframes as it's likely\n     * we're heading for an infinite loop.\n     */\n    let t = 0;\n    while (!state.done && t < maxDuration) {\n      state = sampleAnimation.sample(t);\n      pregeneratedKeyframes.push(state.value);\n      t += sampleDelta;\n    }\n    times = undefined;\n    keyframes = pregeneratedKeyframes;\n    duration = t - sampleDelta;\n    ease = \"linear\";\n  }\n  const animation = animateStyle(value.owner.current, valueName, keyframes, {\n    ...options,\n    duration,\n    /**\n     * This function is currently not called if ease is provided\n     * as a function so the cast is safe.\n     *\n     * However it would be possible for a future refinement to port\n     * in easing pregeneration from Motion One for browsers that\n     * support the upcoming `linear()` easing function.\n     */\n    ease: ease,\n    times\n  });\n  /**\n   * WAAPI animations don't resolve startTime synchronously. But a blocked\n   * thread could delay the startTime resolution by a noticeable amount.\n   * For synching handoff animations with the new Motion animation we want\n   * to ensure startTime is synchronously set.\n   */\n  if (options.syncStart) {\n    animation.startTime = frameData.isProcessing ? frameData.timestamp : document.timeline ? document.timeline.currentTime : performance.now();\n  }\n  const cancelAnimation = () => animation.cancel();\n  const safeCancel = () => {\n    frame.update(cancelAnimation);\n    resolveFinishedPromise();\n    updateFinishedPromise();\n  };\n  /**\n   * Prefer the `onfinish` prop as it's more widely supported than\n   * the `finished` promise.\n   *\n   * Here, we synchronously set the provided MotionValue to the end\n   * keyframe. If we didn't, when the WAAPI animation is finished it would\n   * be removed from the element which would then revert to its old styles.\n   */\n  animation.onfinish = () => {\n    value.set(getFinalKeyframe(keyframes, options));\n    onComplete && onComplete();\n    safeCancel();\n  };\n  /**\n   * Animation interrupt callback.\n   */\n  const controls = {\n    then(resolve, reject) {\n      return currentFinishedPromise.then(resolve, reject);\n    },\n    attachTimeline(timeline) {\n      animation.timeline = timeline;\n      animation.onfinish = null;\n      return noop;\n    },\n    get time() {\n      return millisecondsToSeconds(animation.currentTime || 0);\n    },\n    set time(newTime) {\n      animation.currentTime = secondsToMilliseconds(newTime);\n    },\n    get speed() {\n      return animation.playbackRate;\n    },\n    set speed(newSpeed) {\n      animation.playbackRate = newSpeed;\n    },\n    get duration() {\n      return millisecondsToSeconds(duration);\n    },\n    play: () => {\n      if (hasStopped) return;\n      animation.play();\n      /**\n       * Cancel any pending cancel tasks\n       */\n      cancelFrame(cancelAnimation);\n    },\n    pause: () => animation.pause(),\n    stop: () => {\n      hasStopped = true;\n      if (animation.playState === \"idle\") return;\n      /**\n       * WAAPI doesn't natively have any interruption capabilities.\n       *\n       * Rather than read commited styles back out of the DOM, we can\n       * create a renderless JS animation and sample it twice to calculate\n       * its current value, \"previous\" value, and therefore allow\n       * Motion to calculate velocity for any subsequent animation.\n       */\n      const {\n        currentTime\n      } = animation;\n      if (currentTime) {\n        const sampleAnimation = animateValue({\n          ...options,\n          autoplay: false\n        });\n        value.setWithVelocity(sampleAnimation.sample(currentTime - sampleDelta).value, sampleAnimation.sample(currentTime).value, sampleDelta);\n      }\n      safeCancel();\n    },\n    complete: () => animation.finish(),\n    cancel: safeCancel\n  };\n  return controls;\n}\nexport { createAcceleratedAnimation };", "map": {"version": 3, "names": ["animateStyle", "isWaapiSupportedEasing", "getFinalKeyframe", "animateValue", "millisecondsToSeconds", "secondsToMilliseconds", "memo", "noop", "frameData", "frame", "cancelFrame", "supportsWaapi", "Object", "hasOwnProperty", "call", "Element", "prototype", "acceleratedValues", "Set", "sampleDelta", "maxDuration", "requiresPregeneratedKeyframes", "valueName", "options", "type", "ease", "createAcceleratedAnimation", "value", "onUpdate", "onComplete", "canAccelerateAnimation", "has", "repeatDelay", "repeatType", "damping", "hasStopped", "resolveFinishedPromise", "currentFinishedPromise", "updateFinishedPromise", "Promise", "resolve", "keyframes", "duration", "times", "sampleAnimation", "repeat", "delay", "state", "done", "pregeneratedKeyframes", "t", "sample", "push", "undefined", "animation", "owner", "current", "syncStart", "startTime", "isProcessing", "timestamp", "document", "timeline", "currentTime", "performance", "now", "cancelAnimation", "cancel", "safeCancel", "update", "onfinish", "set", "controls", "then", "reject", "attachTimeline", "time", "newTime", "speed", "playbackRate", "newSpeed", "play", "pause", "stop", "playState", "autoplay", "setWithVelocity", "complete", "finish"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/framer-motion/dist/es/animation/animators/waapi/create-accelerated-animation.mjs"], "sourcesContent": ["import { animateStyle } from './index.mjs';\nimport { isWaapiSupportedEasing } from './easing.mjs';\nimport { getFinalKeyframe } from './utils/get-final-keyframe.mjs';\nimport { animateValue } from '../js/index.mjs';\nimport { millisecondsToSeconds, secondsToMilliseconds } from '../../../utils/time-conversion.mjs';\nimport { memo } from '../../../utils/memo.mjs';\nimport { noop } from '../../../utils/noop.mjs';\nimport { frameData, frame, cancelFrame } from '../../../frameloop/frame.mjs';\n\nconst supportsWaapi = memo(() => Object.hasOwnProperty.call(Element.prototype, \"animate\"));\n/**\n * A list of values that can be hardware-accelerated.\n */\nconst acceleratedValues = new Set([\n    \"opacity\",\n    \"clipPath\",\n    \"filter\",\n    \"transform\",\n    \"backgroundColor\",\n]);\n/**\n * 10ms is chosen here as it strikes a balance between smooth\n * results (more than one keyframe per frame at 60fps) and\n * keyframe quantity.\n */\nconst sampleDelta = 10; //ms\n/**\n * Implement a practical max duration for keyframe generation\n * to prevent infinite loops\n */\nconst maxDuration = 20000;\nconst requiresPregeneratedKeyframes = (valueName, options) => options.type === \"spring\" ||\n    valueName === \"backgroundColor\" ||\n    !isWaapiSupportedEasing(options.ease);\nfunction createAcceleratedAnimation(value, valueName, { onUpdate, onComplete, ...options }) {\n    const canAccelerateAnimation = supportsWaapi() &&\n        acceleratedValues.has(valueName) &&\n        !options.repeatDelay &&\n        options.repeatType !== \"mirror\" &&\n        options.damping !== 0 &&\n        options.type !== \"inertia\";\n    if (!canAccelerateAnimation)\n        return false;\n    /**\n     * TODO: Unify with js/index\n     */\n    let hasStopped = false;\n    let resolveFinishedPromise;\n    let currentFinishedPromise;\n    /**\n     * Resolve the current Promise every time we enter the\n     * finished state. This is WAAPI-compatible behaviour.\n     */\n    const updateFinishedPromise = () => {\n        currentFinishedPromise = new Promise((resolve) => {\n            resolveFinishedPromise = resolve;\n        });\n    };\n    // Create the first finished promise\n    updateFinishedPromise();\n    let { keyframes, duration = 300, ease, times } = options;\n    /**\n     * If this animation needs pre-generated keyframes then generate.\n     */\n    if (requiresPregeneratedKeyframes(valueName, options)) {\n        const sampleAnimation = animateValue({\n            ...options,\n            repeat: 0,\n            delay: 0,\n        });\n        let state = { done: false, value: keyframes[0] };\n        const pregeneratedKeyframes = [];\n        /**\n         * Bail after 20 seconds of pre-generated keyframes as it's likely\n         * we're heading for an infinite loop.\n         */\n        let t = 0;\n        while (!state.done && t < maxDuration) {\n            state = sampleAnimation.sample(t);\n            pregeneratedKeyframes.push(state.value);\n            t += sampleDelta;\n        }\n        times = undefined;\n        keyframes = pregeneratedKeyframes;\n        duration = t - sampleDelta;\n        ease = \"linear\";\n    }\n    const animation = animateStyle(value.owner.current, valueName, keyframes, {\n        ...options,\n        duration,\n        /**\n         * This function is currently not called if ease is provided\n         * as a function so the cast is safe.\n         *\n         * However it would be possible for a future refinement to port\n         * in easing pregeneration from Motion One for browsers that\n         * support the upcoming `linear()` easing function.\n         */\n        ease: ease,\n        times,\n    });\n    /**\n     * WAAPI animations don't resolve startTime synchronously. But a blocked\n     * thread could delay the startTime resolution by a noticeable amount.\n     * For synching handoff animations with the new Motion animation we want\n     * to ensure startTime is synchronously set.\n     */\n    if (options.syncStart) {\n        animation.startTime = frameData.isProcessing\n            ? frameData.timestamp\n            : document.timeline\n                ? document.timeline.currentTime\n                : performance.now();\n    }\n    const cancelAnimation = () => animation.cancel();\n    const safeCancel = () => {\n        frame.update(cancelAnimation);\n        resolveFinishedPromise();\n        updateFinishedPromise();\n    };\n    /**\n     * Prefer the `onfinish` prop as it's more widely supported than\n     * the `finished` promise.\n     *\n     * Here, we synchronously set the provided MotionValue to the end\n     * keyframe. If we didn't, when the WAAPI animation is finished it would\n     * be removed from the element which would then revert to its old styles.\n     */\n    animation.onfinish = () => {\n        value.set(getFinalKeyframe(keyframes, options));\n        onComplete && onComplete();\n        safeCancel();\n    };\n    /**\n     * Animation interrupt callback.\n     */\n    const controls = {\n        then(resolve, reject) {\n            return currentFinishedPromise.then(resolve, reject);\n        },\n        attachTimeline(timeline) {\n            animation.timeline = timeline;\n            animation.onfinish = null;\n            return noop;\n        },\n        get time() {\n            return millisecondsToSeconds(animation.currentTime || 0);\n        },\n        set time(newTime) {\n            animation.currentTime = secondsToMilliseconds(newTime);\n        },\n        get speed() {\n            return animation.playbackRate;\n        },\n        set speed(newSpeed) {\n            animation.playbackRate = newSpeed;\n        },\n        get duration() {\n            return millisecondsToSeconds(duration);\n        },\n        play: () => {\n            if (hasStopped)\n                return;\n            animation.play();\n            /**\n             * Cancel any pending cancel tasks\n             */\n            cancelFrame(cancelAnimation);\n        },\n        pause: () => animation.pause(),\n        stop: () => {\n            hasStopped = true;\n            if (animation.playState === \"idle\")\n                return;\n            /**\n             * WAAPI doesn't natively have any interruption capabilities.\n             *\n             * Rather than read commited styles back out of the DOM, we can\n             * create a renderless JS animation and sample it twice to calculate\n             * its current value, \"previous\" value, and therefore allow\n             * Motion to calculate velocity for any subsequent animation.\n             */\n            const { currentTime } = animation;\n            if (currentTime) {\n                const sampleAnimation = animateValue({\n                    ...options,\n                    autoplay: false,\n                });\n                value.setWithVelocity(sampleAnimation.sample(currentTime - sampleDelta).value, sampleAnimation.sample(currentTime).value, sampleDelta);\n            }\n            safeCancel();\n        },\n        complete: () => animation.finish(),\n        cancel: safeCancel,\n    };\n    return controls;\n}\n\nexport { createAcceleratedAnimation };\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,aAAa;AAC1C,SAASC,sBAAsB,QAAQ,cAAc;AACrD,SAASC,gBAAgB,QAAQ,gCAAgC;AACjE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,qBAAqB,EAAEC,qBAAqB,QAAQ,oCAAoC;AACjG,SAASC,IAAI,QAAQ,yBAAyB;AAC9C,SAASC,IAAI,QAAQ,yBAAyB;AAC9C,SAASC,SAAS,EAAEC,KAAK,EAAEC,WAAW,QAAQ,8BAA8B;AAE5E,MAAMC,aAAa,GAAGL,IAAI,CAAC,MAAMM,MAAM,CAACC,cAAc,CAACC,IAAI,CAACC,OAAO,CAACC,SAAS,EAAE,SAAS,CAAC,CAAC;AAC1F;AACA;AACA;AACA,MAAMC,iBAAiB,GAAG,IAAIC,GAAG,CAAC,CAC9B,SAAS,EACT,UAAU,EACV,QAAQ,EACR,WAAW,EACX,iBAAiB,CACpB,CAAC;AACF;AACA;AACA;AACA;AACA;AACA,MAAMC,WAAW,GAAG,EAAE,CAAC,CAAC;AACxB;AACA;AACA;AACA;AACA,MAAMC,WAAW,GAAG,KAAK;AACzB,MAAMC,6BAA6B,GAAGA,CAACC,SAAS,EAAEC,OAAO,KAAKA,OAAO,CAACC,IAAI,KAAK,QAAQ,IACnFF,SAAS,KAAK,iBAAiB,IAC/B,CAACrB,sBAAsB,CAACsB,OAAO,CAACE,IAAI,CAAC;AACzC,SAASC,0BAA0BA,CAACC,KAAK,EAAEL,SAAS,EAAE;EAAEM,QAAQ;EAAEC,UAAU;EAAE,GAAGN;AAAQ,CAAC,EAAE;EACxF,MAAMO,sBAAsB,GAAGnB,aAAa,CAAC,CAAC,IAC1CM,iBAAiB,CAACc,GAAG,CAACT,SAAS,CAAC,IAChC,CAACC,OAAO,CAACS,WAAW,IACpBT,OAAO,CAACU,UAAU,KAAK,QAAQ,IAC/BV,OAAO,CAACW,OAAO,KAAK,CAAC,IACrBX,OAAO,CAACC,IAAI,KAAK,SAAS;EAC9B,IAAI,CAACM,sBAAsB,EACvB,OAAO,KAAK;EAChB;AACJ;AACA;EACI,IAAIK,UAAU,GAAG,KAAK;EACtB,IAAIC,sBAAsB;EAC1B,IAAIC,sBAAsB;EAC1B;AACJ;AACA;AACA;EACI,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;IAChCD,sBAAsB,GAAG,IAAIE,OAAO,CAAEC,OAAO,IAAK;MAC9CJ,sBAAsB,GAAGI,OAAO;IACpC,CAAC,CAAC;EACN,CAAC;EACD;EACAF,qBAAqB,CAAC,CAAC;EACvB,IAAI;IAAEG,SAAS;IAAEC,QAAQ,GAAG,GAAG;IAAEjB,IAAI;IAAEkB;EAAM,CAAC,GAAGpB,OAAO;EACxD;AACJ;AACA;EACI,IAAIF,6BAA6B,CAACC,SAAS,EAAEC,OAAO,CAAC,EAAE;IACnD,MAAMqB,eAAe,GAAGzC,YAAY,CAAC;MACjC,GAAGoB,OAAO;MACVsB,MAAM,EAAE,CAAC;MACTC,KAAK,EAAE;IACX,CAAC,CAAC;IACF,IAAIC,KAAK,GAAG;MAAEC,IAAI,EAAE,KAAK;MAAErB,KAAK,EAAEc,SAAS,CAAC,CAAC;IAAE,CAAC;IAChD,MAAMQ,qBAAqB,GAAG,EAAE;IAChC;AACR;AACA;AACA;IACQ,IAAIC,CAAC,GAAG,CAAC;IACT,OAAO,CAACH,KAAK,CAACC,IAAI,IAAIE,CAAC,GAAG9B,WAAW,EAAE;MACnC2B,KAAK,GAAGH,eAAe,CAACO,MAAM,CAACD,CAAC,CAAC;MACjCD,qBAAqB,CAACG,IAAI,CAACL,KAAK,CAACpB,KAAK,CAAC;MACvCuB,CAAC,IAAI/B,WAAW;IACpB;IACAwB,KAAK,GAAGU,SAAS;IACjBZ,SAAS,GAAGQ,qBAAqB;IACjCP,QAAQ,GAAGQ,CAAC,GAAG/B,WAAW;IAC1BM,IAAI,GAAG,QAAQ;EACnB;EACA,MAAM6B,SAAS,GAAGtD,YAAY,CAAC2B,KAAK,CAAC4B,KAAK,CAACC,OAAO,EAAElC,SAAS,EAAEmB,SAAS,EAAE;IACtE,GAAGlB,OAAO;IACVmB,QAAQ;IACR;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQjB,IAAI,EAAEA,IAAI;IACVkB;EACJ,CAAC,CAAC;EACF;AACJ;AACA;AACA;AACA;AACA;EACI,IAAIpB,OAAO,CAACkC,SAAS,EAAE;IACnBH,SAAS,CAACI,SAAS,GAAGlD,SAAS,CAACmD,YAAY,GACtCnD,SAAS,CAACoD,SAAS,GACnBC,QAAQ,CAACC,QAAQ,GACbD,QAAQ,CAACC,QAAQ,CAACC,WAAW,GAC7BC,WAAW,CAACC,GAAG,CAAC,CAAC;EAC/B;EACA,MAAMC,eAAe,GAAGA,CAAA,KAAMZ,SAAS,CAACa,MAAM,CAAC,CAAC;EAChD,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACrB3D,KAAK,CAAC4D,MAAM,CAACH,eAAe,CAAC;IAC7B9B,sBAAsB,CAAC,CAAC;IACxBE,qBAAqB,CAAC,CAAC;EAC3B,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIgB,SAAS,CAACgB,QAAQ,GAAG,MAAM;IACvB3C,KAAK,CAAC4C,GAAG,CAACrE,gBAAgB,CAACuC,SAAS,EAAElB,OAAO,CAAC,CAAC;IAC/CM,UAAU,IAAIA,UAAU,CAAC,CAAC;IAC1BuC,UAAU,CAAC,CAAC;EAChB,CAAC;EACD;AACJ;AACA;EACI,MAAMI,QAAQ,GAAG;IACbC,IAAIA,CAACjC,OAAO,EAAEkC,MAAM,EAAE;MAClB,OAAOrC,sBAAsB,CAACoC,IAAI,CAACjC,OAAO,EAAEkC,MAAM,CAAC;IACvD,CAAC;IACDC,cAAcA,CAACb,QAAQ,EAAE;MACrBR,SAAS,CAACQ,QAAQ,GAAGA,QAAQ;MAC7BR,SAAS,CAACgB,QAAQ,GAAG,IAAI;MACzB,OAAO/D,IAAI;IACf,CAAC;IACD,IAAIqE,IAAIA,CAAA,EAAG;MACP,OAAOxE,qBAAqB,CAACkD,SAAS,CAACS,WAAW,IAAI,CAAC,CAAC;IAC5D,CAAC;IACD,IAAIa,IAAIA,CAACC,OAAO,EAAE;MACdvB,SAAS,CAACS,WAAW,GAAG1D,qBAAqB,CAACwE,OAAO,CAAC;IAC1D,CAAC;IACD,IAAIC,KAAKA,CAAA,EAAG;MACR,OAAOxB,SAAS,CAACyB,YAAY;IACjC,CAAC;IACD,IAAID,KAAKA,CAACE,QAAQ,EAAE;MAChB1B,SAAS,CAACyB,YAAY,GAAGC,QAAQ;IACrC,CAAC;IACD,IAAItC,QAAQA,CAAA,EAAG;MACX,OAAOtC,qBAAqB,CAACsC,QAAQ,CAAC;IAC1C,CAAC;IACDuC,IAAI,EAAEA,CAAA,KAAM;MACR,IAAI9C,UAAU,EACV;MACJmB,SAAS,CAAC2B,IAAI,CAAC,CAAC;MAChB;AACZ;AACA;MACYvE,WAAW,CAACwD,eAAe,CAAC;IAChC,CAAC;IACDgB,KAAK,EAAEA,CAAA,KAAM5B,SAAS,CAAC4B,KAAK,CAAC,CAAC;IAC9BC,IAAI,EAAEA,CAAA,KAAM;MACRhD,UAAU,GAAG,IAAI;MACjB,IAAImB,SAAS,CAAC8B,SAAS,KAAK,MAAM,EAC9B;MACJ;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;MACY,MAAM;QAAErB;MAAY,CAAC,GAAGT,SAAS;MACjC,IAAIS,WAAW,EAAE;QACb,MAAMnB,eAAe,GAAGzC,YAAY,CAAC;UACjC,GAAGoB,OAAO;UACV8D,QAAQ,EAAE;QACd,CAAC,CAAC;QACF1D,KAAK,CAAC2D,eAAe,CAAC1C,eAAe,CAACO,MAAM,CAACY,WAAW,GAAG5C,WAAW,CAAC,CAACQ,KAAK,EAAEiB,eAAe,CAACO,MAAM,CAACY,WAAW,CAAC,CAACpC,KAAK,EAAER,WAAW,CAAC;MAC1I;MACAiD,UAAU,CAAC,CAAC;IAChB,CAAC;IACDmB,QAAQ,EAAEA,CAAA,KAAMjC,SAAS,CAACkC,MAAM,CAAC,CAAC;IAClCrB,MAAM,EAAEC;EACZ,CAAC;EACD,OAAOI,QAAQ;AACnB;AAEA,SAAS9C,0BAA0B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}