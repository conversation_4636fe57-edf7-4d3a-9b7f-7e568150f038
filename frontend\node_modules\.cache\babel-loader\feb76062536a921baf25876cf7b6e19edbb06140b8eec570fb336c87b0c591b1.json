{"ast": null, "code": "var passiveSupported = false;\nif (typeof window !== 'undefined') {\n  try {\n    var options = Object.defineProperty({}, 'passive', {\n      get: function () {\n        passiveSupported = true;\n        return true;\n      }\n    });\n    // @ts-ignore\n    window.addEventListener('test', options, options);\n    // @ts-ignore\n    window.removeEventListener('test', options, options);\n  } catch (err) {\n    passiveSupported = false;\n  }\n}\nexport var nonPassive = passiveSupported ? {\n  passive: false\n} : false;", "map": {"version": 3, "names": ["passiveSupported", "window", "options", "Object", "defineProperty", "get", "addEventListener", "removeEventListener", "err", "nonPassive", "passive"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/react-remove-scroll/dist/es2015/aggresiveCapture.js"], "sourcesContent": ["var passiveSupported = false;\nif (typeof window !== 'undefined') {\n    try {\n        var options = Object.defineProperty({}, 'passive', {\n            get: function () {\n                passiveSupported = true;\n                return true;\n            },\n        });\n        // @ts-ignore\n        window.addEventListener('test', options, options);\n        // @ts-ignore\n        window.removeEventListener('test', options, options);\n    }\n    catch (err) {\n        passiveSupported = false;\n    }\n}\nexport var nonPassive = passiveSupported ? { passive: false } : false;\n"], "mappings": "AAAA,IAAIA,gBAAgB,GAAG,KAAK;AAC5B,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;EAC/B,IAAI;IACA,IAAIC,OAAO,GAAGC,MAAM,CAACC,cAAc,CAAC,CAAC,CAAC,EAAE,SAAS,EAAE;MAC/CC,GAAG,EAAE,SAAAA,CAAA,EAAY;QACbL,gBAAgB,GAAG,IAAI;QACvB,OAAO,IAAI;MACf;IACJ,CAAC,CAAC;IACF;IACAC,MAAM,CAACK,gBAAgB,CAAC,MAAM,EAAEJ,OAAO,EAAEA,OAAO,CAAC;IACjD;IACAD,MAAM,CAACM,mBAAmB,CAAC,MAAM,EAAEL,OAAO,EAAEA,OAAO,CAAC;EACxD,CAAC,CACD,OAAOM,GAAG,EAAE;IACRR,gBAAgB,GAAG,KAAK;EAC5B;AACJ;AACA,OAAO,IAAIS,UAAU,GAAGT,gBAAgB,GAAG;EAAEU,OAAO,EAAE;AAAM,CAAC,GAAG,KAAK"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}