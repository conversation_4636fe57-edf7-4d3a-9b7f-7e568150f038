{"ast": null, "code": "import { getTabbableNodes } from './utils/DOMutils';\nimport { getAllAffectedNodes } from './utils/all-affected';\nimport { isGuard, isNotAGuard } from './utils/is';\nimport { getTopCommonParent } from './utils/parenting';\n/**\n * @returns list of focusable elements inside a given top node\n * @see {@link getFocusableNodes} for lower level access\n */\nexport var expandFocusableNodes = function (topNode) {\n  var entries = getAllAffectedNodes(topNode).filter(isNotAGuard);\n  var commonParent = getTopCommonParent(topNode, topNode, entries);\n  var visibilityCache = new Map();\n  var outerNodes = getTabbableNodes([commonParent], visibilityCache, true);\n  var innerElements = getTabbableNodes(entries, visibilityCache).filter(function (_a) {\n    var node = _a.node;\n    return isNotAGuard(node);\n  }).map(function (_a) {\n    var node = _a.node;\n    return node;\n  });\n  return outerNodes.map(function (_a) {\n    var node = _a.node,\n      index = _a.index;\n    return {\n      node: node,\n      index: index,\n      lockItem: innerElements.indexOf(node) >= 0,\n      guard: isGuard(node)\n    };\n  });\n};", "map": {"version": 3, "names": ["getTabbableNodes", "getAllAffectedNodes", "<PERSON><PERSON><PERSON>", "isNotAGuard", "getTopCommonParent", "expandFocusableNodes", "topNode", "entries", "filter", "commonParent", "visibilityCache", "Map", "outerNodes", "innerElements", "_a", "node", "map", "index", "lockItem", "indexOf", "guard"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/focus-lock/dist/es2015/focusables.js"], "sourcesContent": ["import { getTabbableNodes } from './utils/DOMutils';\nimport { getAllAffectedNodes } from './utils/all-affected';\nimport { isGuard, isNotAGuard } from './utils/is';\nimport { getTopCommonParent } from './utils/parenting';\n/**\n * @returns list of focusable elements inside a given top node\n * @see {@link getFocusableNodes} for lower level access\n */\nexport var expandFocusableNodes = function (topNode) {\n    var entries = getAllAffectedNodes(topNode).filter(isNotAGuard);\n    var commonParent = getTopCommonParent(topNode, topNode, entries);\n    var visibilityCache = new Map();\n    var outerNodes = getTabbableNodes([commonParent], visibilityCache, true);\n    var innerElements = getTabbableNodes(entries, visibilityCache)\n        .filter(function (_a) {\n        var node = _a.node;\n        return isNotAGuard(node);\n    })\n        .map(function (_a) {\n        var node = _a.node;\n        return node;\n    });\n    return outerNodes.map(function (_a) {\n        var node = _a.node, index = _a.index;\n        return ({\n            node: node,\n            index: index,\n            lockItem: innerElements.indexOf(node) >= 0,\n            guard: isGuard(node),\n        });\n    });\n};\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,kBAAkB;AACnD,SAASC,mBAAmB,QAAQ,sBAAsB;AAC1D,SAASC,OAAO,EAAEC,WAAW,QAAQ,YAAY;AACjD,SAASC,kBAAkB,QAAQ,mBAAmB;AACtD;AACA;AACA;AACA;AACA,OAAO,IAAIC,oBAAoB,GAAG,SAAAA,CAAUC,OAAO,EAAE;EACjD,IAAIC,OAAO,GAAGN,mBAAmB,CAACK,OAAO,CAAC,CAACE,MAAM,CAACL,WAAW,CAAC;EAC9D,IAAIM,YAAY,GAAGL,kBAAkB,CAACE,OAAO,EAAEA,OAAO,EAAEC,OAAO,CAAC;EAChE,IAAIG,eAAe,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC/B,IAAIC,UAAU,GAAGZ,gBAAgB,CAAC,CAACS,YAAY,CAAC,EAAEC,eAAe,EAAE,IAAI,CAAC;EACxE,IAAIG,aAAa,GAAGb,gBAAgB,CAACO,OAAO,EAAEG,eAAe,CAAC,CACzDF,MAAM,CAAC,UAAUM,EAAE,EAAE;IACtB,IAAIC,IAAI,GAAGD,EAAE,CAACC,IAAI;IAClB,OAAOZ,WAAW,CAACY,IAAI,CAAC;EAC5B,CAAC,CAAC,CACGC,GAAG,CAAC,UAAUF,EAAE,EAAE;IACnB,IAAIC,IAAI,GAAGD,EAAE,CAACC,IAAI;IAClB,OAAOA,IAAI;EACf,CAAC,CAAC;EACF,OAAOH,UAAU,CAACI,GAAG,CAAC,UAAUF,EAAE,EAAE;IAChC,IAAIC,IAAI,GAAGD,EAAE,CAACC,IAAI;MAAEE,KAAK,GAAGH,EAAE,CAACG,KAAK;IACpC,OAAQ;MACJF,IAAI,EAAEA,IAAI;MACVE,KAAK,EAAEA,KAAK;MACZC,QAAQ,EAAEL,aAAa,CAACM,OAAO,CAACJ,IAAI,CAAC,IAAI,CAAC;MAC1CK,KAAK,EAAElB,OAAO,CAACa,IAAI;IACvB,CAAC;EACL,CAAC,CAAC;AACN,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}