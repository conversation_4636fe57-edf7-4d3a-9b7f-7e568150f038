{"ast": null, "code": "const appearAnimationStore = new Map();\nexport { appearAnimationStore };", "map": {"version": 3, "names": ["appearAnimationStore", "Map"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/framer-motion/dist/es/animation/optimized-appear/store.mjs"], "sourcesContent": ["const appearAnimationStore = new Map();\n\nexport { appearAnimationStore };\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG,IAAIC,GAAG,CAAC,CAAC;AAEtC,SAASD,oBAAoB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}