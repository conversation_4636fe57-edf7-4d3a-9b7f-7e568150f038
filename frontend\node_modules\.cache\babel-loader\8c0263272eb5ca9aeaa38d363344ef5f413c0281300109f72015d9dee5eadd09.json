{"ast": null, "code": "import { focusOn } from './commands';\nimport { focusSolver } from './focusSolver';\nvar guardCount = 0;\nvar lockDisabled = false;\n/**\n * The main functionality of the focus-lock package\n *\n * Contains focus at a given node.\n * The last focused element will help to determine which element(first or last) should be focused.\n * The found element will be focused.\n *\n * This is one time action (move), not a persistent focus-lock\n *\n * HTML markers (see {@link import('./constants').FOCUS_AUTO} constants) can control autofocus\n * @see {@link focusSolver} for the same functionality without autofocus\n */\nexport var moveFocusInside = function (topNode, lastNode, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var focusable = focusSolver(topNode, lastNode);\n  // global local side effect to countain recursive lock activation and resolve focus-fighting\n  if (lockDisabled) {\n    return;\n  }\n  if (focusable) {\n    /** +FOCUS-FIGHTING prevention **/\n    if (guardCount > 2) {\n      // we have recursive entered back the lock activation\n      console.error('FocusLock: focus-fighting detected. Only one focus management system could be active. ' + 'See https://github.com/theKashey/focus-lock/#focus-fighting');\n      lockDisabled = true;\n      setTimeout(function () {\n        lockDisabled = false;\n      }, 1);\n      return;\n    }\n    guardCount++;\n    focusOn(focusable.node, options.focusOptions);\n    guardCount--;\n  }\n};", "map": {"version": 3, "names": ["focusOn", "focusSolver", "guardCount", "lockDisabled", "moveFocusInside", "topNode", "lastNode", "options", "focusable", "console", "error", "setTimeout", "node", "focusOptions"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/focus-lock/dist/es2015/moveFocusInside.js"], "sourcesContent": ["import { focusOn } from './commands';\nimport { focusSolver } from './focusSolver';\nvar guardCount = 0;\nvar lockDisabled = false;\n/**\n * The main functionality of the focus-lock package\n *\n * Contains focus at a given node.\n * The last focused element will help to determine which element(first or last) should be focused.\n * The found element will be focused.\n *\n * This is one time action (move), not a persistent focus-lock\n *\n * HTML markers (see {@link import('./constants').FOCUS_AUTO} constants) can control autofocus\n * @see {@link focusSolver} for the same functionality without autofocus\n */\nexport var moveFocusInside = function (topNode, lastNode, options) {\n    if (options === void 0) { options = {}; }\n    var focusable = focusSolver(topNode, lastNode);\n    // global local side effect to countain recursive lock activation and resolve focus-fighting\n    if (lockDisabled) {\n        return;\n    }\n    if (focusable) {\n        /** +FOCUS-FIGHTING prevention **/\n        if (guardCount > 2) {\n            // we have recursive entered back the lock activation\n            console.error('FocusLock: focus-fighting detected. Only one focus management system could be active. ' +\n                'See https://github.com/theKashey/focus-lock/#focus-fighting');\n            lockDisabled = true;\n            setTimeout(function () {\n                lockDisabled = false;\n            }, 1);\n            return;\n        }\n        guardCount++;\n        focusOn(focusable.node, options.focusOptions);\n        guardCount--;\n    }\n};\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,YAAY;AACpC,SAASC,WAAW,QAAQ,eAAe;AAC3C,IAAIC,UAAU,GAAG,CAAC;AAClB,IAAIC,YAAY,GAAG,KAAK;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,eAAe,GAAG,SAAAA,CAAUC,OAAO,EAAEC,QAAQ,EAAEC,OAAO,EAAE;EAC/D,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IAAEA,OAAO,GAAG,CAAC,CAAC;EAAE;EACxC,IAAIC,SAAS,GAAGP,WAAW,CAACI,OAAO,EAAEC,QAAQ,CAAC;EAC9C;EACA,IAAIH,YAAY,EAAE;IACd;EACJ;EACA,IAAIK,SAAS,EAAE;IACX;IACA,IAAIN,UAAU,GAAG,CAAC,EAAE;MAChB;MACAO,OAAO,CAACC,KAAK,CAAC,wFAAwF,GAClG,6DAA6D,CAAC;MAClEP,YAAY,GAAG,IAAI;MACnBQ,UAAU,CAAC,YAAY;QACnBR,YAAY,GAAG,KAAK;MACxB,CAAC,EAAE,CAAC,CAAC;MACL;IACJ;IACAD,UAAU,EAAE;IACZF,OAAO,CAACQ,SAAS,CAACI,IAAI,EAAEL,OAAO,CAACM,YAAY,CAAC;IAC7CX,UAAU,EAAE;EAChB;AACJ,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}