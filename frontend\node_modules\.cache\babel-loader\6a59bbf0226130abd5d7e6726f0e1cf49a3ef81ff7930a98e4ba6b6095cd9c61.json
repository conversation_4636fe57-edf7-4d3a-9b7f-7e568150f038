{"ast": null, "code": "/*!\n * Chart.js v4.4.0\n * https://www.chartjs.org\n * (c) 2023 Chart.js Contributors\n * Released under the MIT License\n */\nimport { Color } from '@kurkle/color';\n\n/**\n * @namespace Chart.helpers\n */ /**\n    * An empty function that can be used, for example, for optional callback.\n    */\nfunction noop() {\n  /* noop */}\n/**\n * Returns a unique id, sequentially generated from a global variable.\n */\nconst uid = (() => {\n  let id = 0;\n  return () => id++;\n})();\n/**\n * Returns true if `value` is neither null nor undefined, else returns false.\n * @param value - The value to test.\n * @since 2.7.0\n */\nfunction isNullOrUndef(value) {\n  return value === null || typeof value === 'undefined';\n}\n/**\n * Returns true if `value` is an array (including typed arrays), else returns false.\n * @param value - The value to test.\n * @function\n */\nfunction isArray(value) {\n  if (Array.isArray && Array.isArray(value)) {\n    return true;\n  }\n  const type = Object.prototype.toString.call(value);\n  if (type.slice(0, 7) === '[object' && type.slice(-6) === 'Array]') {\n    return true;\n  }\n  return false;\n}\n/**\n * Returns true if `value` is an object (excluding null), else returns false.\n * @param value - The value to test.\n * @since 2.7.0\n */\nfunction isObject(value) {\n  return value !== null && Object.prototype.toString.call(value) === '[object Object]';\n}\n/**\n * Returns true if `value` is a finite number, else returns false\n * @param value  - The value to test.\n */\nfunction isNumberFinite(value) {\n  return (typeof value === 'number' || value instanceof Number) && isFinite(+value);\n}\n/**\n * Returns `value` if finite, else returns `defaultValue`.\n * @param value - The value to return if defined.\n * @param defaultValue - The value to return if `value` is not finite.\n */\nfunction finiteOrDefault(value, defaultValue) {\n  return isNumberFinite(value) ? value : defaultValue;\n}\n/**\n * Returns `value` if defined, else returns `defaultValue`.\n * @param value - The value to return if defined.\n * @param defaultValue - The value to return if `value` is undefined.\n */\nfunction valueOrDefault(value, defaultValue) {\n  return typeof value === 'undefined' ? defaultValue : value;\n}\nconst toPercentage = (value, dimension) => typeof value === 'string' && value.endsWith('%') ? parseFloat(value) / 100 : +value / dimension;\nconst toDimension = (value, dimension) => typeof value === 'string' && value.endsWith('%') ? parseFloat(value) / 100 * dimension : +value;\n/**\n * Calls `fn` with the given `args` in the scope defined by `thisArg` and returns the\n * value returned by `fn`. If `fn` is not a function, this method returns undefined.\n * @param fn - The function to call.\n * @param args - The arguments with which `fn` should be called.\n * @param [thisArg] - The value of `this` provided for the call to `fn`.\n */\nfunction callback(fn, args, thisArg) {\n  if (fn && typeof fn.call === 'function') {\n    return fn.apply(thisArg, args);\n  }\n}\nfunction each(loopable, fn, thisArg, reverse) {\n  let i, len, keys;\n  if (isArray(loopable)) {\n    len = loopable.length;\n    if (reverse) {\n      for (i = len - 1; i >= 0; i--) {\n        fn.call(thisArg, loopable[i], i);\n      }\n    } else {\n      for (i = 0; i < len; i++) {\n        fn.call(thisArg, loopable[i], i);\n      }\n    }\n  } else if (isObject(loopable)) {\n    keys = Object.keys(loopable);\n    len = keys.length;\n    for (i = 0; i < len; i++) {\n      fn.call(thisArg, loopable[keys[i]], keys[i]);\n    }\n  }\n}\n/**\n * Returns true if the `a0` and `a1` arrays have the same content, else returns false.\n * @param a0 - The array to compare\n * @param a1 - The array to compare\n * @private\n */\nfunction _elementsEqual(a0, a1) {\n  let i, ilen, v0, v1;\n  if (!a0 || !a1 || a0.length !== a1.length) {\n    return false;\n  }\n  for (i = 0, ilen = a0.length; i < ilen; ++i) {\n    v0 = a0[i];\n    v1 = a1[i];\n    if (v0.datasetIndex !== v1.datasetIndex || v0.index !== v1.index) {\n      return false;\n    }\n  }\n  return true;\n}\n/**\n * Returns a deep copy of `source` without keeping references on objects and arrays.\n * @param source - The value to clone.\n */\nfunction clone(source) {\n  if (isArray(source)) {\n    return source.map(clone);\n  }\n  if (isObject(source)) {\n    const target = Object.create(null);\n    const keys = Object.keys(source);\n    const klen = keys.length;\n    let k = 0;\n    for (; k < klen; ++k) {\n      target[keys[k]] = clone(source[keys[k]]);\n    }\n    return target;\n  }\n  return source;\n}\nfunction isValidKey(key) {\n  return ['__proto__', 'prototype', 'constructor'].indexOf(key) === -1;\n}\n/**\n * The default merger when Chart.helpers.merge is called without merger option.\n * Note(SB): also used by mergeConfig and mergeScaleConfig as fallback.\n * @private\n */\nfunction _merger(key, target, source, options) {\n  if (!isValidKey(key)) {\n    return;\n  }\n  const tval = target[key];\n  const sval = source[key];\n  if (isObject(tval) && isObject(sval)) {\n    // eslint-disable-next-line @typescript-eslint/no-use-before-define\n    merge(tval, sval, options);\n  } else {\n    target[key] = clone(sval);\n  }\n}\nfunction merge(target, source, options) {\n  const sources = isArray(source) ? source : [source];\n  const ilen = sources.length;\n  if (!isObject(target)) {\n    return target;\n  }\n  options = options || {};\n  const merger = options.merger || _merger;\n  let current;\n  for (let i = 0; i < ilen; ++i) {\n    current = sources[i];\n    if (!isObject(current)) {\n      continue;\n    }\n    const keys = Object.keys(current);\n    for (let k = 0, klen = keys.length; k < klen; ++k) {\n      merger(keys[k], target, current, options);\n    }\n  }\n  return target;\n}\nfunction mergeIf(target, source) {\n  // eslint-disable-next-line @typescript-eslint/no-use-before-define\n  return merge(target, source, {\n    merger: _mergerIf\n  });\n}\n/**\n * Merges source[key] in target[key] only if target[key] is undefined.\n * @private\n */\nfunction _mergerIf(key, target, source) {\n  if (!isValidKey(key)) {\n    return;\n  }\n  const tval = target[key];\n  const sval = source[key];\n  if (isObject(tval) && isObject(sval)) {\n    mergeIf(tval, sval);\n  } else if (!Object.prototype.hasOwnProperty.call(target, key)) {\n    target[key] = clone(sval);\n  }\n}\n/**\n * @private\n */\nfunction _deprecated(scope, value, previous, current) {\n  if (value !== undefined) {\n    console.warn(scope + ': \"' + previous + '\" is deprecated. Please use \"' + current + '\" instead');\n  }\n}\n// resolveObjectKey resolver cache\nconst keyResolvers = {\n  // Chart.helpers.core resolveObjectKey should resolve empty key to root object\n  '': v => v,\n  // default resolvers\n  x: o => o.x,\n  y: o => o.y\n};\n/**\n * @private\n */\nfunction _splitKey(key) {\n  const parts = key.split('.');\n  const keys = [];\n  let tmp = '';\n  for (const part of parts) {\n    tmp += part;\n    if (tmp.endsWith('\\\\')) {\n      tmp = tmp.slice(0, -1) + '.';\n    } else {\n      keys.push(tmp);\n      tmp = '';\n    }\n  }\n  return keys;\n}\nfunction _getKeyResolver(key) {\n  const keys = _splitKey(key);\n  return obj => {\n    for (const k of keys) {\n      if (k === '') {\n        break;\n      }\n      obj = obj && obj[k];\n    }\n    return obj;\n  };\n}\nfunction resolveObjectKey(obj, key) {\n  const resolver = keyResolvers[key] || (keyResolvers[key] = _getKeyResolver(key));\n  return resolver(obj);\n}\n/**\n * @private\n */\nfunction _capitalize(str) {\n  return str.charAt(0).toUpperCase() + str.slice(1);\n}\nconst defined = value => typeof value !== 'undefined';\nconst isFunction = value => typeof value === 'function';\n// Adapted from https://stackoverflow.com/questions/31128855/comparing-ecma6-sets-for-equality#31129384\nconst setsEqual = (a, b) => {\n  if (a.size !== b.size) {\n    return false;\n  }\n  for (const item of a) {\n    if (!b.has(item)) {\n      return false;\n    }\n  }\n  return true;\n};\n/**\n * @param e - The event\n * @private\n */\nfunction _isClickEvent(e) {\n  return e.type === 'mouseup' || e.type === 'click' || e.type === 'contextmenu';\n}\n\n/**\n * @alias Chart.helpers.math\n * @namespace\n */\nconst PI = Math.PI;\nconst TAU = 2 * PI;\nconst PITAU = TAU + PI;\nconst INFINITY = Number.POSITIVE_INFINITY;\nconst RAD_PER_DEG = PI / 180;\nconst HALF_PI = PI / 2;\nconst QUARTER_PI = PI / 4;\nconst TWO_THIRDS_PI = PI * 2 / 3;\nconst log10 = Math.log10;\nconst sign = Math.sign;\nfunction almostEquals(x, y, epsilon) {\n  return Math.abs(x - y) < epsilon;\n}\n/**\n * Implementation of the nice number algorithm used in determining where axis labels will go\n */\nfunction niceNum(range) {\n  const roundedRange = Math.round(range);\n  range = almostEquals(range, roundedRange, range / 1000) ? roundedRange : range;\n  const niceRange = Math.pow(10, Math.floor(log10(range)));\n  const fraction = range / niceRange;\n  const niceFraction = fraction <= 1 ? 1 : fraction <= 2 ? 2 : fraction <= 5 ? 5 : 10;\n  return niceFraction * niceRange;\n}\n/**\n * Returns an array of factors sorted from 1 to sqrt(value)\n * @private\n */\nfunction _factorize(value) {\n  const result = [];\n  const sqrt = Math.sqrt(value);\n  let i;\n  for (i = 1; i < sqrt; i++) {\n    if (value % i === 0) {\n      result.push(i);\n      result.push(value / i);\n    }\n  }\n  if (sqrt === (sqrt | 0)) {\n    result.push(sqrt);\n  }\n  result.sort((a, b) => a - b).pop();\n  return result;\n}\nfunction isNumber(n) {\n  return !isNaN(parseFloat(n)) && isFinite(n);\n}\nfunction almostWhole(x, epsilon) {\n  const rounded = Math.round(x);\n  return rounded - epsilon <= x && rounded + epsilon >= x;\n}\n/**\n * @private\n */\nfunction _setMinAndMaxByKey(array, target, property) {\n  let i, ilen, value;\n  for (i = 0, ilen = array.length; i < ilen; i++) {\n    value = array[i][property];\n    if (!isNaN(value)) {\n      target.min = Math.min(target.min, value);\n      target.max = Math.max(target.max, value);\n    }\n  }\n}\nfunction toRadians(degrees) {\n  return degrees * (PI / 180);\n}\nfunction toDegrees(radians) {\n  return radians * (180 / PI);\n}\n/**\n * Returns the number of decimal places\n * i.e. the number of digits after the decimal point, of the value of this Number.\n * @param x - A number.\n * @returns The number of decimal places.\n * @private\n */\nfunction _decimalPlaces(x) {\n  if (!isNumberFinite(x)) {\n    return;\n  }\n  let e = 1;\n  let p = 0;\n  while (Math.round(x * e) / e !== x) {\n    e *= 10;\n    p++;\n  }\n  return p;\n}\n// Gets the angle from vertical upright to the point about a centre.\nfunction getAngleFromPoint(centrePoint, anglePoint) {\n  const distanceFromXCenter = anglePoint.x - centrePoint.x;\n  const distanceFromYCenter = anglePoint.y - centrePoint.y;\n  const radialDistanceFromCenter = Math.sqrt(distanceFromXCenter * distanceFromXCenter + distanceFromYCenter * distanceFromYCenter);\n  let angle = Math.atan2(distanceFromYCenter, distanceFromXCenter);\n  if (angle < -0.5 * PI) {\n    angle += TAU; // make sure the returned angle is in the range of (-PI/2, 3PI/2]\n  }\n\n  return {\n    angle,\n    distance: radialDistanceFromCenter\n  };\n}\nfunction distanceBetweenPoints(pt1, pt2) {\n  return Math.sqrt(Math.pow(pt2.x - pt1.x, 2) + Math.pow(pt2.y - pt1.y, 2));\n}\n/**\n * Shortest distance between angles, in either direction.\n * @private\n */\nfunction _angleDiff(a, b) {\n  return (a - b + PITAU) % TAU - PI;\n}\n/**\n * Normalize angle to be between 0 and 2*PI\n * @private\n */\nfunction _normalizeAngle(a) {\n  return (a % TAU + TAU) % TAU;\n}\n/**\n * @private\n */\nfunction _angleBetween(angle, start, end, sameAngleIsFullCircle) {\n  const a = _normalizeAngle(angle);\n  const s = _normalizeAngle(start);\n  const e = _normalizeAngle(end);\n  const angleToStart = _normalizeAngle(s - a);\n  const angleToEnd = _normalizeAngle(e - a);\n  const startToAngle = _normalizeAngle(a - s);\n  const endToAngle = _normalizeAngle(a - e);\n  return a === s || a === e || sameAngleIsFullCircle && s === e || angleToStart > angleToEnd && startToAngle < endToAngle;\n}\n/**\n * Limit `value` between `min` and `max`\n * @param value\n * @param min\n * @param max\n * @private\n */\nfunction _limitValue(value, min, max) {\n  return Math.max(min, Math.min(max, value));\n}\n/**\n * @param {number} value\n * @private\n */\nfunction _int16Range(value) {\n  return _limitValue(value, -32768, 32767);\n}\n/**\n * @param value\n * @param start\n * @param end\n * @param [epsilon]\n * @private\n */\nfunction _isBetween(value, start, end, epsilon = 1e-6) {\n  return value >= Math.min(start, end) - epsilon && value <= Math.max(start, end) + epsilon;\n}\nfunction _lookup(table, value, cmp) {\n  cmp = cmp || (index => table[index] < value);\n  let hi = table.length - 1;\n  let lo = 0;\n  let mid;\n  while (hi - lo > 1) {\n    mid = lo + hi >> 1;\n    if (cmp(mid)) {\n      lo = mid;\n    } else {\n      hi = mid;\n    }\n  }\n  return {\n    lo,\n    hi\n  };\n}\n/**\n * Binary search\n * @param table - the table search. must be sorted!\n * @param key - property name for the value in each entry\n * @param value - value to find\n * @param last - lookup last index\n * @private\n */\nconst _lookupByKey = (table, key, value, last) => _lookup(table, value, last ? index => {\n  const ti = table[index][key];\n  return ti < value || ti === value && table[index + 1][key] === value;\n} : index => table[index][key] < value);\n/**\n * Reverse binary search\n * @param table - the table search. must be sorted!\n * @param key - property name for the value in each entry\n * @param value - value to find\n * @private\n */\nconst _rlookupByKey = (table, key, value) => _lookup(table, value, index => table[index][key] >= value);\n/**\n * Return subset of `values` between `min` and `max` inclusive.\n * Values are assumed to be in sorted order.\n * @param values - sorted array of values\n * @param min - min value\n * @param max - max value\n */\nfunction _filterBetween(values, min, max) {\n  let start = 0;\n  let end = values.length;\n  while (start < end && values[start] < min) {\n    start++;\n  }\n  while (end > start && values[end - 1] > max) {\n    end--;\n  }\n  return start > 0 || end < values.length ? values.slice(start, end) : values;\n}\nconst arrayEvents = ['push', 'pop', 'shift', 'splice', 'unshift'];\nfunction listenArrayEvents(array, listener) {\n  if (array._chartjs) {\n    array._chartjs.listeners.push(listener);\n    return;\n  }\n  Object.defineProperty(array, '_chartjs', {\n    configurable: true,\n    enumerable: false,\n    value: {\n      listeners: [listener]\n    }\n  });\n  arrayEvents.forEach(key => {\n    const method = '_onData' + _capitalize(key);\n    const base = array[key];\n    Object.defineProperty(array, key, {\n      configurable: true,\n      enumerable: false,\n      value(...args) {\n        const res = base.apply(this, args);\n        array._chartjs.listeners.forEach(object => {\n          if (typeof object[method] === 'function') {\n            object[method](...args);\n          }\n        });\n        return res;\n      }\n    });\n  });\n}\nfunction unlistenArrayEvents(array, listener) {\n  const stub = array._chartjs;\n  if (!stub) {\n    return;\n  }\n  const listeners = stub.listeners;\n  const index = listeners.indexOf(listener);\n  if (index !== -1) {\n    listeners.splice(index, 1);\n  }\n  if (listeners.length > 0) {\n    return;\n  }\n  arrayEvents.forEach(key => {\n    delete array[key];\n  });\n  delete array._chartjs;\n}\n/**\n * @param items\n */\nfunction _arrayUnique(items) {\n  const set = new Set(items);\n  if (set.size === items.length) {\n    return items;\n  }\n  return Array.from(set);\n}\nfunction fontString(pixelSize, fontStyle, fontFamily) {\n  return fontStyle + ' ' + pixelSize + 'px ' + fontFamily;\n}\n/**\n* Request animation polyfill\n*/\nconst requestAnimFrame = function () {\n  if (typeof window === 'undefined') {\n    return function (callback) {\n      return callback();\n    };\n  }\n  return window.requestAnimationFrame;\n}();\n/**\n * Throttles calling `fn` once per animation frame\n * Latest arguments are used on the actual call\n */\nfunction throttled(fn, thisArg) {\n  let argsToUse = [];\n  let ticking = false;\n  return function (...args) {\n    // Save the args for use later\n    argsToUse = args;\n    if (!ticking) {\n      ticking = true;\n      requestAnimFrame.call(window, () => {\n        ticking = false;\n        fn.apply(thisArg, argsToUse);\n      });\n    }\n  };\n}\n/**\n * Debounces calling `fn` for `delay` ms\n */\nfunction debounce(fn, delay) {\n  let timeout;\n  return function (...args) {\n    if (delay) {\n      clearTimeout(timeout);\n      timeout = setTimeout(fn, delay, args);\n    } else {\n      fn.apply(this, args);\n    }\n    return delay;\n  };\n}\n/**\n * Converts 'start' to 'left', 'end' to 'right' and others to 'center'\n * @private\n */\nconst _toLeftRightCenter = align => align === 'start' ? 'left' : align === 'end' ? 'right' : 'center';\n/**\n * Returns `start`, `end` or `(start + end) / 2` depending on `align`. Defaults to `center`\n * @private\n */\nconst _alignStartEnd = (align, start, end) => align === 'start' ? start : align === 'end' ? end : (start + end) / 2;\n/**\n * Returns `left`, `right` or `(left + right) / 2` depending on `align`. Defaults to `left`\n * @private\n */\nconst _textX = (align, left, right, rtl) => {\n  const check = rtl ? 'left' : 'right';\n  return align === check ? right : align === 'center' ? (left + right) / 2 : left;\n};\n/**\n * Return start and count of visible points.\n * @private\n */\nfunction _getStartAndCountOfVisiblePoints(meta, points, animationsDisabled) {\n  const pointCount = points.length;\n  let start = 0;\n  let count = pointCount;\n  if (meta._sorted) {\n    const {\n      iScale,\n      _parsed\n    } = meta;\n    const axis = iScale.axis;\n    const {\n      min,\n      max,\n      minDefined,\n      maxDefined\n    } = iScale.getUserBounds();\n    if (minDefined) {\n      start = _limitValue(Math.min(\n      // @ts-expect-error Need to type _parsed\n      _lookupByKey(_parsed, axis, min).lo,\n      // @ts-expect-error Need to fix types on _lookupByKey\n      animationsDisabled ? pointCount : _lookupByKey(points, axis, iScale.getPixelForValue(min)).lo), 0, pointCount - 1);\n    }\n    if (maxDefined) {\n      count = _limitValue(Math.max(\n      // @ts-expect-error Need to type _parsed\n      _lookupByKey(_parsed, iScale.axis, max, true).hi + 1,\n      // @ts-expect-error Need to fix types on _lookupByKey\n      animationsDisabled ? 0 : _lookupByKey(points, axis, iScale.getPixelForValue(max), true).hi + 1), start, pointCount) - start;\n    } else {\n      count = pointCount - start;\n    }\n  }\n  return {\n    start,\n    count\n  };\n}\n/**\n * Checks if the scale ranges have changed.\n * @param {object} meta - dataset meta.\n * @returns {boolean}\n * @private\n */\nfunction _scaleRangesChanged(meta) {\n  const {\n    xScale,\n    yScale,\n    _scaleRanges\n  } = meta;\n  const newRanges = {\n    xmin: xScale.min,\n    xmax: xScale.max,\n    ymin: yScale.min,\n    ymax: yScale.max\n  };\n  if (!_scaleRanges) {\n    meta._scaleRanges = newRanges;\n    return true;\n  }\n  const changed = _scaleRanges.xmin !== xScale.min || _scaleRanges.xmax !== xScale.max || _scaleRanges.ymin !== yScale.min || _scaleRanges.ymax !== yScale.max;\n  Object.assign(_scaleRanges, newRanges);\n  return changed;\n}\nconst atEdge = t => t === 0 || t === 1;\nconst elasticIn = (t, s, p) => -(Math.pow(2, 10 * (t -= 1)) * Math.sin((t - s) * TAU / p));\nconst elasticOut = (t, s, p) => Math.pow(2, -10 * t) * Math.sin((t - s) * TAU / p) + 1;\n/**\n * Easing functions adapted from Robert Penner's easing equations.\n * @namespace Chart.helpers.easing.effects\n * @see http://www.robertpenner.com/easing/\n */\nconst effects = {\n  linear: t => t,\n  easeInQuad: t => t * t,\n  easeOutQuad: t => -t * (t - 2),\n  easeInOutQuad: t => (t /= 0.5) < 1 ? 0.5 * t * t : -0.5 * (--t * (t - 2) - 1),\n  easeInCubic: t => t * t * t,\n  easeOutCubic: t => (t -= 1) * t * t + 1,\n  easeInOutCubic: t => (t /= 0.5) < 1 ? 0.5 * t * t * t : 0.5 * ((t -= 2) * t * t + 2),\n  easeInQuart: t => t * t * t * t,\n  easeOutQuart: t => -((t -= 1) * t * t * t - 1),\n  easeInOutQuart: t => (t /= 0.5) < 1 ? 0.5 * t * t * t * t : -0.5 * ((t -= 2) * t * t * t - 2),\n  easeInQuint: t => t * t * t * t * t,\n  easeOutQuint: t => (t -= 1) * t * t * t * t + 1,\n  easeInOutQuint: t => (t /= 0.5) < 1 ? 0.5 * t * t * t * t * t : 0.5 * ((t -= 2) * t * t * t * t + 2),\n  easeInSine: t => -Math.cos(t * HALF_PI) + 1,\n  easeOutSine: t => Math.sin(t * HALF_PI),\n  easeInOutSine: t => -0.5 * (Math.cos(PI * t) - 1),\n  easeInExpo: t => t === 0 ? 0 : Math.pow(2, 10 * (t - 1)),\n  easeOutExpo: t => t === 1 ? 1 : -Math.pow(2, -10 * t) + 1,\n  easeInOutExpo: t => atEdge(t) ? t : t < 0.5 ? 0.5 * Math.pow(2, 10 * (t * 2 - 1)) : 0.5 * (-Math.pow(2, -10 * (t * 2 - 1)) + 2),\n  easeInCirc: t => t >= 1 ? t : -(Math.sqrt(1 - t * t) - 1),\n  easeOutCirc: t => Math.sqrt(1 - (t -= 1) * t),\n  easeInOutCirc: t => (t /= 0.5) < 1 ? -0.5 * (Math.sqrt(1 - t * t) - 1) : 0.5 * (Math.sqrt(1 - (t -= 2) * t) + 1),\n  easeInElastic: t => atEdge(t) ? t : elasticIn(t, 0.075, 0.3),\n  easeOutElastic: t => atEdge(t) ? t : elasticOut(t, 0.075, 0.3),\n  easeInOutElastic(t) {\n    const s = 0.1125;\n    const p = 0.45;\n    return atEdge(t) ? t : t < 0.5 ? 0.5 * elasticIn(t * 2, s, p) : 0.5 + 0.5 * elasticOut(t * 2 - 1, s, p);\n  },\n  easeInBack(t) {\n    const s = 1.70158;\n    return t * t * ((s + 1) * t - s);\n  },\n  easeOutBack(t) {\n    const s = 1.70158;\n    return (t -= 1) * t * ((s + 1) * t + s) + 1;\n  },\n  easeInOutBack(t) {\n    let s = 1.70158;\n    if ((t /= 0.5) < 1) {\n      return 0.5 * (t * t * (((s *= 1.525) + 1) * t - s));\n    }\n    return 0.5 * ((t -= 2) * t * (((s *= 1.525) + 1) * t + s) + 2);\n  },\n  easeInBounce: t => 1 - effects.easeOutBounce(1 - t),\n  easeOutBounce(t) {\n    const m = 7.5625;\n    const d = 2.75;\n    if (t < 1 / d) {\n      return m * t * t;\n    }\n    if (t < 2 / d) {\n      return m * (t -= 1.5 / d) * t + 0.75;\n    }\n    if (t < 2.5 / d) {\n      return m * (t -= 2.25 / d) * t + 0.9375;\n    }\n    return m * (t -= 2.625 / d) * t + 0.984375;\n  },\n  easeInOutBounce: t => t < 0.5 ? effects.easeInBounce(t * 2) * 0.5 : effects.easeOutBounce(t * 2 - 1) * 0.5 + 0.5\n};\nfunction isPatternOrGradient(value) {\n  if (value && typeof value === 'object') {\n    const type = value.toString();\n    return type === '[object CanvasPattern]' || type === '[object CanvasGradient]';\n  }\n  return false;\n}\nfunction color(value) {\n  return isPatternOrGradient(value) ? value : new Color(value);\n}\nfunction getHoverColor(value) {\n  return isPatternOrGradient(value) ? value : new Color(value).saturate(0.5).darken(0.1).hexString();\n}\nconst numbers = ['x', 'y', 'borderWidth', 'radius', 'tension'];\nconst colors = ['color', 'borderColor', 'backgroundColor'];\nfunction applyAnimationsDefaults(defaults) {\n  defaults.set('animation', {\n    delay: undefined,\n    duration: 1000,\n    easing: 'easeOutQuart',\n    fn: undefined,\n    from: undefined,\n    loop: undefined,\n    to: undefined,\n    type: undefined\n  });\n  defaults.describe('animation', {\n    _fallback: false,\n    _indexable: false,\n    _scriptable: name => name !== 'onProgress' && name !== 'onComplete' && name !== 'fn'\n  });\n  defaults.set('animations', {\n    colors: {\n      type: 'color',\n      properties: colors\n    },\n    numbers: {\n      type: 'number',\n      properties: numbers\n    }\n  });\n  defaults.describe('animations', {\n    _fallback: 'animation'\n  });\n  defaults.set('transitions', {\n    active: {\n      animation: {\n        duration: 400\n      }\n    },\n    resize: {\n      animation: {\n        duration: 0\n      }\n    },\n    show: {\n      animations: {\n        colors: {\n          from: 'transparent'\n        },\n        visible: {\n          type: 'boolean',\n          duration: 0\n        }\n      }\n    },\n    hide: {\n      animations: {\n        colors: {\n          to: 'transparent'\n        },\n        visible: {\n          type: 'boolean',\n          easing: 'linear',\n          fn: v => v | 0\n        }\n      }\n    }\n  });\n}\nfunction applyLayoutsDefaults(defaults) {\n  defaults.set('layout', {\n    autoPadding: true,\n    padding: {\n      top: 0,\n      right: 0,\n      bottom: 0,\n      left: 0\n    }\n  });\n}\nconst intlCache = new Map();\nfunction getNumberFormat(locale, options) {\n  options = options || {};\n  const cacheKey = locale + JSON.stringify(options);\n  let formatter = intlCache.get(cacheKey);\n  if (!formatter) {\n    formatter = new Intl.NumberFormat(locale, options);\n    intlCache.set(cacheKey, formatter);\n  }\n  return formatter;\n}\nfunction formatNumber(num, locale, options) {\n  return getNumberFormat(locale, options).format(num);\n}\nconst formatters = {\n  values(value) {\n    return isArray(value) ? value : '' + value;\n  },\n  numeric(tickValue, index, ticks) {\n    if (tickValue === 0) {\n      return '0';\n    }\n    const locale = this.chart.options.locale;\n    let notation;\n    let delta = tickValue;\n    if (ticks.length > 1) {\n      const maxTick = Math.max(Math.abs(ticks[0].value), Math.abs(ticks[ticks.length - 1].value));\n      if (maxTick < 1e-4 || maxTick > 1e+15) {\n        notation = 'scientific';\n      }\n      delta = calculateDelta(tickValue, ticks);\n    }\n    const logDelta = log10(Math.abs(delta));\n    const numDecimal = isNaN(logDelta) ? 1 : Math.max(Math.min(-1 * Math.floor(logDelta), 20), 0);\n    const options = {\n      notation,\n      minimumFractionDigits: numDecimal,\n      maximumFractionDigits: numDecimal\n    };\n    Object.assign(options, this.options.ticks.format);\n    return formatNumber(tickValue, locale, options);\n  },\n  logarithmic(tickValue, index, ticks) {\n    if (tickValue === 0) {\n      return '0';\n    }\n    const remain = ticks[index].significand || tickValue / Math.pow(10, Math.floor(log10(tickValue)));\n    if ([1, 2, 3, 5, 10, 15].includes(remain) || index > 0.8 * ticks.length) {\n      return formatters.numeric.call(this, tickValue, index, ticks);\n    }\n    return '';\n  }\n};\nfunction calculateDelta(tickValue, ticks) {\n  let delta = ticks.length > 3 ? ticks[2].value - ticks[1].value : ticks[1].value - ticks[0].value;\n  if (Math.abs(delta) >= 1 && tickValue !== Math.floor(tickValue)) {\n    delta = tickValue - Math.floor(tickValue);\n  }\n  return delta;\n}\nvar Ticks = {\n  formatters\n};\nfunction applyScaleDefaults(defaults) {\n  defaults.set('scale', {\n    display: true,\n    offset: false,\n    reverse: false,\n    beginAtZero: false,\n    bounds: 'ticks',\n    clip: true,\n    grace: 0,\n    grid: {\n      display: true,\n      lineWidth: 1,\n      drawOnChartArea: true,\n      drawTicks: true,\n      tickLength: 8,\n      tickWidth: (_ctx, options) => options.lineWidth,\n      tickColor: (_ctx, options) => options.color,\n      offset: false\n    },\n    border: {\n      display: true,\n      dash: [],\n      dashOffset: 0.0,\n      width: 1\n    },\n    title: {\n      display: false,\n      text: '',\n      padding: {\n        top: 4,\n        bottom: 4\n      }\n    },\n    ticks: {\n      minRotation: 0,\n      maxRotation: 50,\n      mirror: false,\n      textStrokeWidth: 0,\n      textStrokeColor: '',\n      padding: 3,\n      display: true,\n      autoSkip: true,\n      autoSkipPadding: 3,\n      labelOffset: 0,\n      callback: Ticks.formatters.values,\n      minor: {},\n      major: {},\n      align: 'center',\n      crossAlign: 'near',\n      showLabelBackdrop: false,\n      backdropColor: 'rgba(255, 255, 255, 0.75)',\n      backdropPadding: 2\n    }\n  });\n  defaults.route('scale.ticks', 'color', '', 'color');\n  defaults.route('scale.grid', 'color', '', 'borderColor');\n  defaults.route('scale.border', 'color', '', 'borderColor');\n  defaults.route('scale.title', 'color', '', 'color');\n  defaults.describe('scale', {\n    _fallback: false,\n    _scriptable: name => !name.startsWith('before') && !name.startsWith('after') && name !== 'callback' && name !== 'parser',\n    _indexable: name => name !== 'borderDash' && name !== 'tickBorderDash' && name !== 'dash'\n  });\n  defaults.describe('scales', {\n    _fallback: 'scale'\n  });\n  defaults.describe('scale.ticks', {\n    _scriptable: name => name !== 'backdropPadding' && name !== 'callback',\n    _indexable: name => name !== 'backdropPadding'\n  });\n}\nconst overrides = Object.create(null);\nconst descriptors = Object.create(null);\nfunction getScope$1(node, key) {\n  if (!key) {\n    return node;\n  }\n  const keys = key.split('.');\n  for (let i = 0, n = keys.length; i < n; ++i) {\n    const k = keys[i];\n    node = node[k] || (node[k] = Object.create(null));\n  }\n  return node;\n}\nfunction set(root, scope, values) {\n  if (typeof scope === 'string') {\n    return merge(getScope$1(root, scope), values);\n  }\n  return merge(getScope$1(root, ''), scope);\n}\nclass Defaults {\n  constructor(_descriptors, _appliers) {\n    this.animation = undefined;\n    this.backgroundColor = 'rgba(0,0,0,0.1)';\n    this.borderColor = 'rgba(0,0,0,0.1)';\n    this.color = '#666';\n    this.datasets = {};\n    this.devicePixelRatio = context => context.chart.platform.getDevicePixelRatio();\n    this.elements = {};\n    this.events = ['mousemove', 'mouseout', 'click', 'touchstart', 'touchmove'];\n    this.font = {\n      family: \"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif\",\n      size: 12,\n      style: 'normal',\n      lineHeight: 1.2,\n      weight: null\n    };\n    this.hover = {};\n    this.hoverBackgroundColor = (ctx, options) => getHoverColor(options.backgroundColor);\n    this.hoverBorderColor = (ctx, options) => getHoverColor(options.borderColor);\n    this.hoverColor = (ctx, options) => getHoverColor(options.color);\n    this.indexAxis = 'x';\n    this.interaction = {\n      mode: 'nearest',\n      intersect: true,\n      includeInvisible: false\n    };\n    this.maintainAspectRatio = true;\n    this.onHover = null;\n    this.onClick = null;\n    this.parsing = true;\n    this.plugins = {};\n    this.responsive = true;\n    this.scale = undefined;\n    this.scales = {};\n    this.showLine = true;\n    this.drawActiveElementsOnTop = true;\n    this.describe(_descriptors);\n    this.apply(_appliers);\n  }\n  set(scope, values) {\n    return set(this, scope, values);\n  }\n  get(scope) {\n    return getScope$1(this, scope);\n  }\n  describe(scope, values) {\n    return set(descriptors, scope, values);\n  }\n  override(scope, values) {\n    return set(overrides, scope, values);\n  }\n  route(scope, name, targetScope, targetName) {\n    const scopeObject = getScope$1(this, scope);\n    const targetScopeObject = getScope$1(this, targetScope);\n    const privateName = '_' + name;\n    Object.defineProperties(scopeObject, {\n      [privateName]: {\n        value: scopeObject[name],\n        writable: true\n      },\n      [name]: {\n        enumerable: true,\n        get() {\n          const local = this[privateName];\n          const target = targetScopeObject[targetName];\n          if (isObject(local)) {\n            return Object.assign({}, target, local);\n          }\n          return valueOrDefault(local, target);\n        },\n        set(value) {\n          this[privateName] = value;\n        }\n      }\n    });\n  }\n  apply(appliers) {\n    appliers.forEach(apply => apply(this));\n  }\n}\nvar defaults = /* #__PURE__ */new Defaults({\n  _scriptable: name => !name.startsWith('on'),\n  _indexable: name => name !== 'events',\n  hover: {\n    _fallback: 'interaction'\n  },\n  interaction: {\n    _scriptable: false,\n    _indexable: false\n  }\n}, [applyAnimationsDefaults, applyLayoutsDefaults, applyScaleDefaults]);\n\n/**\n * Converts the given font object into a CSS font string.\n * @param font - A font object.\n * @return The CSS font string. See https://developer.mozilla.org/en-US/docs/Web/CSS/font\n * @private\n */\nfunction toFontString(font) {\n  if (!font || isNullOrUndef(font.size) || isNullOrUndef(font.family)) {\n    return null;\n  }\n  return (font.style ? font.style + ' ' : '') + (font.weight ? font.weight + ' ' : '') + font.size + 'px ' + font.family;\n}\n/**\n * @private\n */\nfunction _measureText(ctx, data, gc, longest, string) {\n  let textWidth = data[string];\n  if (!textWidth) {\n    textWidth = data[string] = ctx.measureText(string).width;\n    gc.push(string);\n  }\n  if (textWidth > longest) {\n    longest = textWidth;\n  }\n  return longest;\n}\n/**\n * @private\n */ // eslint-disable-next-line complexity\nfunction _longestText(ctx, font, arrayOfThings, cache) {\n  cache = cache || {};\n  let data = cache.data = cache.data || {};\n  let gc = cache.garbageCollect = cache.garbageCollect || [];\n  if (cache.font !== font) {\n    data = cache.data = {};\n    gc = cache.garbageCollect = [];\n    cache.font = font;\n  }\n  ctx.save();\n  ctx.font = font;\n  let longest = 0;\n  const ilen = arrayOfThings.length;\n  let i, j, jlen, thing, nestedThing;\n  for (i = 0; i < ilen; i++) {\n    thing = arrayOfThings[i];\n    // Undefined strings and arrays should not be measured\n    if (thing !== undefined && thing !== null && !isArray(thing)) {\n      longest = _measureText(ctx, data, gc, longest, thing);\n    } else if (isArray(thing)) {\n      // if it is an array lets measure each element\n      // to do maybe simplify this function a bit so we can do this more recursively?\n      for (j = 0, jlen = thing.length; j < jlen; j++) {\n        nestedThing = thing[j];\n        // Undefined strings and arrays should not be measured\n        if (nestedThing !== undefined && nestedThing !== null && !isArray(nestedThing)) {\n          longest = _measureText(ctx, data, gc, longest, nestedThing);\n        }\n      }\n    }\n  }\n  ctx.restore();\n  const gcLen = gc.length / 2;\n  if (gcLen > arrayOfThings.length) {\n    for (i = 0; i < gcLen; i++) {\n      delete data[gc[i]];\n    }\n    gc.splice(0, gcLen);\n  }\n  return longest;\n}\n/**\n * Returns the aligned pixel value to avoid anti-aliasing blur\n * @param chart - The chart instance.\n * @param pixel - A pixel value.\n * @param width - The width of the element.\n * @returns The aligned pixel value.\n * @private\n */\nfunction _alignPixel(chart, pixel, width) {\n  const devicePixelRatio = chart.currentDevicePixelRatio;\n  const halfWidth = width !== 0 ? Math.max(width / 2, 0.5) : 0;\n  return Math.round((pixel - halfWidth) * devicePixelRatio) / devicePixelRatio + halfWidth;\n}\n/**\n * Clears the entire canvas.\n */\nfunction clearCanvas(canvas, ctx) {\n  ctx = ctx || canvas.getContext('2d');\n  ctx.save();\n  // canvas.width and canvas.height do not consider the canvas transform,\n  // while clearRect does\n  ctx.resetTransform();\n  ctx.clearRect(0, 0, canvas.width, canvas.height);\n  ctx.restore();\n}\nfunction drawPoint(ctx, options, x, y) {\n  // eslint-disable-next-line @typescript-eslint/no-use-before-define\n  drawPointLegend(ctx, options, x, y, null);\n}\n// eslint-disable-next-line complexity\nfunction drawPointLegend(ctx, options, x, y, w) {\n  let type, xOffset, yOffset, size, cornerRadius, width, xOffsetW, yOffsetW;\n  const style = options.pointStyle;\n  const rotation = options.rotation;\n  const radius = options.radius;\n  let rad = (rotation || 0) * RAD_PER_DEG;\n  if (style && typeof style === 'object') {\n    type = style.toString();\n    if (type === '[object HTMLImageElement]' || type === '[object HTMLCanvasElement]') {\n      ctx.save();\n      ctx.translate(x, y);\n      ctx.rotate(rad);\n      ctx.drawImage(style, -style.width / 2, -style.height / 2, style.width, style.height);\n      ctx.restore();\n      return;\n    }\n  }\n  if (isNaN(radius) || radius <= 0) {\n    return;\n  }\n  ctx.beginPath();\n  switch (style) {\n    // Default includes circle\n    default:\n      if (w) {\n        ctx.ellipse(x, y, w / 2, radius, 0, 0, TAU);\n      } else {\n        ctx.arc(x, y, radius, 0, TAU);\n      }\n      ctx.closePath();\n      break;\n    case 'triangle':\n      width = w ? w / 2 : radius;\n      ctx.moveTo(x + Math.sin(rad) * width, y - Math.cos(rad) * radius);\n      rad += TWO_THIRDS_PI;\n      ctx.lineTo(x + Math.sin(rad) * width, y - Math.cos(rad) * radius);\n      rad += TWO_THIRDS_PI;\n      ctx.lineTo(x + Math.sin(rad) * width, y - Math.cos(rad) * radius);\n      ctx.closePath();\n      break;\n    case 'rectRounded':\n      // NOTE: the rounded rect implementation changed to use `arc` instead of\n      // `quadraticCurveTo` since it generates better results when rect is\n      // almost a circle. 0.516 (instead of 0.5) produces results with visually\n      // closer proportion to the previous impl and it is inscribed in the\n      // circle with `radius`. For more details, see the following PRs:\n      // https://github.com/chartjs/Chart.js/issues/5597\n      // https://github.com/chartjs/Chart.js/issues/5858\n      cornerRadius = radius * 0.516;\n      size = radius - cornerRadius;\n      xOffset = Math.cos(rad + QUARTER_PI) * size;\n      xOffsetW = Math.cos(rad + QUARTER_PI) * (w ? w / 2 - cornerRadius : size);\n      yOffset = Math.sin(rad + QUARTER_PI) * size;\n      yOffsetW = Math.sin(rad + QUARTER_PI) * (w ? w / 2 - cornerRadius : size);\n      ctx.arc(x - xOffsetW, y - yOffset, cornerRadius, rad - PI, rad - HALF_PI);\n      ctx.arc(x + yOffsetW, y - xOffset, cornerRadius, rad - HALF_PI, rad);\n      ctx.arc(x + xOffsetW, y + yOffset, cornerRadius, rad, rad + HALF_PI);\n      ctx.arc(x - yOffsetW, y + xOffset, cornerRadius, rad + HALF_PI, rad + PI);\n      ctx.closePath();\n      break;\n    case 'rect':\n      if (!rotation) {\n        size = Math.SQRT1_2 * radius;\n        width = w ? w / 2 : size;\n        ctx.rect(x - width, y - size, 2 * width, 2 * size);\n        break;\n      }\n      rad += QUARTER_PI;\n    /* falls through */\n    case 'rectRot':\n      xOffsetW = Math.cos(rad) * (w ? w / 2 : radius);\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      yOffsetW = Math.sin(rad) * (w ? w / 2 : radius);\n      ctx.moveTo(x - xOffsetW, y - yOffset);\n      ctx.lineTo(x + yOffsetW, y - xOffset);\n      ctx.lineTo(x + xOffsetW, y + yOffset);\n      ctx.lineTo(x - yOffsetW, y + xOffset);\n      ctx.closePath();\n      break;\n    case 'crossRot':\n      rad += QUARTER_PI;\n    /* falls through */\n    case 'cross':\n      xOffsetW = Math.cos(rad) * (w ? w / 2 : radius);\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      yOffsetW = Math.sin(rad) * (w ? w / 2 : radius);\n      ctx.moveTo(x - xOffsetW, y - yOffset);\n      ctx.lineTo(x + xOffsetW, y + yOffset);\n      ctx.moveTo(x + yOffsetW, y - xOffset);\n      ctx.lineTo(x - yOffsetW, y + xOffset);\n      break;\n    case 'star':\n      xOffsetW = Math.cos(rad) * (w ? w / 2 : radius);\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      yOffsetW = Math.sin(rad) * (w ? w / 2 : radius);\n      ctx.moveTo(x - xOffsetW, y - yOffset);\n      ctx.lineTo(x + xOffsetW, y + yOffset);\n      ctx.moveTo(x + yOffsetW, y - xOffset);\n      ctx.lineTo(x - yOffsetW, y + xOffset);\n      rad += QUARTER_PI;\n      xOffsetW = Math.cos(rad) * (w ? w / 2 : radius);\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      yOffsetW = Math.sin(rad) * (w ? w / 2 : radius);\n      ctx.moveTo(x - xOffsetW, y - yOffset);\n      ctx.lineTo(x + xOffsetW, y + yOffset);\n      ctx.moveTo(x + yOffsetW, y - xOffset);\n      ctx.lineTo(x - yOffsetW, y + xOffset);\n      break;\n    case 'line':\n      xOffset = w ? w / 2 : Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      ctx.moveTo(x - xOffset, y - yOffset);\n      ctx.lineTo(x + xOffset, y + yOffset);\n      break;\n    case 'dash':\n      ctx.moveTo(x, y);\n      ctx.lineTo(x + Math.cos(rad) * (w ? w / 2 : radius), y + Math.sin(rad) * radius);\n      break;\n    case false:\n      ctx.closePath();\n      break;\n  }\n  ctx.fill();\n  if (options.borderWidth > 0) {\n    ctx.stroke();\n  }\n}\n/**\n * Returns true if the point is inside the rectangle\n * @param point - The point to test\n * @param area - The rectangle\n * @param margin - allowed margin\n * @private\n */\nfunction _isPointInArea(point, area, margin) {\n  margin = margin || 0.5; // margin - default is to match rounded decimals\n  return !area || point && point.x > area.left - margin && point.x < area.right + margin && point.y > area.top - margin && point.y < area.bottom + margin;\n}\nfunction clipArea(ctx, area) {\n  ctx.save();\n  ctx.beginPath();\n  ctx.rect(area.left, area.top, area.right - area.left, area.bottom - area.top);\n  ctx.clip();\n}\nfunction unclipArea(ctx) {\n  ctx.restore();\n}\n/**\n * @private\n */\nfunction _steppedLineTo(ctx, previous, target, flip, mode) {\n  if (!previous) {\n    return ctx.lineTo(target.x, target.y);\n  }\n  if (mode === 'middle') {\n    const midpoint = (previous.x + target.x) / 2.0;\n    ctx.lineTo(midpoint, previous.y);\n    ctx.lineTo(midpoint, target.y);\n  } else if (mode === 'after' !== !!flip) {\n    ctx.lineTo(previous.x, target.y);\n  } else {\n    ctx.lineTo(target.x, previous.y);\n  }\n  ctx.lineTo(target.x, target.y);\n}\n/**\n * @private\n */\nfunction _bezierCurveTo(ctx, previous, target, flip) {\n  if (!previous) {\n    return ctx.lineTo(target.x, target.y);\n  }\n  ctx.bezierCurveTo(flip ? previous.cp1x : previous.cp2x, flip ? previous.cp1y : previous.cp2y, flip ? target.cp2x : target.cp1x, flip ? target.cp2y : target.cp1y, target.x, target.y);\n}\nfunction setRenderOpts(ctx, opts) {\n  if (opts.translation) {\n    ctx.translate(opts.translation[0], opts.translation[1]);\n  }\n  if (!isNullOrUndef(opts.rotation)) {\n    ctx.rotate(opts.rotation);\n  }\n  if (opts.color) {\n    ctx.fillStyle = opts.color;\n  }\n  if (opts.textAlign) {\n    ctx.textAlign = opts.textAlign;\n  }\n  if (opts.textBaseline) {\n    ctx.textBaseline = opts.textBaseline;\n  }\n}\nfunction decorateText(ctx, x, y, line, opts) {\n  if (opts.strikethrough || opts.underline) {\n    /**\n    * Now that IE11 support has been dropped, we can use more\n    * of the TextMetrics object. The actual bounding boxes\n    * are unflagged in Chrome, Firefox, Edge, and Safari so they\n    * can be safely used.\n    * See https://developer.mozilla.org/en-US/docs/Web/API/TextMetrics#Browser_compatibility\n    */\n    const metrics = ctx.measureText(line);\n    const left = x - metrics.actualBoundingBoxLeft;\n    const right = x + metrics.actualBoundingBoxRight;\n    const top = y - metrics.actualBoundingBoxAscent;\n    const bottom = y + metrics.actualBoundingBoxDescent;\n    const yDecoration = opts.strikethrough ? (top + bottom) / 2 : bottom;\n    ctx.strokeStyle = ctx.fillStyle;\n    ctx.beginPath();\n    ctx.lineWidth = opts.decorationWidth || 2;\n    ctx.moveTo(left, yDecoration);\n    ctx.lineTo(right, yDecoration);\n    ctx.stroke();\n  }\n}\nfunction drawBackdrop(ctx, opts) {\n  const oldColor = ctx.fillStyle;\n  ctx.fillStyle = opts.color;\n  ctx.fillRect(opts.left, opts.top, opts.width, opts.height);\n  ctx.fillStyle = oldColor;\n}\n/**\n * Render text onto the canvas\n */\nfunction renderText(ctx, text, x, y, font, opts = {}) {\n  const lines = isArray(text) ? text : [text];\n  const stroke = opts.strokeWidth > 0 && opts.strokeColor !== '';\n  let i, line;\n  ctx.save();\n  ctx.font = font.string;\n  setRenderOpts(ctx, opts);\n  for (i = 0; i < lines.length; ++i) {\n    line = lines[i];\n    if (opts.backdrop) {\n      drawBackdrop(ctx, opts.backdrop);\n    }\n    if (stroke) {\n      if (opts.strokeColor) {\n        ctx.strokeStyle = opts.strokeColor;\n      }\n      if (!isNullOrUndef(opts.strokeWidth)) {\n        ctx.lineWidth = opts.strokeWidth;\n      }\n      ctx.strokeText(line, x, y, opts.maxWidth);\n    }\n    ctx.fillText(line, x, y, opts.maxWidth);\n    decorateText(ctx, x, y, line, opts);\n    y += Number(font.lineHeight);\n  }\n  ctx.restore();\n}\n/**\n * Add a path of a rectangle with rounded corners to the current sub-path\n * @param ctx - Context\n * @param rect - Bounding rect\n */\nfunction addRoundedRectPath(ctx, rect) {\n  const {\n    x,\n    y,\n    w,\n    h,\n    radius\n  } = rect;\n  // top left arc\n  ctx.arc(x + radius.topLeft, y + radius.topLeft, radius.topLeft, 1.5 * PI, PI, true);\n  // line from top left to bottom left\n  ctx.lineTo(x, y + h - radius.bottomLeft);\n  // bottom left arc\n  ctx.arc(x + radius.bottomLeft, y + h - radius.bottomLeft, radius.bottomLeft, PI, HALF_PI, true);\n  // line from bottom left to bottom right\n  ctx.lineTo(x + w - radius.bottomRight, y + h);\n  // bottom right arc\n  ctx.arc(x + w - radius.bottomRight, y + h - radius.bottomRight, radius.bottomRight, HALF_PI, 0, true);\n  // line from bottom right to top right\n  ctx.lineTo(x + w, y + radius.topRight);\n  // top right arc\n  ctx.arc(x + w - radius.topRight, y + radius.topRight, radius.topRight, 0, -HALF_PI, true);\n  // line from top right to top left\n  ctx.lineTo(x + radius.topLeft, y);\n}\nconst LINE_HEIGHT = /^(normal|(\\d+(?:\\.\\d+)?)(px|em|%)?)$/;\nconst FONT_STYLE = /^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;\n/**\n * @alias Chart.helpers.options\n * @namespace\n */ /**\n    * Converts the given line height `value` in pixels for a specific font `size`.\n    * @param value - The lineHeight to parse (eg. 1.6, '14px', '75%', '1.6em').\n    * @param size - The font size (in pixels) used to resolve relative `value`.\n    * @returns The effective line height in pixels (size * 1.2 if value is invalid).\n    * @see https://developer.mozilla.org/en-US/docs/Web/CSS/line-height\n    * @since 2.7.0\n    */\nfunction toLineHeight(value, size) {\n  const matches = ('' + value).match(LINE_HEIGHT);\n  if (!matches || matches[1] === 'normal') {\n    return size * 1.2;\n  }\n  value = +matches[2];\n  switch (matches[3]) {\n    case 'px':\n      return value;\n    case '%':\n      value /= 100;\n      break;\n  }\n  return size * value;\n}\nconst numberOrZero = v => +v || 0;\nfunction _readValueToProps(value, props) {\n  const ret = {};\n  const objProps = isObject(props);\n  const keys = objProps ? Object.keys(props) : props;\n  const read = isObject(value) ? objProps ? prop => valueOrDefault(value[prop], value[props[prop]]) : prop => value[prop] : () => value;\n  for (const prop of keys) {\n    ret[prop] = numberOrZero(read(prop));\n  }\n  return ret;\n}\n/**\n * Converts the given value into a TRBL object.\n * @param value - If a number, set the value to all TRBL component,\n *  else, if an object, use defined properties and sets undefined ones to 0.\n *  x / y are shorthands for same value for left/right and top/bottom.\n * @returns The padding values (top, right, bottom, left)\n * @since 3.0.0\n */\nfunction toTRBL(value) {\n  return _readValueToProps(value, {\n    top: 'y',\n    right: 'x',\n    bottom: 'y',\n    left: 'x'\n  });\n}\n/**\n * Converts the given value into a TRBL corners object (similar with css border-radius).\n * @param value - If a number, set the value to all TRBL corner components,\n *  else, if an object, use defined properties and sets undefined ones to 0.\n * @returns The TRBL corner values (topLeft, topRight, bottomLeft, bottomRight)\n * @since 3.0.0\n */\nfunction toTRBLCorners(value) {\n  return _readValueToProps(value, ['topLeft', 'topRight', 'bottomLeft', 'bottomRight']);\n}\n/**\n * Converts the given value into a padding object with pre-computed width/height.\n * @param value - If a number, set the value to all TRBL component,\n *  else, if an object, use defined properties and sets undefined ones to 0.\n *  x / y are shorthands for same value for left/right and top/bottom.\n * @returns The padding values (top, right, bottom, left, width, height)\n * @since 2.7.0\n */\nfunction toPadding(value) {\n  const obj = toTRBL(value);\n  obj.width = obj.left + obj.right;\n  obj.height = obj.top + obj.bottom;\n  return obj;\n}\n/**\n * Parses font options and returns the font object.\n * @param options - A object that contains font options to be parsed.\n * @param fallback - A object that contains fallback font options.\n * @return The font object.\n * @private\n */\nfunction toFont(options, fallback) {\n  options = options || {};\n  fallback = fallback || defaults.font;\n  let size = valueOrDefault(options.size, fallback.size);\n  if (typeof size === 'string') {\n    size = parseInt(size, 10);\n  }\n  let style = valueOrDefault(options.style, fallback.style);\n  if (style && !('' + style).match(FONT_STYLE)) {\n    console.warn('Invalid font style specified: \"' + style + '\"');\n    style = undefined;\n  }\n  const font = {\n    family: valueOrDefault(options.family, fallback.family),\n    lineHeight: toLineHeight(valueOrDefault(options.lineHeight, fallback.lineHeight), size),\n    size,\n    style,\n    weight: valueOrDefault(options.weight, fallback.weight),\n    string: ''\n  };\n  font.string = toFontString(font);\n  return font;\n}\n/**\n * Evaluates the given `inputs` sequentially and returns the first defined value.\n * @param inputs - An array of values, falling back to the last value.\n * @param context - If defined and the current value is a function, the value\n * is called with `context` as first argument and the result becomes the new input.\n * @param index - If defined and the current value is an array, the value\n * at `index` become the new input.\n * @param info - object to return information about resolution in\n * @param info.cacheable - Will be set to `false` if option is not cacheable.\n * @since 2.7.0\n */\nfunction resolve(inputs, context, index, info) {\n  let cacheable = true;\n  let i, ilen, value;\n  for (i = 0, ilen = inputs.length; i < ilen; ++i) {\n    value = inputs[i];\n    if (value === undefined) {\n      continue;\n    }\n    if (context !== undefined && typeof value === 'function') {\n      value = value(context);\n      cacheable = false;\n    }\n    if (index !== undefined && isArray(value)) {\n      value = value[index % value.length];\n      cacheable = false;\n    }\n    if (value !== undefined) {\n      if (info && !cacheable) {\n        info.cacheable = false;\n      }\n      return value;\n    }\n  }\n}\n/**\n * @param minmax\n * @param grace\n * @param beginAtZero\n * @private\n */\nfunction _addGrace(minmax, grace, beginAtZero) {\n  const {\n    min,\n    max\n  } = minmax;\n  const change = toDimension(grace, (max - min) / 2);\n  const keepZero = (value, add) => beginAtZero && value === 0 ? 0 : value + add;\n  return {\n    min: keepZero(min, -Math.abs(change)),\n    max: keepZero(max, change)\n  };\n}\nfunction createContext(parentContext, context) {\n  return Object.assign(Object.create(parentContext), context);\n}\n\n/**\n * Creates a Proxy for resolving raw values for options.\n * @param scopes - The option scopes to look for values, in resolution order\n * @param prefixes - The prefixes for values, in resolution order.\n * @param rootScopes - The root option scopes\n * @param fallback - Parent scopes fallback\n * @param getTarget - callback for getting the target for changed values\n * @returns Proxy\n * @private\n */\nfunction _createResolver(scopes, prefixes = [''], rootScopes, fallback, getTarget = () => scopes[0]) {\n  const finalRootScopes = rootScopes || scopes;\n  if (typeof fallback === 'undefined') {\n    fallback = _resolve('_fallback', scopes);\n  }\n  const cache = {\n    [Symbol.toStringTag]: 'Object',\n    _cacheable: true,\n    _scopes: scopes,\n    _rootScopes: finalRootScopes,\n    _fallback: fallback,\n    _getTarget: getTarget,\n    override: scope => _createResolver([scope, ...scopes], prefixes, finalRootScopes, fallback)\n  };\n  return new Proxy(cache, {\n    /**\n    * A trap for the delete operator.\n    */\n    deleteProperty(target, prop) {\n      delete target[prop]; // remove from cache\n      delete target._keys; // remove cached keys\n      delete scopes[0][prop]; // remove from top level scope\n      return true;\n    },\n    /**\n    * A trap for getting property values.\n    */\n    get(target, prop) {\n      return _cached(target, prop, () => _resolveWithPrefixes(prop, prefixes, scopes, target));\n    },\n    /**\n    * A trap for Object.getOwnPropertyDescriptor.\n    * Also used by Object.hasOwnProperty.\n    */\n    getOwnPropertyDescriptor(target, prop) {\n      return Reflect.getOwnPropertyDescriptor(target._scopes[0], prop);\n    },\n    /**\n    * A trap for Object.getPrototypeOf.\n    */\n    getPrototypeOf() {\n      return Reflect.getPrototypeOf(scopes[0]);\n    },\n    /**\n    * A trap for the in operator.\n    */\n    has(target, prop) {\n      return getKeysFromAllScopes(target).includes(prop);\n    },\n    /**\n    * A trap for Object.getOwnPropertyNames and Object.getOwnPropertySymbols.\n    */\n    ownKeys(target) {\n      return getKeysFromAllScopes(target);\n    },\n    /**\n    * A trap for setting property values.\n    */\n    set(target, prop, value) {\n      const storage = target._storage || (target._storage = getTarget());\n      target[prop] = storage[prop] = value; // set to top level scope + cache\n      delete target._keys; // remove cached keys\n      return true;\n    }\n  });\n}\n/**\n * Returns an Proxy for resolving option values with context.\n * @param proxy - The Proxy returned by `_createResolver`\n * @param context - Context object for scriptable/indexable options\n * @param subProxy - The proxy provided for scriptable options\n * @param descriptorDefaults - Defaults for descriptors\n * @private\n */\nfunction _attachContext(proxy, context, subProxy, descriptorDefaults) {\n  const cache = {\n    _cacheable: false,\n    _proxy: proxy,\n    _context: context,\n    _subProxy: subProxy,\n    _stack: new Set(),\n    _descriptors: _descriptors(proxy, descriptorDefaults),\n    setContext: ctx => _attachContext(proxy, ctx, subProxy, descriptorDefaults),\n    override: scope => _attachContext(proxy.override(scope), context, subProxy, descriptorDefaults)\n  };\n  return new Proxy(cache, {\n    /**\n    * A trap for the delete operator.\n    */\n    deleteProperty(target, prop) {\n      delete target[prop]; // remove from cache\n      delete proxy[prop]; // remove from proxy\n      return true;\n    },\n    /**\n    * A trap for getting property values.\n    */\n    get(target, prop, receiver) {\n      return _cached(target, prop, () => _resolveWithContext(target, prop, receiver));\n    },\n    /**\n    * A trap for Object.getOwnPropertyDescriptor.\n    * Also used by Object.hasOwnProperty.\n    */\n    getOwnPropertyDescriptor(target, prop) {\n      return target._descriptors.allKeys ? Reflect.has(proxy, prop) ? {\n        enumerable: true,\n        configurable: true\n      } : undefined : Reflect.getOwnPropertyDescriptor(proxy, prop);\n    },\n    /**\n    * A trap for Object.getPrototypeOf.\n    */\n    getPrototypeOf() {\n      return Reflect.getPrototypeOf(proxy);\n    },\n    /**\n    * A trap for the in operator.\n    */\n    has(target, prop) {\n      return Reflect.has(proxy, prop);\n    },\n    /**\n    * A trap for Object.getOwnPropertyNames and Object.getOwnPropertySymbols.\n    */\n    ownKeys() {\n      return Reflect.ownKeys(proxy);\n    },\n    /**\n    * A trap for setting property values.\n    */\n    set(target, prop, value) {\n      proxy[prop] = value; // set to proxy\n      delete target[prop]; // remove from cache\n      return true;\n    }\n  });\n}\n/**\n * @private\n */\nfunction _descriptors(proxy, defaults = {\n  scriptable: true,\n  indexable: true\n}) {\n  const {\n    _scriptable = defaults.scriptable,\n    _indexable = defaults.indexable,\n    _allKeys = defaults.allKeys\n  } = proxy;\n  return {\n    allKeys: _allKeys,\n    scriptable: _scriptable,\n    indexable: _indexable,\n    isScriptable: isFunction(_scriptable) ? _scriptable : () => _scriptable,\n    isIndexable: isFunction(_indexable) ? _indexable : () => _indexable\n  };\n}\nconst readKey = (prefix, name) => prefix ? prefix + _capitalize(name) : name;\nconst needsSubResolver = (prop, value) => isObject(value) && prop !== 'adapters' && (Object.getPrototypeOf(value) === null || value.constructor === Object);\nfunction _cached(target, prop, resolve) {\n  if (Object.prototype.hasOwnProperty.call(target, prop)) {\n    return target[prop];\n  }\n  const value = resolve();\n  // cache the resolved value\n  target[prop] = value;\n  return value;\n}\nfunction _resolveWithContext(target, prop, receiver) {\n  const {\n    _proxy,\n    _context,\n    _subProxy,\n    _descriptors: descriptors\n  } = target;\n  let value = _proxy[prop]; // resolve from proxy\n  // resolve with context\n  if (isFunction(value) && descriptors.isScriptable(prop)) {\n    value = _resolveScriptable(prop, value, target, receiver);\n  }\n  if (isArray(value) && value.length) {\n    value = _resolveArray(prop, value, target, descriptors.isIndexable);\n  }\n  if (needsSubResolver(prop, value)) {\n    // if the resolved value is an object, create a sub resolver for it\n    value = _attachContext(value, _context, _subProxy && _subProxy[prop], descriptors);\n  }\n  return value;\n}\nfunction _resolveScriptable(prop, getValue, target, receiver) {\n  const {\n    _proxy,\n    _context,\n    _subProxy,\n    _stack\n  } = target;\n  if (_stack.has(prop)) {\n    throw new Error('Recursion detected: ' + Array.from(_stack).join('->') + '->' + prop);\n  }\n  _stack.add(prop);\n  let value = getValue(_context, _subProxy || receiver);\n  _stack.delete(prop);\n  if (needsSubResolver(prop, value)) {\n    // When scriptable option returns an object, create a resolver on that.\n    value = createSubResolver(_proxy._scopes, _proxy, prop, value);\n  }\n  return value;\n}\nfunction _resolveArray(prop, value, target, isIndexable) {\n  const {\n    _proxy,\n    _context,\n    _subProxy,\n    _descriptors: descriptors\n  } = target;\n  if (typeof _context.index !== 'undefined' && isIndexable(prop)) {\n    return value[_context.index % value.length];\n  } else if (isObject(value[0])) {\n    // Array of objects, return array or resolvers\n    const arr = value;\n    const scopes = _proxy._scopes.filter(s => s !== arr);\n    value = [];\n    for (const item of arr) {\n      const resolver = createSubResolver(scopes, _proxy, prop, item);\n      value.push(_attachContext(resolver, _context, _subProxy && _subProxy[prop], descriptors));\n    }\n  }\n  return value;\n}\nfunction resolveFallback(fallback, prop, value) {\n  return isFunction(fallback) ? fallback(prop, value) : fallback;\n}\nconst getScope = (key, parent) => key === true ? parent : typeof key === 'string' ? resolveObjectKey(parent, key) : undefined;\nfunction addScopes(set, parentScopes, key, parentFallback, value) {\n  for (const parent of parentScopes) {\n    const scope = getScope(key, parent);\n    if (scope) {\n      set.add(scope);\n      const fallback = resolveFallback(scope._fallback, key, value);\n      if (typeof fallback !== 'undefined' && fallback !== key && fallback !== parentFallback) {\n        // When we reach the descriptor that defines a new _fallback, return that.\n        // The fallback will resume to that new scope.\n        return fallback;\n      }\n    } else if (scope === false && typeof parentFallback !== 'undefined' && key !== parentFallback) {\n      // Fallback to `false` results to `false`, when falling back to different key.\n      // For example `interaction` from `hover` or `plugins.tooltip` and `animation` from `animations`\n      return null;\n    }\n  }\n  return false;\n}\nfunction createSubResolver(parentScopes, resolver, prop, value) {\n  const rootScopes = resolver._rootScopes;\n  const fallback = resolveFallback(resolver._fallback, prop, value);\n  const allScopes = [...parentScopes, ...rootScopes];\n  const set = new Set();\n  set.add(value);\n  let key = addScopesFromKey(set, allScopes, prop, fallback || prop, value);\n  if (key === null) {\n    return false;\n  }\n  if (typeof fallback !== 'undefined' && fallback !== prop) {\n    key = addScopesFromKey(set, allScopes, fallback, key, value);\n    if (key === null) {\n      return false;\n    }\n  }\n  return _createResolver(Array.from(set), [''], rootScopes, fallback, () => subGetTarget(resolver, prop, value));\n}\nfunction addScopesFromKey(set, allScopes, key, fallback, item) {\n  while (key) {\n    key = addScopes(set, allScopes, key, fallback, item);\n  }\n  return key;\n}\nfunction subGetTarget(resolver, prop, value) {\n  const parent = resolver._getTarget();\n  if (!(prop in parent)) {\n    parent[prop] = {};\n  }\n  const target = parent[prop];\n  if (isArray(target) && isObject(value)) {\n    // For array of objects, the object is used to store updated values\n    return value;\n  }\n  return target || {};\n}\nfunction _resolveWithPrefixes(prop, prefixes, scopes, proxy) {\n  let value;\n  for (const prefix of prefixes) {\n    value = _resolve(readKey(prefix, prop), scopes);\n    if (typeof value !== 'undefined') {\n      return needsSubResolver(prop, value) ? createSubResolver(scopes, proxy, prop, value) : value;\n    }\n  }\n}\nfunction _resolve(key, scopes) {\n  for (const scope of scopes) {\n    if (!scope) {\n      continue;\n    }\n    const value = scope[key];\n    if (typeof value !== 'undefined') {\n      return value;\n    }\n  }\n}\nfunction getKeysFromAllScopes(target) {\n  let keys = target._keys;\n  if (!keys) {\n    keys = target._keys = resolveKeysFromAllScopes(target._scopes);\n  }\n  return keys;\n}\nfunction resolveKeysFromAllScopes(scopes) {\n  const set = new Set();\n  for (const scope of scopes) {\n    for (const key of Object.keys(scope).filter(k => !k.startsWith('_'))) {\n      set.add(key);\n    }\n  }\n  return Array.from(set);\n}\nfunction _parseObjectDataRadialScale(meta, data, start, count) {\n  const {\n    iScale\n  } = meta;\n  const {\n    key = 'r'\n  } = this._parsing;\n  const parsed = new Array(count);\n  let i, ilen, index, item;\n  for (i = 0, ilen = count; i < ilen; ++i) {\n    index = i + start;\n    item = data[index];\n    parsed[i] = {\n      r: iScale.parse(resolveObjectKey(item, key), index)\n    };\n  }\n  return parsed;\n}\nconst EPSILON = Number.EPSILON || 1e-14;\nconst getPoint = (points, i) => i < points.length && !points[i].skip && points[i];\nconst getValueAxis = indexAxis => indexAxis === 'x' ? 'y' : 'x';\nfunction splineCurve(firstPoint, middlePoint, afterPoint, t) {\n  // Props to Rob Spencer at scaled innovation for his post on splining between points\n  // http://scaledinnovation.com/analytics/splines/aboutSplines.html\n  // This function must also respect \"skipped\" points\n  const previous = firstPoint.skip ? middlePoint : firstPoint;\n  const current = middlePoint;\n  const next = afterPoint.skip ? middlePoint : afterPoint;\n  const d01 = distanceBetweenPoints(current, previous);\n  const d12 = distanceBetweenPoints(next, current);\n  let s01 = d01 / (d01 + d12);\n  let s12 = d12 / (d01 + d12);\n  // If all points are the same, s01 & s02 will be inf\n  s01 = isNaN(s01) ? 0 : s01;\n  s12 = isNaN(s12) ? 0 : s12;\n  const fa = t * s01; // scaling factor for triangle Ta\n  const fb = t * s12;\n  return {\n    previous: {\n      x: current.x - fa * (next.x - previous.x),\n      y: current.y - fa * (next.y - previous.y)\n    },\n    next: {\n      x: current.x + fb * (next.x - previous.x),\n      y: current.y + fb * (next.y - previous.y)\n    }\n  };\n}\n/**\n * Adjust tangents to ensure monotonic properties\n */\nfunction monotoneAdjust(points, deltaK, mK) {\n  const pointsLen = points.length;\n  let alphaK, betaK, tauK, squaredMagnitude, pointCurrent;\n  let pointAfter = getPoint(points, 0);\n  for (let i = 0; i < pointsLen - 1; ++i) {\n    pointCurrent = pointAfter;\n    pointAfter = getPoint(points, i + 1);\n    if (!pointCurrent || !pointAfter) {\n      continue;\n    }\n    if (almostEquals(deltaK[i], 0, EPSILON)) {\n      mK[i] = mK[i + 1] = 0;\n      continue;\n    }\n    alphaK = mK[i] / deltaK[i];\n    betaK = mK[i + 1] / deltaK[i];\n    squaredMagnitude = Math.pow(alphaK, 2) + Math.pow(betaK, 2);\n    if (squaredMagnitude <= 9) {\n      continue;\n    }\n    tauK = 3 / Math.sqrt(squaredMagnitude);\n    mK[i] = alphaK * tauK * deltaK[i];\n    mK[i + 1] = betaK * tauK * deltaK[i];\n  }\n}\nfunction monotoneCompute(points, mK, indexAxis = 'x') {\n  const valueAxis = getValueAxis(indexAxis);\n  const pointsLen = points.length;\n  let delta, pointBefore, pointCurrent;\n  let pointAfter = getPoint(points, 0);\n  for (let i = 0; i < pointsLen; ++i) {\n    pointBefore = pointCurrent;\n    pointCurrent = pointAfter;\n    pointAfter = getPoint(points, i + 1);\n    if (!pointCurrent) {\n      continue;\n    }\n    const iPixel = pointCurrent[indexAxis];\n    const vPixel = pointCurrent[valueAxis];\n    if (pointBefore) {\n      delta = (iPixel - pointBefore[indexAxis]) / 3;\n      pointCurrent[`cp1${indexAxis}`] = iPixel - delta;\n      pointCurrent[`cp1${valueAxis}`] = vPixel - delta * mK[i];\n    }\n    if (pointAfter) {\n      delta = (pointAfter[indexAxis] - iPixel) / 3;\n      pointCurrent[`cp2${indexAxis}`] = iPixel + delta;\n      pointCurrent[`cp2${valueAxis}`] = vPixel + delta * mK[i];\n    }\n  }\n}\n/**\n * This function calculates Bézier control points in a similar way than |splineCurve|,\n * but preserves monotonicity of the provided data and ensures no local extremums are added\n * between the dataset discrete points due to the interpolation.\n * See : https://en.wikipedia.org/wiki/Monotone_cubic_interpolation\n */\nfunction splineCurveMonotone(points, indexAxis = 'x') {\n  const valueAxis = getValueAxis(indexAxis);\n  const pointsLen = points.length;\n  const deltaK = Array(pointsLen).fill(0);\n  const mK = Array(pointsLen);\n  // Calculate slopes (deltaK) and initialize tangents (mK)\n  let i, pointBefore, pointCurrent;\n  let pointAfter = getPoint(points, 0);\n  for (i = 0; i < pointsLen; ++i) {\n    pointBefore = pointCurrent;\n    pointCurrent = pointAfter;\n    pointAfter = getPoint(points, i + 1);\n    if (!pointCurrent) {\n      continue;\n    }\n    if (pointAfter) {\n      const slopeDelta = pointAfter[indexAxis] - pointCurrent[indexAxis];\n      // In the case of two points that appear at the same x pixel, slopeDeltaX is 0\n      deltaK[i] = slopeDelta !== 0 ? (pointAfter[valueAxis] - pointCurrent[valueAxis]) / slopeDelta : 0;\n    }\n    mK[i] = !pointBefore ? deltaK[i] : !pointAfter ? deltaK[i - 1] : sign(deltaK[i - 1]) !== sign(deltaK[i]) ? 0 : (deltaK[i - 1] + deltaK[i]) / 2;\n  }\n  monotoneAdjust(points, deltaK, mK);\n  monotoneCompute(points, mK, indexAxis);\n}\nfunction capControlPoint(pt, min, max) {\n  return Math.max(Math.min(pt, max), min);\n}\nfunction capBezierPoints(points, area) {\n  let i, ilen, point, inArea, inAreaPrev;\n  let inAreaNext = _isPointInArea(points[0], area);\n  for (i = 0, ilen = points.length; i < ilen; ++i) {\n    inAreaPrev = inArea;\n    inArea = inAreaNext;\n    inAreaNext = i < ilen - 1 && _isPointInArea(points[i + 1], area);\n    if (!inArea) {\n      continue;\n    }\n    point = points[i];\n    if (inAreaPrev) {\n      point.cp1x = capControlPoint(point.cp1x, area.left, area.right);\n      point.cp1y = capControlPoint(point.cp1y, area.top, area.bottom);\n    }\n    if (inAreaNext) {\n      point.cp2x = capControlPoint(point.cp2x, area.left, area.right);\n      point.cp2y = capControlPoint(point.cp2y, area.top, area.bottom);\n    }\n  }\n}\n/**\n * @private\n */\nfunction _updateBezierControlPoints(points, options, area, loop, indexAxis) {\n  let i, ilen, point, controlPoints;\n  // Only consider points that are drawn in case the spanGaps option is used\n  if (options.spanGaps) {\n    points = points.filter(pt => !pt.skip);\n  }\n  if (options.cubicInterpolationMode === 'monotone') {\n    splineCurveMonotone(points, indexAxis);\n  } else {\n    let prev = loop ? points[points.length - 1] : points[0];\n    for (i = 0, ilen = points.length; i < ilen; ++i) {\n      point = points[i];\n      controlPoints = splineCurve(prev, point, points[Math.min(i + 1, ilen - (loop ? 0 : 1)) % ilen], options.tension);\n      point.cp1x = controlPoints.previous.x;\n      point.cp1y = controlPoints.previous.y;\n      point.cp2x = controlPoints.next.x;\n      point.cp2y = controlPoints.next.y;\n      prev = point;\n    }\n  }\n  if (options.capBezierPoints) {\n    capBezierPoints(points, area);\n  }\n}\n\n/**\n * Note: typedefs are auto-exported, so use a made-up `dom` namespace where\n * necessary to avoid duplicates with `export * from './helpers`; see\n * https://github.com/microsoft/TypeScript/issues/46011\n * @typedef { import('../core/core.controller.js').default } dom.Chart\n * @typedef { import('../../types').ChartEvent } ChartEvent\n */ /**\n    * @private\n    */\nfunction _isDomSupported() {\n  return typeof window !== 'undefined' && typeof document !== 'undefined';\n}\n/**\n * @private\n */\nfunction _getParentNode(domNode) {\n  let parent = domNode.parentNode;\n  if (parent && parent.toString() === '[object ShadowRoot]') {\n    parent = parent.host;\n  }\n  return parent;\n}\n/**\n * convert max-width/max-height values that may be percentages into a number\n * @private\n */\nfunction parseMaxStyle(styleValue, node, parentProperty) {\n  let valueInPixels;\n  if (typeof styleValue === 'string') {\n    valueInPixels = parseInt(styleValue, 10);\n    if (styleValue.indexOf('%') !== -1) {\n      // percentage * size in dimension\n      valueInPixels = valueInPixels / 100 * node.parentNode[parentProperty];\n    }\n  } else {\n    valueInPixels = styleValue;\n  }\n  return valueInPixels;\n}\nconst getComputedStyle = element => element.ownerDocument.defaultView.getComputedStyle(element, null);\nfunction getStyle(el, property) {\n  return getComputedStyle(el).getPropertyValue(property);\n}\nconst positions = ['top', 'right', 'bottom', 'left'];\nfunction getPositionedStyle(styles, style, suffix) {\n  const result = {};\n  suffix = suffix ? '-' + suffix : '';\n  for (let i = 0; i < 4; i++) {\n    const pos = positions[i];\n    result[pos] = parseFloat(styles[style + '-' + pos + suffix]) || 0;\n  }\n  result.width = result.left + result.right;\n  result.height = result.top + result.bottom;\n  return result;\n}\nconst useOffsetPos = (x, y, target) => (x > 0 || y > 0) && (!target || !target.shadowRoot);\n/**\n * @param e\n * @param canvas\n * @returns Canvas position\n */\nfunction getCanvasPosition(e, canvas) {\n  const touches = e.touches;\n  const source = touches && touches.length ? touches[0] : e;\n  const {\n    offsetX,\n    offsetY\n  } = source;\n  let box = false;\n  let x, y;\n  if (useOffsetPos(offsetX, offsetY, e.target)) {\n    x = offsetX;\n    y = offsetY;\n  } else {\n    const rect = canvas.getBoundingClientRect();\n    x = source.clientX - rect.left;\n    y = source.clientY - rect.top;\n    box = true;\n  }\n  return {\n    x,\n    y,\n    box\n  };\n}\n/**\n * Gets an event's x, y coordinates, relative to the chart area\n * @param event\n * @param chart\n * @returns x and y coordinates of the event\n */\nfunction getRelativePosition(event, chart) {\n  if ('native' in event) {\n    return event;\n  }\n  const {\n    canvas,\n    currentDevicePixelRatio\n  } = chart;\n  const style = getComputedStyle(canvas);\n  const borderBox = style.boxSizing === 'border-box';\n  const paddings = getPositionedStyle(style, 'padding');\n  const borders = getPositionedStyle(style, 'border', 'width');\n  const {\n    x,\n    y,\n    box\n  } = getCanvasPosition(event, canvas);\n  const xOffset = paddings.left + (box && borders.left);\n  const yOffset = paddings.top + (box && borders.top);\n  let {\n    width,\n    height\n  } = chart;\n  if (borderBox) {\n    width -= paddings.width + borders.width;\n    height -= paddings.height + borders.height;\n  }\n  return {\n    x: Math.round((x - xOffset) / width * canvas.width / currentDevicePixelRatio),\n    y: Math.round((y - yOffset) / height * canvas.height / currentDevicePixelRatio)\n  };\n}\nfunction getContainerSize(canvas, width, height) {\n  let maxWidth, maxHeight;\n  if (width === undefined || height === undefined) {\n    const container = _getParentNode(canvas);\n    if (!container) {\n      width = canvas.clientWidth;\n      height = canvas.clientHeight;\n    } else {\n      const rect = container.getBoundingClientRect(); // this is the border box of the container\n      const containerStyle = getComputedStyle(container);\n      const containerBorder = getPositionedStyle(containerStyle, 'border', 'width');\n      const containerPadding = getPositionedStyle(containerStyle, 'padding');\n      width = rect.width - containerPadding.width - containerBorder.width;\n      height = rect.height - containerPadding.height - containerBorder.height;\n      maxWidth = parseMaxStyle(containerStyle.maxWidth, container, 'clientWidth');\n      maxHeight = parseMaxStyle(containerStyle.maxHeight, container, 'clientHeight');\n    }\n  }\n  return {\n    width,\n    height,\n    maxWidth: maxWidth || INFINITY,\n    maxHeight: maxHeight || INFINITY\n  };\n}\nconst round1 = v => Math.round(v * 10) / 10;\n// eslint-disable-next-line complexity\nfunction getMaximumSize(canvas, bbWidth, bbHeight, aspectRatio) {\n  const style = getComputedStyle(canvas);\n  const margins = getPositionedStyle(style, 'margin');\n  const maxWidth = parseMaxStyle(style.maxWidth, canvas, 'clientWidth') || INFINITY;\n  const maxHeight = parseMaxStyle(style.maxHeight, canvas, 'clientHeight') || INFINITY;\n  const containerSize = getContainerSize(canvas, bbWidth, bbHeight);\n  let {\n    width,\n    height\n  } = containerSize;\n  if (style.boxSizing === 'content-box') {\n    const borders = getPositionedStyle(style, 'border', 'width');\n    const paddings = getPositionedStyle(style, 'padding');\n    width -= paddings.width + borders.width;\n    height -= paddings.height + borders.height;\n  }\n  width = Math.max(0, width - margins.width);\n  height = Math.max(0, aspectRatio ? width / aspectRatio : height - margins.height);\n  width = round1(Math.min(width, maxWidth, containerSize.maxWidth));\n  height = round1(Math.min(height, maxHeight, containerSize.maxHeight));\n  if (width && !height) {\n    // https://github.com/chartjs/Chart.js/issues/4659\n    // If the canvas has width, but no height, default to aspectRatio of 2 (canvas default)\n    height = round1(width / 2);\n  }\n  const maintainHeight = bbWidth !== undefined || bbHeight !== undefined;\n  if (maintainHeight && aspectRatio && containerSize.height && height > containerSize.height) {\n    height = containerSize.height;\n    width = round1(Math.floor(height * aspectRatio));\n  }\n  return {\n    width,\n    height\n  };\n}\n/**\n * @param chart\n * @param forceRatio\n * @param forceStyle\n * @returns True if the canvas context size or transformation has changed.\n */\nfunction retinaScale(chart, forceRatio, forceStyle) {\n  const pixelRatio = forceRatio || 1;\n  const deviceHeight = Math.floor(chart.height * pixelRatio);\n  const deviceWidth = Math.floor(chart.width * pixelRatio);\n  chart.height = Math.floor(chart.height);\n  chart.width = Math.floor(chart.width);\n  const canvas = chart.canvas;\n  // If no style has been set on the canvas, the render size is used as display size,\n  // making the chart visually bigger, so let's enforce it to the \"correct\" values.\n  // See https://github.com/chartjs/Chart.js/issues/3575\n  if (canvas.style && (forceStyle || !canvas.style.height && !canvas.style.width)) {\n    canvas.style.height = `${chart.height}px`;\n    canvas.style.width = `${chart.width}px`;\n  }\n  if (chart.currentDevicePixelRatio !== pixelRatio || canvas.height !== deviceHeight || canvas.width !== deviceWidth) {\n    chart.currentDevicePixelRatio = pixelRatio;\n    canvas.height = deviceHeight;\n    canvas.width = deviceWidth;\n    chart.ctx.setTransform(pixelRatio, 0, 0, pixelRatio, 0, 0);\n    return true;\n  }\n  return false;\n}\n/**\n * Detects support for options object argument in addEventListener.\n * https://developer.mozilla.org/en-US/docs/Web/API/EventTarget/addEventListener#Safely_detecting_option_support\n * @private\n */\nconst supportsEventListenerOptions = function () {\n  let passiveSupported = false;\n  try {\n    const options = {\n      get passive() {\n        passiveSupported = true;\n        return false;\n      }\n    };\n    window.addEventListener('test', null, options);\n    window.removeEventListener('test', null, options);\n  } catch (e) {\n    // continue regardless of error\n  }\n  return passiveSupported;\n}();\n/**\n * The \"used\" size is the final value of a dimension property after all calculations have\n * been performed. This method uses the computed style of `element` but returns undefined\n * if the computed style is not expressed in pixels. That can happen in some cases where\n * `element` has a size relative to its parent and this last one is not yet displayed,\n * for example because of `display: none` on a parent node.\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/used_value\n * @returns Size in pixels or undefined if unknown.\n */\nfunction readUsedSize(element, property) {\n  const value = getStyle(element, property);\n  const matches = value && value.match(/^(\\d+)(\\.\\d+)?px$/);\n  return matches ? +matches[1] : undefined;\n}\n\n/**\n * @private\n */\nfunction _pointInLine(p1, p2, t, mode) {\n  return {\n    x: p1.x + t * (p2.x - p1.x),\n    y: p1.y + t * (p2.y - p1.y)\n  };\n}\n/**\n * @private\n */\nfunction _steppedInterpolation(p1, p2, t, mode) {\n  return {\n    x: p1.x + t * (p2.x - p1.x),\n    y: mode === 'middle' ? t < 0.5 ? p1.y : p2.y : mode === 'after' ? t < 1 ? p1.y : p2.y : t > 0 ? p2.y : p1.y\n  };\n}\n/**\n * @private\n */\nfunction _bezierInterpolation(p1, p2, t, mode) {\n  const cp1 = {\n    x: p1.cp2x,\n    y: p1.cp2y\n  };\n  const cp2 = {\n    x: p2.cp1x,\n    y: p2.cp1y\n  };\n  const a = _pointInLine(p1, cp1, t);\n  const b = _pointInLine(cp1, cp2, t);\n  const c = _pointInLine(cp2, p2, t);\n  const d = _pointInLine(a, b, t);\n  const e = _pointInLine(b, c, t);\n  return _pointInLine(d, e, t);\n}\nconst getRightToLeftAdapter = function (rectX, width) {\n  return {\n    x(x) {\n      return rectX + rectX + width - x;\n    },\n    setWidth(w) {\n      width = w;\n    },\n    textAlign(align) {\n      if (align === 'center') {\n        return align;\n      }\n      return align === 'right' ? 'left' : 'right';\n    },\n    xPlus(x, value) {\n      return x - value;\n    },\n    leftForLtr(x, itemWidth) {\n      return x - itemWidth;\n    }\n  };\n};\nconst getLeftToRightAdapter = function () {\n  return {\n    x(x) {\n      return x;\n    },\n    setWidth(w) {},\n    textAlign(align) {\n      return align;\n    },\n    xPlus(x, value) {\n      return x + value;\n    },\n    leftForLtr(x, _itemWidth) {\n      return x;\n    }\n  };\n};\nfunction getRtlAdapter(rtl, rectX, width) {\n  return rtl ? getRightToLeftAdapter(rectX, width) : getLeftToRightAdapter();\n}\nfunction overrideTextDirection(ctx, direction) {\n  let style, original;\n  if (direction === 'ltr' || direction === 'rtl') {\n    style = ctx.canvas.style;\n    original = [style.getPropertyValue('direction'), style.getPropertyPriority('direction')];\n    style.setProperty('direction', direction, 'important');\n    ctx.prevTextDirection = original;\n  }\n}\nfunction restoreTextDirection(ctx, original) {\n  if (original !== undefined) {\n    delete ctx.prevTextDirection;\n    ctx.canvas.style.setProperty('direction', original[0], original[1]);\n  }\n}\nfunction propertyFn(property) {\n  if (property === 'angle') {\n    return {\n      between: _angleBetween,\n      compare: _angleDiff,\n      normalize: _normalizeAngle\n    };\n  }\n  return {\n    between: _isBetween,\n    compare: (a, b) => a - b,\n    normalize: x => x\n  };\n}\nfunction normalizeSegment({\n  start,\n  end,\n  count,\n  loop,\n  style\n}) {\n  return {\n    start: start % count,\n    end: end % count,\n    loop: loop && (end - start + 1) % count === 0,\n    style\n  };\n}\nfunction getSegment(segment, points, bounds) {\n  const {\n    property,\n    start: startBound,\n    end: endBound\n  } = bounds;\n  const {\n    between,\n    normalize\n  } = propertyFn(property);\n  const count = points.length;\n  let {\n    start,\n    end,\n    loop\n  } = segment;\n  let i, ilen;\n  if (loop) {\n    start += count;\n    end += count;\n    for (i = 0, ilen = count; i < ilen; ++i) {\n      if (!between(normalize(points[start % count][property]), startBound, endBound)) {\n        break;\n      }\n      start--;\n      end--;\n    }\n    start %= count;\n    end %= count;\n  }\n  if (end < start) {\n    end += count;\n  }\n  return {\n    start,\n    end,\n    loop,\n    style: segment.style\n  };\n}\nfunction _boundSegment(segment, points, bounds) {\n  if (!bounds) {\n    return [segment];\n  }\n  const {\n    property,\n    start: startBound,\n    end: endBound\n  } = bounds;\n  const count = points.length;\n  const {\n    compare,\n    between,\n    normalize\n  } = propertyFn(property);\n  const {\n    start,\n    end,\n    loop,\n    style\n  } = getSegment(segment, points, bounds);\n  const result = [];\n  let inside = false;\n  let subStart = null;\n  let value, point, prevValue;\n  const startIsBefore = () => between(startBound, prevValue, value) && compare(startBound, prevValue) !== 0;\n  const endIsBefore = () => compare(endBound, value) === 0 || between(endBound, prevValue, value);\n  const shouldStart = () => inside || startIsBefore();\n  const shouldStop = () => !inside || endIsBefore();\n  for (let i = start, prev = start; i <= end; ++i) {\n    point = points[i % count];\n    if (point.skip) {\n      continue;\n    }\n    value = normalize(point[property]);\n    if (value === prevValue) {\n      continue;\n    }\n    inside = between(value, startBound, endBound);\n    if (subStart === null && shouldStart()) {\n      subStart = compare(value, startBound) === 0 ? i : prev;\n    }\n    if (subStart !== null && shouldStop()) {\n      result.push(normalizeSegment({\n        start: subStart,\n        end: i,\n        loop,\n        count,\n        style\n      }));\n      subStart = null;\n    }\n    prev = i;\n    prevValue = value;\n  }\n  if (subStart !== null) {\n    result.push(normalizeSegment({\n      start: subStart,\n      end,\n      loop,\n      count,\n      style\n    }));\n  }\n  return result;\n}\nfunction _boundSegments(line, bounds) {\n  const result = [];\n  const segments = line.segments;\n  for (let i = 0; i < segments.length; i++) {\n    const sub = _boundSegment(segments[i], line.points, bounds);\n    if (sub.length) {\n      result.push(...sub);\n    }\n  }\n  return result;\n}\nfunction findStartAndEnd(points, count, loop, spanGaps) {\n  let start = 0;\n  let end = count - 1;\n  if (loop && !spanGaps) {\n    while (start < count && !points[start].skip) {\n      start++;\n    }\n  }\n  while (start < count && points[start].skip) {\n    start++;\n  }\n  start %= count;\n  if (loop) {\n    end += start;\n  }\n  while (end > start && points[end % count].skip) {\n    end--;\n  }\n  end %= count;\n  return {\n    start,\n    end\n  };\n}\nfunction solidSegments(points, start, max, loop) {\n  const count = points.length;\n  const result = [];\n  let last = start;\n  let prev = points[start];\n  let end;\n  for (end = start + 1; end <= max; ++end) {\n    const cur = points[end % count];\n    if (cur.skip || cur.stop) {\n      if (!prev.skip) {\n        loop = false;\n        result.push({\n          start: start % count,\n          end: (end - 1) % count,\n          loop\n        });\n        start = last = cur.stop ? end : null;\n      }\n    } else {\n      last = end;\n      if (prev.skip) {\n        start = end;\n      }\n    }\n    prev = cur;\n  }\n  if (last !== null) {\n    result.push({\n      start: start % count,\n      end: last % count,\n      loop\n    });\n  }\n  return result;\n}\nfunction _computeSegments(line, segmentOptions) {\n  const points = line.points;\n  const spanGaps = line.options.spanGaps;\n  const count = points.length;\n  if (!count) {\n    return [];\n  }\n  const loop = !!line._loop;\n  const {\n    start,\n    end\n  } = findStartAndEnd(points, count, loop, spanGaps);\n  if (spanGaps === true) {\n    return splitByStyles(line, [{\n      start,\n      end,\n      loop\n    }], points, segmentOptions);\n  }\n  const max = end < start ? end + count : end;\n  const completeLoop = !!line._fullLoop && start === 0 && end === count - 1;\n  return splitByStyles(line, solidSegments(points, start, max, completeLoop), points, segmentOptions);\n}\nfunction splitByStyles(line, segments, points, segmentOptions) {\n  if (!segmentOptions || !segmentOptions.setContext || !points) {\n    return segments;\n  }\n  return doSplitByStyles(line, segments, points, segmentOptions);\n}\nfunction doSplitByStyles(line, segments, points, segmentOptions) {\n  const chartContext = line._chart.getContext();\n  const baseStyle = readStyle(line.options);\n  const {\n    _datasetIndex: datasetIndex,\n    options: {\n      spanGaps\n    }\n  } = line;\n  const count = points.length;\n  const result = [];\n  let prevStyle = baseStyle;\n  let start = segments[0].start;\n  let i = start;\n  function addStyle(s, e, l, st) {\n    const dir = spanGaps ? -1 : 1;\n    if (s === e) {\n      return;\n    }\n    s += count;\n    while (points[s % count].skip) {\n      s -= dir;\n    }\n    while (points[e % count].skip) {\n      e += dir;\n    }\n    if (s % count !== e % count) {\n      result.push({\n        start: s % count,\n        end: e % count,\n        loop: l,\n        style: st\n      });\n      prevStyle = st;\n      start = e % count;\n    }\n  }\n  for (const segment of segments) {\n    start = spanGaps ? start : segment.start;\n    let prev = points[start % count];\n    let style;\n    for (i = start + 1; i <= segment.end; i++) {\n      const pt = points[i % count];\n      style = readStyle(segmentOptions.setContext(createContext(chartContext, {\n        type: 'segment',\n        p0: prev,\n        p1: pt,\n        p0DataIndex: (i - 1) % count,\n        p1DataIndex: i % count,\n        datasetIndex\n      })));\n      if (styleChanged(style, prevStyle)) {\n        addStyle(start, i - 1, segment.loop, prevStyle);\n      }\n      prev = pt;\n      prevStyle = style;\n    }\n    if (start < i - 1) {\n      addStyle(start, i - 1, segment.loop, prevStyle);\n    }\n  }\n  return result;\n}\nfunction readStyle(options) {\n  return {\n    backgroundColor: options.backgroundColor,\n    borderCapStyle: options.borderCapStyle,\n    borderDash: options.borderDash,\n    borderDashOffset: options.borderDashOffset,\n    borderJoinStyle: options.borderJoinStyle,\n    borderWidth: options.borderWidth,\n    borderColor: options.borderColor\n  };\n}\nfunction styleChanged(style, prevStyle) {\n  if (!prevStyle) {\n    return false;\n  }\n  const cache = [];\n  const replacer = function (key, value) {\n    if (!isPatternOrGradient(value)) {\n      return value;\n    }\n    if (!cache.includes(value)) {\n      cache.push(value);\n    }\n    return cache.indexOf(value);\n  };\n  return JSON.stringify(style, replacer) !== JSON.stringify(prevStyle, replacer);\n}\nexport { unclipArea as $, _rlookupByKey as A, _lookupByKey as B, _isPointInArea as C, getAngleFromPoint as D, toPadding as E, each as F, getMaximumSize as G, HALF_PI as H, _getParentNode as I, readUsedSize as J, supportsEventListenerOptions as K, throttled as L, _isDomSupported as M, _factorize as N, finiteOrDefault as O, PI as P, callback as Q, _addGrace as R, _limitValue as S, TAU as T, toDegrees as U, _measureText as V, _int16Range as W, _alignPixel as X, clipArea as Y, renderText as Z, _arrayUnique as _, resolve as a, fontString as a$, toFont as a0, _toLeftRightCenter as a1, _alignStartEnd as a2, overrides as a3, merge as a4, _capitalize as a5, descriptors as a6, isFunction as a7, _attachContext as a8, _createResolver as a9, overrideTextDirection as aA, _textX as aB, restoreTextDirection as aC, drawPointLegend as aD, distanceBetweenPoints as aE, noop as aF, _setMinAndMaxByKey as aG, niceNum as aH, almostWhole as aI, almostEquals as aJ, _decimalPlaces as aK, Ticks as aL, log10 as aM, _longestText as aN, _filterBetween as aO, _lookup as aP, isPatternOrGradient as aQ, getHoverColor as aR, clone as aS, _merger as aT, _mergerIf as aU, _deprecated as aV, _splitKey as aW, toFontString as aX, splineCurve as aY, splineCurveMonotone as aZ, getStyle as a_, _descriptors as aa, mergeIf as ab, uid as ac, debounce as ad, retinaScale as ae, clearCanvas as af, setsEqual as ag, _elementsEqual as ah, _isClickEvent as ai, _isBetween as aj, _readValueToProps as ak, _updateBezierControlPoints as al, _computeSegments as am, _boundSegments as an, _steppedInterpolation as ao, _bezierInterpolation as ap, _pointInLine as aq, _steppedLineTo as ar, _bezierCurveTo as as, drawPoint as at, addRoundedRectPath as au, toTRBL as av, toTRBLCorners as aw, _boundSegment as ax, _normalizeAngle as ay, getRtlAdapter as az, isArray as b, toLineHeight as b0, PITAU as b1, INFINITY as b2, RAD_PER_DEG as b3, QUARTER_PI as b4, TWO_THIRDS_PI as b5, _angleDiff as b6, color as c, defaults as d, effects as e, resolveObjectKey as f, isNumberFinite as g, defined as h, isObject as i, createContext as j, isNullOrUndef as k, listenArrayEvents as l, toPercentage as m, toDimension as n, formatNumber as o, _angleBetween as p, _getStartAndCountOfVisiblePoints as q, requestAnimFrame as r, sign as s, toRadians as t, unlistenArrayEvents as u, valueOrDefault as v, _scaleRangesChanged as w, isNumber as x, _parseObjectDataRadialScale as y, getRelativePosition as z };", "map": {"version": 3, "names": ["noop", "uid", "id", "isNullOrUndef", "value", "isArray", "Array", "type", "Object", "prototype", "toString", "call", "slice", "isObject", "isNumberFinite", "Number", "isFinite", "finiteOrDefault", "defaultValue", "valueOrDefault", "toPercentage", "dimension", "endsWith", "parseFloat", "toDimension", "callback", "fn", "args", "thisArg", "apply", "each", "loopable", "reverse", "i", "len", "keys", "length", "_elementsEqual", "a0", "a1", "ilen", "v0", "v1", "datasetIndex", "index", "clone", "source", "map", "target", "create", "klen", "k", "is<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "indexOf", "_merger", "options", "tval", "sval", "merge", "sources", "merger", "current", "mergeIf", "_mergerIf", "hasOwnProperty", "_deprecated", "scope", "previous", "undefined", "console", "warn", "keyResolvers", "v", "x", "o", "y", "_splitKey", "parts", "split", "tmp", "part", "push", "_getKeyResolver", "obj", "resolveObjectKey", "resolver", "_capitalize", "str", "char<PERSON>t", "toUpperCase", "defined", "isFunction", "setsEqual", "a", "b", "size", "item", "has", "_isClickEvent", "e", "PI", "Math", "TAU", "PITAU", "INFINITY", "POSITIVE_INFINITY", "RAD_PER_DEG", "HALF_PI", "QUARTER_PI", "TWO_THIRDS_PI", "log10", "sign", "almostEquals", "epsilon", "abs", "niceNum", "range", "roundedRange", "round", "niceRange", "pow", "floor", "fraction", "niceFraction", "_factorize", "result", "sqrt", "sort", "pop", "isNumber", "n", "isNaN", "almostWhole", "rounded", "_setMinAndMaxByKey", "array", "property", "min", "max", "toRadians", "degrees", "toDegrees", "radians", "_decimalPlaces", "p", "getAngleFromPoint", "centrePoint", "anglePoint", "distanceFromXCenter", "distanceFromYCenter", "radialDistanceFromCenter", "angle", "atan2", "distance", "distanceBetweenPoints", "pt1", "pt2", "_angleDiff", "_normalizeAngle", "_angleBetween", "start", "end", "sameAngleIsFullCircle", "s", "angleToStart", "angleToEnd", "startToAngle", "endToAngle", "_limitValue", "_int16Range", "_isBetween", "_lookup", "table", "cmp", "hi", "lo", "mid", "_lookup<PERSON><PERSON><PERSON><PERSON>", "last", "ti", "_rlookupByKey", "_filterBetween", "values", "arrayEvents", "listenArrayEvents", "listener", "_chartjs", "listeners", "defineProperty", "configurable", "enumerable", "for<PERSON>ach", "method", "base", "res", "object", "unlistenArrayEvents", "stub", "splice", "_arrayUnique", "items", "set", "Set", "from", "fontString", "pixelSize", "fontStyle", "fontFamily", "requestAnimFrame", "window", "requestAnimationFrame", "throttled", "argsToUse", "ticking", "debounce", "delay", "timeout", "clearTimeout", "setTimeout", "_toLeftRightCenter", "align", "_alignStartEnd", "_textX", "left", "right", "rtl", "check", "_getStartAndCountOfVisiblePoints", "meta", "points", "animationsDisabled", "pointCount", "count", "_sorted", "iScale", "_parsed", "axis", "minDefined", "maxDefined", "getUserBounds", "getPixelForValue", "_scaleRangesChanged", "xScale", "yScale", "_scaleRanges", "newRang<PERSON>", "xmin", "xmax", "ymin", "ymax", "changed", "assign", "atEdge", "t", "elasticIn", "sin", "elasticOut", "effects", "linear", "easeInQuad", "easeOutQuad", "easeInOutQuad", "easeInCubic", "easeOutCubic", "easeInOutCubic", "easeInQuart", "easeOutQuart", "easeInOutQuart", "easeInQuint", "easeOutQuint", "easeInOutQuint", "easeInSine", "cos", "easeOutSine", "easeInOutSine", "easeInExpo", "easeOutExpo", "easeInOutExpo", "easeInCirc", "easeOutCirc", "easeInOutCirc", "easeInElastic", "easeOutElastic", "easeInOutElastic", "easeInBack", "easeOutBack", "easeInOutBack", "easeInBounce", "easeOutBounce", "m", "d", "easeInOutBounce", "isPatternOrGradient", "color", "Color", "getHoverColor", "saturate", "darken", "hexString", "numbers", "colors", "applyAnimationsDefaults", "defaults", "duration", "easing", "loop", "to", "describe", "_fallback", "_indexable", "_scriptable", "name", "properties", "active", "animation", "resize", "show", "animations", "visible", "hide", "applyLayoutsDefaults", "autoPadding", "padding", "top", "bottom", "intlCache", "Map", "getNumberFormat", "locale", "cache<PERSON>ey", "JSON", "stringify", "formatter", "get", "Intl", "NumberFormat", "formatNumber", "num", "format", "formatters", "numeric", "tickValue", "ticks", "chart", "notation", "delta", "maxTick", "calculateDelta", "log<PERSON><PERSON><PERSON>", "numDecimal", "minimumFractionDigits", "maximumFractionDigits", "logarithmic", "remain", "significand", "includes", "Ticks", "applyScaleDefaults", "display", "offset", "beginAtZero", "bounds", "clip", "grace", "grid", "lineWidth", "drawOnChartArea", "drawTicks", "tick<PERSON><PERSON>th", "tickWidth", "_ctx", "tickColor", "border", "dash", "dashOffset", "width", "title", "text", "minRotation", "maxRotation", "mirror", "textStrokeWidth", "textStrokeColor", "autoSkip", "autoSkipPadding", "labelOffset", "minor", "major", "crossAlign", "showLabelBackdrop", "backdropColor", "backdropPadding", "route", "startsWith", "overrides", "descriptors", "getScope$1", "node", "root", "De<PERSON>ults", "constructor", "_descriptors", "_appliers", "backgroundColor", "borderColor", "datasets", "devicePixelRatio", "context", "platform", "getDevicePixelRatio", "elements", "events", "font", "family", "style", "lineHeight", "weight", "hover", "hoverBackgroundColor", "ctx", "hoverBorderColor", "hoverColor", "indexAxis", "interaction", "mode", "intersect", "includeInvisible", "maintainAspectRatio", "onHover", "onClick", "parsing", "plugins", "responsive", "scale", "scales", "showLine", "drawActiveElementsOnTop", "override", "targetScope", "targetName", "scopeObject", "targetScopeObject", "privateName", "defineProperties", "writable", "local", "appliers", "toFontString", "_measureText", "data", "gc", "longest", "string", "textWidth", "measureText", "_longestText", "arrayOfThings", "cache", "garbageCollect", "save", "j", "jlen", "thing", "nestedThing", "restore", "gcLen", "_alignPixel", "pixel", "currentDevicePixelRatio", "halfWidth", "clearCanvas", "canvas", "getContext", "resetTransform", "clearRect", "height", "drawPoint", "drawPointLegend", "w", "xOffset", "yOffset", "cornerRadius", "xOffsetW", "yOffsetW", "pointStyle", "rotation", "radius", "rad", "translate", "rotate", "drawImage", "beginPath", "ellipse", "arc", "closePath", "moveTo", "lineTo", "SQRT1_2", "rect", "fill", "borderWidth", "stroke", "_isPointInArea", "point", "area", "margin", "clipArea", "unclipArea", "_steppedLineTo", "flip", "midpoint", "_bezierCurveTo", "bezierCurveTo", "cp1x", "cp2x", "cp1y", "cp2y", "setRenderOpts", "opts", "translation", "fillStyle", "textAlign", "textBaseline", "decorateText", "line", "strikethrough", "underline", "metrics", "actualBoundingBoxLeft", "actualBoundingBoxRight", "actualBoundingBoxAscent", "actualBoundingBoxDescent", "yDecoration", "strokeStyle", "decorationWidth", "drawBackdrop", "oldColor", "fillRect", "renderText", "lines", "strokeWidth", "strokeColor", "backdrop", "strokeText", "max<PERSON><PERSON><PERSON>", "fillText", "addRoundedRectPath", "h", "topLeft", "bottomLeft", "bottomRight", "topRight", "LINE_HEIGHT", "FONT_STYLE", "toLineHeight", "matches", "match", "numberOrZero", "_readValueToProps", "props", "ret", "objProps", "read", "prop", "toTRBL", "toTRBLCorners", "toPadding", "toFont", "fallback", "parseInt", "resolve", "inputs", "info", "cacheable", "_addGrace", "minmax", "change", "keepZero", "add", "createContext", "parentContext", "_createResolver", "scopes", "prefixes", "rootScopes", "get<PERSON><PERSON><PERSON>", "finalRootScopes", "_resolve", "Symbol", "toStringTag", "_cacheable", "_scopes", "_rootScopes", "_getTarget", "Proxy", "deleteProperty", "_keys", "_cached", "_resolveWithPrefixes", "getOwnPropertyDescriptor", "Reflect", "getPrototypeOf", "getKeysFromAllScopes", "ownKeys", "storage", "_storage", "_attachContext", "proxy", "subProxy", "descriptor<PERSON><PERSON><PERSON><PERSON>", "_proxy", "_context", "_subProxy", "_stack", "setContext", "receiver", "_resolveWithContext", "allKeys", "scriptable", "indexable", "_allKeys", "isScriptable", "isIndexable", "read<PERSON><PERSON>", "prefix", "needsSubResolver", "_resolveScriptable", "_resolveArray", "getValue", "Error", "join", "delete", "createSubResolver", "arr", "filter", "<PERSON><PERSON><PERSON><PERSON>", "getScope", "parent", "addScopes", "parentScopes", "parentFallback", "allScopes", "addScopesFromKey", "subGetTarget", "resolveKeysFromAllScopes", "_parseObjectDataRadialScale", "_parsing", "parsed", "r", "parse", "EPSILON", "getPoint", "skip", "getValueAxis", "splineCurve", "firstPoint", "middlePoint", "afterPoint", "next", "d01", "d12", "s01", "s12", "fa", "fb", "monotoneAdjust", "deltaK", "mK", "pointsLen", "alphaK", "betaK", "tauK", "squaredMagnitude", "pointCurrent", "pointAfter", "monotoneCompute", "valueAxis", "pointBefore", "iPixel", "vPixel", "splineCurveMonotone", "slopeDel<PERSON>", "capControlPoint", "pt", "capBezierPoints", "inArea", "inAreaPrev", "inAreaNext", "_updateBezierControlPoints", "controlPoints", "spanGaps", "cubicInterpolationMode", "prev", "tension", "_isDomSupported", "document", "_getParentNode", "domNode", "parentNode", "host", "parseMaxStyle", "styleValue", "parentProperty", "valueInPixels", "getComputedStyle", "element", "ownerDocument", "defaultView", "getStyle", "el", "getPropertyValue", "positions", "getPositionedStyle", "styles", "suffix", "pos", "useOffsetPos", "shadowRoot", "getCanvasPosition", "touches", "offsetX", "offsetY", "box", "getBoundingClientRect", "clientX", "clientY", "getRelativePosition", "event", "borderBox", "boxSizing", "paddings", "borders", "getContainerSize", "maxHeight", "container", "clientWidth", "clientHeight", "containerStyle", "containerBorder", "containerPadding", "round1", "getMaximumSize", "bb<PERSON><PERSON><PERSON>", "bbHeight", "aspectRatio", "margins", "containerSize", "maintainHeight", "retinaScale", "forceRatio", "forceStyle", "pixelRatio", "deviceHeight", "deviceWidth", "setTransform", "supportsEventListenerOptions", "passiveSupported", "passive", "addEventListener", "removeEventListener", "readUsedSize", "_pointInLine", "p1", "p2", "_steppedInterpolation", "_bezierInterpolation", "cp1", "cp2", "c", "getRightToLeftAdapter", "rectX", "<PERSON><PERSON><PERSON><PERSON>", "xPlus", "leftForLtr", "itemWidth", "getLeftToRightAdapter", "_itemWidth", "getRtlAdapter", "overrideTextDirection", "direction", "original", "getPropertyPriority", "setProperty", "prevTextDirection", "restoreTextDirection", "propertyFn", "between", "compare", "normalize", "normalizeSegment", "getSegment", "segment", "startBound", "endBound", "_boundSegment", "inside", "subStart", "prevValue", "startIsBefore", "endIsBefore", "shouldStart", "shouldStop", "_boundSegments", "segments", "sub", "findStartAndEnd", "solidSegments", "cur", "stop", "_computeSegments", "segmentOptions", "_loop", "splitByStyles", "completeLoop", "_fullLoop", "doSplitByStyles", "chartContext", "_chart", "baseStyle", "readStyle", "_datasetIndex", "prevStyle", "addStyle", "l", "st", "dir", "p0", "p0DataIndex", "p1DataIndex", "styleChanged", "borderCapStyle", "borderDash", "borderDashOffset", "borderJoinStyle", "replacer"], "sources": ["C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\chart.js\\src\\helpers\\helpers.core.ts", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\chart.js\\src\\helpers\\helpers.math.ts", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\chart.js\\src\\helpers\\helpers.collection.ts", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\chart.js\\src\\helpers\\helpers.extras.ts", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\chart.js\\src\\helpers\\helpers.easing.ts", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\chart.js\\src\\helpers\\helpers.color.ts", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\chart.js\\src\\core\\core.animations.defaults.js", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\chart.js\\src\\core\\core.layouts.defaults.js", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\chart.js\\src\\helpers\\helpers.intl.ts", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\chart.js\\src\\core\\core.ticks.js", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\chart.js\\src\\core\\core.scale.defaults.js", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\chart.js\\src\\core\\core.defaults.js", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\chart.js\\src\\helpers\\helpers.canvas.ts", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\chart.js\\src\\helpers\\helpers.options.ts", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\chart.js\\src\\helpers\\helpers.config.ts", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\chart.js\\src\\helpers\\helpers.curve.ts", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\chart.js\\src\\helpers\\helpers.dom.ts", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\chart.js\\src\\helpers\\helpers.interpolation.ts", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\chart.js\\src\\helpers\\helpers.rtl.ts", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\chart.js\\src\\helpers\\helpers.segment.js"], "sourcesContent": ["/**\n * @namespace Chart.helpers\n */\n\nimport type {AnyObject} from '../types/basic.js';\nimport type {ActiveDataPoint, ChartEvent} from '../types/index.js';\n\n/**\n * An empty function that can be used, for example, for optional callback.\n */\nexport function noop() {\n  /* noop */\n}\n\n/**\n * Returns a unique id, sequentially generated from a global variable.\n */\nexport const uid = (() => {\n  let id = 0;\n  return () => id++;\n})();\n\n/**\n * Returns true if `value` is neither null nor undefined, else returns false.\n * @param value - The value to test.\n * @since 2.7.0\n */\nexport function isNullOrUndef(value: unknown): value is null | undefined {\n  return value === null || typeof value === 'undefined';\n}\n\n/**\n * Returns true if `value` is an array (including typed arrays), else returns false.\n * @param value - The value to test.\n * @function\n */\nexport function isArray<T = unknown>(value: unknown): value is T[] {\n  if (Array.isArray && Array.isArray(value)) {\n    return true;\n  }\n  const type = Object.prototype.toString.call(value);\n  if (type.slice(0, 7) === '[object' && type.slice(-6) === 'Array]') {\n    return true;\n  }\n  return false;\n}\n\n/**\n * Returns true if `value` is an object (excluding null), else returns false.\n * @param value - The value to test.\n * @since 2.7.0\n */\nexport function isObject(value: unknown): value is AnyObject {\n  return value !== null && Object.prototype.toString.call(value) === '[object Object]';\n}\n\n/**\n * Returns true if `value` is a finite number, else returns false\n * @param value  - The value to test.\n */\nfunction isNumberFinite(value: unknown): value is number {\n  return (typeof value === 'number' || value instanceof Number) && isFinite(+value);\n}\nexport {\n  isNumberFinite as isFinite,\n};\n\n/**\n * Returns `value` if finite, else returns `defaultValue`.\n * @param value - The value to return if defined.\n * @param defaultValue - The value to return if `value` is not finite.\n */\nexport function finiteOrDefault(value: unknown, defaultValue: number) {\n  return isNumberFinite(value) ? value : defaultValue;\n}\n\n/**\n * Returns `value` if defined, else returns `defaultValue`.\n * @param value - The value to return if defined.\n * @param defaultValue - The value to return if `value` is undefined.\n */\nexport function valueOrDefault<T>(value: T | undefined, defaultValue: T) {\n  return typeof value === 'undefined' ? defaultValue : value;\n}\n\nexport const toPercentage = (value: number | string, dimension: number) =>\n  typeof value === 'string' && value.endsWith('%') ?\n    parseFloat(value) / 100\n    : +value / dimension;\n\nexport const toDimension = (value: number | string, dimension: number) =>\n  typeof value === 'string' && value.endsWith('%') ?\n    parseFloat(value) / 100 * dimension\n    : +value;\n\n/**\n * Calls `fn` with the given `args` in the scope defined by `thisArg` and returns the\n * value returned by `fn`. If `fn` is not a function, this method returns undefined.\n * @param fn - The function to call.\n * @param args - The arguments with which `fn` should be called.\n * @param [thisArg] - The value of `this` provided for the call to `fn`.\n */\nexport function callback<T extends (this: TA, ...restArgs: unknown[]) => R, TA, R>(\n  fn: T | undefined,\n  args: unknown[],\n  thisArg?: TA\n): R | undefined {\n  if (fn && typeof fn.call === 'function') {\n    return fn.apply(thisArg, args);\n  }\n}\n\n/**\n * Note(SB) for performance sake, this method should only be used when loopable type\n * is unknown or in none intensive code (not called often and small loopable). Else\n * it's preferable to use a regular for() loop and save extra function calls.\n * @param loopable - The object or array to be iterated.\n * @param fn - The function to call for each item.\n * @param [thisArg] - The value of `this` provided for the call to `fn`.\n * @param [reverse] - If true, iterates backward on the loopable.\n */\nexport function each<T, TA>(\n  loopable: Record<string, T>,\n  fn: (this: TA, v: T, i: string) => void,\n  thisArg?: TA,\n  reverse?: boolean\n): void;\nexport function each<T, TA>(\n  loopable: T[],\n  fn: (this: TA, v: T, i: number) => void,\n  thisArg?: TA,\n  reverse?: boolean\n): void;\nexport function each<T, TA>(\n  loopable: T[] | Record<string, T>,\n  fn: (this: TA, v: T, i: any) => void,\n  thisArg?: TA,\n  reverse?: boolean\n) {\n  let i: number, len: number, keys: string[];\n  if (isArray(loopable)) {\n    len = loopable.length;\n    if (reverse) {\n      for (i = len - 1; i >= 0; i--) {\n        fn.call(thisArg, loopable[i], i);\n      }\n    } else {\n      for (i = 0; i < len; i++) {\n        fn.call(thisArg, loopable[i], i);\n      }\n    }\n  } else if (isObject(loopable)) {\n    keys = Object.keys(loopable);\n    len = keys.length;\n    for (i = 0; i < len; i++) {\n      fn.call(thisArg, loopable[keys[i]], keys[i]);\n    }\n  }\n}\n\n/**\n * Returns true if the `a0` and `a1` arrays have the same content, else returns false.\n * @param a0 - The array to compare\n * @param a1 - The array to compare\n * @private\n */\nexport function _elementsEqual(a0: ActiveDataPoint[], a1: ActiveDataPoint[]) {\n  let i: number, ilen: number, v0: ActiveDataPoint, v1: ActiveDataPoint;\n\n  if (!a0 || !a1 || a0.length !== a1.length) {\n    return false;\n  }\n\n  for (i = 0, ilen = a0.length; i < ilen; ++i) {\n    v0 = a0[i];\n    v1 = a1[i];\n\n    if (v0.datasetIndex !== v1.datasetIndex || v0.index !== v1.index) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\n/**\n * Returns a deep copy of `source` without keeping references on objects and arrays.\n * @param source - The value to clone.\n */\nexport function clone<T>(source: T): T {\n  if (isArray(source)) {\n    return source.map(clone) as unknown as T;\n  }\n\n  if (isObject(source)) {\n    const target = Object.create(null);\n    const keys = Object.keys(source);\n    const klen = keys.length;\n    let k = 0;\n\n    for (; k < klen; ++k) {\n      target[keys[k]] = clone(source[keys[k]]);\n    }\n\n    return target;\n  }\n\n  return source;\n}\n\nfunction isValidKey(key: string) {\n  return ['__proto__', 'prototype', 'constructor'].indexOf(key) === -1;\n}\n\n/**\n * The default merger when Chart.helpers.merge is called without merger option.\n * Note(SB): also used by mergeConfig and mergeScaleConfig as fallback.\n * @private\n */\nexport function _merger(key: string, target: AnyObject, source: AnyObject, options: AnyObject) {\n  if (!isValidKey(key)) {\n    return;\n  }\n\n  const tval = target[key];\n  const sval = source[key];\n\n  if (isObject(tval) && isObject(sval)) {\n    // eslint-disable-next-line @typescript-eslint/no-use-before-define\n    merge(tval, sval, options);\n  } else {\n    target[key] = clone(sval);\n  }\n}\n\nexport interface MergeOptions {\n  merger?: (key: string, target: AnyObject, source: AnyObject, options?: AnyObject) => void;\n}\n\n/**\n * Recursively deep copies `source` properties into `target` with the given `options`.\n * IMPORTANT: `target` is not cloned and will be updated with `source` properties.\n * @param target - The target object in which all sources are merged into.\n * @param source - Object(s) to merge into `target`.\n * @param [options] - Merging options:\n * @param [options.merger] - The merge method (key, target, source, options)\n * @returns The `target` object.\n */\nexport function merge<T>(target: T, source: [], options?: MergeOptions): T;\nexport function merge<T, S1>(target: T, source: S1, options?: MergeOptions): T & S1;\nexport function merge<T, S1>(target: T, source: [S1], options?: MergeOptions): T & S1;\nexport function merge<T, S1, S2>(target: T, source: [S1, S2], options?: MergeOptions): T & S1 & S2;\nexport function merge<T, S1, S2, S3>(target: T, source: [S1, S2, S3], options?: MergeOptions): T & S1 & S2 & S3;\nexport function merge<T, S1, S2, S3, S4>(\n  target: T,\n  source: [S1, S2, S3, S4],\n  options?: MergeOptions\n): T & S1 & S2 & S3 & S4;\nexport function merge<T>(target: T, source: AnyObject[], options?: MergeOptions): AnyObject;\nexport function merge<T>(target: T, source: AnyObject[], options?: MergeOptions): AnyObject {\n  const sources = isArray(source) ? source : [source];\n  const ilen = sources.length;\n\n  if (!isObject(target)) {\n    return target as AnyObject;\n  }\n\n  options = options || {};\n  const merger = options.merger || _merger;\n  let current: AnyObject;\n\n  for (let i = 0; i < ilen; ++i) {\n    current = sources[i];\n    if (!isObject(current)) {\n      continue;\n    }\n\n    const keys = Object.keys(current);\n    for (let k = 0, klen = keys.length; k < klen; ++k) {\n      merger(keys[k], target, current, options as AnyObject);\n    }\n  }\n\n  return target;\n}\n\n/**\n * Recursively deep copies `source` properties into `target` *only* if not defined in target.\n * IMPORTANT: `target` is not cloned and will be updated with `source` properties.\n * @param target - The target object in which all sources are merged into.\n * @param source - Object(s) to merge into `target`.\n * @returns The `target` object.\n */\nexport function mergeIf<T>(target: T, source: []): T;\nexport function mergeIf<T, S1>(target: T, source: S1): T & S1;\nexport function mergeIf<T, S1>(target: T, source: [S1]): T & S1;\nexport function mergeIf<T, S1, S2>(target: T, source: [S1, S2]): T & S1 & S2;\nexport function mergeIf<T, S1, S2, S3>(target: T, source: [S1, S2, S3]): T & S1 & S2 & S3;\nexport function mergeIf<T, S1, S2, S3, S4>(target: T, source: [S1, S2, S3, S4]): T & S1 & S2 & S3 & S4;\nexport function mergeIf<T>(target: T, source: AnyObject[]): AnyObject;\nexport function mergeIf<T>(target: T, source: AnyObject[]): AnyObject {\n  // eslint-disable-next-line @typescript-eslint/no-use-before-define\n  return merge<T>(target, source, {merger: _mergerIf});\n}\n\n/**\n * Merges source[key] in target[key] only if target[key] is undefined.\n * @private\n */\nexport function _mergerIf(key: string, target: AnyObject, source: AnyObject) {\n  if (!isValidKey(key)) {\n    return;\n  }\n\n  const tval = target[key];\n  const sval = source[key];\n\n  if (isObject(tval) && isObject(sval)) {\n    mergeIf(tval, sval);\n  } else if (!Object.prototype.hasOwnProperty.call(target, key)) {\n    target[key] = clone(sval);\n  }\n}\n\n/**\n * @private\n */\nexport function _deprecated(scope: string, value: unknown, previous: string, current: string) {\n  if (value !== undefined) {\n    console.warn(scope + ': \"' + previous +\n      '\" is deprecated. Please use \"' + current + '\" instead');\n  }\n}\n\n// resolveObjectKey resolver cache\nconst keyResolvers = {\n  // Chart.helpers.core resolveObjectKey should resolve empty key to root object\n  '': v => v,\n  // default resolvers\n  x: o => o.x,\n  y: o => o.y\n};\n\n/**\n * @private\n */\nexport function _splitKey(key: string) {\n  const parts = key.split('.');\n  const keys: string[] = [];\n  let tmp = '';\n  for (const part of parts) {\n    tmp += part;\n    if (tmp.endsWith('\\\\')) {\n      tmp = tmp.slice(0, -1) + '.';\n    } else {\n      keys.push(tmp);\n      tmp = '';\n    }\n  }\n  return keys;\n}\n\nfunction _getKeyResolver(key: string) {\n  const keys = _splitKey(key);\n  return obj => {\n    for (const k of keys) {\n      if (k === '') {\n        // For backward compatibility:\n        // Chart.helpers.core resolveObjectKey should break at empty key\n        break;\n      }\n      obj = obj && obj[k];\n    }\n    return obj;\n  };\n}\n\nexport function resolveObjectKey(obj: AnyObject, key: string): any {\n  const resolver = keyResolvers[key] || (keyResolvers[key] = _getKeyResolver(key));\n  return resolver(obj);\n}\n\n/**\n * @private\n */\nexport function _capitalize(str: string) {\n  return str.charAt(0).toUpperCase() + str.slice(1);\n}\n\n\nexport const defined = (value: unknown) => typeof value !== 'undefined';\n\nexport const isFunction = (value: unknown): value is (...args: any[]) => any => typeof value === 'function';\n\n// Adapted from https://stackoverflow.com/questions/31128855/comparing-ecma6-sets-for-equality#31129384\nexport const setsEqual = <T>(a: Set<T>, b: Set<T>) => {\n  if (a.size !== b.size) {\n    return false;\n  }\n\n  for (const item of a) {\n    if (!b.has(item)) {\n      return false;\n    }\n  }\n\n  return true;\n};\n\n/**\n * @param e - The event\n * @private\n */\nexport function _isClickEvent(e: ChartEvent) {\n  return e.type === 'mouseup' || e.type === 'click' || e.type === 'contextmenu';\n}\n", "import type {Point} from '../types/geometric.js';\nimport {isFinite as isFiniteNumber} from './helpers.core.js';\n\n/**\n * @alias Chart.helpers.math\n * @namespace\n */\n\nexport const PI = Math.PI;\nexport const TAU = 2 * PI;\nexport const PITAU = TAU + PI;\nexport const INFINITY = Number.POSITIVE_INFINITY;\nexport const RAD_PER_DEG = PI / 180;\nexport const HALF_PI = PI / 2;\nexport const QUARTER_PI = PI / 4;\nexport const TWO_THIRDS_PI = PI * 2 / 3;\n\nexport const log10 = Math.log10;\nexport const sign = Math.sign;\n\nexport function almostEquals(x: number, y: number, epsilon: number) {\n  return Math.abs(x - y) < epsilon;\n}\n\n/**\n * Implementation of the nice number algorithm used in determining where axis labels will go\n */\nexport function niceNum(range: number) {\n  const roundedRange = Math.round(range);\n  range = almostEquals(range, roundedRange, range / 1000) ? roundedRange : range;\n  const niceRange = Math.pow(10, Math.floor(log10(range)));\n  const fraction = range / niceRange;\n  const niceFraction = fraction <= 1 ? 1 : fraction <= 2 ? 2 : fraction <= 5 ? 5 : 10;\n  return niceFraction * niceRange;\n}\n\n/**\n * Returns an array of factors sorted from 1 to sqrt(value)\n * @private\n */\nexport function _factorize(value: number) {\n  const result: number[] = [];\n  const sqrt = Math.sqrt(value);\n  let i: number;\n\n  for (i = 1; i < sqrt; i++) {\n    if (value % i === 0) {\n      result.push(i);\n      result.push(value / i);\n    }\n  }\n  if (sqrt === (sqrt | 0)) { // if value is a square number\n    result.push(sqrt);\n  }\n\n  result.sort((a, b) => a - b).pop();\n  return result;\n}\n\nexport function isNumber(n: unknown): n is number {\n  return !isNaN(parseFloat(n as string)) && isFinite(n as number);\n}\n\nexport function almostWhole(x: number, epsilon: number) {\n  const rounded = Math.round(x);\n  return ((rounded - epsilon) <= x) && ((rounded + epsilon) >= x);\n}\n\n/**\n * @private\n */\nexport function _setMinAndMaxByKey(\n  array: Record<string, number>[],\n  target: { min: number, max: number },\n  property: string\n) {\n  let i: number, ilen: number, value: number;\n\n  for (i = 0, ilen = array.length; i < ilen; i++) {\n    value = array[i][property];\n    if (!isNaN(value)) {\n      target.min = Math.min(target.min, value);\n      target.max = Math.max(target.max, value);\n    }\n  }\n}\n\nexport function toRadians(degrees: number) {\n  return degrees * (PI / 180);\n}\n\nexport function toDegrees(radians: number) {\n  return radians * (180 / PI);\n}\n\n/**\n * Returns the number of decimal places\n * i.e. the number of digits after the decimal point, of the value of this Number.\n * @param x - A number.\n * @returns The number of decimal places.\n * @private\n */\nexport function _decimalPlaces(x: number) {\n  if (!isFiniteNumber(x)) {\n    return;\n  }\n  let e = 1;\n  let p = 0;\n  while (Math.round(x * e) / e !== x) {\n    e *= 10;\n    p++;\n  }\n  return p;\n}\n\n// Gets the angle from vertical upright to the point about a centre.\nexport function getAngleFromPoint(\n  centrePoint: Point,\n  anglePoint: Point\n) {\n  const distanceFromXCenter = anglePoint.x - centrePoint.x;\n  const distanceFromYCenter = anglePoint.y - centrePoint.y;\n  const radialDistanceFromCenter = Math.sqrt(distanceFromXCenter * distanceFromXCenter + distanceFromYCenter * distanceFromYCenter);\n\n  let angle = Math.atan2(distanceFromYCenter, distanceFromXCenter);\n\n  if (angle < (-0.5 * PI)) {\n    angle += TAU; // make sure the returned angle is in the range of (-PI/2, 3PI/2]\n  }\n\n  return {\n    angle,\n    distance: radialDistanceFromCenter\n  };\n}\n\nexport function distanceBetweenPoints(pt1: Point, pt2: Point) {\n  return Math.sqrt(Math.pow(pt2.x - pt1.x, 2) + Math.pow(pt2.y - pt1.y, 2));\n}\n\n/**\n * Shortest distance between angles, in either direction.\n * @private\n */\nexport function _angleDiff(a: number, b: number) {\n  return (a - b + PITAU) % TAU - PI;\n}\n\n/**\n * Normalize angle to be between 0 and 2*PI\n * @private\n */\nexport function _normalizeAngle(a: number) {\n  return (a % TAU + TAU) % TAU;\n}\n\n/**\n * @private\n */\nexport function _angleBetween(angle: number, start: number, end: number, sameAngleIsFullCircle?: boolean) {\n  const a = _normalizeAngle(angle);\n  const s = _normalizeAngle(start);\n  const e = _normalizeAngle(end);\n  const angleToStart = _normalizeAngle(s - a);\n  const angleToEnd = _normalizeAngle(e - a);\n  const startToAngle = _normalizeAngle(a - s);\n  const endToAngle = _normalizeAngle(a - e);\n  return a === s || a === e || (sameAngleIsFullCircle && s === e)\n    || (angleToStart > angleToEnd && startToAngle < endToAngle);\n}\n\n/**\n * Limit `value` between `min` and `max`\n * @param value\n * @param min\n * @param max\n * @private\n */\nexport function _limitValue(value: number, min: number, max: number) {\n  return Math.max(min, Math.min(max, value));\n}\n\n/**\n * @param {number} value\n * @private\n */\nexport function _int16Range(value: number) {\n  return _limitValue(value, -32768, 32767);\n}\n\n/**\n * @param value\n * @param start\n * @param end\n * @param [epsilon]\n * @private\n */\nexport function _isBetween(value: number, start: number, end: number, epsilon = 1e-6) {\n  return value >= Math.min(start, end) - epsilon && value <= Math.max(start, end) + epsilon;\n}\n", "import {_capitalize} from './helpers.core.js';\n\n/**\n * Binary search\n * @param table - the table search. must be sorted!\n * @param value - value to find\n * @param cmp\n * @private\n */\nexport function _lookup(\n  table: number[],\n  value: number,\n  cmp?: (value: number) => boolean\n): {lo: number, hi: number};\nexport function _lookup<T>(\n  table: T[],\n  value: number,\n  cmp: (value: number) => boolean\n): {lo: number, hi: number};\nexport function _lookup(\n  table: unknown[],\n  value: number,\n  cmp?: (value: number) => boolean\n) {\n  cmp = cmp || ((index) => table[index] < value);\n  let hi = table.length - 1;\n  let lo = 0;\n  let mid: number;\n\n  while (hi - lo > 1) {\n    mid = (lo + hi) >> 1;\n    if (cmp(mid)) {\n      lo = mid;\n    } else {\n      hi = mid;\n    }\n  }\n\n  return {lo, hi};\n}\n\n/**\n * Binary search\n * @param table - the table search. must be sorted!\n * @param key - property name for the value in each entry\n * @param value - value to find\n * @param last - lookup last index\n * @private\n */\nexport const _lookupByKey = (\n  table: Record<string, number>[],\n  key: string,\n  value: number,\n  last?: boolean\n) =>\n  _lookup(table, value, last\n    ? index => {\n      const ti = table[index][key];\n      return ti < value || ti === value && table[index + 1][key] === value;\n    }\n    : index => table[index][key] < value);\n\n/**\n * Reverse binary search\n * @param table - the table search. must be sorted!\n * @param key - property name for the value in each entry\n * @param value - value to find\n * @private\n */\nexport const _rlookupByKey = (\n  table: Record<string, number>[],\n  key: string,\n  value: number\n) =>\n  _lookup(table, value, index => table[index][key] >= value);\n\n/**\n * Return subset of `values` between `min` and `max` inclusive.\n * Values are assumed to be in sorted order.\n * @param values - sorted array of values\n * @param min - min value\n * @param max - max value\n */\nexport function _filterBetween(values: number[], min: number, max: number) {\n  let start = 0;\n  let end = values.length;\n\n  while (start < end && values[start] < min) {\n    start++;\n  }\n  while (end > start && values[end - 1] > max) {\n    end--;\n  }\n\n  return start > 0 || end < values.length\n    ? values.slice(start, end)\n    : values;\n}\n\nconst arrayEvents = ['push', 'pop', 'shift', 'splice', 'unshift'] as const;\n\nexport interface ArrayListener<T> {\n  _onDataPush?(...item: T[]): void;\n  _onDataPop?(): void;\n  _onDataShift?(): void;\n  _onDataSplice?(index: number, deleteCount: number, ...items: T[]): void;\n  _onDataUnshift?(...item: T[]): void;\n}\n\n/**\n * Hooks the array methods that add or remove values ('push', pop', 'shift', 'splice',\n * 'unshift') and notify the listener AFTER the array has been altered. Listeners are\n * called on the '_onData*' callbacks (e.g. _onDataPush, etc.) with same arguments.\n */\nexport function listenArrayEvents<T>(array: T[], listener: ArrayListener<T>): void;\nexport function listenArrayEvents(array, listener) {\n  if (array._chartjs) {\n    array._chartjs.listeners.push(listener);\n    return;\n  }\n\n  Object.defineProperty(array, '_chartjs', {\n    configurable: true,\n    enumerable: false,\n    value: {\n      listeners: [listener]\n    }\n  });\n\n  arrayEvents.forEach((key) => {\n    const method = '_onData' + _capitalize(key);\n    const base = array[key];\n\n    Object.defineProperty(array, key, {\n      configurable: true,\n      enumerable: false,\n      value(...args) {\n        const res = base.apply(this, args);\n\n        array._chartjs.listeners.forEach((object) => {\n          if (typeof object[method] === 'function') {\n            object[method](...args);\n          }\n        });\n\n        return res;\n      }\n    });\n  });\n}\n\n\n/**\n * Removes the given array event listener and cleanup extra attached properties (such as\n * the _chartjs stub and overridden methods) if array doesn't have any more listeners.\n */\nexport function unlistenArrayEvents<T>(array: T[], listener: ArrayListener<T>): void;\nexport function unlistenArrayEvents(array, listener) {\n  const stub = array._chartjs;\n  if (!stub) {\n    return;\n  }\n\n  const listeners = stub.listeners;\n  const index = listeners.indexOf(listener);\n  if (index !== -1) {\n    listeners.splice(index, 1);\n  }\n\n  if (listeners.length > 0) {\n    return;\n  }\n\n  arrayEvents.forEach((key) => {\n    delete array[key];\n  });\n\n  delete array._chartjs;\n}\n\n/**\n * @param items\n */\nexport function _arrayUnique<T>(items: T[]) {\n  const set = new Set<T>(items);\n\n  if (set.size === items.length) {\n    return items;\n  }\n\n  return Array.from(set);\n}\n", "import type {ChartMeta, PointElement} from '../types/index.js';\n\nimport {_limitValue} from './helpers.math.js';\nimport {_lookupByKey} from './helpers.collection.js';\n\nexport function fontString(pixelSize: number, fontStyle: string, fontFamily: string) {\n  return fontStyle + ' ' + pixelSize + 'px ' + fontFamily;\n}\n\n/**\n* Request animation polyfill\n*/\nexport const requestAnimFrame = (function() {\n  if (typeof window === 'undefined') {\n    return function(callback) {\n      return callback();\n    };\n  }\n  return window.requestAnimationFrame;\n}());\n\n/**\n * Throttles calling `fn` once per animation frame\n * Latest arguments are used on the actual call\n */\nexport function throttled<TArgs extends Array<any>>(\n  fn: (...args: TArgs) => void,\n  thisArg: any,\n) {\n  let argsToUse = [] as TArgs;\n  let ticking = false;\n\n  return function(...args: TArgs) {\n    // Save the args for use later\n    argsToUse = args;\n    if (!ticking) {\n      ticking = true;\n      requestAnimFrame.call(window, () => {\n        ticking = false;\n        fn.apply(thisArg, argsToUse);\n      });\n    }\n  };\n}\n\n/**\n * Debounces calling `fn` for `delay` ms\n */\nexport function debounce<TArgs extends Array<any>>(fn: (...args: TArgs) => void, delay: number) {\n  let timeout;\n  return function(...args: TArgs) {\n    if (delay) {\n      clearTimeout(timeout);\n      timeout = setTimeout(fn, delay, args);\n    } else {\n      fn.apply(this, args);\n    }\n    return delay;\n  };\n}\n\n/**\n * Converts 'start' to 'left', 'end' to 'right' and others to 'center'\n * @private\n */\nexport const _toLeftRightCenter = (align: 'start' | 'end' | 'center') => align === 'start' ? 'left' : align === 'end' ? 'right' : 'center';\n\n/**\n * Returns `start`, `end` or `(start + end) / 2` depending on `align`. Defaults to `center`\n * @private\n */\nexport const _alignStartEnd = (align: 'start' | 'end' | 'center', start: number, end: number) => align === 'start' ? start : align === 'end' ? end : (start + end) / 2;\n\n/**\n * Returns `left`, `right` or `(left + right) / 2` depending on `align`. Defaults to `left`\n * @private\n */\nexport const _textX = (align: 'left' | 'right' | 'center', left: number, right: number, rtl: boolean) => {\n  const check = rtl ? 'left' : 'right';\n  return align === check ? right : align === 'center' ? (left + right) / 2 : left;\n};\n\n/**\n * Return start and count of visible points.\n * @private\n */\nexport function _getStartAndCountOfVisiblePoints(meta: ChartMeta<'line' | 'scatter'>, points: PointElement[], animationsDisabled: boolean) {\n  const pointCount = points.length;\n\n  let start = 0;\n  let count = pointCount;\n\n  if (meta._sorted) {\n    const {iScale, _parsed} = meta;\n    const axis = iScale.axis;\n    const {min, max, minDefined, maxDefined} = iScale.getUserBounds();\n\n    if (minDefined) {\n      start = _limitValue(Math.min(\n        // @ts-expect-error Need to type _parsed\n        _lookupByKey(_parsed, axis, min).lo,\n        // @ts-expect-error Need to fix types on _lookupByKey\n        animationsDisabled ? pointCount : _lookupByKey(points, axis, iScale.getPixelForValue(min)).lo),\n      0, pointCount - 1);\n    }\n    if (maxDefined) {\n      count = _limitValue(Math.max(\n        // @ts-expect-error Need to type _parsed\n        _lookupByKey(_parsed, iScale.axis, max, true).hi + 1,\n        // @ts-expect-error Need to fix types on _lookupByKey\n        animationsDisabled ? 0 : _lookupByKey(points, axis, iScale.getPixelForValue(max), true).hi + 1),\n      start, pointCount) - start;\n    } else {\n      count = pointCount - start;\n    }\n  }\n\n  return {start, count};\n}\n\n/**\n * Checks if the scale ranges have changed.\n * @param {object} meta - dataset meta.\n * @returns {boolean}\n * @private\n */\nexport function _scaleRangesChanged(meta) {\n  const {xScale, yScale, _scaleRanges} = meta;\n  const newRanges = {\n    xmin: xScale.min,\n    xmax: xScale.max,\n    ymin: yScale.min,\n    ymax: yScale.max\n  };\n  if (!_scaleRanges) {\n    meta._scaleRanges = newRanges;\n    return true;\n  }\n  const changed = _scaleRanges.xmin !== xScale.min\n\t\t|| _scaleRanges.xmax !== xScale.max\n\t\t|| _scaleRanges.ymin !== yScale.min\n\t\t|| _scaleRanges.ymax !== yScale.max;\n\n  Object.assign(_scaleRanges, newRanges);\n  return changed;\n}\n", "import {PI, TAU, HALF_PI} from './helpers.math.js';\n\nconst atEdge = (t: number) => t === 0 || t === 1;\nconst elasticIn = (t: number, s: number, p: number) => -(Math.pow(2, 10 * (t -= 1)) * Math.sin((t - s) * TAU / p));\nconst elasticOut = (t: number, s: number, p: number) => Math.pow(2, -10 * t) * Math.sin((t - s) * TAU / p) + 1;\n\n/**\n * Easing functions adapted from <PERSON>'s easing equations.\n * @namespace Chart.helpers.easing.effects\n * @see http://www.robertpenner.com/easing/\n */\nconst effects = {\n  linear: (t: number) => t,\n\n  easeInQuad: (t: number) => t * t,\n\n  easeOutQuad: (t: number) => -t * (t - 2),\n\n  easeInOutQuad: (t: number) => ((t /= 0.5) < 1)\n    ? 0.5 * t * t\n    : -0.5 * ((--t) * (t - 2) - 1),\n\n  easeInCubic: (t: number) => t * t * t,\n\n  easeOutCubic: (t: number) => (t -= 1) * t * t + 1,\n\n  easeInOutCubic: (t: number) => ((t /= 0.5) < 1)\n    ? 0.5 * t * t * t\n    : 0.5 * ((t -= 2) * t * t + 2),\n\n  easeInQuart: (t: number) => t * t * t * t,\n\n  easeOutQuart: (t: number) => -((t -= 1) * t * t * t - 1),\n\n  easeInOutQuart: (t: number) => ((t /= 0.5) < 1)\n    ? 0.5 * t * t * t * t\n    : -0.5 * ((t -= 2) * t * t * t - 2),\n\n  easeInQuint: (t: number) => t * t * t * t * t,\n\n  easeOutQuint: (t: number) => (t -= 1) * t * t * t * t + 1,\n\n  easeInOutQuint: (t: number) => ((t /= 0.5) < 1)\n    ? 0.5 * t * t * t * t * t\n    : 0.5 * ((t -= 2) * t * t * t * t + 2),\n\n  easeInSine: (t: number) => -Math.cos(t * HALF_PI) + 1,\n\n  easeOutSine: (t: number) => Math.sin(t * HALF_PI),\n\n  easeInOutSine: (t: number) => -0.5 * (Math.cos(PI * t) - 1),\n\n  easeInExpo: (t: number) => (t === 0) ? 0 : Math.pow(2, 10 * (t - 1)),\n\n  easeOutExpo: (t: number) => (t === 1) ? 1 : -Math.pow(2, -10 * t) + 1,\n\n  easeInOutExpo: (t: number) => atEdge(t) ? t : t < 0.5\n    ? 0.5 * Math.pow(2, 10 * (t * 2 - 1))\n    : 0.5 * (-Math.pow(2, -10 * (t * 2 - 1)) + 2),\n\n  easeInCirc: (t: number) => (t >= 1) ? t : -(Math.sqrt(1 - t * t) - 1),\n\n  easeOutCirc: (t: number) => Math.sqrt(1 - (t -= 1) * t),\n\n  easeInOutCirc: (t: number) => ((t /= 0.5) < 1)\n    ? -0.5 * (Math.sqrt(1 - t * t) - 1)\n    : 0.5 * (Math.sqrt(1 - (t -= 2) * t) + 1),\n\n  easeInElastic: (t: number) => atEdge(t) ? t : elasticIn(t, 0.075, 0.3),\n\n  easeOutElastic: (t: number) => atEdge(t) ? t : elasticOut(t, 0.075, 0.3),\n\n  easeInOutElastic(t: number) {\n    const s = 0.1125;\n    const p = 0.45;\n    return atEdge(t) ? t :\n      t < 0.5\n        ? 0.5 * elasticIn(t * 2, s, p)\n        : 0.5 + 0.5 * elasticOut(t * 2 - 1, s, p);\n  },\n\n  easeInBack(t: number) {\n    const s = 1.70158;\n    return t * t * ((s + 1) * t - s);\n  },\n\n  easeOutBack(t: number) {\n    const s = 1.70158;\n    return (t -= 1) * t * ((s + 1) * t + s) + 1;\n  },\n\n  easeInOutBack(t: number) {\n    let s = 1.70158;\n    if ((t /= 0.5) < 1) {\n      return 0.5 * (t * t * (((s *= (1.525)) + 1) * t - s));\n    }\n    return 0.5 * ((t -= 2) * t * (((s *= (1.525)) + 1) * t + s) + 2);\n  },\n\n  easeInBounce: (t: number) => 1 - effects.easeOutBounce(1 - t),\n\n  easeOutBounce(t: number) {\n    const m = 7.5625;\n    const d = 2.75;\n    if (t < (1 / d)) {\n      return m * t * t;\n    }\n    if (t < (2 / d)) {\n      return m * (t -= (1.5 / d)) * t + 0.75;\n    }\n    if (t < (2.5 / d)) {\n      return m * (t -= (2.25 / d)) * t + 0.9375;\n    }\n    return m * (t -= (2.625 / d)) * t + 0.984375;\n  },\n\n  easeInOutBounce: (t: number) => (t < 0.5)\n    ? effects.easeInBounce(t * 2) * 0.5\n    : effects.easeOutBounce(t * 2 - 1) * 0.5 + 0.5,\n} as const;\n\nexport type EasingFunction = keyof typeof effects\n\nexport default effects;\n", "import {Color} from '@kurkle/color';\n\nexport function isPatternOrGradient(value: unknown): value is CanvasPattern | CanvasGradient {\n  if (value && typeof value === 'object') {\n    const type = value.toString();\n    return type === '[object CanvasPattern]' || type === '[object CanvasGradient]';\n  }\n\n  return false;\n}\n\nexport function color(value: CanvasGradient): CanvasGradient;\nexport function color(value: CanvasPattern): CanvasPattern;\nexport function color(\n  value:\n  | string\n  | { r: number; g: number; b: number; a: number }\n  | [number, number, number]\n  | [number, number, number, number]\n): Color;\nexport function color(value) {\n  return isPatternOrGradient(value) ? value : new Color(value);\n}\n\nexport function getHoverColor(value: CanvasGradient): CanvasGradient;\nexport function getHoverColor(value: CanvasPattern): CanvasPattern;\nexport function getHoverColor(value: string): string;\nexport function getHoverColor(value) {\n  return isPatternOrGradient(value)\n    ? value\n    : new Color(value).saturate(0.5).darken(0.1).hexString();\n}\n", "const numbers = ['x', 'y', 'borderWidth', 'radius', 'tension'];\nconst colors = ['color', 'borderColor', 'backgroundColor'];\n\nexport function applyAnimationsDefaults(defaults) {\n  defaults.set('animation', {\n    delay: undefined,\n    duration: 1000,\n    easing: 'easeOutQuart',\n    fn: undefined,\n    from: undefined,\n    loop: undefined,\n    to: undefined,\n    type: undefined,\n  });\n\n  defaults.describe('animation', {\n    _fallback: false,\n    _indexable: false,\n    _scriptable: (name) => name !== 'onProgress' && name !== 'onComplete' && name !== 'fn',\n  });\n\n  defaults.set('animations', {\n    colors: {\n      type: 'color',\n      properties: colors\n    },\n    numbers: {\n      type: 'number',\n      properties: numbers\n    },\n  });\n\n  defaults.describe('animations', {\n    _fallback: 'animation',\n  });\n\n  defaults.set('transitions', {\n    active: {\n      animation: {\n        duration: 400\n      }\n    },\n    resize: {\n      animation: {\n        duration: 0\n      }\n    },\n    show: {\n      animations: {\n        colors: {\n          from: 'transparent'\n        },\n        visible: {\n          type: 'boolean',\n          duration: 0 // show immediately\n        },\n      }\n    },\n    hide: {\n      animations: {\n        colors: {\n          to: 'transparent'\n        },\n        visible: {\n          type: 'boolean',\n          easing: 'linear',\n          fn: v => v | 0 // for keeping the dataset visible all the way through the animation\n        },\n      }\n    }\n  });\n}\n", "export function applyLayoutsDefaults(defaults) {\n  defaults.set('layout', {\n    autoPadding: true,\n    padding: {\n      top: 0,\n      right: 0,\n      bottom: 0,\n      left: 0\n    }\n  });\n}\n", "\nconst intlCache = new Map<string, Intl.NumberFormat>();\n\nfunction getNumberFormat(locale: string, options?: Intl.NumberFormatOptions) {\n  options = options || {};\n  const cacheKey = locale + JSON.stringify(options);\n  let formatter = intlCache.get(cacheKey);\n  if (!formatter) {\n    formatter = new Intl.NumberFormat(locale, options);\n    intlCache.set(cacheKey, formatter);\n  }\n  return formatter;\n}\n\nexport function formatNumber(num: number, locale: string, options?: Intl.NumberFormatOptions) {\n  return getNumberFormat(locale, options).format(num);\n}\n", "import {isArray} from '../helpers/helpers.core.js';\nimport {formatNumber} from '../helpers/helpers.intl.js';\nimport {log10} from '../helpers/helpers.math.js';\n\n/**\n * Namespace to hold formatters for different types of ticks\n * @namespace Chart.Ticks.formatters\n */\nconst formatters = {\n  /**\n   * Formatter for value labels\n   * @method Chart.Ticks.formatters.values\n   * @param value the value to display\n   * @return {string|string[]} the label to display\n   */\n  values(value) {\n    return isArray(value) ? /** @type {string[]} */ (value) : '' + value;\n  },\n\n  /**\n   * Formatter for numeric ticks\n   * @method Chart.Ticks.formatters.numeric\n   * @param tickValue {number} the value to be formatted\n   * @param index {number} the position of the tickValue parameter in the ticks array\n   * @param ticks {object[]} the list of ticks being converted\n   * @return {string} string representation of the tickValue parameter\n   */\n  numeric(tickValue, index, ticks) {\n    if (tickValue === 0) {\n      return '0'; // never show decimal places for 0\n    }\n\n    const locale = this.chart.options.locale;\n    let notation;\n    let delta = tickValue; // This is used when there are less than 2 ticks as the tick interval.\n\n    if (ticks.length > 1) {\n      // all ticks are small or there huge numbers; use scientific notation\n      const maxTick = Math.max(Math.abs(ticks[0].value), Math.abs(ticks[ticks.length - 1].value));\n      if (maxTick < 1e-4 || maxTick > 1e+15) {\n        notation = 'scientific';\n      }\n\n      delta = calculateDelta(tickValue, ticks);\n    }\n\n    const logDelta = log10(Math.abs(delta));\n\n    // When datasets have values approaching Number.MAX_VALUE, the tick calculations might result in\n    // infinity and eventually NaN. Passing NaN for minimumFractionDigits or maximumFractionDigits\n    // will make the number formatter throw. So instead we check for isNaN and use a fallback value.\n    //\n    // toFixed has a max of 20 decimal places\n    const numDecimal = isNaN(logDelta) ? 1 : Math.max(Math.min(-1 * Math.floor(logDelta), 20), 0);\n\n    const options = {notation, minimumFractionDigits: numDecimal, maximumFractionDigits: numDecimal};\n    Object.assign(options, this.options.ticks.format);\n\n    return formatNumber(tickValue, locale, options);\n  },\n\n\n  /**\n   * Formatter for logarithmic ticks\n   * @method Chart.Ticks.formatters.logarithmic\n   * @param tickValue {number} the value to be formatted\n   * @param index {number} the position of the tickValue parameter in the ticks array\n   * @param ticks {object[]} the list of ticks being converted\n   * @return {string} string representation of the tickValue parameter\n   */\n  logarithmic(tickValue, index, ticks) {\n    if (tickValue === 0) {\n      return '0';\n    }\n    const remain = ticks[index].significand || (tickValue / (Math.pow(10, Math.floor(log10(tickValue)))));\n    if ([1, 2, 3, 5, 10, 15].includes(remain) || index > 0.8 * ticks.length) {\n      return formatters.numeric.call(this, tickValue, index, ticks);\n    }\n    return '';\n  }\n\n};\n\n\nfunction calculateDelta(tickValue, ticks) {\n  // Figure out how many digits to show\n  // The space between the first two ticks might be smaller than normal spacing\n  let delta = ticks.length > 3 ? ticks[2].value - ticks[1].value : ticks[1].value - ticks[0].value;\n\n  // If we have a number like 2.5 as the delta, figure out how many decimal places we need\n  if (Math.abs(delta) >= 1 && tickValue !== Math.floor(tickValue)) {\n    // not an integer\n    delta = tickValue - Math.floor(tickValue);\n  }\n  return delta;\n}\n\n/**\n * Namespace to hold static tick generation functions\n * @namespace Chart.Ticks\n */\nexport default {formatters};\n", "import Ticks from './core.ticks.js';\n\nexport function applyScaleDefaults(defaults) {\n  defaults.set('scale', {\n    display: true,\n    offset: false,\n    reverse: false,\n    beginAtZero: false,\n\n    /**\n     * Scale boundary strategy (bypassed by min/max time options)\n     * - `data`: make sure data are fully visible, ticks outside are removed\n     * - `ticks`: make sure ticks are fully visible, data outside are truncated\n     * @see https://github.com/chartjs/Chart.js/pull/4556\n     * @since 3.0.0\n     */\n    bounds: 'ticks',\n\n    clip: true,\n\n    /**\n     * Addition grace added to max and reduced from min data value.\n     * @since 3.0.0\n     */\n    grace: 0,\n\n    // grid line settings\n    grid: {\n      display: true,\n      lineWidth: 1,\n      drawOnChartArea: true,\n      drawTicks: true,\n      tickLength: 8,\n      tickWidth: (_ctx, options) => options.lineWidth,\n      tickColor: (_ctx, options) => options.color,\n      offset: false,\n    },\n\n    border: {\n      display: true,\n      dash: [],\n      dashOffset: 0.0,\n      width: 1\n    },\n\n    // scale title\n    title: {\n      // display property\n      display: false,\n\n      // actual label\n      text: '',\n\n      // top/bottom padding\n      padding: {\n        top: 4,\n        bottom: 4\n      }\n    },\n\n    // label settings\n    ticks: {\n      minRotation: 0,\n      maxRotation: 50,\n      mirror: false,\n      textStrokeWidth: 0,\n      textStrokeColor: '',\n      padding: 3,\n      display: true,\n      autoSkip: true,\n      autoSkipPadding: 3,\n      labelOffset: 0,\n      // We pass through arrays to be rendered as multiline labels, we convert Others to strings here.\n      callback: Ticks.formatters.values,\n      minor: {},\n      major: {},\n      align: 'center',\n      crossAlign: 'near',\n\n      showLabelBackdrop: false,\n      backdropColor: 'rgba(255, 255, 255, 0.75)',\n      backdropPadding: 2,\n    }\n  });\n\n  defaults.route('scale.ticks', 'color', '', 'color');\n  defaults.route('scale.grid', 'color', '', 'borderColor');\n  defaults.route('scale.border', 'color', '', 'borderColor');\n  defaults.route('scale.title', 'color', '', 'color');\n\n  defaults.describe('scale', {\n    _fallback: false,\n    _scriptable: (name) => !name.startsWith('before') && !name.startsWith('after') && name !== 'callback' && name !== 'parser',\n    _indexable: (name) => name !== 'borderDash' && name !== 'tickBorderDash' && name !== 'dash',\n  });\n\n  defaults.describe('scales', {\n    _fallback: 'scale',\n  });\n\n  defaults.describe('scale.ticks', {\n    _scriptable: (name) => name !== 'backdropPadding' && name !== 'callback',\n    _indexable: (name) => name !== 'backdropPadding',\n  });\n}\n", "import {getHoverColor} from '../helpers/helpers.color.js';\nimport {isObject, merge, valueOrDefault} from '../helpers/helpers.core.js';\nimport {applyAnimationsDefaults} from './core.animations.defaults.js';\nimport {applyLayoutsDefaults} from './core.layouts.defaults.js';\nimport {applyScaleDefaults} from './core.scale.defaults.js';\n\nexport const overrides = Object.create(null);\nexport const descriptors = Object.create(null);\n\n/**\n * @param {object} node\n * @param {string} key\n * @return {object}\n */\nfunction getScope(node, key) {\n  if (!key) {\n    return node;\n  }\n  const keys = key.split('.');\n  for (let i = 0, n = keys.length; i < n; ++i) {\n    const k = keys[i];\n    node = node[k] || (node[k] = Object.create(null));\n  }\n  return node;\n}\n\nfunction set(root, scope, values) {\n  if (typeof scope === 'string') {\n    return merge(getScope(root, scope), values);\n  }\n  return merge(getScope(root, ''), scope);\n}\n\n/**\n * Please use the module's default export which provides a singleton instance\n * Note: class is exported for typedoc\n */\nexport class Defaults {\n  constructor(_descriptors, _appliers) {\n    this.animation = undefined;\n    this.backgroundColor = 'rgba(0,0,0,0.1)';\n    this.borderColor = 'rgba(0,0,0,0.1)';\n    this.color = '#666';\n    this.datasets = {};\n    this.devicePixelRatio = (context) => context.chart.platform.getDevicePixelRatio();\n    this.elements = {};\n    this.events = [\n      'mousemove',\n      'mouseout',\n      'click',\n      'touchstart',\n      'touchmove'\n    ];\n    this.font = {\n      family: \"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif\",\n      size: 12,\n      style: 'normal',\n      lineHeight: 1.2,\n      weight: null\n    };\n    this.hover = {};\n    this.hoverBackgroundColor = (ctx, options) => getHoverColor(options.backgroundColor);\n    this.hoverBorderColor = (ctx, options) => getHoverColor(options.borderColor);\n    this.hoverColor = (ctx, options) => getHoverColor(options.color);\n    this.indexAxis = 'x';\n    this.interaction = {\n      mode: 'nearest',\n      intersect: true,\n      includeInvisible: false\n    };\n    this.maintainAspectRatio = true;\n    this.onHover = null;\n    this.onClick = null;\n    this.parsing = true;\n    this.plugins = {};\n    this.responsive = true;\n    this.scale = undefined;\n    this.scales = {};\n    this.showLine = true;\n    this.drawActiveElementsOnTop = true;\n\n    this.describe(_descriptors);\n    this.apply(_appliers);\n  }\n\n  /**\n\t * @param {string|object} scope\n\t * @param {object} [values]\n\t */\n  set(scope, values) {\n    return set(this, scope, values);\n  }\n\n  /**\n\t * @param {string} scope\n\t */\n  get(scope) {\n    return getScope(this, scope);\n  }\n\n  /**\n\t * @param {string|object} scope\n\t * @param {object} [values]\n\t */\n  describe(scope, values) {\n    return set(descriptors, scope, values);\n  }\n\n  override(scope, values) {\n    return set(overrides, scope, values);\n  }\n\n  /**\n\t * Routes the named defaults to fallback to another scope/name.\n\t * This routing is useful when those target values, like defaults.color, are changed runtime.\n\t * If the values would be copied, the runtime change would not take effect. By routing, the\n\t * fallback is evaluated at each access, so its always up to date.\n\t *\n\t * Example:\n\t *\n\t * \tdefaults.route('elements.arc', 'backgroundColor', '', 'color')\n\t *   - reads the backgroundColor from defaults.color when undefined locally\n\t *\n\t * @param {string} scope Scope this route applies to.\n\t * @param {string} name Property name that should be routed to different namespace when not defined here.\n\t * @param {string} targetScope The namespace where those properties should be routed to.\n\t * Empty string ('') is the root of defaults.\n\t * @param {string} targetName The target name in the target scope the property should be routed to.\n\t */\n  route(scope, name, targetScope, targetName) {\n    const scopeObject = getScope(this, scope);\n    const targetScopeObject = getScope(this, targetScope);\n    const privateName = '_' + name;\n\n    Object.defineProperties(scopeObject, {\n      // A private property is defined to hold the actual value, when this property is set in its scope (set in the setter)\n      [privateName]: {\n        value: scopeObject[name],\n        writable: true\n      },\n      // The actual property is defined as getter/setter so we can do the routing when value is not locally set.\n      [name]: {\n        enumerable: true,\n        get() {\n          const local = this[privateName];\n          const target = targetScopeObject[targetName];\n          if (isObject(local)) {\n            return Object.assign({}, target, local);\n          }\n          return valueOrDefault(local, target);\n        },\n        set(value) {\n          this[privateName] = value;\n        }\n      }\n    });\n  }\n\n  apply(appliers) {\n    appliers.forEach((apply) => apply(this));\n  }\n}\n\n// singleton instance\nexport default /* #__PURE__ */ new Defaults({\n  _scriptable: (name) => !name.startsWith('on'),\n  _indexable: (name) => name !== 'events',\n  hover: {\n    _fallback: 'interaction'\n  },\n  interaction: {\n    _scriptable: false,\n    _indexable: false,\n  }\n}, [applyAnimationsDefaults, applyLayoutsDefaults, applyScaleDefaults]);\n", "import type {\n  Chart,\n  Point,\n  FontSpec,\n  CanvasFontSpec,\n  PointStyle,\n  RenderTextOpts,\n  BackdropOptions\n} from '../types/index.js';\nimport type {\n  TRBL,\n  SplinePoint,\n  RoundedRect,\n  TRBLCorners\n} from '../types/geometric.js';\nimport {isArray, isNullOrUndef} from './helpers.core.js';\nimport {PI, TAU, HALF_PI, QUARTER_PI, TWO_THIRDS_PI, RAD_PER_DEG} from './helpers.math.js';\n\n/**\n * Converts the given font object into a CSS font string.\n * @param font - A font object.\n * @return The CSS font string. See https://developer.mozilla.org/en-US/docs/Web/CSS/font\n * @private\n */\nexport function toFontString(font: FontSpec) {\n  if (!font || isNullOrUndef(font.size) || isNullOrUndef(font.family)) {\n    return null;\n  }\n\n  return (font.style ? font.style + ' ' : '')\n\t\t+ (font.weight ? font.weight + ' ' : '')\n\t\t+ font.size + 'px '\n\t\t+ font.family;\n}\n\n/**\n * @private\n */\nexport function _measureText(\n  ctx: CanvasRenderingContext2D,\n  data: Record<string, number>,\n  gc: string[],\n  longest: number,\n  string: string\n) {\n  let textWidth = data[string];\n  if (!textWidth) {\n    textWidth = data[string] = ctx.measureText(string).width;\n    gc.push(string);\n  }\n  if (textWidth > longest) {\n    longest = textWidth;\n  }\n  return longest;\n}\n\ntype Thing = string | undefined | null\ntype Things = (Thing | Thing[])[]\n\n/**\n * @private\n */\n// eslint-disable-next-line complexity\nexport function _longestText(\n  ctx: CanvasRenderingContext2D,\n  font: string,\n  arrayOfThings: Things,\n  cache?: {data?: Record<string, number>, garbageCollect?: string[], font?: string}\n) {\n  cache = cache || {};\n  let data = cache.data = cache.data || {};\n  let gc = cache.garbageCollect = cache.garbageCollect || [];\n\n  if (cache.font !== font) {\n    data = cache.data = {};\n    gc = cache.garbageCollect = [];\n    cache.font = font;\n  }\n\n  ctx.save();\n\n  ctx.font = font;\n  let longest = 0;\n  const ilen = arrayOfThings.length;\n  let i: number, j: number, jlen: number, thing: Thing | Thing[], nestedThing: Thing | Thing[];\n  for (i = 0; i < ilen; i++) {\n    thing = arrayOfThings[i];\n\n    // Undefined strings and arrays should not be measured\n    if (thing !== undefined && thing !== null && !isArray(thing)) {\n      longest = _measureText(ctx, data, gc, longest, thing);\n    } else if (isArray(thing)) {\n      // if it is an array lets measure each element\n      // to do maybe simplify this function a bit so we can do this more recursively?\n      for (j = 0, jlen = thing.length; j < jlen; j++) {\n        nestedThing = thing[j];\n        // Undefined strings and arrays should not be measured\n        if (nestedThing !== undefined && nestedThing !== null && !isArray(nestedThing)) {\n          longest = _measureText(ctx, data, gc, longest, nestedThing);\n        }\n      }\n    }\n  }\n\n  ctx.restore();\n\n  const gcLen = gc.length / 2;\n  if (gcLen > arrayOfThings.length) {\n    for (i = 0; i < gcLen; i++) {\n      delete data[gc[i]];\n    }\n    gc.splice(0, gcLen);\n  }\n  return longest;\n}\n\n/**\n * Returns the aligned pixel value to avoid anti-aliasing blur\n * @param chart - The chart instance.\n * @param pixel - A pixel value.\n * @param width - The width of the element.\n * @returns The aligned pixel value.\n * @private\n */\nexport function _alignPixel(chart: Chart, pixel: number, width: number) {\n  const devicePixelRatio = chart.currentDevicePixelRatio;\n  const halfWidth = width !== 0 ? Math.max(width / 2, 0.5) : 0;\n  return Math.round((pixel - halfWidth) * devicePixelRatio) / devicePixelRatio + halfWidth;\n}\n\n/**\n * Clears the entire canvas.\n */\nexport function clearCanvas(canvas: HTMLCanvasElement, ctx?: CanvasRenderingContext2D) {\n  ctx = ctx || canvas.getContext('2d');\n\n  ctx.save();\n  // canvas.width and canvas.height do not consider the canvas transform,\n  // while clearRect does\n  ctx.resetTransform();\n  ctx.clearRect(0, 0, canvas.width, canvas.height);\n  ctx.restore();\n}\n\nexport interface DrawPointOptions {\n  pointStyle: PointStyle;\n  rotation?: number;\n  radius: number;\n  borderWidth: number;\n}\n\nexport function drawPoint(\n  ctx: CanvasRenderingContext2D,\n  options: DrawPointOptions,\n  x: number,\n  y: number\n) {\n  // eslint-disable-next-line @typescript-eslint/no-use-before-define\n  drawPointLegend(ctx, options, x, y, null);\n}\n\n// eslint-disable-next-line complexity\nexport function drawPointLegend(\n  ctx: CanvasRenderingContext2D,\n  options: DrawPointOptions,\n  x: number,\n  y: number,\n  w: number\n) {\n  let type: string, xOffset: number, yOffset: number, size: number, cornerRadius: number, width: number, xOffsetW: number, yOffsetW: number;\n  const style = options.pointStyle;\n  const rotation = options.rotation;\n  const radius = options.radius;\n  let rad = (rotation || 0) * RAD_PER_DEG;\n\n  if (style && typeof style === 'object') {\n    type = style.toString();\n    if (type === '[object HTMLImageElement]' || type === '[object HTMLCanvasElement]') {\n      ctx.save();\n      ctx.translate(x, y);\n      ctx.rotate(rad);\n      ctx.drawImage(style, -style.width / 2, -style.height / 2, style.width, style.height);\n      ctx.restore();\n      return;\n    }\n  }\n\n  if (isNaN(radius) || radius <= 0) {\n    return;\n  }\n\n  ctx.beginPath();\n\n  switch (style) {\n  // Default includes circle\n    default:\n      if (w) {\n        ctx.ellipse(x, y, w / 2, radius, 0, 0, TAU);\n      } else {\n        ctx.arc(x, y, radius, 0, TAU);\n      }\n      ctx.closePath();\n      break;\n    case 'triangle':\n      width = w ? w / 2 : radius;\n      ctx.moveTo(x + Math.sin(rad) * width, y - Math.cos(rad) * radius);\n      rad += TWO_THIRDS_PI;\n      ctx.lineTo(x + Math.sin(rad) * width, y - Math.cos(rad) * radius);\n      rad += TWO_THIRDS_PI;\n      ctx.lineTo(x + Math.sin(rad) * width, y - Math.cos(rad) * radius);\n      ctx.closePath();\n      break;\n    case 'rectRounded':\n    // NOTE: the rounded rect implementation changed to use `arc` instead of\n    // `quadraticCurveTo` since it generates better results when rect is\n    // almost a circle. 0.516 (instead of 0.5) produces results with visually\n    // closer proportion to the previous impl and it is inscribed in the\n    // circle with `radius`. For more details, see the following PRs:\n    // https://github.com/chartjs/Chart.js/issues/5597\n    // https://github.com/chartjs/Chart.js/issues/5858\n      cornerRadius = radius * 0.516;\n      size = radius - cornerRadius;\n      xOffset = Math.cos(rad + QUARTER_PI) * size;\n      xOffsetW = Math.cos(rad + QUARTER_PI) * (w ? w / 2 - cornerRadius : size);\n      yOffset = Math.sin(rad + QUARTER_PI) * size;\n      yOffsetW = Math.sin(rad + QUARTER_PI) * (w ? w / 2 - cornerRadius : size);\n      ctx.arc(x - xOffsetW, y - yOffset, cornerRadius, rad - PI, rad - HALF_PI);\n      ctx.arc(x + yOffsetW, y - xOffset, cornerRadius, rad - HALF_PI, rad);\n      ctx.arc(x + xOffsetW, y + yOffset, cornerRadius, rad, rad + HALF_PI);\n      ctx.arc(x - yOffsetW, y + xOffset, cornerRadius, rad + HALF_PI, rad + PI);\n      ctx.closePath();\n      break;\n    case 'rect':\n      if (!rotation) {\n        size = Math.SQRT1_2 * radius;\n        width = w ? w / 2 : size;\n        ctx.rect(x - width, y - size, 2 * width, 2 * size);\n        break;\n      }\n      rad += QUARTER_PI;\n    /* falls through */\n    case 'rectRot':\n      xOffsetW = Math.cos(rad) * (w ? w / 2 : radius);\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      yOffsetW = Math.sin(rad) * (w ? w / 2 : radius);\n      ctx.moveTo(x - xOffsetW, y - yOffset);\n      ctx.lineTo(x + yOffsetW, y - xOffset);\n      ctx.lineTo(x + xOffsetW, y + yOffset);\n      ctx.lineTo(x - yOffsetW, y + xOffset);\n      ctx.closePath();\n      break;\n    case 'crossRot':\n      rad += QUARTER_PI;\n    /* falls through */\n    case 'cross':\n      xOffsetW = Math.cos(rad) * (w ? w / 2 : radius);\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      yOffsetW = Math.sin(rad) * (w ? w / 2 : radius);\n      ctx.moveTo(x - xOffsetW, y - yOffset);\n      ctx.lineTo(x + xOffsetW, y + yOffset);\n      ctx.moveTo(x + yOffsetW, y - xOffset);\n      ctx.lineTo(x - yOffsetW, y + xOffset);\n      break;\n    case 'star':\n      xOffsetW = Math.cos(rad) * (w ? w / 2 : radius);\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      yOffsetW = Math.sin(rad) * (w ? w / 2 : radius);\n      ctx.moveTo(x - xOffsetW, y - yOffset);\n      ctx.lineTo(x + xOffsetW, y + yOffset);\n      ctx.moveTo(x + yOffsetW, y - xOffset);\n      ctx.lineTo(x - yOffsetW, y + xOffset);\n      rad += QUARTER_PI;\n      xOffsetW = Math.cos(rad) * (w ? w / 2 : radius);\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      yOffsetW = Math.sin(rad) * (w ? w / 2 : radius);\n      ctx.moveTo(x - xOffsetW, y - yOffset);\n      ctx.lineTo(x + xOffsetW, y + yOffset);\n      ctx.moveTo(x + yOffsetW, y - xOffset);\n      ctx.lineTo(x - yOffsetW, y + xOffset);\n      break;\n    case 'line':\n      xOffset = w ? w / 2 : Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      ctx.moveTo(x - xOffset, y - yOffset);\n      ctx.lineTo(x + xOffset, y + yOffset);\n      break;\n    case 'dash':\n      ctx.moveTo(x, y);\n      ctx.lineTo(x + Math.cos(rad) * (w ? w / 2 : radius), y + Math.sin(rad) * radius);\n      break;\n    case false:\n      ctx.closePath();\n      break;\n  }\n\n  ctx.fill();\n  if (options.borderWidth > 0) {\n    ctx.stroke();\n  }\n}\n\n/**\n * Returns true if the point is inside the rectangle\n * @param point - The point to test\n * @param area - The rectangle\n * @param margin - allowed margin\n * @private\n */\nexport function _isPointInArea(\n  point: Point,\n  area: TRBL,\n  margin?: number\n) {\n  margin = margin || 0.5; // margin - default is to match rounded decimals\n\n  return !area || (point && point.x > area.left - margin && point.x < area.right + margin &&\n\t\tpoint.y > area.top - margin && point.y < area.bottom + margin);\n}\n\nexport function clipArea(ctx: CanvasRenderingContext2D, area: TRBL) {\n  ctx.save();\n  ctx.beginPath();\n  ctx.rect(area.left, area.top, area.right - area.left, area.bottom - area.top);\n  ctx.clip();\n}\n\nexport function unclipArea(ctx: CanvasRenderingContext2D) {\n  ctx.restore();\n}\n\n/**\n * @private\n */\nexport function _steppedLineTo(\n  ctx: CanvasRenderingContext2D,\n  previous: Point,\n  target: Point,\n  flip?: boolean,\n  mode?: string\n) {\n  if (!previous) {\n    return ctx.lineTo(target.x, target.y);\n  }\n  if (mode === 'middle') {\n    const midpoint = (previous.x + target.x) / 2.0;\n    ctx.lineTo(midpoint, previous.y);\n    ctx.lineTo(midpoint, target.y);\n  } else if (mode === 'after' !== !!flip) {\n    ctx.lineTo(previous.x, target.y);\n  } else {\n    ctx.lineTo(target.x, previous.y);\n  }\n  ctx.lineTo(target.x, target.y);\n}\n\n/**\n * @private\n */\nexport function _bezierCurveTo(\n  ctx: CanvasRenderingContext2D,\n  previous: SplinePoint,\n  target: SplinePoint,\n  flip?: boolean\n) {\n  if (!previous) {\n    return ctx.lineTo(target.x, target.y);\n  }\n  ctx.bezierCurveTo(\n    flip ? previous.cp1x : previous.cp2x,\n    flip ? previous.cp1y : previous.cp2y,\n    flip ? target.cp2x : target.cp1x,\n    flip ? target.cp2y : target.cp1y,\n    target.x,\n    target.y);\n}\n\nfunction setRenderOpts(ctx: CanvasRenderingContext2D, opts: RenderTextOpts) {\n  if (opts.translation) {\n    ctx.translate(opts.translation[0], opts.translation[1]);\n  }\n\n  if (!isNullOrUndef(opts.rotation)) {\n    ctx.rotate(opts.rotation);\n  }\n\n  if (opts.color) {\n    ctx.fillStyle = opts.color;\n  }\n\n  if (opts.textAlign) {\n    ctx.textAlign = opts.textAlign;\n  }\n\n  if (opts.textBaseline) {\n    ctx.textBaseline = opts.textBaseline;\n  }\n}\n\nfunction decorateText(\n  ctx: CanvasRenderingContext2D,\n  x: number,\n  y: number,\n  line: string,\n  opts: RenderTextOpts\n) {\n  if (opts.strikethrough || opts.underline) {\n    /**\n     * Now that IE11 support has been dropped, we can use more\n     * of the TextMetrics object. The actual bounding boxes\n     * are unflagged in Chrome, Firefox, Edge, and Safari so they\n     * can be safely used.\n     * See https://developer.mozilla.org/en-US/docs/Web/API/TextMetrics#Browser_compatibility\n     */\n    const metrics = ctx.measureText(line);\n    const left = x - metrics.actualBoundingBoxLeft;\n    const right = x + metrics.actualBoundingBoxRight;\n    const top = y - metrics.actualBoundingBoxAscent;\n    const bottom = y + metrics.actualBoundingBoxDescent;\n    const yDecoration = opts.strikethrough ? (top + bottom) / 2 : bottom;\n\n    ctx.strokeStyle = ctx.fillStyle;\n    ctx.beginPath();\n    ctx.lineWidth = opts.decorationWidth || 2;\n    ctx.moveTo(left, yDecoration);\n    ctx.lineTo(right, yDecoration);\n    ctx.stroke();\n  }\n}\n\nfunction drawBackdrop(ctx: CanvasRenderingContext2D, opts: BackdropOptions) {\n  const oldColor = ctx.fillStyle;\n\n  ctx.fillStyle = opts.color as string;\n  ctx.fillRect(opts.left, opts.top, opts.width, opts.height);\n  ctx.fillStyle = oldColor;\n}\n\n/**\n * Render text onto the canvas\n */\nexport function renderText(\n  ctx: CanvasRenderingContext2D,\n  text: string | string[],\n  x: number,\n  y: number,\n  font: CanvasFontSpec,\n  opts: RenderTextOpts = {}\n) {\n  const lines = isArray(text) ? text : [text];\n  const stroke = opts.strokeWidth > 0 && opts.strokeColor !== '';\n  let i: number, line: string;\n\n  ctx.save();\n  ctx.font = font.string;\n  setRenderOpts(ctx, opts);\n\n  for (i = 0; i < lines.length; ++i) {\n    line = lines[i];\n\n    if (opts.backdrop) {\n      drawBackdrop(ctx, opts.backdrop);\n    }\n\n    if (stroke) {\n      if (opts.strokeColor) {\n        ctx.strokeStyle = opts.strokeColor;\n      }\n\n      if (!isNullOrUndef(opts.strokeWidth)) {\n        ctx.lineWidth = opts.strokeWidth;\n      }\n\n      ctx.strokeText(line, x, y, opts.maxWidth);\n    }\n\n    ctx.fillText(line, x, y, opts.maxWidth);\n    decorateText(ctx, x, y, line, opts);\n\n    y += Number(font.lineHeight);\n  }\n\n  ctx.restore();\n}\n\n/**\n * Add a path of a rectangle with rounded corners to the current sub-path\n * @param ctx - Context\n * @param rect - Bounding rect\n */\nexport function addRoundedRectPath(\n  ctx: CanvasRenderingContext2D,\n  rect: RoundedRect & { radius: TRBLCorners }\n) {\n  const {x, y, w, h, radius} = rect;\n\n  // top left arc\n  ctx.arc(x + radius.topLeft, y + radius.topLeft, radius.topLeft, 1.5 * PI, PI, true);\n\n  // line from top left to bottom left\n  ctx.lineTo(x, y + h - radius.bottomLeft);\n\n  // bottom left arc\n  ctx.arc(x + radius.bottomLeft, y + h - radius.bottomLeft, radius.bottomLeft, PI, HALF_PI, true);\n\n  // line from bottom left to bottom right\n  ctx.lineTo(x + w - radius.bottomRight, y + h);\n\n  // bottom right arc\n  ctx.arc(x + w - radius.bottomRight, y + h - radius.bottomRight, radius.bottomRight, HALF_PI, 0, true);\n\n  // line from bottom right to top right\n  ctx.lineTo(x + w, y + radius.topRight);\n\n  // top right arc\n  ctx.arc(x + w - radius.topRight, y + radius.topRight, radius.topRight, 0, -HALF_PI, true);\n\n  // line from top right to top left\n  ctx.lineTo(x + radius.topLeft, y);\n}\n", "import defaults from '../core/core.defaults.js';\nimport {isArray, isObject, toDimension, valueOrDefault} from './helpers.core.js';\nimport {toFontString} from './helpers.canvas.js';\nimport type {ChartArea, FontSpec, Point} from '../types/index.js';\nimport type {TRBL, TRBLCorners} from '../types/geometric.js';\n\nconst LINE_HEIGHT = /^(normal|(\\d+(?:\\.\\d+)?)(px|em|%)?)$/;\nconst FONT_STYLE = /^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;\n\n/**\n * @alias Chart.helpers.options\n * @namespace\n */\n/**\n * Converts the given line height `value` in pixels for a specific font `size`.\n * @param value - The lineHeight to parse (eg. 1.6, '14px', '75%', '1.6em').\n * @param size - The font size (in pixels) used to resolve relative `value`.\n * @returns The effective line height in pixels (size * 1.2 if value is invalid).\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/line-height\n * @since 2.7.0\n */\nexport function toLineHeight(value: number | string, size: number): number {\n  const matches = ('' + value).match(LINE_HEIGHT);\n  if (!matches || matches[1] === 'normal') {\n    return size * 1.2;\n  }\n\n  value = +matches[2];\n\n  switch (matches[3]) {\n    case 'px':\n      return value;\n    case '%':\n      value /= 100;\n      break;\n    default:\n      break;\n  }\n\n  return size * value;\n}\n\nconst numberOrZero = (v: unknown) => +v || 0;\n\n/**\n * @param value\n * @param props\n */\nexport function _readValueToProps<K extends string>(value: number | Record<K, number>, props: K[]): Record<K, number>;\nexport function _readValueToProps<K extends string, T extends string>(value: number | Record<K & T, number>, props: Record<T, K>): Record<T, number>;\nexport function _readValueToProps(value: number | Record<string, number>, props: string[] | Record<string, string>) {\n  const ret = {};\n  const objProps = isObject(props);\n  const keys = objProps ? Object.keys(props) : props;\n  const read = isObject(value)\n    ? objProps\n      ? prop => valueOrDefault(value[prop], value[props[prop]])\n      : prop => value[prop]\n    : () => value;\n\n  for (const prop of keys) {\n    ret[prop] = numberOrZero(read(prop));\n  }\n  return ret;\n}\n\n/**\n * Converts the given value into a TRBL object.\n * @param value - If a number, set the value to all TRBL component,\n *  else, if an object, use defined properties and sets undefined ones to 0.\n *  x / y are shorthands for same value for left/right and top/bottom.\n * @returns The padding values (top, right, bottom, left)\n * @since 3.0.0\n */\nexport function toTRBL(value: number | TRBL | Point) {\n  return _readValueToProps(value, {top: 'y', right: 'x', bottom: 'y', left: 'x'});\n}\n\n/**\n * Converts the given value into a TRBL corners object (similar with css border-radius).\n * @param value - If a number, set the value to all TRBL corner components,\n *  else, if an object, use defined properties and sets undefined ones to 0.\n * @returns The TRBL corner values (topLeft, topRight, bottomLeft, bottomRight)\n * @since 3.0.0\n */\nexport function toTRBLCorners(value: number | TRBLCorners) {\n  return _readValueToProps(value, ['topLeft', 'topRight', 'bottomLeft', 'bottomRight']);\n}\n\n/**\n * Converts the given value into a padding object with pre-computed width/height.\n * @param value - If a number, set the value to all TRBL component,\n *  else, if an object, use defined properties and sets undefined ones to 0.\n *  x / y are shorthands for same value for left/right and top/bottom.\n * @returns The padding values (top, right, bottom, left, width, height)\n * @since 2.7.0\n */\nexport function toPadding(value?: number | TRBL): ChartArea {\n  const obj = toTRBL(value) as ChartArea;\n\n  obj.width = obj.left + obj.right;\n  obj.height = obj.top + obj.bottom;\n\n  return obj;\n}\n\n/**\n * Parses font options and returns the font object.\n * @param options - A object that contains font options to be parsed.\n * @param fallback - A object that contains fallback font options.\n * @return The font object.\n * @private\n */\n\nexport function toFont(options: Partial<FontSpec>, fallback?: Partial<FontSpec>) {\n  options = options || {};\n  fallback = fallback || defaults.font as FontSpec;\n\n  let size = valueOrDefault(options.size, fallback.size);\n\n  if (typeof size === 'string') {\n    size = parseInt(size, 10);\n  }\n  let style = valueOrDefault(options.style, fallback.style);\n  if (style && !('' + style).match(FONT_STYLE)) {\n    console.warn('Invalid font style specified: \"' + style + '\"');\n    style = undefined;\n  }\n\n  const font = {\n    family: valueOrDefault(options.family, fallback.family),\n    lineHeight: toLineHeight(valueOrDefault(options.lineHeight, fallback.lineHeight), size),\n    size,\n    style,\n    weight: valueOrDefault(options.weight, fallback.weight),\n    string: ''\n  };\n\n  font.string = toFontString(font);\n  return font;\n}\n\n/**\n * Evaluates the given `inputs` sequentially and returns the first defined value.\n * @param inputs - An array of values, falling back to the last value.\n * @param context - If defined and the current value is a function, the value\n * is called with `context` as first argument and the result becomes the new input.\n * @param index - If defined and the current value is an array, the value\n * at `index` become the new input.\n * @param info - object to return information about resolution in\n * @param info.cacheable - Will be set to `false` if option is not cacheable.\n * @since 2.7.0\n */\nexport function resolve(inputs: Array<unknown>, context?: object, index?: number, info?: { cacheable: boolean }) {\n  let cacheable = true;\n  let i: number, ilen: number, value: unknown;\n\n  for (i = 0, ilen = inputs.length; i < ilen; ++i) {\n    value = inputs[i];\n    if (value === undefined) {\n      continue;\n    }\n    if (context !== undefined && typeof value === 'function') {\n      value = value(context);\n      cacheable = false;\n    }\n    if (index !== undefined && isArray(value)) {\n      value = value[index % value.length];\n      cacheable = false;\n    }\n    if (value !== undefined) {\n      if (info && !cacheable) {\n        info.cacheable = false;\n      }\n      return value;\n    }\n  }\n}\n\n/**\n * @param minmax\n * @param grace\n * @param beginAtZero\n * @private\n */\nexport function _addGrace(minmax: { min: number; max: number; }, grace: number | string, beginAtZero: boolean) {\n  const {min, max} = minmax;\n  const change = toDimension(grace, (max - min) / 2);\n  const keepZero = (value: number, add: number) => beginAtZero && value === 0 ? 0 : value + add;\n  return {\n    min: keepZero(min, -Math.abs(change)),\n    max: keepZero(max, change)\n  };\n}\n\n/**\n * Create a context inheriting parentContext\n * @param parentContext\n * @param context\n * @returns\n */\nexport function createContext<T extends object>(parentContext: null, context: T): T;\nexport function createContext<T extends object, P extends T>(parentContext: P, context: T): P & T;\nexport function createContext(parentContext: object, context: object) {\n  return Object.assign(Object.create(parentContext), context);\n}\n", "/* eslint-disable @typescript-eslint/no-use-before-define */\nimport type {AnyObject} from '../types/basic.js';\nimport type {ChartMeta} from '../types/index.js';\nimport type {\n  ResolverObjectKey,\n  ResolverCache,\n  ResolverProxy,\n  DescriptorDefaults,\n  Descriptor,\n  ContextCache,\n  ContextProxy\n} from './helpers.config.types.js';\nimport {isArray, isFunction, isObject, resolveObjectKey, _capitalize} from './helpers.core.js';\n\nexport * from './helpers.config.types.js';\n\n/**\n * Creates a Proxy for resolving raw values for options.\n * @param scopes - The option scopes to look for values, in resolution order\n * @param prefixes - The prefixes for values, in resolution order.\n * @param rootScopes - The root option scopes\n * @param fallback - Parent scopes fallback\n * @param getTarget - callback for getting the target for changed values\n * @returns Proxy\n * @private\n */\nexport function _createResolver<\n  T extends AnyObject[] = AnyObject[],\n  R extends AnyObject[] = T\n>(\n  scopes: T,\n  prefixes = [''],\n  rootScopes?: R,\n  fallback?: ResolverObjectKey,\n  getTarget = () => scopes[0]\n) {\n  const finalRootScopes = rootScopes || scopes;\n  if (typeof fallback === 'undefined') {\n    fallback = _resolve('_fallback', scopes);\n  }\n  const cache: ResolverCache<T, R> = {\n    [Symbol.toStringTag]: 'Object',\n    _cacheable: true,\n    _scopes: scopes,\n    _rootScopes: finalRootScopes,\n    _fallback: fallback,\n    _getTarget: getTarget,\n    override: (scope: AnyObject) => _createResolver([scope, ...scopes], prefixes, finalRootScopes, fallback),\n  };\n  return new Proxy(cache, {\n    /**\n     * A trap for the delete operator.\n     */\n    deleteProperty(target, prop: string) {\n      delete target[prop]; // remove from cache\n      delete target._keys; // remove cached keys\n      delete scopes[0][prop]; // remove from top level scope\n      return true;\n    },\n\n    /**\n     * A trap for getting property values.\n     */\n    get(target, prop: string) {\n      return _cached(target, prop,\n        () => _resolveWithPrefixes(prop, prefixes, scopes, target));\n    },\n\n    /**\n     * A trap for Object.getOwnPropertyDescriptor.\n     * Also used by Object.hasOwnProperty.\n     */\n    getOwnPropertyDescriptor(target, prop) {\n      return Reflect.getOwnPropertyDescriptor(target._scopes[0], prop);\n    },\n\n    /**\n     * A trap for Object.getPrototypeOf.\n     */\n    getPrototypeOf() {\n      return Reflect.getPrototypeOf(scopes[0]);\n    },\n\n    /**\n     * A trap for the in operator.\n     */\n    has(target, prop: string) {\n      return getKeysFromAllScopes(target).includes(prop);\n    },\n\n    /**\n     * A trap for Object.getOwnPropertyNames and Object.getOwnPropertySymbols.\n     */\n    ownKeys(target) {\n      return getKeysFromAllScopes(target);\n    },\n\n    /**\n     * A trap for setting property values.\n     */\n    set(target, prop: string, value) {\n      const storage = target._storage || (target._storage = getTarget());\n      target[prop] = storage[prop] = value; // set to top level scope + cache\n      delete target._keys; // remove cached keys\n      return true;\n    }\n  }) as ResolverProxy<T, R>;\n}\n\n/**\n * Returns an Proxy for resolving option values with context.\n * @param proxy - The Proxy returned by `_createResolver`\n * @param context - Context object for scriptable/indexable options\n * @param subProxy - The proxy provided for scriptable options\n * @param descriptorDefaults - Defaults for descriptors\n * @private\n */\nexport function _attachContext<\n  T extends AnyObject[] = AnyObject[],\n  R extends AnyObject[] = T\n>(\n  proxy: ResolverProxy<T, R>,\n  context: AnyObject,\n  subProxy?: ResolverProxy<T, R>,\n  descriptorDefaults?: DescriptorDefaults\n) {\n  const cache: ContextCache<T, R> = {\n    _cacheable: false,\n    _proxy: proxy,\n    _context: context,\n    _subProxy: subProxy,\n    _stack: new Set(),\n    _descriptors: _descriptors(proxy, descriptorDefaults),\n    setContext: (ctx: AnyObject) => _attachContext(proxy, ctx, subProxy, descriptorDefaults),\n    override: (scope: AnyObject) => _attachContext(proxy.override(scope), context, subProxy, descriptorDefaults)\n  };\n  return new Proxy(cache, {\n    /**\n     * A trap for the delete operator.\n     */\n    deleteProperty(target, prop) {\n      delete target[prop]; // remove from cache\n      delete proxy[prop]; // remove from proxy\n      return true;\n    },\n\n    /**\n     * A trap for getting property values.\n     */\n    get(target, prop: string, receiver) {\n      return _cached(target, prop,\n        () => _resolveWithContext(target, prop, receiver));\n    },\n\n    /**\n     * A trap for Object.getOwnPropertyDescriptor.\n     * Also used by Object.hasOwnProperty.\n     */\n    getOwnPropertyDescriptor(target, prop) {\n      return target._descriptors.allKeys\n        ? Reflect.has(proxy, prop) ? {enumerable: true, configurable: true} : undefined\n        : Reflect.getOwnPropertyDescriptor(proxy, prop);\n    },\n\n    /**\n     * A trap for Object.getPrototypeOf.\n     */\n    getPrototypeOf() {\n      return Reflect.getPrototypeOf(proxy);\n    },\n\n    /**\n     * A trap for the in operator.\n     */\n    has(target, prop) {\n      return Reflect.has(proxy, prop);\n    },\n\n    /**\n     * A trap for Object.getOwnPropertyNames and Object.getOwnPropertySymbols.\n     */\n    ownKeys() {\n      return Reflect.ownKeys(proxy);\n    },\n\n    /**\n     * A trap for setting property values.\n     */\n    set(target, prop, value) {\n      proxy[prop] = value; // set to proxy\n      delete target[prop]; // remove from cache\n      return true;\n    }\n  }) as ContextProxy<T, R>;\n}\n\n/**\n * @private\n */\nexport function _descriptors(\n  proxy: ResolverCache,\n  defaults: DescriptorDefaults = {scriptable: true, indexable: true}\n): Descriptor {\n  const {_scriptable = defaults.scriptable, _indexable = defaults.indexable, _allKeys = defaults.allKeys} = proxy;\n  return {\n    allKeys: _allKeys,\n    scriptable: _scriptable,\n    indexable: _indexable,\n    isScriptable: isFunction(_scriptable) ? _scriptable : () => _scriptable,\n    isIndexable: isFunction(_indexable) ? _indexable : () => _indexable\n  };\n}\n\nconst readKey = (prefix: string, name: string) => prefix ? prefix + _capitalize(name) : name;\nconst needsSubResolver = (prop: string, value: unknown) => isObject(value) && prop !== 'adapters' &&\n  (Object.getPrototypeOf(value) === null || value.constructor === Object);\n\nfunction _cached(\n  target: AnyObject,\n  prop: string,\n  resolve: () => unknown\n) {\n  if (Object.prototype.hasOwnProperty.call(target, prop)) {\n    return target[prop];\n  }\n\n  const value = resolve();\n  // cache the resolved value\n  target[prop] = value;\n  return value;\n}\n\nfunction _resolveWithContext(\n  target: ContextCache,\n  prop: string,\n  receiver: AnyObject\n) {\n  const {_proxy, _context, _subProxy, _descriptors: descriptors} = target;\n  let value = _proxy[prop]; // resolve from proxy\n\n  // resolve with context\n  if (isFunction(value) && descriptors.isScriptable(prop)) {\n    value = _resolveScriptable(prop, value, target, receiver);\n  }\n  if (isArray(value) && value.length) {\n    value = _resolveArray(prop, value, target, descriptors.isIndexable);\n  }\n  if (needsSubResolver(prop, value)) {\n    // if the resolved value is an object, create a sub resolver for it\n    value = _attachContext(value, _context, _subProxy && _subProxy[prop], descriptors);\n  }\n  return value;\n}\n\nfunction _resolveScriptable(\n  prop: string,\n  getValue: (ctx: AnyObject, sub: AnyObject) => unknown,\n  target: ContextCache,\n  receiver: AnyObject\n) {\n  const {_proxy, _context, _subProxy, _stack} = target;\n  if (_stack.has(prop)) {\n    throw new Error('Recursion detected: ' + Array.from(_stack).join('->') + '->' + prop);\n  }\n  _stack.add(prop);\n  let value = getValue(_context, _subProxy || receiver);\n  _stack.delete(prop);\n  if (needsSubResolver(prop, value)) {\n    // When scriptable option returns an object, create a resolver on that.\n    value = createSubResolver(_proxy._scopes, _proxy, prop, value);\n  }\n  return value;\n}\n\nfunction _resolveArray(\n  prop: string,\n  value: unknown[],\n  target: ContextCache,\n  isIndexable: (key: string) => boolean\n) {\n  const {_proxy, _context, _subProxy, _descriptors: descriptors} = target;\n\n  if (typeof _context.index !== 'undefined' && isIndexable(prop)) {\n    return value[_context.index % value.length];\n  } else if (isObject(value[0])) {\n    // Array of objects, return array or resolvers\n    const arr = value;\n    const scopes = _proxy._scopes.filter(s => s !== arr);\n    value = [];\n    for (const item of arr) {\n      const resolver = createSubResolver(scopes, _proxy, prop, item);\n      value.push(_attachContext(resolver, _context, _subProxy && _subProxy[prop], descriptors));\n    }\n  }\n  return value;\n}\n\nfunction resolveFallback(\n  fallback: ResolverObjectKey | ((prop: ResolverObjectKey, value: unknown) => ResolverObjectKey),\n  prop: ResolverObjectKey,\n  value: unknown\n) {\n  return isFunction(fallback) ? fallback(prop, value) : fallback;\n}\n\nconst getScope = (key: ResolverObjectKey, parent: AnyObject) => key === true ? parent\n  : typeof key === 'string' ? resolveObjectKey(parent, key) : undefined;\n\nfunction addScopes(\n  set: Set<AnyObject>,\n  parentScopes: AnyObject[],\n  key: ResolverObjectKey,\n  parentFallback: ResolverObjectKey,\n  value: unknown\n) {\n  for (const parent of parentScopes) {\n    const scope = getScope(key, parent);\n    if (scope) {\n      set.add(scope);\n      const fallback = resolveFallback(scope._fallback, key, value);\n      if (typeof fallback !== 'undefined' && fallback !== key && fallback !== parentFallback) {\n        // When we reach the descriptor that defines a new _fallback, return that.\n        // The fallback will resume to that new scope.\n        return fallback;\n      }\n    } else if (scope === false && typeof parentFallback !== 'undefined' && key !== parentFallback) {\n      // Fallback to `false` results to `false`, when falling back to different key.\n      // For example `interaction` from `hover` or `plugins.tooltip` and `animation` from `animations`\n      return null;\n    }\n  }\n  return false;\n}\n\nfunction createSubResolver(\n  parentScopes: AnyObject[],\n  resolver: ResolverCache,\n  prop: ResolverObjectKey,\n  value: unknown\n) {\n  const rootScopes = resolver._rootScopes;\n  const fallback = resolveFallback(resolver._fallback, prop, value);\n  const allScopes = [...parentScopes, ...rootScopes];\n  const set = new Set<AnyObject>();\n  set.add(value);\n  let key = addScopesFromKey(set, allScopes, prop, fallback || prop, value);\n  if (key === null) {\n    return false;\n  }\n  if (typeof fallback !== 'undefined' && fallback !== prop) {\n    key = addScopesFromKey(set, allScopes, fallback, key, value);\n    if (key === null) {\n      return false;\n    }\n  }\n  return _createResolver(Array.from(set), [''], rootScopes, fallback,\n    () => subGetTarget(resolver, prop as string, value));\n}\n\nfunction addScopesFromKey(\n  set: Set<AnyObject>,\n  allScopes: AnyObject[],\n  key: ResolverObjectKey,\n  fallback: ResolverObjectKey,\n  item: unknown\n) {\n  while (key) {\n    key = addScopes(set, allScopes, key, fallback, item);\n  }\n  return key;\n}\n\nfunction subGetTarget(\n  resolver: ResolverCache,\n  prop: string,\n  value: unknown\n) {\n  const parent = resolver._getTarget();\n  if (!(prop in parent)) {\n    parent[prop] = {};\n  }\n  const target = parent[prop];\n  if (isArray(target) && isObject(value)) {\n    // For array of objects, the object is used to store updated values\n    return value;\n  }\n  return target || {};\n}\n\nfunction _resolveWithPrefixes(\n  prop: string,\n  prefixes: string[],\n  scopes: AnyObject[],\n  proxy: ResolverProxy\n) {\n  let value: unknown;\n  for (const prefix of prefixes) {\n    value = _resolve(readKey(prefix, prop), scopes);\n    if (typeof value !== 'undefined') {\n      return needsSubResolver(prop, value)\n        ? createSubResolver(scopes, proxy, prop, value)\n        : value;\n    }\n  }\n}\n\nfunction _resolve(key: string, scopes: AnyObject[]) {\n  for (const scope of scopes) {\n    if (!scope) {\n      continue;\n    }\n    const value = scope[key];\n    if (typeof value !== 'undefined') {\n      return value;\n    }\n  }\n}\n\nfunction getKeysFromAllScopes(target: ResolverCache) {\n  let keys = target._keys;\n  if (!keys) {\n    keys = target._keys = resolveKeysFromAllScopes(target._scopes);\n  }\n  return keys;\n}\n\nfunction resolveKeysFromAllScopes(scopes: AnyObject[]) {\n  const set = new Set<string>();\n  for (const scope of scopes) {\n    for (const key of Object.keys(scope).filter(k => !k.startsWith('_'))) {\n      set.add(key);\n    }\n  }\n  return Array.from(set);\n}\n\nexport function _parseObjectDataRadialScale(\n  meta: ChartMeta<'line' | 'scatter'>,\n  data: AnyObject[],\n  start: number,\n  count: number\n) {\n  const {iScale} = meta;\n  const {key = 'r'} = this._parsing;\n  const parsed = new Array<{r: unknown}>(count);\n  let i: number, ilen: number, index: number, item: AnyObject;\n\n  for (i = 0, ilen = count; i < ilen; ++i) {\n    index = i + start;\n    item = data[index];\n    parsed[i] = {\n      r: iScale.parse(resolveObjectKey(item, key), index)\n    };\n  }\n  return parsed;\n}\n", "import {almostEquals, distanceBetweenPoints, sign} from './helpers.math.js';\nimport {_isPointInArea} from './helpers.canvas.js';\nimport type {ChartArea} from '../types/index.js';\nimport type {SplinePoint} from '../types/geometric.js';\n\nconst EPSILON = Number.EPSILON || 1e-14;\n\ntype OptionalSplinePoint = SplinePoint | false\nconst getPoint = (points: SplinePoint[], i: number): OptionalSplinePoint => i < points.length && !points[i].skip && points[i];\nconst getValueAxis = (indexAxis: 'x' | 'y') => indexAxis === 'x' ? 'y' : 'x';\n\nexport function splineCurve(\n  firstPoint: SplinePoint,\n  middlePoint: SplinePoint,\n  afterPoint: SplinePoint,\n  t: number\n): {\n    previous: SplinePoint\n    next: SplinePoint\n  } {\n  // Props to <PERSON> at scaled innovation for his post on splining between points\n  // http://scaledinnovation.com/analytics/splines/aboutSplines.html\n\n  // This function must also respect \"skipped\" points\n\n  const previous = firstPoint.skip ? middlePoint : firstPoint;\n  const current = middlePoint;\n  const next = afterPoint.skip ? middlePoint : afterPoint;\n  const d01 = distanceBetweenPoints(current, previous);\n  const d12 = distanceBetweenPoints(next, current);\n\n  let s01 = d01 / (d01 + d12);\n  let s12 = d12 / (d01 + d12);\n\n  // If all points are the same, s01 & s02 will be inf\n  s01 = isNaN(s01) ? 0 : s01;\n  s12 = isNaN(s12) ? 0 : s12;\n\n  const fa = t * s01; // scaling factor for triangle Ta\n  const fb = t * s12;\n\n  return {\n    previous: {\n      x: current.x - fa * (next.x - previous.x),\n      y: current.y - fa * (next.y - previous.y)\n    },\n    next: {\n      x: current.x + fb * (next.x - previous.x),\n      y: current.y + fb * (next.y - previous.y)\n    }\n  };\n}\n\n/**\n * Adjust tangents to ensure monotonic properties\n */\nfunction monotoneAdjust(points: SplinePoint[], deltaK: number[], mK: number[]) {\n  const pointsLen = points.length;\n\n  let alphaK: number, betaK: number, tauK: number, squaredMagnitude: number, pointCurrent: OptionalSplinePoint;\n  let pointAfter = getPoint(points, 0);\n  for (let i = 0; i < pointsLen - 1; ++i) {\n    pointCurrent = pointAfter;\n    pointAfter = getPoint(points, i + 1);\n    if (!pointCurrent || !pointAfter) {\n      continue;\n    }\n\n    if (almostEquals(deltaK[i], 0, EPSILON)) {\n      mK[i] = mK[i + 1] = 0;\n      continue;\n    }\n\n    alphaK = mK[i] / deltaK[i];\n    betaK = mK[i + 1] / deltaK[i];\n    squaredMagnitude = Math.pow(alphaK, 2) + Math.pow(betaK, 2);\n    if (squaredMagnitude <= 9) {\n      continue;\n    }\n\n    tauK = 3 / Math.sqrt(squaredMagnitude);\n    mK[i] = alphaK * tauK * deltaK[i];\n    mK[i + 1] = betaK * tauK * deltaK[i];\n  }\n}\n\nfunction monotoneCompute(points: SplinePoint[], mK: number[], indexAxis: 'x' | 'y' = 'x') {\n  const valueAxis = getValueAxis(indexAxis);\n  const pointsLen = points.length;\n  let delta: number, pointBefore: OptionalSplinePoint, pointCurrent: OptionalSplinePoint;\n  let pointAfter = getPoint(points, 0);\n\n  for (let i = 0; i < pointsLen; ++i) {\n    pointBefore = pointCurrent;\n    pointCurrent = pointAfter;\n    pointAfter = getPoint(points, i + 1);\n    if (!pointCurrent) {\n      continue;\n    }\n\n    const iPixel = pointCurrent[indexAxis];\n    const vPixel = pointCurrent[valueAxis];\n    if (pointBefore) {\n      delta = (iPixel - pointBefore[indexAxis]) / 3;\n      pointCurrent[`cp1${indexAxis}`] = iPixel - delta;\n      pointCurrent[`cp1${valueAxis}`] = vPixel - delta * mK[i];\n    }\n    if (pointAfter) {\n      delta = (pointAfter[indexAxis] - iPixel) / 3;\n      pointCurrent[`cp2${indexAxis}`] = iPixel + delta;\n      pointCurrent[`cp2${valueAxis}`] = vPixel + delta * mK[i];\n    }\n  }\n}\n\n/**\n * This function calculates Bézier control points in a similar way than |splineCurve|,\n * but preserves monotonicity of the provided data and ensures no local extremums are added\n * between the dataset discrete points due to the interpolation.\n * See : https://en.wikipedia.org/wiki/Monotone_cubic_interpolation\n */\nexport function splineCurveMonotone(points: SplinePoint[], indexAxis: 'x' | 'y' = 'x') {\n  const valueAxis = getValueAxis(indexAxis);\n  const pointsLen = points.length;\n  const deltaK: number[] = Array(pointsLen).fill(0);\n  const mK: number[] = Array(pointsLen);\n\n  // Calculate slopes (deltaK) and initialize tangents (mK)\n  let i, pointBefore: OptionalSplinePoint, pointCurrent: OptionalSplinePoint;\n  let pointAfter = getPoint(points, 0);\n\n  for (i = 0; i < pointsLen; ++i) {\n    pointBefore = pointCurrent;\n    pointCurrent = pointAfter;\n    pointAfter = getPoint(points, i + 1);\n    if (!pointCurrent) {\n      continue;\n    }\n\n    if (pointAfter) {\n      const slopeDelta = pointAfter[indexAxis] - pointCurrent[indexAxis];\n\n      // In the case of two points that appear at the same x pixel, slopeDeltaX is 0\n      deltaK[i] = slopeDelta !== 0 ? (pointAfter[valueAxis] - pointCurrent[valueAxis]) / slopeDelta : 0;\n    }\n    mK[i] = !pointBefore ? deltaK[i]\n      : !pointAfter ? deltaK[i - 1]\n        : (sign(deltaK[i - 1]) !== sign(deltaK[i])) ? 0\n          : (deltaK[i - 1] + deltaK[i]) / 2;\n  }\n\n  monotoneAdjust(points, deltaK, mK);\n\n  monotoneCompute(points, mK, indexAxis);\n}\n\nfunction capControlPoint(pt: number, min: number, max: number) {\n  return Math.max(Math.min(pt, max), min);\n}\n\nfunction capBezierPoints(points: SplinePoint[], area: ChartArea) {\n  let i, ilen, point, inArea, inAreaPrev;\n  let inAreaNext = _isPointInArea(points[0], area);\n  for (i = 0, ilen = points.length; i < ilen; ++i) {\n    inAreaPrev = inArea;\n    inArea = inAreaNext;\n    inAreaNext = i < ilen - 1 && _isPointInArea(points[i + 1], area);\n    if (!inArea) {\n      continue;\n    }\n    point = points[i];\n    if (inAreaPrev) {\n      point.cp1x = capControlPoint(point.cp1x, area.left, area.right);\n      point.cp1y = capControlPoint(point.cp1y, area.top, area.bottom);\n    }\n    if (inAreaNext) {\n      point.cp2x = capControlPoint(point.cp2x, area.left, area.right);\n      point.cp2y = capControlPoint(point.cp2y, area.top, area.bottom);\n    }\n  }\n}\n\n/**\n * @private\n */\nexport function _updateBezierControlPoints(\n  points: SplinePoint[],\n  options,\n  area: ChartArea,\n  loop: boolean,\n  indexAxis: 'x' | 'y'\n) {\n  let i: number, ilen: number, point: SplinePoint, controlPoints: ReturnType<typeof splineCurve>;\n\n  // Only consider points that are drawn in case the spanGaps option is used\n  if (options.spanGaps) {\n    points = points.filter((pt) => !pt.skip);\n  }\n\n  if (options.cubicInterpolationMode === 'monotone') {\n    splineCurveMonotone(points, indexAxis);\n  } else {\n    let prev = loop ? points[points.length - 1] : points[0];\n    for (i = 0, ilen = points.length; i < ilen; ++i) {\n      point = points[i];\n      controlPoints = splineCurve(\n        prev,\n        point,\n        points[Math.min(i + 1, ilen - (loop ? 0 : 1)) % ilen],\n        options.tension\n      );\n      point.cp1x = controlPoints.previous.x;\n      point.cp1y = controlPoints.previous.y;\n      point.cp2x = controlPoints.next.x;\n      point.cp2y = controlPoints.next.y;\n      prev = point;\n    }\n  }\n\n  if (options.capBezierPoints) {\n    capBezierPoints(points, area);\n  }\n}\n", "import type {ChartArea, Scale} from '../types/index.js';\nimport type Chart from '../core/core.controller.js';\nimport type {ChartEvent} from '../types.js';\nimport {INFINITY} from './helpers.math.js';\n\n/**\n * Note: typedefs are auto-exported, so use a made-up `dom` namespace where\n * necessary to avoid duplicates with `export * from './helpers`; see\n * https://github.com/microsoft/TypeScript/issues/46011\n * @typedef { import('../core/core.controller.js').default } dom.Chart\n * @typedef { import('../../types').ChartEvent } ChartEvent\n */\n\n/**\n * @private\n */\nexport function _isDomSupported(): boolean {\n  return typeof window !== 'undefined' && typeof document !== 'undefined';\n}\n\n/**\n * @private\n */\nexport function _getParentNode(domNode: HTMLCanvasElement): HTMLCanvasElement {\n  let parent = domNode.parentNode;\n  if (parent && parent.toString() === '[object ShadowRoot]') {\n    parent = (parent as ShadowRoot).host;\n  }\n  return parent as HTMLCanvasElement;\n}\n\n/**\n * convert max-width/max-height values that may be percentages into a number\n * @private\n */\n\nfunction parseMaxStyle(styleValue: string | number, node: HTMLElement, parentProperty: string) {\n  let valueInPixels: number;\n  if (typeof styleValue === 'string') {\n    valueInPixels = parseInt(styleValue, 10);\n\n    if (styleValue.indexOf('%') !== -1) {\n      // percentage * size in dimension\n      valueInPixels = (valueInPixels / 100) * node.parentNode[parentProperty];\n    }\n  } else {\n    valueInPixels = styleValue;\n  }\n\n  return valueInPixels;\n}\n\nconst getComputedStyle = (element: HTMLElement): CSSStyleDeclaration =>\n  element.ownerDocument.defaultView.getComputedStyle(element, null);\n\nexport function getStyle(el: HTMLElement, property: string): string {\n  return getComputedStyle(el).getPropertyValue(property);\n}\n\nconst positions = ['top', 'right', 'bottom', 'left'];\nfunction getPositionedStyle(styles: CSSStyleDeclaration, style: string, suffix?: string): ChartArea {\n  const result = {} as ChartArea;\n  suffix = suffix ? '-' + suffix : '';\n  for (let i = 0; i < 4; i++) {\n    const pos = positions[i];\n    result[pos] = parseFloat(styles[style + '-' + pos + suffix]) || 0;\n  }\n  result.width = result.left + result.right;\n  result.height = result.top + result.bottom;\n  return result;\n}\n\nconst useOffsetPos = (x: number, y: number, target: HTMLElement | EventTarget) =>\n  (x > 0 || y > 0) && (!target || !(target as HTMLElement).shadowRoot);\n\n/**\n * @param e\n * @param canvas\n * @returns Canvas position\n */\nfunction getCanvasPosition(\n  e: Event | TouchEvent | MouseEvent,\n  canvas: HTMLCanvasElement\n): {\n    x: number;\n    y: number;\n    box: boolean;\n  } {\n  const touches = (e as TouchEvent).touches;\n  const source = (touches && touches.length ? touches[0] : e) as MouseEvent;\n  const {offsetX, offsetY} = source as MouseEvent;\n  let box = false;\n  let x, y;\n  if (useOffsetPos(offsetX, offsetY, e.target)) {\n    x = offsetX;\n    y = offsetY;\n  } else {\n    const rect = canvas.getBoundingClientRect();\n    x = source.clientX - rect.left;\n    y = source.clientY - rect.top;\n    box = true;\n  }\n  return {x, y, box};\n}\n\n/**\n * Gets an event's x, y coordinates, relative to the chart area\n * @param event\n * @param chart\n * @returns x and y coordinates of the event\n */\n\nexport function getRelativePosition(\n  event: Event | ChartEvent | TouchEvent | MouseEvent,\n  chart: Chart\n): { x: number; y: number } {\n  if ('native' in event) {\n    return event;\n  }\n\n  const {canvas, currentDevicePixelRatio} = chart;\n  const style = getComputedStyle(canvas);\n  const borderBox = style.boxSizing === 'border-box';\n  const paddings = getPositionedStyle(style, 'padding');\n  const borders = getPositionedStyle(style, 'border', 'width');\n  const {x, y, box} = getCanvasPosition(event, canvas);\n  const xOffset = paddings.left + (box && borders.left);\n  const yOffset = paddings.top + (box && borders.top);\n\n  let {width, height} = chart;\n  if (borderBox) {\n    width -= paddings.width + borders.width;\n    height -= paddings.height + borders.height;\n  }\n  return {\n    x: Math.round((x - xOffset) / width * canvas.width / currentDevicePixelRatio),\n    y: Math.round((y - yOffset) / height * canvas.height / currentDevicePixelRatio)\n  };\n}\n\nfunction getContainerSize(canvas: HTMLCanvasElement, width: number, height: number): Partial<Scale> {\n  let maxWidth: number, maxHeight: number;\n\n  if (width === undefined || height === undefined) {\n    const container = _getParentNode(canvas);\n    if (!container) {\n      width = canvas.clientWidth;\n      height = canvas.clientHeight;\n    } else {\n      const rect = container.getBoundingClientRect(); // this is the border box of the container\n      const containerStyle = getComputedStyle(container);\n      const containerBorder = getPositionedStyle(containerStyle, 'border', 'width');\n      const containerPadding = getPositionedStyle(containerStyle, 'padding');\n      width = rect.width - containerPadding.width - containerBorder.width;\n      height = rect.height - containerPadding.height - containerBorder.height;\n      maxWidth = parseMaxStyle(containerStyle.maxWidth, container, 'clientWidth');\n      maxHeight = parseMaxStyle(containerStyle.maxHeight, container, 'clientHeight');\n    }\n  }\n  return {\n    width,\n    height,\n    maxWidth: maxWidth || INFINITY,\n    maxHeight: maxHeight || INFINITY\n  };\n}\n\nconst round1 = (v: number) => Math.round(v * 10) / 10;\n\n// eslint-disable-next-line complexity\nexport function getMaximumSize(\n  canvas: HTMLCanvasElement,\n  bbWidth?: number,\n  bbHeight?: number,\n  aspectRatio?: number\n): { width: number; height: number } {\n  const style = getComputedStyle(canvas);\n  const margins = getPositionedStyle(style, 'margin');\n  const maxWidth = parseMaxStyle(style.maxWidth, canvas, 'clientWidth') || INFINITY;\n  const maxHeight = parseMaxStyle(style.maxHeight, canvas, 'clientHeight') || INFINITY;\n  const containerSize = getContainerSize(canvas, bbWidth, bbHeight);\n  let {width, height} = containerSize;\n\n  if (style.boxSizing === 'content-box') {\n    const borders = getPositionedStyle(style, 'border', 'width');\n    const paddings = getPositionedStyle(style, 'padding');\n    width -= paddings.width + borders.width;\n    height -= paddings.height + borders.height;\n  }\n  width = Math.max(0, width - margins.width);\n  height = Math.max(0, aspectRatio ? width / aspectRatio : height - margins.height);\n  width = round1(Math.min(width, maxWidth, containerSize.maxWidth));\n  height = round1(Math.min(height, maxHeight, containerSize.maxHeight));\n  if (width && !height) {\n    // https://github.com/chartjs/Chart.js/issues/4659\n    // If the canvas has width, but no height, default to aspectRatio of 2 (canvas default)\n    height = round1(width / 2);\n  }\n\n  const maintainHeight = bbWidth !== undefined || bbHeight !== undefined;\n\n  if (maintainHeight && aspectRatio && containerSize.height && height > containerSize.height) {\n    height = containerSize.height;\n    width = round1(Math.floor(height * aspectRatio));\n  }\n\n  return {width, height};\n}\n\n/**\n * @param chart\n * @param forceRatio\n * @param forceStyle\n * @returns True if the canvas context size or transformation has changed.\n */\nexport function retinaScale(\n  chart: Chart,\n  forceRatio: number,\n  forceStyle?: boolean\n): boolean | void {\n  const pixelRatio = forceRatio || 1;\n  const deviceHeight = Math.floor(chart.height * pixelRatio);\n  const deviceWidth = Math.floor(chart.width * pixelRatio);\n\n  chart.height = Math.floor(chart.height);\n  chart.width = Math.floor(chart.width);\n\n  const canvas = chart.canvas;\n\n  // If no style has been set on the canvas, the render size is used as display size,\n  // making the chart visually bigger, so let's enforce it to the \"correct\" values.\n  // See https://github.com/chartjs/Chart.js/issues/3575\n  if (canvas.style && (forceStyle || (!canvas.style.height && !canvas.style.width))) {\n    canvas.style.height = `${chart.height}px`;\n    canvas.style.width = `${chart.width}px`;\n  }\n\n  if (chart.currentDevicePixelRatio !== pixelRatio\n      || canvas.height !== deviceHeight\n      || canvas.width !== deviceWidth) {\n    chart.currentDevicePixelRatio = pixelRatio;\n    canvas.height = deviceHeight;\n    canvas.width = deviceWidth;\n    chart.ctx.setTransform(pixelRatio, 0, 0, pixelRatio, 0, 0);\n    return true;\n  }\n  return false;\n}\n\n/**\n * Detects support for options object argument in addEventListener.\n * https://developer.mozilla.org/en-US/docs/Web/API/EventTarget/addEventListener#Safely_detecting_option_support\n * @private\n */\nexport const supportsEventListenerOptions = (function() {\n  let passiveSupported = false;\n  try {\n    const options = {\n      get passive() { // This function will be called when the browser attempts to access the passive property.\n        passiveSupported = true;\n        return false;\n      }\n    } as EventListenerOptions;\n\n    window.addEventListener('test', null, options);\n    window.removeEventListener('test', null, options);\n  } catch (e) {\n    // continue regardless of error\n  }\n  return passiveSupported;\n}());\n\n/**\n * The \"used\" size is the final value of a dimension property after all calculations have\n * been performed. This method uses the computed style of `element` but returns undefined\n * if the computed style is not expressed in pixels. That can happen in some cases where\n * `element` has a size relative to its parent and this last one is not yet displayed,\n * for example because of `display: none` on a parent node.\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/used_value\n * @returns Size in pixels or undefined if unknown.\n */\n\nexport function readUsedSize(\n  element: HTMLElement,\n  property: 'width' | 'height'\n): number | undefined {\n  const value = getStyle(element, property);\n  const matches = value && value.match(/^(\\d+)(\\.\\d+)?px$/);\n  return matches ? +matches[1] : undefined;\n}\n", "import type {Point, SplinePoint} from '../types/geometric.js';\n\n/**\n * @private\n */\nexport function _pointInLine(p1: Point, p2: Point, t: number, mode?) { // eslint-disable-line @typescript-eslint/no-unused-vars\n  return {\n    x: p1.x + t * (p2.x - p1.x),\n    y: p1.y + t * (p2.y - p1.y)\n  };\n}\n\n/**\n * @private\n */\nexport function _steppedInterpolation(\n  p1: Point,\n  p2: Point,\n  t: number, mode: 'middle' | 'after' | unknown\n) {\n  return {\n    x: p1.x + t * (p2.x - p1.x),\n    y: mode === 'middle' ? t < 0.5 ? p1.y : p2.y\n      : mode === 'after' ? t < 1 ? p1.y : p2.y\n        : t > 0 ? p2.y : p1.y\n  };\n}\n\n/**\n * @private\n */\nexport function _bezierInterpolation(p1: SplinePoint, p2: SplinePoint, t: number, mode?) { // eslint-disable-line @typescript-eslint/no-unused-vars\n  const cp1 = {x: p1.cp2x, y: p1.cp2y};\n  const cp2 = {x: p2.cp1x, y: p2.cp1y};\n  const a = _pointInLine(p1, cp1, t);\n  const b = _pointInLine(cp1, cp2, t);\n  const c = _pointInLine(cp2, p2, t);\n  const d = _pointInLine(a, b, t);\n  const e = _pointInLine(b, c, t);\n  return _pointInLine(d, e, t);\n}\n", "export interface RTLAdapter {\n  x(x: number): number;\n  setWidth(w: number): void;\n  textAlign(align: 'center' | 'left' | 'right'): 'center' | 'left' | 'right';\n  xPlus(x: number, value: number): number;\n  leftForLtr(x: number, itemWidth: number): number;\n}\n\nconst getRightToLeftAdapter = function(rectX: number, width: number): RTLAdapter {\n  return {\n    x(x) {\n      return rectX + rectX + width - x;\n    },\n    setWidth(w) {\n      width = w;\n    },\n    textAlign(align) {\n      if (align === 'center') {\n        return align;\n      }\n      return align === 'right' ? 'left' : 'right';\n    },\n    xPlus(x, value) {\n      return x - value;\n    },\n    leftForLtr(x, itemWidth) {\n      return x - itemWidth;\n    },\n  };\n};\n\nconst getLeftToRightAdapter = function(): RTLAdapter {\n  return {\n    x(x) {\n      return x;\n    },\n    setWidth(w) { // eslint-disable-line no-unused-vars\n    },\n    textAlign(align) {\n      return align;\n    },\n    xPlus(x, value) {\n      return x + value;\n    },\n    leftForLtr(x, _itemWidth) { // eslint-disable-line @typescript-eslint/no-unused-vars\n      return x;\n    },\n  };\n};\n\nexport function getRtlAdapter(rtl: boolean, rectX: number, width: number) {\n  return rtl ? getRightToLeftAdapter(rectX, width) : getLeftToRightAdapter();\n}\n\nexport function overrideTextDirection(ctx: CanvasRenderingContext2D, direction: 'ltr' | 'rtl') {\n  let style: CSSStyleDeclaration, original: [string, string];\n  if (direction === 'ltr' || direction === 'rtl') {\n    style = ctx.canvas.style;\n    original = [\n      style.getPropertyValue('direction'),\n      style.getPropertyPriority('direction'),\n    ];\n\n    style.setProperty('direction', direction, 'important');\n    (ctx as { prevTextDirection?: [string, string] }).prevTextDirection = original;\n  }\n}\n\nexport function restoreTextDirection(ctx: CanvasRenderingContext2D, original?: [string, string]) {\n  if (original !== undefined) {\n    delete (ctx as { prevTextDirection?: [string, string] }).prevTextDirection;\n    ctx.canvas.style.setProperty('direction', original[0], original[1]);\n  }\n}\n", "import {_angleBetween, _angleDiff, _isBetween, _normalizeAngle} from './helpers.math.js';\nimport {createContext} from './helpers.options.js';\nimport {isPatternOrGradient} from './helpers.color.js';\n\n/**\n * @typedef { import('../elements/element.line.js').default } LineElement\n * @typedef { import('../elements/element.point.js').default } PointElement\n * @typedef {{start: number, end: number, loop: boolean, style?: any}} Segment\n */\n\nfunction propertyFn(property) {\n  if (property === 'angle') {\n    return {\n      between: _angleBetween,\n      compare: _angleDiff,\n      normalize: _normalizeAngle,\n    };\n  }\n  return {\n    between: _isBetween,\n    compare: (a, b) => a - b,\n    normalize: x => x\n  };\n}\n\nfunction normalizeSegment({start, end, count, loop, style}) {\n  return {\n    start: start % count,\n    end: end % count,\n    loop: loop && (end - start + 1) % count === 0,\n    style\n  };\n}\n\nfunction getSegment(segment, points, bounds) {\n  const {property, start: startBound, end: endBound} = bounds;\n  const {between, normalize} = propertyFn(property);\n  const count = points.length;\n  // eslint-disable-next-line prefer-const\n  let {start, end, loop} = segment;\n  let i, ilen;\n\n  if (loop) {\n    start += count;\n    end += count;\n    for (i = 0, ilen = count; i < ilen; ++i) {\n      if (!between(normalize(points[start % count][property]), startBound, endBound)) {\n        break;\n      }\n      start--;\n      end--;\n    }\n    start %= count;\n    end %= count;\n  }\n\n  if (end < start) {\n    end += count;\n  }\n  return {start, end, loop, style: segment.style};\n}\n\n/**\n * Returns the sub-segment(s) of a line segment that fall in the given bounds\n * @param {object} segment\n * @param {number} segment.start - start index of the segment, referring the points array\n * @param {number} segment.end - end index of the segment, referring the points array\n * @param {boolean} segment.loop - indicates that the segment is a loop\n * @param {object} [segment.style] - segment style\n * @param {PointElement[]} points - the points that this segment refers to\n * @param {object} [bounds]\n * @param {string} bounds.property - the property of a `PointElement` we are bounding. `x`, `y` or `angle`.\n * @param {number} bounds.start - start value of the property\n * @param {number} bounds.end - end value of the property\n * @private\n **/\nexport function _boundSegment(segment, points, bounds) {\n  if (!bounds) {\n    return [segment];\n  }\n\n  const {property, start: startBound, end: endBound} = bounds;\n  const count = points.length;\n  const {compare, between, normalize} = propertyFn(property);\n  const {start, end, loop, style} = getSegment(segment, points, bounds);\n\n  const result = [];\n  let inside = false;\n  let subStart = null;\n  let value, point, prevValue;\n\n  const startIsBefore = () => between(startBound, prevValue, value) && compare(startBound, prevValue) !== 0;\n  const endIsBefore = () => compare(endBound, value) === 0 || between(endBound, prevValue, value);\n  const shouldStart = () => inside || startIsBefore();\n  const shouldStop = () => !inside || endIsBefore();\n\n  for (let i = start, prev = start; i <= end; ++i) {\n    point = points[i % count];\n\n    if (point.skip) {\n      continue;\n    }\n\n    value = normalize(point[property]);\n\n    if (value === prevValue) {\n      continue;\n    }\n\n    inside = between(value, startBound, endBound);\n\n    if (subStart === null && shouldStart()) {\n      subStart = compare(value, startBound) === 0 ? i : prev;\n    }\n\n    if (subStart !== null && shouldStop()) {\n      result.push(normalizeSegment({start: subStart, end: i, loop, count, style}));\n      subStart = null;\n    }\n    prev = i;\n    prevValue = value;\n  }\n\n  if (subStart !== null) {\n    result.push(normalizeSegment({start: subStart, end, loop, count, style}));\n  }\n\n  return result;\n}\n\n\n/**\n * Returns the segments of the line that are inside given bounds\n * @param {LineElement} line\n * @param {object} [bounds]\n * @param {string} bounds.property - the property we are bounding with. `x`, `y` or `angle`.\n * @param {number} bounds.start - start value of the `property`\n * @param {number} bounds.end - end value of the `property`\n * @private\n */\nexport function _boundSegments(line, bounds) {\n  const result = [];\n  const segments = line.segments;\n\n  for (let i = 0; i < segments.length; i++) {\n    const sub = _boundSegment(segments[i], line.points, bounds);\n    if (sub.length) {\n      result.push(...sub);\n    }\n  }\n  return result;\n}\n\n/**\n * Find start and end index of a line.\n */\nfunction findStartAndEnd(points, count, loop, spanGaps) {\n  let start = 0;\n  let end = count - 1;\n\n  if (loop && !spanGaps) {\n    // loop and not spanning gaps, first find a gap to start from\n    while (start < count && !points[start].skip) {\n      start++;\n    }\n  }\n\n  // find first non skipped point (after the first gap possibly)\n  while (start < count && points[start].skip) {\n    start++;\n  }\n\n  // if we looped to count, start needs to be 0\n  start %= count;\n\n  if (loop) {\n    // loop will go past count, if start > 0\n    end += start;\n  }\n\n  while (end > start && points[end % count].skip) {\n    end--;\n  }\n\n  // end could be more than count, normalize\n  end %= count;\n\n  return {start, end};\n}\n\n/**\n * Compute solid segments from Points, when spanGaps === false\n * @param {PointElement[]} points - the points\n * @param {number} start - start index\n * @param {number} max - max index (can go past count on a loop)\n * @param {boolean} loop - boolean indicating that this would be a loop if no gaps are found\n */\nfunction solidSegments(points, start, max, loop) {\n  const count = points.length;\n  const result = [];\n  let last = start;\n  let prev = points[start];\n  let end;\n\n  for (end = start + 1; end <= max; ++end) {\n    const cur = points[end % count];\n    if (cur.skip || cur.stop) {\n      if (!prev.skip) {\n        loop = false;\n        result.push({start: start % count, end: (end - 1) % count, loop});\n        // @ts-ignore\n        start = last = cur.stop ? end : null;\n      }\n    } else {\n      last = end;\n      if (prev.skip) {\n        start = end;\n      }\n    }\n    prev = cur;\n  }\n\n  if (last !== null) {\n    result.push({start: start % count, end: last % count, loop});\n  }\n\n  return result;\n}\n\n/**\n * Compute the continuous segments that define the whole line\n * There can be skipped points within a segment, if spanGaps is true.\n * @param {LineElement} line\n * @param {object} [segmentOptions]\n * @return {Segment[]}\n * @private\n */\nexport function _computeSegments(line, segmentOptions) {\n  const points = line.points;\n  const spanGaps = line.options.spanGaps;\n  const count = points.length;\n\n  if (!count) {\n    return [];\n  }\n\n  const loop = !!line._loop;\n  const {start, end} = findStartAndEnd(points, count, loop, spanGaps);\n\n  if (spanGaps === true) {\n    return splitByStyles(line, [{start, end, loop}], points, segmentOptions);\n  }\n\n  const max = end < start ? end + count : end;\n  const completeLoop = !!line._fullLoop && start === 0 && end === count - 1;\n  return splitByStyles(line, solidSegments(points, start, max, completeLoop), points, segmentOptions);\n}\n\n/**\n * @param {Segment[]} segments\n * @param {PointElement[]} points\n * @param {object} [segmentOptions]\n * @return {Segment[]}\n */\nfunction splitByStyles(line, segments, points, segmentOptions) {\n  if (!segmentOptions || !segmentOptions.setContext || !points) {\n    return segments;\n  }\n  return doSplitByStyles(line, segments, points, segmentOptions);\n}\n\n/**\n * @param {LineElement} line\n * @param {Segment[]} segments\n * @param {PointElement[]} points\n * @param {object} [segmentOptions]\n * @return {Segment[]}\n */\nfunction doSplitByStyles(line, segments, points, segmentOptions) {\n  const chartContext = line._chart.getContext();\n  const baseStyle = readStyle(line.options);\n  const {_datasetIndex: datasetIndex, options: {spanGaps}} = line;\n  const count = points.length;\n  const result = [];\n  let prevStyle = baseStyle;\n  let start = segments[0].start;\n  let i = start;\n\n  function addStyle(s, e, l, st) {\n    const dir = spanGaps ? -1 : 1;\n    if (s === e) {\n      return;\n    }\n    // Style can not start/end on a skipped point, adjust indices accordingly\n    s += count;\n    while (points[s % count].skip) {\n      s -= dir;\n    }\n    while (points[e % count].skip) {\n      e += dir;\n    }\n    if (s % count !== e % count) {\n      result.push({start: s % count, end: e % count, loop: l, style: st});\n      prevStyle = st;\n      start = e % count;\n    }\n  }\n\n  for (const segment of segments) {\n    start = spanGaps ? start : segment.start;\n    let prev = points[start % count];\n    let style;\n    for (i = start + 1; i <= segment.end; i++) {\n      const pt = points[i % count];\n      style = readStyle(segmentOptions.setContext(createContext(chartContext, {\n        type: 'segment',\n        p0: prev,\n        p1: pt,\n        p0DataIndex: (i - 1) % count,\n        p1DataIndex: i % count,\n        datasetIndex\n      })));\n      if (styleChanged(style, prevStyle)) {\n        addStyle(start, i - 1, segment.loop, prevStyle);\n      }\n      prev = pt;\n      prevStyle = style;\n    }\n    if (start < i - 1) {\n      addStyle(start, i - 1, segment.loop, prevStyle);\n    }\n  }\n\n  return result;\n}\n\nfunction readStyle(options) {\n  return {\n    backgroundColor: options.backgroundColor,\n    borderCapStyle: options.borderCapStyle,\n    borderDash: options.borderDash,\n    borderDashOffset: options.borderDashOffset,\n    borderJoinStyle: options.borderJoinStyle,\n    borderWidth: options.borderWidth,\n    borderColor: options.borderColor\n  };\n}\n\nfunction styleChanged(style, prevStyle) {\n  if (!prevStyle) {\n    return false;\n  }\n  const cache = [];\n  const replacer = function(key, value) {\n    if (!isPatternOrGradient(value)) {\n      return value;\n    }\n    if (!cache.includes(value)) {\n      cache.push(value);\n    }\n    return cache.indexOf(value);\n  };\n  return JSON.stringify(style, replacer) !== JSON.stringify(prevStyle, replacer);\n}\n"], "mappings": ";;;;;;;;AAAA;;GAAA,C;;;AAUO,SAASA,IAAOA,CAAA;EACrB;AAGF;;AAEC;AACM,MAAMC,GAAM,GAAC,OAAM;EACxB,IAAIC,EAAK;EACT,OAAO,MAAMA,EAAA;AACf;AAEA;;;;AAIC;AACM,SAASC,aAAcA,CAAAC,KAAc,EAA6B;EACvE,OAAOA,KAAU,SAAI,IAAI,OAAOA,KAAU;AAC5C;AAEA;;;;AAIC;AACM,SAASC,OAAqBA,CAAAD,KAAc,EAAgB;EACjE,IAAIE,KAAA,CAAMD,OAAO,IAAIC,KAAM,CAAAD,OAAO,CAACD,KAAQ;IACzC,OAAO,IAAI;;EAEb,MAAMG,IAAA,GAAOC,MAAO,CAAAC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACP,KAAA;EAC5C,IAAIG,IAAA,CAAKK,KAAK,CAAC,CAAG,SAAO,SAAa,IAAAL,IAAA,CAAKK,KAAK,CAAC,CAAC,OAAO,QAAU;IACjE,OAAO,IAAI;;EAEb,OAAO,KAAK;AACd;AAEA;;;;AAIC;AACM,SAASC,QAASA,CAAAT,KAAc,EAAsB;EAC3D,OAAOA,KAAA,KAAU,IAAI,IAAII,MAAO,CAAAC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACP,KAAW;AACrE;AAEA;;;;AAIA,SAASU,cAAeA,CAAAV,KAAc,EAAmB;EACvD,OAAQ,QAAOA,KAAA,KAAU,YAAYA,KAAiB,YAAAW,MAAK,KAAMC,QAAA,CAAS,CAACZ,KAAA;AAC7E;AAKA;;;;AAIC;AACM,SAASa,gBAAgBb,KAAc,EAAEc,YAAoB,EAAE;EACpE,OAAOJ,cAAA,CAAeV,KAAS,IAAAA,KAAA,GAAQc,YAAY;AACrD;AAEA;;;;AAIC;AACM,SAASC,eAAkBf,KAAoB,EAAEc,YAAe,EAAE;EACvE,OAAO,OAAOd,KAAA,KAAU,WAAc,GAAAc,YAAA,GAAed,KAAK;AAC5D;MAEagB,YAAe,GAAAA,CAAChB,KAAA,EAAwBiB,SACnD,YAAOjB,KAAA,KAAU,QAAY,IAAAA,KAAA,CAAMkB,QAAQ,CAAC,OAC1CC,UAAW,CAAAnB,KAAA,IAAS,MAClB,CAACA,KAAA,GAAQiB,SAAA;MAEFG,WAAc,GAAAA,CAACpB,KAAA,EAAwBiB,SAClD,YAAOjB,KAAA,KAAU,QAAY,IAAAA,KAAA,CAAMkB,QAAQ,CAAC,OAC1CC,UAAW,CAAAnB,KAAA,IAAS,MAAMiB,SACxB,IAACjB,KAAA;AAEP;;;;;;;AAOO,SAASqB,QACdA,CAAAC,EAAiB,EACjBC,IAAe,EACfC,OAAY,EACG;EACf,IAAIF,EAAM,WAAOA,EAAG,CAAAf,IAAI,KAAK,UAAY;IACvC,OAAOe,EAAA,CAAGG,KAAK,CAACD,OAAS,EAAAD,IAAA;;AAE7B;AAuBO,SAASG,KACdC,QAAiC,EACjCL,EAAoC,EACpCE,OAAY,EACZI,OAAiB,EACjB;EACA,IAAIC,CAAA,EAAWC,GAAa,EAAAC,IAAA;EAC5B,IAAI9B,OAAA,CAAQ0B,QAAW;IACrBG,GAAA,GAAMH,QAAA,CAASK,MAAM;IACrB,IAAIJ,OAAS;MACX,KAAKC,CAAI,GAAAC,GAAA,GAAM,CAAG,EAAAD,CAAA,IAAK,GAAGA,CAAK;QAC7BP,EAAA,CAAGf,IAAI,CAACiB,OAAA,EAASG,QAAQ,CAACE,CAAA,CAAE,EAAEA,CAAA;MAChC;KACK;MACL,KAAKA,CAAI,MAAGA,CAAI,GAAAC,GAAA,EAAKD,CAAK;QACxBP,EAAA,CAAGf,IAAI,CAACiB,OAAA,EAASG,QAAQ,CAACE,CAAA,CAAE,EAAEA,CAAA;MAChC;;GAEG,UAAIpB,QAAA,CAASkB,QAAW;IAC7BI,IAAO,GAAA3B,MAAA,CAAO2B,IAAI,CAACJ,QAAA;IACnBG,GAAA,GAAMC,IAAA,CAAKC,MAAM;IACjB,KAAKH,CAAI,MAAGA,CAAI,GAAAC,GAAA,EAAKD,CAAK;MACxBP,EAAA,CAAGf,IAAI,CAACiB,OAAS,EAAAG,QAAQ,CAACI,IAAI,CAACF,CAAA,CAAE,CAAC,EAAEE,IAAI,CAACF,CAAE;IAC7C;;AAEJ;AAEA;;;;;AAKC;AACM,SAASI,eAAeC,EAAqB,EAAEC,EAAqB,EAAE;EAC3E,IAAIN,CAAA,EAAWO,IAAA,EAAcC,EAAqB,EAAAC,EAAA;EAElD,IAAI,CAACJ,EAAA,IAAM,CAACC,EAAA,IAAMD,EAAA,CAAGF,MAAM,KAAKG,EAAG,CAAAH,MAAM,EAAE;IACzC,OAAO,KAAK;;EAGd,KAAKH,CAAA,GAAI,GAAGO,IAAO,GAAAF,EAAA,CAAGF,MAAM,EAAEH,CAAA,GAAIO,IAAM,IAAEP,CAAG;IAC3CQ,EAAK,GAAAH,EAAE,CAACL,CAAE;IACVS,EAAK,GAAAH,EAAE,CAACN,CAAE;IAEV,IAAIQ,EAAA,CAAGE,YAAY,KAAKD,EAAG,CAAAC,YAAY,IAAIF,EAAA,CAAGG,KAAK,KAAKF,EAAG,CAAAE,KAAK,EAAE;MAChE,OAAO,KAAK;;EAEhB;EAEA,OAAO,IAAI;AACb;AAEA;;;AAGC;AACM,SAASC,KAASA,CAAAC,MAAS,EAAK;EACrC,IAAIzC,OAAA,CAAQyC,MAAS;IACnB,OAAOA,MAAA,CAAOC,GAAG,CAACF,KAAA;;EAGpB,IAAIhC,QAAA,CAASiC,MAAS;IACpB,MAAME,MAAS,GAAAxC,MAAA,CAAOyC,MAAM,CAAC,IAAI;IACjC,MAAMd,IAAA,GAAO3B,MAAO,CAAA2B,IAAI,CAACW,MAAA;IACzB,MAAMI,IAAA,GAAOf,IAAA,CAAKC,MAAM;IACxB,IAAIe,CAAI;IAER,OAAOA,CAAA,GAAID,IAAM,IAAEC,CAAG;MACpBH,MAAM,CAACb,IAAI,CAACgB,CAAA,CAAE,CAAC,GAAGN,KAAM,CAAAC,MAAM,CAACX,IAAI,CAACgB,CAAA,CAAE,CAAC;IACzC;IAEA,OAAOH,MAAA;;EAGT,OAAOF,MAAA;AACT;AAEA,SAASM,WAAWC,GAAW,EAAE;EAC/B,OAAO,CAAC,aAAa,aAAa,cAAc,CAACC,OAAO,CAACD,GAAA,MAAS,CAAC;AACrE;AAEA;;;;;AAKO,SAASE,QAAQF,GAAW,EAAEL,MAAiB,EAAEF,MAAiB,EAAEU,OAAkB,EAAE;EAC7F,IAAI,CAACJ,UAAA,CAAWC,GAAM;IACpB;;EAGF,MAAMI,IAAA,GAAOT,MAAM,CAACK,GAAI;EACxB,MAAMK,IAAA,GAAOZ,MAAM,CAACO,GAAI;EAExB,IAAIxC,QAAA,CAAS4C,IAAS,KAAA5C,QAAA,CAAS6C,IAAO;;IAEpCC,KAAA,CAAMF,IAAA,EAAMC,IAAM,EAAAF,OAAA;GACb;IACLR,MAAM,CAACK,GAAI,IAAGR,KAAM,CAAAa,IAAA;;AAExB;AA0BO,SAASC,KAASA,CAAAX,MAAS,EAAEF,MAAmB,EAAEU,OAAsB,EAAa;EAC1F,MAAMI,OAAA,GAAUvD,OAAQ,CAAAyC,MAAA,IAAUA,MAAS,IAACA,MAAA,CAAO;EACnD,MAAMN,IAAA,GAAOoB,OAAA,CAAQxB,MAAM;EAE3B,IAAI,CAACvB,QAAA,CAASmC,MAAS;IACrB,OAAOA,MAAA;;EAGTQ,OAAA,GAAUA,OAAA,IAAW,EAAC;EACtB,MAAMK,MAAA,GAASL,OAAQ,CAAAK,MAAM,IAAIN,OAAA;EACjC,IAAIO,OAAA;EAEJ,KAAK,IAAI7B,CAAI,MAAGA,CAAI,GAAAO,IAAA,EAAM,EAAEP,CAAG;IAC7B6B,OAAU,GAAAF,OAAO,CAAC3B,CAAE;IACpB,IAAI,CAACpB,QAAA,CAASiD,OAAU;MACtB;;IAGF,MAAM3B,IAAA,GAAO3B,MAAO,CAAA2B,IAAI,CAAC2B,OAAA;IACzB,KAAK,IAAIX,CAAI,MAAGD,IAAO,GAAAf,IAAA,CAAKC,MAAM,EAAEe,CAAA,GAAID,IAAM,IAAEC,CAAG;MACjDU,MAAA,CAAO1B,IAAI,CAACgB,CAAE,GAAEH,MAAA,EAAQc,OAAS,EAAAN,OAAA;IACnC;EACF;EAEA,OAAOR,MAAA;AACT;AAgBO,SAASe,QAAWf,MAAS,EAAEF,MAAmB,EAAa;;EAEpE,OAAOa,KAAA,CAASX,MAAA,EAAQF,MAAQ;IAACe,MAAQ,EAAAG;EAAS;AACpD;AAEA;;;;AAIO,SAASA,SAAUA,CAAAX,GAAW,EAAEL,MAAiB,EAAEF,MAAiB,EAAE;EAC3E,IAAI,CAACM,UAAA,CAAWC,GAAM;IACpB;;EAGF,MAAMI,IAAA,GAAOT,MAAM,CAACK,GAAI;EACxB,MAAMK,IAAA,GAAOZ,MAAM,CAACO,GAAI;EAExB,IAAIxC,QAAA,CAAS4C,IAAS,KAAA5C,QAAA,CAAS6C,IAAO;IACpCK,OAAA,CAAQN,IAAM,EAAAC,IAAA;GACT,UAAI,CAAClD,MAAA,CAAOC,SAAS,CAACwD,cAAc,CAACtD,IAAI,CAACqC,MAAA,EAAQK,GAAM;IAC7DL,MAAM,CAACK,GAAI,IAAGR,KAAM,CAAAa,IAAA;;AAExB;AAEA;;;AAGO,SAASQ,YAAYC,KAAa,EAAE/D,KAAc,EAAEgE,QAAgB,EAAEN,OAAe,EAAE;EAC5F,IAAI1D,KAAA,KAAUiE,SAAW;IACvBC,OAAA,CAAQC,IAAI,CAACJ,KAAA,GAAQ,KAAQ,GAAAC,QAAA,GAC3B,kCAAkCN,OAAU;;AAElD;AAEA;AACA,MAAMU,YAAe;;EAEnB,IAAIC,CAAK,IAAAA,CAAA;;EAETC,CAAG,EAAAC,CAAK,IAAAA,CAAA,CAAED,CAAC;EACXE,CAAG,EAAAD,CAAK,IAAAA,CAAA,CAAEC;AACZ;AAEA;;AAEC;AACM,SAASC,SAAUA,CAAAxB,GAAW,EAAE;EACrC,MAAMyB,KAAA,GAAQzB,GAAI,CAAA0B,KAAK,CAAC;EACxB,MAAM5C,IAAA,GAAiB,EAAE;EACzB,IAAI6C,GAAM;EACV,KAAK,MAAMC,IAAA,IAAQH,KAAO;IACxBE,GAAO,IAAAC,IAAA;IACP,IAAID,GAAA,CAAI1D,QAAQ,CAAC,IAAO;MACtB0D,GAAA,GAAMA,GAAI,CAAApE,KAAK,CAAC,GAAG,CAAC,CAAK;KACpB;MACLuB,IAAA,CAAK+C,IAAI,CAACF,GAAA;MACVA,GAAM;;EAEV;EACA,OAAO7C,IAAA;AACT;AAEA,SAASgD,gBAAgB9B,GAAW,EAAE;EACpC,MAAMlB,IAAA,GAAO0C,SAAU,CAAAxB,GAAA;EACvB,OAAO+B,GAAO;IACZ,KAAK,MAAMjC,CAAA,IAAKhB,IAAM;MACpB,IAAIgB,CAAA,KAAM,EAAI;QAGZ;;MAEFiC,GAAM,GAAAA,GAAA,IAAOA,GAAG,CAACjC,CAAE;IACrB;IACA,OAAOiC,GAAA;EACT;AACF;AAEO,SAASC,iBAAiBD,GAAc,EAAE/B,GAAW,EAAO;EACjE,MAAMiC,QAAA,GAAWd,YAAY,CAACnB,GAAI,MAAKmB,YAAY,CAACnB,GAAA,CAAI,GAAG8B,eAAA,CAAgB9B,GAAG;EAC9E,OAAOiC,QAAS,CAAAF,GAAA;AAClB;AAEA;;AAEC;AACM,SAASG,WAAYA,CAAAC,GAAW,EAAE;EACvC,OAAOA,GAAA,CAAIC,MAAM,CAAC,GAAGC,WAAW,EAAK,GAAAF,GAAA,CAAI5E,KAAK,CAAC;AACjD;MAGa+E,OAAU,GAACvF,KAAmB,WAAOA,KAAA,KAAU;MAE/CwF,UAAa,GAACxF,KAAqD,WAAOA,KAAA,KAAU;AAEjG;AACa,MAAAyF,SAAA,GAAYA,CAAIC,CAAA,EAAWC,CAAc;EACpD,IAAID,CAAE,CAAAE,IAAI,KAAKD,CAAA,CAAEC,IAAI,EAAE;IACrB,OAAO,KAAK;;EAGd,KAAK,MAAMC,IAAA,IAAQH,CAAG;IACpB,IAAI,CAACC,CAAA,CAAEG,GAAG,CAACD,IAAO;MAChB,OAAO,KAAK;;EAEhB;EAEA,OAAO,IAAI;AACb;AAEA;;;AAGC;AACM,SAASE,aAAcA,CAAAC,CAAa,EAAE;EAC3C,OAAOA,CAAA,CAAE7F,IAAI,KAAK,SAAa,IAAA6F,CAAA,CAAE7F,IAAI,KAAK,WAAW6F,CAAE,CAAA7F,IAAI,KAAK;AAClE;;AC5ZA;;;AAGC;AAEM,MAAM8F,EAAK,GAAAC,IAAA,CAAKD,EAAA;AAChB,MAAME,GAAM,OAAIF,EAAA;AAChB,MAAMG,KAAQ,GAAAD,GAAA,GAAMF,EAAA;AACd,MAAAI,QAAA,GAAW1F,MAAO,CAAA2F,iBAAA;AACxB,MAAMC,WAAc,GAAAN,EAAA,GAAK;AACzB,MAAMO,OAAU,GAAAP,EAAA,GAAK;AACrB,MAAMQ,UAAa,GAAAR,EAAA,GAAK;AAClB,MAAAS,aAAA,GAAgBT,EAAK,OAAI;AAEzB,MAAAU,KAAA,GAAQT,IAAK,CAAAS,KAAA;AACb,MAAAC,IAAA,GAAOV,IAAK,CAAAU,IAAA;AAElB,SAASC,YAAaA,CAAAvC,CAAS,EAAEE,CAAS,EAAEsC,OAAe,EAAE;EAClE,OAAOZ,IAAK,CAAAa,GAAG,CAACzC,CAAA,GAAIE,CAAK,IAAAsC,OAAA;AAC3B;AAEA;;AAEC;AACM,SAASE,OAAQA,CAAAC,KAAa,EAAE;EACrC,MAAMC,YAAA,GAAehB,IAAK,CAAAiB,KAAK,CAACF,KAAA;EAChCA,KAAA,GAAQJ,YAAA,CAAaI,KAAO,EAAAC,YAAA,EAAcD,KAAQ,WAAQC,YAAA,GAAeD,KAAK;EAC9E,MAAMG,SAAA,GAAYlB,IAAA,CAAKmB,GAAG,CAAC,IAAInB,IAAK,CAAAoB,KAAK,CAACX,KAAM,CAAAM,KAAA;EAChD,MAAMM,QAAA,GAAWN,KAAQ,GAAAG,SAAA;EACzB,MAAMI,YAAA,GAAeD,QAAY,QAAI,CAAI,GAAAA,QAAA,IAAY,IAAI,CAAI,GAAAA,QAAA,IAAY,CAAI,OAAI,EAAE;EACnF,OAAOC,YAAe,GAAAJ,SAAA;AACxB;AAEA;;;AAGC;AACM,SAASK,UAAWA,CAAAzH,KAAa,EAAE;EACxC,MAAM0H,MAAA,GAAmB,EAAE;EAC3B,MAAMC,IAAA,GAAOzB,IAAK,CAAAyB,IAAI,CAAC3H,KAAA;EACvB,IAAI6B,CAAA;EAEJ,KAAKA,CAAI,MAAGA,CAAI,GAAA8F,IAAA,EAAM9F,CAAK;IACzB,IAAI7B,KAAA,GAAQ6B,CAAA,KAAM,CAAG;MACnB6F,MAAA,CAAO5C,IAAI,CAACjD,CAAA;MACZ6F,MAAO,CAAA5C,IAAI,CAAC9E,KAAQ,GAAA6B,CAAA;;EAExB;EACA,IAAI8F,IAAU,MAAAA,IAAO,KAAI;IACvBD,MAAA,CAAO5C,IAAI,CAAC6C,IAAA;;EAGdD,MAAA,CAAOE,IAAI,CAAC,CAAClC,CAAA,EAAGC,CAAM,KAAAD,CAAA,GAAIC,CAAA,EAAGkC,GAAG;EAChC,OAAOH,MAAA;AACT;AAEO,SAASI,QAASA,CAAAC,CAAU,EAAe;EAChD,OAAO,CAACC,KAAA,CAAM7G,UAAW,CAAA4G,CAAA,MAAiBnH,QAAS,CAAAmH,CAAA;AACrD;AAEO,SAASE,YAAY3D,CAAS,EAAEwC,OAAe,EAAE;EACtD,MAAMoB,OAAA,GAAUhC,IAAK,CAAAiB,KAAK,CAAC7C,CAAA;EAC3B,OAAO4D,OAAY,GAAApB,OAAA,IAAYxC,CAAO,IAAC4D,OAAA,GAAUpB,OAAY,IAAAxC,CAAA;AAC/D;AAEA;;;AAGO,SAAS6D,kBACdA,CAAAC,KAA+B,EAC/BxF,MAAoC,EACpCyF,QAAgB,EAChB;EACA,IAAIxG,CAAA,EAAWO,IAAc,EAAApC,KAAA;EAE7B,KAAK6B,CAAA,GAAI,GAAGO,IAAO,GAAAgG,KAAA,CAAMpG,MAAM,EAAEH,CAAA,GAAIO,IAAA,EAAMP,CAAK;IAC9C7B,KAAA,GAAQoI,KAAK,CAACvG,CAAE,EAACwG,QAAS;IAC1B,IAAI,CAACL,KAAA,CAAMhI,KAAQ;MACjB4C,MAAA,CAAO0F,GAAG,GAAGpC,IAAA,CAAKoC,GAAG,CAAC1F,MAAA,CAAO0F,GAAG,EAAEtI,KAAA;MAClC4C,MAAA,CAAO2F,GAAG,GAAGrC,IAAA,CAAKqC,GAAG,CAAC3F,MAAA,CAAO2F,GAAG,EAAEvI,KAAA;;EAEtC;AACF;AAEO,SAASwI,SAAUA,CAAAC,OAAe,EAAE;EACzC,OAAOA,OAAA,IAAWxC,EAAA,GAAK,GAAE;AAC3B;AAEO,SAASyC,SAAUA,CAAAC,OAAe,EAAE;EACzC,OAAOA,OAAA,IAAW,MAAM1C,EAAC;AAC3B;AAEA;;;;;;AAMC;AACM,SAAS2C,cAAeA,CAAAtE,CAAS,EAAE;EACxC,IAAI,CAAC5D,cAAA,CAAe4D,CAAI;IACtB;;EAEF,IAAI0B,CAAI;EACR,IAAI6C,CAAI;EACR,OAAO3C,IAAA,CAAKiB,KAAK,CAAC7C,CAAI,GAAA0B,CAAA,IAAKA,CAAA,KAAM1B,CAAG;IAClC0B,CAAK;IACL6C,CAAA;EACF;EACA,OAAOA,CAAA;AACT;AAEA;AACO,SAASC,kBACdC,WAAkB,EAClBC,UAAiB,EACjB;EACA,MAAMC,mBAAsB,GAAAD,UAAA,CAAW1E,CAAC,GAAGyE,WAAA,CAAYzE,CAAC;EACxD,MAAM4E,mBAAsB,GAAAF,UAAA,CAAWxE,CAAC,GAAGuE,WAAA,CAAYvE,CAAC;EACxD,MAAM2E,wBAAA,GAA2BjD,IAAK,CAAAyB,IAAI,CAACsB,mBAAA,GAAsBA,mBAAA,GAAsBC,mBAAsB,GAAAA,mBAAA;EAE7G,IAAIE,KAAQ,GAAAlD,IAAA,CAAKmD,KAAK,CAACH,mBAAqB,EAAAD,mBAAA;EAE5C,IAAIG,KAAA,GAAS,CAAC,MAAMnD,EAAK;IACvBmD,KAAA,IAASjD,GAAA;;;EAGX,OAAO;IACLiD,KAAA;IACAE,QAAU,EAAAH;EACZ;AACF;AAEO,SAASI,sBAAsBC,GAAU,EAAEC,GAAU,EAAE;EAC5D,OAAOvD,IAAA,CAAKyB,IAAI,CAACzB,IAAA,CAAKmB,GAAG,CAACoC,GAAA,CAAInF,CAAC,GAAGkF,GAAA,CAAIlF,CAAC,EAAE,KAAK4B,IAAA,CAAKmB,GAAG,CAACoC,GAAA,CAAIjF,CAAC,GAAGgF,GAAI,CAAAhF,CAAC,EAAE;AACxE;AAEA;;;AAGC;AACM,SAASkF,WAAWhE,CAAS,EAAEC,CAAS,EAAE;EAC/C,OAAO,CAACD,CAAA,GAAIC,CAAI,GAAAS,KAAI,IAAKD,GAAM,GAAAF,EAAA;AACjC;AAEA;;;AAGC;AACM,SAAS0D,eAAgBA,CAAAjE,CAAS,EAAE;EACzC,OAAO,CAACA,CAAI,GAAAS,GAAA,GAAMA,GAAE,IAAKA,GAAA;AAC3B;AAEA;;;AAGO,SAASyD,cAAcR,KAAa,EAAES,KAAa,EAAEC,GAAW,EAAEC,qBAA+B,EAAE;EACxG,MAAMrE,CAAA,GAAIiE,eAAgB,CAAAP,KAAA;EAC1B,MAAMY,CAAA,GAAIL,eAAgB,CAAAE,KAAA;EAC1B,MAAM7D,CAAA,GAAI2D,eAAgB,CAAAG,GAAA;EAC1B,MAAMG,YAAA,GAAeN,eAAA,CAAgBK,CAAI,GAAAtE,CAAA;EACzC,MAAMwE,UAAA,GAAaP,eAAA,CAAgB3D,CAAI,GAAAN,CAAA;EACvC,MAAMyE,YAAA,GAAeR,eAAA,CAAgBjE,CAAI,GAAAsE,CAAA;EACzC,MAAMI,UAAA,GAAaT,eAAA,CAAgBjE,CAAI,GAAAM,CAAA;EACvC,OAAON,CAAA,KAAMsE,CAAA,IAAKtE,CAAM,KAAAM,CAAA,IAAM+D,qBAAA,IAAyBC,CAAM,KAAAhE,CAAA,IACvDiE,YAAe,GAAAC,UAAA,IAAcC,YAAe,GAAAC,UAAA;AACpD;AAEA;;;;;;;AAOO,SAASC,WAAYA,CAAArK,KAAa,EAAEsI,GAAW,EAAEC,GAAW,EAAE;EACnE,OAAOrC,IAAA,CAAKqC,GAAG,CAACD,GAAA,EAAKpC,IAAK,CAAAoC,GAAG,CAACC,GAAK,EAAAvI,KAAA;AACrC;AAEA;;;AAGC;AACM,SAASsK,WAAYA,CAAAtK,KAAa,EAAE;EACzC,OAAOqK,WAAA,CAAYrK,KAAO,GAAC,KAAO;AACpC;AAEA;;;;;;;AAOO,SAASuK,WAAWvK,KAAa,EAAE6J,KAAa,EAAEC,GAAW,EAAEhD,OAAU,OAAI,EAAE;EACpF,OAAO9G,KAAS,IAAAkG,IAAA,CAAKoC,GAAG,CAACuB,KAAO,EAAAC,GAAA,IAAOhD,OAAW,IAAA9G,KAAA,IAASkG,IAAK,CAAAqC,GAAG,CAACsB,KAAA,EAAOC,GAAO,IAAAhD,OAAA;AACpF;ACpLO,SAAS0D,OACdA,CAAAC,KAAgB,EAChBzK,KAAa,EACb0K,GAAgC,EAChC;EACAA,GAAM,GAAAA,GAAA,KAASlI,KAAA,IAAUiI,KAAK,CAACjI,KAAA,CAAM,GAAGxC,KAAI;EAC5C,IAAI2K,EAAA,GAAKF,KAAM,CAAAzI,MAAM,GAAG;EACxB,IAAI4I,EAAK;EACT,IAAIC,GAAA;EAEJ,OAAOF,EAAA,GAAKC,EAAA,GAAK,CAAG;IAClBC,GAAM,GAACD,EAAA,GAAKD,EAAO;IACnB,IAAID,GAAA,CAAIG,GAAM;MACZD,EAAK,GAAAC,GAAA;KACA;MACLF,EAAK,GAAAE,GAAA;;EAET;EAEA,OAAO;IAACD,EAAA;IAAID;EAAE;AAChB;AAEA;;;;;;;AAOC;AACM,MAAMG,YAAe,GAAAA,CAC1BL,KACA,EAAAxH,GAAA,EACAjD,KACA,EAAA+K,IAAA,KAEAP,OAAQ,CAAAC,KAAA,EAAOzK,KAAO,EAAA+K,IAAA,GAClBvI,KAAS;EACT,MAAMwI,EAAK,GAAAP,KAAK,CAACjI,KAAA,CAAM,CAACS,GAAI;EAC5B,OAAO+H,EAAA,GAAKhL,KAAS,IAAAgL,EAAA,KAAOhL,KAAS,IAAAyK,KAAK,CAACjI,KAAQ,KAAE,CAACS,GAAA,CAAI,KAAKjD,KAAA;CAE/D,GAAAwC,KAAA,IAASiI,KAAK,CAACjI,KAAA,CAAM,CAACS,GAAA,CAAI,GAAGjD,KAAK;AAExC;;;;;;AAMC;AACY,MAAAiL,aAAA,GAAgBA,CAC3BR,KACA,EAAAxH,GAAA,EACAjD,KAAA,KAEAwK,OAAQ,CAAAC,KAAA,EAAOzK,KAAO,EAAAwC,KAAA,IAASiI,KAAK,CAACjI,KAAA,CAAM,CAACS,GAAA,CAAI,IAAIjD,KAAO;AAE7D;;;;;;;AAOO,SAASkL,cAAeA,CAAAC,MAAgB,EAAE7C,GAAW,EAAEC,GAAW,EAAE;EACzE,IAAIsB,KAAQ;EACZ,IAAIC,GAAA,GAAMqB,MAAA,CAAOnJ,MAAM;EAEvB,OAAO6H,KAAA,GAAQC,GAAO,IAAAqB,MAAM,CAACtB,KAAA,CAAM,GAAGvB,GAAK;IACzCuB,KAAA;EACF;EACA,OAAOC,GAAA,GAAMD,KAAS,IAAAsB,MAAM,CAACrB,GAAM,KAAE,GAAGvB,GAAK;IAC3CuB,GAAA;EACF;EAEA,OAAOD,KAAA,GAAQ,CAAK,IAAAC,GAAA,GAAMqB,MAAO,CAAAnJ,MAAM,GACnCmJ,MAAA,CAAO3K,KAAK,CAACqJ,KAAO,EAAAC,GAAA,IACpBqB,MAAM;AACZ;AAEA,MAAMC,WAAc,IAAC,QAAQ,OAAO,SAAS,UAAU,UAAU;AAgB1D,SAASC,kBAAkBjD,KAAK,EAAEkD,QAAQ,EAAE;EACjD,IAAIlD,KAAA,CAAMmD,QAAQ,EAAE;IAClBnD,KAAA,CAAMmD,QAAQ,CAACC,SAAS,CAAC1G,IAAI,CAACwG,QAAA;IAC9B;;EAGFlL,MAAO,CAAAqL,cAAc,CAACrD,KAAA,EAAO,UAAY;IACvCsD,YAAA,EAAc,IAAI;IAClBC,UAAA,EAAY,KAAK;IACjB3L,KAAO;MACLwL,SAAW,GAACF,QAAA;IACd;EACF;EAEAF,WAAY,CAAAQ,OAAO,CAAE3I,GAAQ;IAC3B,MAAM4I,MAAA,GAAS,YAAY1G,WAAY,CAAAlC,GAAA;IACvC,MAAM6I,IAAA,GAAO1D,KAAK,CAACnF,GAAI;IAEvB7C,MAAO,CAAAqL,cAAc,CAACrD,KAAA,EAAOnF,GAAK;MAChCyI,YAAA,EAAc,IAAI;MAClBC,UAAA,EAAY,KAAK;MACjB3L,KAAMA,CAAA,GAAGuB,IAAI,EAAE;QACb,MAAMwK,GAAM,GAAAD,IAAA,CAAKrK,KAAK,CAAC,IAAI,EAAEF,IAAA;QAE7B6G,KAAA,CAAMmD,QAAQ,CAACC,SAAS,CAACI,OAAO,CAAEI,MAAW;UAC3C,IAAI,OAAOA,MAAM,CAACH,MAAA,CAAO,KAAK,UAAY;YACxCG,MAAM,CAACH,MAAA,CAAO,CAAI,GAAAtK,IAAA;;QAEtB;QAEA,OAAOwK,GAAA;MACT;IACF;EACF;AACF;AAQO,SAASE,oBAAoB7D,KAAK,EAAEkD,QAAQ,EAAE;EACnD,MAAMY,IAAA,GAAO9D,KAAA,CAAMmD,QAAQ;EAC3B,IAAI,CAACW,IAAM;IACT;;EAGF,MAAMV,SAAA,GAAYU,IAAA,CAAKV,SAAS;EAChC,MAAMhJ,KAAA,GAAQgJ,SAAU,CAAAtI,OAAO,CAACoI,QAAA;EAChC,IAAI9I,KAAA,KAAU,CAAC,CAAG;IAChBgJ,SAAU,CAAAW,MAAM,CAAC3J,KAAO;;EAG1B,IAAIgJ,SAAA,CAAUxJ,MAAM,GAAG,CAAG;IACxB;;EAGFoJ,WAAY,CAAAQ,OAAO,CAAE3I,GAAQ;IAC3B,OAAOmF,KAAK,CAACnF,GAAI;EACnB;EAEA,OAAOmF,KAAA,CAAMmD,QAAQ;AACvB;AAEA;;AAEC;AACM,SAASa,YAAgBA,CAAAC,KAAU,EAAE;EAC1C,MAAMC,GAAA,GAAM,IAAIC,GAAO,CAAAF,KAAA;EAEvB,IAAIC,GAAI,CAAA1G,IAAI,KAAKyG,KAAA,CAAMrK,MAAM,EAAE;IAC7B,OAAOqK,KAAA;;EAGT,OAAOnM,KAAA,CAAMsM,IAAI,CAACF,GAAA;AACpB;AC1LO,SAASG,UAAWA,CAAAC,SAAiB,EAAEC,SAAiB,EAAEC,UAAkB,EAAE;EACnF,OAAOD,SAAA,GAAY,GAAM,GAAAD,SAAA,GAAY,KAAQ,GAAAE,UAAA;AAC/C;AAEA;;AAEA;AACa,MAAAC,gBAAA,GAAoB,YAAW;EAC1C,IAAI,OAAOC,MAAA,KAAW,WAAa;IACjC,OAAO,UAASzL,QAAQ,EAAE;MACxB,OAAOA,QAAA;IACT;;EAEF,OAAOyL,MAAA,CAAOC,qBAAqB;AACrC,CAAK;AAEL;;;AAGC;AACM,SAASC,UACd1L,EAA4B,EAC5BE,OAAY,EACZ;EACA,IAAIyL,SAAA,GAAY,EAAE;EAClB,IAAIC,OAAA,GAAU,KAAK;EAEnB,OAAO,UAAS,GAAG3L,IAAW,EAAE;;IAE9B0L,SAAY,GAAA1L,IAAA;IACZ,IAAI,CAAC2L,OAAS;MACZA,OAAA,GAAU,IAAI;MACdL,gBAAiB,CAAAtM,IAAI,CAACuM,MAAA,EAAQ,MAAM;QAClCI,OAAA,GAAU,KAAK;QACf5L,EAAG,CAAAG,KAAK,CAACD,OAAS,EAAAyL,SAAA;MACpB;;EAEJ;AACF;AAEA;;AAEC;AACM,SAASE,SAAmC7L,EAA4B,EAAE8L,KAAa,EAAE;EAC9F,IAAIC,OAAA;EACJ,OAAO,UAAS,GAAG9L,IAAW,EAAE;IAC9B,IAAI6L,KAAO;MACTE,YAAa,CAAAD,OAAA;MACbA,OAAU,GAAAE,UAAA,CAAWjM,EAAA,EAAI8L,KAAO,EAAA7L,IAAA;KAC3B;MACLD,EAAG,CAAAG,KAAK,CAAC,IAAI,EAAEF,IAAA;;IAEjB,OAAO6L,KAAA;EACT;AACF;AAEA;;;AAGC;AACM,MAAMI,kBAAqB,GAACC,KAAsC,IAAAA,KAAA,KAAU,OAAU,YAASA,KAAU,aAAQ,OAAU;AAElI;;;AAGC;AACY,MAAAC,cAAA,GAAiBA,CAACD,KAAmC,EAAA5D,KAAA,EAAeC,GAAA,KAAgB2D,KAAU,eAAU5D,KAAA,GAAQ4D,KAAU,aAAQ3D,GAAA,GAAM,CAACD,KAAA,GAAQC,GAAE,IAAK;AAErK;;;AAGC;AACY,MAAA6D,MAAA,GAASA,CAACF,KAAoC,EAAAG,IAAA,EAAcC,KAAA,EAAeC,GAAiB;EACvG,MAAMC,KAAA,GAAQD,GAAM,YAAS,OAAO;EACpC,OAAOL,KAAA,KAAUM,KAAQ,GAAAF,KAAA,GAAQJ,KAAU,gBAAW,CAACG,IAAO,GAAAC,KAAI,IAAK,IAAID,IAAI;AACjF;AAEA;;;;AAIO,SAASI,gCAAiCA,CAAAC,IAAmC,EAAEC,MAAsB,EAAEC,kBAA2B,EAAE;EACzI,MAAMC,UAAA,GAAaF,MAAA,CAAOlM,MAAM;EAEhC,IAAI6H,KAAQ;EACZ,IAAIwE,KAAQ,GAAAD,UAAA;EAEZ,IAAIH,IAAA,CAAKK,OAAO,EAAE;IAChB,MAAM;MAACC,MAAA;MAAQC;IAAA,CAAQ,GAAGP,IAAA;IAC1B,MAAMQ,IAAA,GAAOF,MAAA,CAAOE,IAAI;IACxB,MAAM;MAACnG,GAAG;MAAEC,GAAG;MAAEmG,UAAU;MAAEC;IAAU,CAAC,GAAGJ,MAAA,CAAOK,aAAa;IAE/D,IAAIF,UAAY;MACd7E,KAAA,GAAQQ,WAAY,CAAAnE,IAAA,CAAKoC,GAAG;MAAA;MAE1BwC,YAAA,CAAa0D,OAAS,EAAAC,IAAA,EAAMnG,GAAK,EAAAsC,EAAE;MAAA;MAEnCuD,kBAAqB,GAAAC,UAAA,GAAatD,YAAa,CAAAoD,MAAA,EAAQO,IAAM,EAAAF,MAAA,CAAOM,gBAAgB,CAACvG,GAAM,GAAAsC,EAAE,CAC/F,KAAGwD,UAAa;;IAElB,IAAIO,UAAY;MACdN,KAAA,GAAQhE,WAAY,CAAAnE,IAAA,CAAKqC,GAAG;MAAA;MAE1BuC,YAAa,CAAA0D,OAAA,EAASD,MAAO,CAAAE,IAAI,EAAElG,GAAA,EAAK,IAAI,CAAE,CAAAoC,EAAE,GAAG;MAAA;MAEnDwD,kBAAA,GAAqB,CAAI,GAAArD,YAAA,CAAaoD,MAAQ,EAAAO,IAAA,EAAMF,MAAA,CAAOM,gBAAgB,CAACtG,GAAM,OAAI,EAAEoC,EAAE,GAAG,CAAC,GAChGd,KAAA,EAAOuE,UAAc,IAAAvE,KAAA;KAChB;MACLwE,KAAA,GAAQD,UAAa,GAAAvE,KAAA;;;EAIzB,OAAO;IAACA,KAAA;IAAOwE;EAAK;AACtB;AAEA;;;;;AAKC;AACM,SAASS,mBAAoBA,CAAAb,IAAI,EAAE;EACxC,MAAM;IAACc,MAAM;IAAEC,MAAA;IAAQC;EAAA,CAAa,GAAGhB,IAAA;EACvC,MAAMiB,SAAY;IAChBC,IAAA,EAAMJ,MAAA,CAAOzG,GAAG;IAChB8G,IAAA,EAAML,MAAA,CAAOxG,GAAG;IAChB8G,IAAA,EAAML,MAAA,CAAO1G,GAAG;IAChBgH,IAAA,EAAMN,MAAA,CAAOzG;EACf;EACA,IAAI,CAAC0G,YAAc;IACjBhB,IAAA,CAAKgB,YAAY,GAAGC,SAAA;IACpB,OAAO,IAAI;;EAEb,MAAMK,OAAA,GAAUN,YAAA,CAAaE,IAAI,KAAKJ,MAAA,CAAOzG,GAAG,IAC7C2G,YAAa,CAAAG,IAAI,KAAKL,MAAA,CAAOxG,GAAG,IAChC0G,YAAA,CAAaI,IAAI,KAAKL,MAAO,CAAA1G,GAAG,IAChC2G,YAAa,CAAAK,IAAI,KAAKN,MAAA,CAAOzG,GAAG;EAEnCnI,MAAO,CAAAoP,MAAM,CAACP,YAAc,EAAAC,SAAA;EAC5B,OAAOK,OAAA;AACT;AC/IA,MAAME,MAAS,GAACC,CAAc,IAAAA,CAAA,KAAM,KAAKA,CAAM;AAC/C,MAAMC,SAAA,GAAYA,CAACD,CAAA,EAAW1F,CAAW,EAAAnB,CAAA,KAAc,EAAE3C,IAAA,CAAKmB,GAAG,CAAC,CAAG,QAAMqI,CAAK,MAAM,IAAAxJ,IAAA,CAAK0J,GAAG,CAAC,CAACF,CAAI,GAAA1F,CAAA,IAAK7D,GAAA,GAAM0C,CAAC;AAChH,MAAMgH,UAAA,GAAaA,CAACH,CAAW,EAAA1F,CAAA,EAAWnB,CAAA,KAAc3C,IAAK,CAAAmB,GAAG,CAAC,CAAG,GAAC,KAAKqI,CAAK,IAAAxJ,IAAA,CAAK0J,GAAG,CAAE,CAAAF,CAAI,GAAA1F,CAAA,IAAK7D,GAAA,GAAM0C,CAAK;AAE7G;;;;AAIC;AAAA,MACKiH,OAAU;EACdC,MAAA,EAASL,CAAc,IAAAA,CAAA;EAEvBM,UAAY,EAACN,CAAA,IAAcA,CAAI,GAAAA,CAAA;EAE/BO,WAAA,EAAcP,CAAc,KAACA,CAAK,IAAAA,CAAA,GAAI;EAEtCQ,aAAe,EAACR,CAAA,IAAgB,CAAAA,CAAK,OAAE,IAAK,IACxC,GAAM,GAAAA,CAAA,GAAIA,CAAA,GACV,CAAC,OAAQ,EAAEA,CAAA,IAAMA,CAAI,KAAK,KAAE;EAEhCS,WAAa,EAACT,CAAc,IAAAA,CAAA,GAAIA,CAAI,GAAAA,CAAA;EAEpCU,YAAc,EAACV,CAAA,IAAc,CAACA,CAAA,IAAK,KAAKA,CAAA,GAAIA,CAAI;EAEhDW,cAAgB,EAACX,CAAA,IAAgB,CAAAA,CAAK,OAAE,IAAK,IACzC,GAAM,GAAAA,CAAA,GAAIA,CAAA,GAAIA,CACd,UAAQ,CAAAA,CAAA,IAAK,KAAKA,CAAA,GAAIA,CAAI,KAAE;EAEhCY,WAAA,EAAcZ,CAAA,IAAcA,CAAI,GAAAA,CAAA,GAAIA,CAAI,GAAAA,CAAA;EAExCa,YAAA,EAAeb,CAAA,IAAc,EAAE,CAACA,CAAK,SAAKA,CAAA,GAAIA,CAAI,GAAAA,CAAA,GAAI;EAEtDc,cAAgB,EAACd,CAAc,IAAC,CAACA,CAAK,OAAE,IAAK,IACzC,GAAM,GAAAA,CAAA,GAAIA,CAAI,GAAAA,CAAA,GAAIA,CAAA,GAClB,CAAC,OAAQ,CAAAA,CAAA,IAAK,KAAKA,CAAI,GAAAA,CAAA,GAAIA,CAAI,KAAE;EAErCe,WAAA,EAAcf,CAAA,IAAcA,CAAI,GAAAA,CAAA,GAAIA,CAAA,GAAIA,CAAI,GAAAA,CAAA;EAE5CgB,YAAc,EAAChB,CAAc,IAAC,CAAAA,CAAA,IAAK,KAAKA,CAAA,GAAIA,CAAI,GAAAA,CAAA,GAAIA,CAAI;EAExDiB,cAAgB,EAACjB,CAAc,IAAC,CAACA,CAAK,OAAE,IAAK,IACzC,GAAM,GAAAA,CAAA,GAAIA,CAAI,GAAAA,CAAA,GAAIA,CAAI,GAAAA,CAAA,GACtB,GAAO,KAACA,CAAK,SAAKA,CAAA,GAAIA,CAAI,GAAAA,CAAA,GAAIA,CAAI,KAAE;EAExCkB,UAAA,EAAalB,CAAc,KAACxJ,IAAA,CAAK2K,GAAG,CAACnB,CAAA,GAAIlJ,OAAW;EAEpDsK,WAAA,EAAcpB,CAAA,IAAcxJ,IAAK,CAAA0J,GAAG,CAACF,CAAI,GAAAlJ,OAAA;EAEzCuK,aAAe,EAACrB,CAAc,KAAC,GAAO,IAAAxJ,IAAA,CAAK2K,GAAG,CAAC5K,EAAK,GAAAyJ,CAAA,IAAK;EAEzDsB,UAAA,EAAatB,CAAA,IAAcA,CAAC,KAAM,IAAK,CAAI,GAAAxJ,IAAA,CAAKmB,GAAG,CAAC,CAAG,QAAMqI,CAAA,GAAI,EAAG;EAEpEuB,WAAA,EAAcvB,CAAA,IAAcA,CAAC,KAAM,IAAK,CAAI,IAACxJ,IAAK,CAAAmB,GAAG,CAAC,GAAG,CAAC,KAAKqI,CAAA,IAAK,CAAC;EAErEwB,aAAA,EAAgBxB,CAAA,IAAcD,MAAO,CAAAC,CAAA,IAAKA,CAAA,GAAIA,CAAI,SAC9C,GAAM,GAAAxJ,IAAA,CAAKmB,GAAG,CAAC,CAAG,QAAMqI,CAAI,OAAI,MAChC,GAAO,KAACxJ,IAAA,CAAKmB,GAAG,CAAC,GAAG,CAAC,MAAMqI,CAAI,OAAI,MAAM,EAAE;EAE/CyB,UAAA,EAAazB,CAAA,IAAcA,CAAC,IAAK,IAAKA,CAAI,KAAExJ,IAAA,CAAKyB,IAAI,CAAC,IAAI+H,CAAI,GAAAA,CAAA,IAAK,EAAE;EAErE0B,WAAa,EAAC1B,CAAc,IAAAxJ,IAAA,CAAKyB,IAAI,CAAC,IAAI,CAAC+H,CAAK,SAAKA,CAAA;EAErD2B,aAAA,EAAgB3B,CAAA,IAAc,CAAEA,CAAK,OAAE,IAAK,IACxC,CAAC,OAAOxJ,IAAA,CAAKyB,IAAI,CAAC,IAAI+H,CAAI,GAAAA,CAAA,IAAK,KAC/B,GAAO,IAAAxJ,IAAA,CAAKyB,IAAI,CAAC,CAAI,GAAC,CAAA+H,CAAK,SAAKA,CAAA,IAAK,EAAE;EAE3C4B,aAAe,EAAC5B,CAAA,IAAcD,MAAO,CAAAC,CAAA,IAAKA,CAAA,GAAIC,SAAU,CAAAD,CAAA,EAAG,OAAO,GAAI;EAEtE6B,cAAgB,EAAC7B,CAAA,IAAcD,MAAO,CAAAC,CAAA,IAAKA,CAAA,GAAIG,UAAW,CAAAH,CAAA,EAAG,OAAO,GAAI;EAExE8B,iBAAiB9B,CAAS,EAAE;IAC1B,MAAM1F,CAAI;IACV,MAAMnB,CAAI;IACV,OAAO4G,MAAA,CAAOC,CAAK,IAAAA,CAAA,GACjBA,CAAA,GAAI,GACA,SAAMC,SAAA,CAAUD,CAAI,MAAG1F,CAAG,EAAAnB,CAAA,IAC1B,MAAM,GAAM,GAAAgH,UAAA,CAAWH,CAAA,GAAI,CAAI,MAAG1F,CAAA,EAAGnB,CAAE;EAC/C;EAEA4I,WAAW/B,CAAS,EAAE;IACpB,MAAM1F,CAAI;IACV,OAAO0F,CAAA,GAAIA,CAAA,IAAM,CAAA1F,CAAI,QAAK0F,CAAA,GAAI1F,CAAA;EAChC;EAEA0H,YAAYhC,CAAS,EAAE;IACrB,MAAM1F,CAAI;IACV,OAAO,CAAC0F,CAAK,SAAKA,CAAK,KAAC1F,CAAI,QAAK0F,CAAA,GAAI1F,CAAA,CAAK;EAC5C;EAEA2H,cAAcjC,CAAS,EAAE;IACvB,IAAI1F,CAAI;IACR,IAAI,CAAC0F,CAAK,OAAE,IAAK,CAAG;MAClB,OAAO,OAAOA,CAAA,GAAIA,CAAK,KAAE,CAAA1F,CAAA,IAAM,KAAK,IAAK,KAAK0F,CAAA,GAAI1F,CAAA,CAAC;;IAErD,OAAO,OAAO,CAAC0F,CAAA,IAAK,KAAKA,CAAA,IAAM,EAAC1F,CAAA,IAAM,KAAK,IAAK,KAAK0F,CAAA,GAAI1F,CAAA,IAAK;EAChE;EAEA4H,YAAA,EAAelC,CAAc,QAAII,OAAQ,CAAA+B,aAAa,CAAC,CAAI,GAAAnC,CAAA;EAE3DmC,cAAcnC,CAAS,EAAE;IACvB,MAAMoC,CAAI;IACV,MAAMC,CAAI;IACV,IAAIrC,CAAA,GAAK,IAAIqC,CAAI;MACf,OAAOD,CAAA,GAAIpC,CAAI,GAAAA,CAAA;;IAEjB,IAAIA,CAAA,GAAK,IAAIqC,CAAI;MACf,OAAOD,CAAA,IAAKpC,CAAA,IAAM,GAAM,GAAAqC,CAAC,IAAKrC,CAAI;;IAEpC,IAAIA,CAAA,GAAK,MAAMqC,CAAI;MACjB,OAAOD,CAAA,IAAKpC,CAAA,IAAM,IAAO,GAAAqC,CAAC,IAAKrC,CAAI;;IAErC,OAAOoC,CAAA,IAAKpC,CAAA,IAAM,KAAQ,GAAAqC,CAAC,IAAKrC,CAAI;EACtC;EAEAsC,eAAA,EAAkBtC,CAAc,IAACA,CAAA,GAAI,GACjC,GAAAI,OAAA,CAAQ8B,YAAY,CAAClC,CAAA,GAAI,CAAK,UAC9BI,OAAA,CAAQ+B,aAAa,CAACnC,CAAA,GAAI,CAAI,QAAK,MAAM;AAC/C;ACrHO,SAASuC,mBAAoBA,CAAAjS,KAAc,EAA2C;EAC3F,IAAIA,KAAA,IAAS,OAAOA,KAAA,KAAU,QAAU;IACtC,MAAMG,IAAA,GAAOH,KAAA,CAAMM,QAAQ;IAC3B,OAAOH,IAAA,KAAS,4BAA4BA,IAAS;;EAGvD,OAAO,KAAK;AACd;AAWO,SAAS+R,KAAMA,CAAAlS,KAAK,EAAE;EAC3B,OAAOiS,mBAAoB,CAAAjS,KAAA,IAASA,KAAQ,OAAImS,KAAA,CAAMnS,KAAM;AAC9D;AAKO,SAASoS,aAAcA,CAAApS,KAAK,EAAE;EACnC,OAAOiS,mBAAoB,CAAAjS,KAAA,IACvBA,KACA,OAAImS,KAAM,CAAAnS,KAAA,EAAOqS,QAAQ,CAAC,GAAK,EAAAC,MAAM,CAAC,KAAKC,SAAS,EAAE;AAC5D;AC/BA,MAAMC,OAAU,IAAC,KAAK,KAAK,eAAe,UAAU,UAAU;AAC9D,MAAMC,MAAS,IAAC,SAAS,eAAe,kBAAkB;AAEnD,SAASC,uBAAwBA,CAAAC,QAAQ,EAAE;EAChDA,QAAS,CAAArG,GAAG,CAAC,WAAa;IACxBc,KAAO,EAAAnJ,SAAA;IACP2O,QAAU;IACVC,MAAQ;IACRvR,EAAI,EAAA2C,SAAA;IACJuI,IAAM,EAAAvI,SAAA;IACN6O,IAAM,EAAA7O,SAAA;IACN8O,EAAI,EAAA9O,SAAA;IACJ9D,IAAM,EAAA8D;EACR;EAEA0O,QAAS,CAAAK,QAAQ,CAAC,WAAa;IAC7BC,SAAA,EAAW,KAAK;IAChBC,UAAA,EAAY,KAAK;IACjBC,WAAA,EAAcC,IAAS,IAAAA,IAAA,KAAS,YAAgB,IAAAA,IAAA,KAAS,gBAAgBA,IAAS;EACpF;EAEAT,QAAS,CAAArG,GAAG,CAAC,YAAc;IACzBmG,MAAQ;MACNtS,IAAM;MACNkT,UAAY,EAAAZ;IACd;IACAD,OAAS;MACPrS,IAAM;MACNkT,UAAY,EAAAb;IACd;EACF;EAEAG,QAAS,CAAAK,QAAQ,CAAC,YAAc;IAC9BC,SAAW;EACb;EAEAN,QAAS,CAAArG,GAAG,CAAC,aAAe;IAC1BgH,MAAQ;MACNC,SAAW;QACTX,QAAU;MACZ;IACF;IACAY,MAAQ;MACND,SAAW;QACTX,QAAU;MACZ;IACF;IACAa,IAAM;MACJC,UAAY;QACVjB,MAAQ;UACNjG,IAAM;QACR;QACAmH,OAAS;UACPxT,IAAM;UACNyS,QAAA,EAAU;QACZ;MACF;IACF;IACAgB,IAAM;MACJF,UAAY;QACVjB,MAAQ;UACNM,EAAI;QACN;QACAY,OAAS;UACPxT,IAAM;UACN0S,MAAQ;UACRvR,EAAA,EAAI+C,CAAA,IAAKA,CAAI;QACf;MACF;IACF;EACF;AACF;ACvEO,SAASwP,oBAAqBA,CAAAlB,QAAQ,EAAE;EAC7CA,QAAS,CAAArG,GAAG,CAAC,QAAU;IACrBwH,WAAA,EAAa,IAAI;IACjBC,OAAS;MACPC,GAAK;MACLnG,KAAO;MACPoG,MAAQ;MACRrG,IAAM;IACR;EACF;AACF;ACTA,MAAMsG,SAAA,GAAY,IAAIC,GAAA;AAEtB,SAASC,eAAgBA,CAAAC,MAAc,EAAEjR,OAAkC,EAAE;EAC3EA,OAAA,GAAUA,OAAA,IAAW,EAAC;EACtB,MAAMkR,QAAW,GAAAD,MAAA,GAASE,IAAK,CAAAC,SAAS,CAACpR,OAAA;EACzC,IAAIqR,SAAA,GAAYP,SAAU,CAAAQ,GAAG,CAACJ,QAAA;EAC9B,IAAI,CAACG,SAAW;IACdA,SAAA,GAAY,IAAIE,IAAA,CAAKC,YAAY,CAACP,MAAQ,EAAAjR,OAAA;IAC1C8Q,SAAU,CAAA5H,GAAG,CAACgI,QAAU,EAAAG,SAAA;;EAE1B,OAAOA,SAAA;AACT;AAEO,SAASI,YAAaA,CAAAC,GAAW,EAAET,MAAc,EAAEjR,OAAkC,EAAE;EAC5F,OAAOgR,eAAgB,CAAAC,MAAA,EAAQjR,OAAS,EAAA2R,MAAM,CAACD,GAAA;AACjD;ACRA,MAAME,UAAa;EAOjB7J,OAAOnL,KAAK,EAAE;IACZ,OAAOC,OAAA,CAAQD,KAAS,IAAyBA,KAAA,GAAS,KAAKA,KAAK;EACtE;EAUAiV,QAAQC,SAAS,EAAE1S,KAAK,EAAE2S,KAAK,EAAE;IAC/B,IAAID,SAAA,KAAc,CAAG;MACnB,OAAO;;IAGT,MAAMb,MAAA,GAAS,IAAI,CAACe,KAAK,CAAChS,OAAO,CAACiR,MAAM;IACxC,IAAIgB,QAAA;IACJ,IAAIC,KAAA,GAAQJ,SAAA;IAEZ,IAAIC,KAAA,CAAMnT,MAAM,GAAG,CAAG;MAEpB,MAAMuT,OAAA,GAAUrP,IAAA,CAAKqC,GAAG,CAACrC,IAAA,CAAKa,GAAG,CAACoO,KAAK,CAAC,CAAE,EAACnV,KAAK,CAAG,EAAAkG,IAAA,CAAKa,GAAG,CAACoO,KAAK,CAACA,KAAA,CAAMnT,MAAM,GAAG,CAAE,EAAChC,KAAK;MACzF,IAAIuV,OAAA,GAAU,IAAQ,IAAAA,OAAA,GAAU,KAAO;QACrCF,QAAW;;MAGbC,KAAA,GAAQE,cAAA,CAAeN,SAAW,EAAAC,KAAA;;IAGpC,MAAMM,QAAW,GAAA9O,KAAA,CAAMT,IAAK,CAAAa,GAAG,CAACuO,KAAA;IAOhC,MAAMI,UAAA,GAAa1N,KAAM,CAAAyN,QAAA,IAAY,CAAI,GAAAvP,IAAA,CAAKqC,GAAG,CAACrC,IAAA,CAAKoC,GAAG,CAAC,CAAC,CAAI,GAAApC,IAAA,CAAKoB,KAAK,CAACmO,QAAA,GAAW,KAAK,CAAE;IAE7F,MAAMrS,OAAU;MAACiS,QAAA;MAAUM,qBAAuB,EAAAD,UAAA;MAAYE,qBAAuB,EAAAF;IAAU;IAC/FtV,MAAO,CAAAoP,MAAM,CAACpM,OAAS,MAAI,CAACA,OAAO,CAAC+R,KAAK,CAACJ,MAAM;IAEhD,OAAOF,YAAA,CAAaK,SAAA,EAAWb,MAAQ,EAAAjR,OAAA;EACzC;EAWAyS,YAAYX,SAAS,EAAE1S,KAAK,EAAE2S,KAAK,EAAE;IACnC,IAAID,SAAA,KAAc,CAAG;MACnB,OAAO;;IAET,MAAMY,MAAS,GAAAX,KAAK,CAAC3S,KAAA,CAAM,CAACuT,WAAW,IAAKb,SAAa,GAAAhP,IAAA,CAAKmB,GAAG,CAAC,IAAInB,IAAK,CAAAoB,KAAK,CAACX,KAAM,CAAAuO,SAAA;IACvF,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,CAACc,QAAQ,CAACF,MAAA,KAAWtT,KAAA,GAAQ,GAAM,GAAA2S,KAAA,CAAMnT,MAAM,EAAE;MACvE,OAAOgT,UAAA,CAAWC,OAAO,CAAC1U,IAAI,CAAC,IAAI,EAAE2U,SAAA,EAAW1S,KAAO,EAAA2S,KAAA;;IAEzD,OAAO;EACT;AAEF;AAGA,SAASK,cAAeA,CAAAN,SAAS,EAAEC,KAAK,EAAE;EAGxC,IAAIG,KAAA,GAAQH,KAAM,CAAAnT,MAAM,GAAG,IAAImT,KAAK,CAAC,CAAE,EAACnV,KAAK,GAAGmV,KAAK,CAAC,CAAE,EAACnV,KAAK,GAAGmV,KAAK,CAAC,CAAE,EAACnV,KAAK,GAAGmV,KAAK,CAAC,CAAE,EAACnV,KAAK;EAGhG,IAAIkG,IAAA,CAAKa,GAAG,CAACuO,KAAA,KAAU,KAAKJ,SAAc,KAAAhP,IAAA,CAAKoB,KAAK,CAAC4N,SAAY;IAE/DI,KAAQ,GAAAJ,SAAA,GAAYhP,IAAK,CAAAoB,KAAK,CAAC4N,SAAA;;EAEjC,OAAOI,KAAA;AACT;AAMA,IAAAW,KAAA,GAAe;EAACjB;AAAU,CAAE;ACnGrB,SAASkB,kBAAmBA,CAAAvD,QAAQ,EAAE;EAC3CA,QAAS,CAAArG,GAAG,CAAC,OAAS;IACpB6J,OAAA,EAAS,IAAI;IACbC,MAAA,EAAQ,KAAK;IACbxU,OAAA,EAAS,KAAK;IACdyU,WAAA,EAAa,KAAK;IASlBC,MAAQ;IAERC,IAAA,EAAM,IAAI;IAMVC,KAAO;IAGPC,IAAM;MACJN,OAAA,EAAS,IAAI;MACbO,SAAW;MACXC,eAAA,EAAiB,IAAI;MACrBC,SAAA,EAAW,IAAI;MACfC,UAAY;MACZC,SAAA,EAAWA,CAACC,IAAA,EAAM3T,OAAY,KAAAA,OAAA,CAAQsT,SAAS;MAC/CM,SAAA,EAAWA,CAACD,IAAA,EAAM3T,OAAY,KAAAA,OAAA,CAAQ8O,KAAK;MAC3CkE,MAAA,EAAQ;IACV;IAEAa,MAAQ;MACNd,OAAA,EAAS,IAAI;MACbe,IAAA,EAAM,EAAE;MACRC,UAAY;MACZC,KAAO;IACT;IAGAC,KAAO;MAELlB,OAAA,EAAS,KAAK;MAGdmB,IAAM;MAGNvD,OAAS;QACPC,GAAK;QACLC,MAAQ;MACV;IACF;IAGAkB,KAAO;MACLoC,WAAa;MACbC,WAAa;MACbC,MAAA,EAAQ,KAAK;MACbC,eAAiB;MACjBC,eAAiB;MACjB5D,OAAS;MACToC,OAAA,EAAS,IAAI;MACbyB,QAAA,EAAU,IAAI;MACdC,eAAiB;MACjBC,WAAa;MAEbzW,QAAU,EAAA4U,KAAA,CAAMjB,UAAU,CAAC7J,MAAM;MACjC4M,KAAA,EAAO,EAAC;MACRC,KAAA,EAAO,EAAC;MACRvK,KAAO;MACPwK,UAAY;MAEZC,iBAAA,EAAmB,KAAK;MACxBC,aAAe;MACfC,eAAiB;IACnB;EACF;EAEAzF,QAAA,CAAS0F,KAAK,CAAC,aAAe,WAAS,EAAI;EAC3C1F,QAAA,CAAS0F,KAAK,CAAC,YAAc,WAAS,EAAI;EAC1C1F,QAAA,CAAS0F,KAAK,CAAC,cAAgB,WAAS,EAAI;EAC5C1F,QAAA,CAAS0F,KAAK,CAAC,aAAe,WAAS,EAAI;EAE3C1F,QAAS,CAAAK,QAAQ,CAAC,OAAS;IACzBC,SAAA,EAAW,KAAK;IAChBE,WAAA,EAAcC,IAAA,IAAS,CAACA,IAAA,CAAKkF,UAAU,CAAC,aAAa,CAAClF,IAAA,CAAKkF,UAAU,CAAC,OAAY,KAAAlF,IAAA,KAAS,cAAcA,IAAS;IAClHF,UAAA,EAAaE,IAAS,IAAAA,IAAA,KAAS,YAAgB,IAAAA,IAAA,KAAS,oBAAoBA,IAAS;EACvF;EAEAT,QAAS,CAAAK,QAAQ,CAAC,QAAU;IAC1BC,SAAW;EACb;EAEAN,QAAS,CAAAK,QAAQ,CAAC,aAAe;IAC/BG,WAAA,EAAcC,IAAA,IAASA,IAAS,0BAAqBA,IAAS;IAC9DF,UAAY,EAACE,IAAA,IAASA,IAAS;EACjC;AACF;MClGamF,SAAY,GAAAnY,MAAA,CAAOyC,MAAM,CAAC,IAAI;MAC9B2V,WAAc,GAAApY,MAAA,CAAOyC,MAAM,CAAC,IAAI;AAO7C,SAAS4V,WAASC,IAAI,EAAEzV,GAAG,EAAE;EAC3B,IAAI,CAACA,GAAK;IACR,OAAOyV,IAAA;;EAET,MAAM3W,IAAA,GAAOkB,GAAI,CAAA0B,KAAK,CAAC;EACvB,KAAK,IAAI9C,CAAI,MAAGkG,CAAI,GAAAhG,IAAA,CAAKC,MAAM,EAAEH,CAAA,GAAIkG,CAAG,IAAElG,CAAG;IAC3C,MAAMkB,CAAA,GAAIhB,IAAI,CAACF,CAAE;IACjB6W,IAAA,GAAOA,IAAI,CAAC3V,CAAE,MAAK2V,IAAI,CAAC3V,CAAA,CAAE,GAAG3C,MAAA,CAAOyC,MAAM,CAAC,IAAI;EACjD;EACA,OAAO6V,IAAA;AACT;AAEA,SAASpM,IAAIqM,IAAI,EAAE5U,KAAK,EAAEoH,MAAM,EAAE;EAChC,IAAI,OAAOpH,KAAA,KAAU,QAAU;IAC7B,OAAOR,KAAA,CAAMkV,UAAS,CAAAE,IAAA,EAAM5U,KAAQ,GAAAoH,MAAA;;EAEtC,OAAO5H,KAAA,CAAMkV,UAAS,CAAAE,IAAA,EAAM,EAAK,GAAA5U,KAAA;AACnC;AAMO,MAAM6U,QAAA;EACXC,WAAYA,CAAAC,YAAY,EAAEC,SAAS,EAAE;IACnC,IAAI,CAACxF,SAAS,GAAGtP,SAAA;IACjB,IAAI,CAAC+U,eAAe,GAAG;IACvB,IAAI,CAACC,WAAW,GAAG;IACnB,IAAI,CAAC/G,KAAK,GAAG;IACb,IAAI,CAACgH,QAAQ,GAAG,EAAC;IACjB,IAAI,CAACC,gBAAgB,GAAIC,OAAA,IAAYA,OAAA,CAAQhE,KAAK,CAACiE,QAAQ,CAACC,mBAAmB;IAC/E,IAAI,CAACC,QAAQ,GAAG,EAAC;IACjB,IAAI,CAACC,MAAM,GAAG,CACZ,aACA,YACA,SACA,cACA,YACD;IACD,IAAI,CAACC,IAAI,GAAG;MACVC,MAAQ;MACR9T,IAAM;MACN+T,KAAO;MACPC,UAAY;MACZC,MAAA,EAAQ;IACV;IACA,IAAI,CAACC,KAAK,GAAG,EAAC;IACd,IAAI,CAACC,oBAAoB,GAAG,CAACC,GAAA,EAAK5W,OAAY,KAAAgP,aAAA,CAAchP,OAAA,CAAQ4V,eAAe;IACnF,IAAI,CAACiB,gBAAgB,GAAG,CAACD,GAAA,EAAK5W,OAAY,KAAAgP,aAAA,CAAchP,OAAA,CAAQ6V,WAAW;IAC3E,IAAI,CAACiB,UAAU,GAAG,CAACF,GAAA,EAAK5W,OAAY,KAAAgP,aAAA,CAAchP,OAAA,CAAQ8O,KAAK;IAC/D,IAAI,CAACiI,SAAS,GAAG;IACjB,IAAI,CAACC,WAAW,GAAG;MACjBC,IAAM;MACNC,SAAA,EAAW,IAAI;MACfC,gBAAA,EAAkB;IACpB;IACA,IAAI,CAACC,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,OAAO,GAAG,EAAC;IAChB,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,KAAK,GAAG7W,SAAA;IACb,IAAI,CAAC8W,MAAM,GAAG,EAAC;IACf,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,uBAAuB,GAAG,IAAI;IAEnC,IAAI,CAACjI,QAAQ,CAAC8F,YAAA;IACd,IAAI,CAACrX,KAAK,CAACsX,SAAA;EACb;EAMAzM,GAAIA,CAAAvI,KAAK,EAAEoH,MAAM,EAAE;IACjB,OAAOmB,GAAA,CAAI,IAAI,EAAEvI,KAAO,EAAAoH,MAAA;EAC1B;EAKAuJ,IAAI3Q,KAAK,EAAE;IACT,OAAO0U,UAAA,CAAS,IAAI,EAAE1U,KAAA;EACxB;EAMAiP,QAASA,CAAAjP,KAAK,EAAEoH,MAAM,EAAE;IACtB,OAAOmB,GAAA,CAAIkM,WAAA,EAAazU,KAAO,EAAAoH,MAAA;EACjC;EAEA+P,QAASA,CAAAnX,KAAK,EAAEoH,MAAM,EAAE;IACtB,OAAOmB,GAAA,CAAIiM,SAAA,EAAWxU,KAAO,EAAAoH,MAAA;EAC/B;EAmBAkN,MAAMtU,KAAK,EAAEqP,IAAI,EAAE+H,WAAW,EAAEC,UAAU,EAAE;IAC1C,MAAMC,WAAA,GAAc5C,UAAS,KAAI,EAAE1U,KAAA;IACnC,MAAMuX,iBAAA,GAAoB7C,UAAS,KAAI,EAAE0C,WAAA;IACzC,MAAMI,WAAA,GAAc,GAAM,GAAAnI,IAAA;IAE1BhT,MAAO,CAAAob,gBAAgB,CAACH,WAAa;MAEnC,CAACE,WAAA,GAAc;QACbvb,KAAO,EAAAqb,WAAW,CAACjI,IAAK;QACxBqI,QAAA,EAAU;MACZ;MAEA,CAACrI,IAAA,GAAO;QACNzH,UAAA,EAAY,IAAI;QAChB+I,GAAMA,CAAA;UACJ,MAAMgH,KAAA,GAAQ,IAAI,CAACH,WAAY;UAC/B,MAAM3Y,MAAA,GAAS0Y,iBAAiB,CAACF,UAAW;UAC5C,IAAI3a,QAAA,CAASib,KAAQ;YACnB,OAAOtb,MAAO,CAAAoP,MAAM,CAAC,IAAI5M,MAAQ,EAAA8Y,KAAA;;UAEnC,OAAO3a,cAAA,CAAe2a,KAAO,EAAA9Y,MAAA;QAC/B;QACA0J,IAAItM,KAAK,EAAE;UACT,IAAI,CAACub,WAAA,CAAY,GAAGvb,KAAA;QACtB;MACF;IACF;EACF;EAEAyB,MAAMka,QAAQ,EAAE;IACdA,QAAA,CAAS/P,OAAO,CAAEnK,KAAA,IAAUA,KAAA,CAAM,IAAI;EACxC;AACF;AAGA,IAAAkR,QAAA,GAAe,eAAgB,IAAIiG,QAAS;EAC1CzF,WAAA,EAAcC,IAAA,IAAS,CAACA,IAAA,CAAKkF,UAAU,CAAC;EACxCpF,UAAY,EAACE,IAAA,IAASA,IAAS;EAC/B0G,KAAO;IACL7G,SAAW;EACb;EACAmH,WAAa;IACXjH,WAAA,EAAa,KAAK;IAClBD,UAAA,EAAY;EACd;AACF,CAAG,GAACR,uBAAA,EAAyBmB,oBAAA,EAAsBqC,kBAAA,CAAmB,CAAE;;AC5JxE;;;;;AAKC;AACM,SAAS0F,YAAaA,CAAAnC,IAAc,EAAE;EAC3C,IAAI,CAACA,IAAA,IAAQ1Z,aAAc,CAAA0Z,IAAA,CAAK7T,IAAI,CAAK,IAAA7F,aAAA,CAAc0Z,IAAK,CAAAC,MAAM,CAAG;IACnE,OAAO,IAAI;;EAGb,OAAO,CAACD,IAAK,CAAAE,KAAK,GAAGF,IAAA,CAAKE,KAAK,GAAG,GAAM,KAAE,KACvCF,IAAA,CAAKI,MAAM,GAAGJ,IAAK,CAAAI,MAAM,GAAG,MAAM,EAAC,CACpC,GAAAJ,IAAA,CAAK7T,IAAI,GAAG,KACZ,GAAA6T,IAAA,CAAKC,MAAM;AACf;AAEA;;AAEC;AACM,SAASmC,YACdA,CAAA7B,GAA6B,EAC7B8B,IAA4B,EAC5BC,EAAY,EACZC,OAAe,EACfC,MAAc,EACd;EACA,IAAIC,SAAA,GAAYJ,IAAI,CAACG,MAAO;EAC5B,IAAI,CAACC,SAAW;IACdA,SAAY,GAAAJ,IAAI,CAACG,MAAO,IAAGjC,GAAA,CAAImC,WAAW,CAACF,MAAA,EAAQ7E,KAAK;IACxD2E,EAAA,CAAGjX,IAAI,CAACmX,MAAA;;EAEV,IAAIC,SAAA,GAAYF,OAAS;IACvBA,OAAU,GAAAE,SAAA;;EAEZ,OAAOF,OAAA;AACT;AAKA;;AAEC,GAFD,CAEC;AAEM,SAASI,aACdpC,GAA6B,EAC7BP,IAAY,EACZ4C,aAAqB,EACrBC,KAAiF,EACjF;EACAA,KAAA,GAAQA,KAAA,IAAS,EAAC;EAClB,IAAIR,IAAA,GAAOQ,KAAM,CAAAR,IAAI,GAAGQ,KAAM,CAAAR,IAAI,IAAI,EAAC;EACvC,IAAIC,EAAA,GAAKO,KAAM,CAAAC,cAAc,GAAGD,KAAM,CAAAC,cAAc,IAAI,EAAE;EAE1D,IAAID,KAAA,CAAM7C,IAAI,KAAKA,IAAM;IACvBqC,IAAO,GAAAQ,KAAA,CAAMR,IAAI,GAAG,EAAC;IACrBC,EAAK,GAAAO,KAAA,CAAMC,cAAc,GAAG,EAAE;IAC9BD,KAAA,CAAM7C,IAAI,GAAGA,IAAA;;EAGfO,GAAA,CAAIwC,IAAI;EAERxC,GAAA,CAAIP,IAAI,GAAGA,IAAA;EACX,IAAIuC,OAAU;EACd,MAAM5Z,IAAA,GAAOia,aAAA,CAAcra,MAAM;EACjC,IAAIH,CAAA,EAAW4a,CAAW,EAAAC,IAAA,EAAcC,KAAwB,EAAAC,WAAA;EAChE,KAAK/a,CAAI,MAAGA,CAAI,GAAAO,IAAA,EAAMP,CAAK;IACzB8a,KAAQ,GAAAN,aAAa,CAACxa,CAAE;;IAGxB,IAAI8a,KAAA,KAAU1Y,SAAa,IAAA0Y,KAAA,KAAU,IAAI,IAAI,CAAC1c,OAAA,CAAQ0c,KAAQ;MAC5DX,OAAA,GAAUH,YAAa,CAAA7B,GAAA,EAAK8B,IAAM,EAAAC,EAAA,EAAIC,OAAS,EAAAW,KAAA;KAC1C,UAAI1c,OAAA,CAAQ0c,KAAQ;;;MAGzB,KAAKF,CAAA,GAAI,GAAGC,IAAO,GAAAC,KAAA,CAAM3a,MAAM,EAAEya,CAAA,GAAIC,IAAA,EAAMD,CAAK;QAC9CG,WAAc,GAAAD,KAAK,CAACF,CAAE;;QAEtB,IAAIG,WAAA,KAAgB3Y,SAAa,IAAA2Y,WAAA,KAAgB,IAAI,IAAI,CAAC3c,OAAA,CAAQ2c,WAAc;UAC9EZ,OAAA,GAAUH,YAAa,CAAA7B,GAAA,EAAK8B,IAAM,EAAAC,EAAA,EAAIC,OAAS,EAAAY,WAAA;;MAEnD;;EAEJ;EAEA5C,GAAA,CAAI6C,OAAO;EAEX,MAAMC,KAAA,GAAQf,EAAG,CAAA/Z,MAAM,GAAG;EAC1B,IAAI8a,KAAA,GAAQT,aAAc,CAAAra,MAAM,EAAE;IAChC,KAAKH,CAAI,MAAGA,CAAI,GAAAib,KAAA,EAAOjb,CAAK;MAC1B,OAAOia,IAAI,CAACC,EAAE,CAACla,CAAA,CAAE,CAAC;IACpB;IACAka,EAAG,CAAA5P,MAAM,CAAC,CAAG,EAAA2Q,KAAA;;EAEf,OAAOd,OAAA;AACT;AAEA;;;;;;;;AAQO,SAASe,WAAYA,CAAA3H,KAAY,EAAE4H,KAAa,EAAE5F,KAAa,EAAE;EACtE,MAAM+B,gBAAA,GAAmB/D,KAAA,CAAM6H,uBAAuB;EACtD,MAAMC,SAAA,GAAY9F,KAAA,KAAU,CAAI,GAAAlR,IAAA,CAAKqC,GAAG,CAAC6O,KAAA,GAAQ,CAAG,SAAO,CAAC;EAC5D,OAAOlR,IAAA,CAAKiB,KAAK,CAAE,CAAA6V,KAAQ,GAAAE,SAAQ,IAAK/D,gBAAA,IAAoBA,gBAAmB,GAAA+D,SAAA;AACjF;AAEA;;AAEC;AACM,SAASC,YAAYC,MAAyB,EAAEpD,GAA8B,EAAE;EACrFA,GAAM,GAAAA,GAAA,IAAOoD,MAAO,CAAAC,UAAU,CAAC;EAE/BrD,GAAA,CAAIwC,IAAI;;;EAGRxC,GAAA,CAAIsD,cAAc;EAClBtD,GAAI,CAAAuD,SAAS,CAAC,CAAG,KAAGH,MAAA,CAAOhG,KAAK,EAAEgG,MAAA,CAAOI,MAAM;EAC/CxD,GAAA,CAAI6C,OAAO;AACb;AASO,SAASY,UACdzD,GAA6B,EAC7B5W,OAAyB,EACzBkB,CAAS,EACTE,CAAS,EACT;;EAEAkZ,eAAA,CAAgB1D,GAAK,EAAA5W,OAAA,EAASkB,CAAG,EAAAE,CAAA,EAAG,IAAI;AAC1C;AAEA;AACO,SAASkZ,eACdA,CAAA1D,GAA6B,EAC7B5W,OAAyB,EACzBkB,CAAS,EACTE,CAAS,EACTmZ,CAAS,EACT;EACA,IAAIxd,IAAA,EAAcyd,OAAiB,EAAAC,OAAA,EAAiBjY,IAAc,EAAAkY,YAAA,EAAsB1G,KAAA,EAAe2G,QAAkB,EAAAC,QAAA;EACzH,MAAMrE,KAAA,GAAQvW,OAAA,CAAQ6a,UAAU;EAChC,MAAMC,QAAA,GAAW9a,OAAA,CAAQ8a,QAAQ;EACjC,MAAMC,MAAA,GAAS/a,OAAA,CAAQ+a,MAAM;EAC7B,IAAIC,GAAM,GAAC,CAAAF,QAAA,IAAY,KAAK3X,WAAA;EAE5B,IAAIoT,KAAA,IAAS,OAAOA,KAAA,KAAU,QAAU;IACtCxZ,IAAA,GAAOwZ,KAAA,CAAMrZ,QAAQ;IACrB,IAAIH,IAAA,KAAS,2BAA+B,IAAAA,IAAA,KAAS,4BAA8B;MACjF6Z,GAAA,CAAIwC,IAAI;MACRxC,GAAI,CAAAqE,SAAS,CAAC/Z,CAAG,EAAAE,CAAA;MACjBwV,GAAA,CAAIsE,MAAM,CAACF,GAAA;MACXpE,GAAA,CAAIuE,SAAS,CAAC5E,KAAA,EAAO,CAACA,KAAA,CAAMvC,KAAK,GAAG,GAAG,CAACuC,KAAA,CAAM6D,MAAM,GAAG,GAAG7D,KAAA,CAAMvC,KAAK,EAAEuC,KAAA,CAAM6D,MAAM;MACnFxD,GAAA,CAAI6C,OAAO;MACX;;;EAIJ,IAAI7U,KAAA,CAAMmW,MAAW,KAAAA,MAAA,IAAU,CAAG;IAChC;;EAGFnE,GAAA,CAAIwE,SAAS;EAEb,QAAQ7E,KAAA;;IAEN;MACE,IAAIgE,CAAG;QACL3D,GAAI,CAAAyE,OAAO,CAACna,CAAG,EAAAE,CAAA,EAAGmZ,CAAA,GAAI,CAAG,EAAAQ,MAAA,EAAQ,GAAG,CAAG,EAAAhY,GAAA;OAClC;QACL6T,GAAA,CAAI0E,GAAG,CAACpa,CAAG,EAAAE,CAAA,EAAG2Z,MAAA,EAAQ,CAAG,EAAAhY,GAAA;;MAE3B6T,GAAA,CAAI2E,SAAS;MACb;IACF,KAAK;MACHvH,KAAQ,GAAAuG,CAAA,GAAIA,CAAI,OAAIQ,MAAM;MAC1BnE,GAAA,CAAI4E,MAAM,CAACta,CAAI,GAAA4B,IAAA,CAAK0J,GAAG,CAACwO,GAAO,IAAAhH,KAAA,EAAO5S,CAAI,GAAA0B,IAAA,CAAK2K,GAAG,CAACuN,GAAO,IAAAD,MAAA;MAC1DC,GAAO,IAAA1X,aAAA;MACPsT,GAAA,CAAI6E,MAAM,CAACva,CAAI,GAAA4B,IAAA,CAAK0J,GAAG,CAACwO,GAAO,IAAAhH,KAAA,EAAO5S,CAAI,GAAA0B,IAAA,CAAK2K,GAAG,CAACuN,GAAO,IAAAD,MAAA;MAC1DC,GAAO,IAAA1X,aAAA;MACPsT,GAAA,CAAI6E,MAAM,CAACva,CAAI,GAAA4B,IAAA,CAAK0J,GAAG,CAACwO,GAAO,IAAAhH,KAAA,EAAO5S,CAAI,GAAA0B,IAAA,CAAK2K,GAAG,CAACuN,GAAO,IAAAD,MAAA;MAC1DnE,GAAA,CAAI2E,SAAS;MACb;IACF,KAAK;;;;;;;;MAQHb,YAAA,GAAeK,MAAS;MACxBvY,IAAA,GAAOuY,MAAS,GAAAL,YAAA;MAChBF,OAAA,GAAU1X,IAAK,CAAA2K,GAAG,CAACuN,GAAA,GAAM3X,UAAc,IAAAb,IAAA;MACvCmY,QAAW,GAAA7X,IAAA,CAAK2K,GAAG,CAACuN,GAAM,GAAA3X,UAAA,KAAekX,CAAA,GAAIA,CAAI,OAAIG,YAAe,GAAAlY,IAAI,CAAD;MACvEiY,OAAA,GAAU3X,IAAK,CAAA0J,GAAG,CAACwO,GAAA,GAAM3X,UAAc,IAAAb,IAAA;MACvCoY,QAAW,GAAA9X,IAAA,CAAK0J,GAAG,CAACwO,GAAM,GAAA3X,UAAA,KAAekX,CAAA,GAAIA,CAAI,OAAIG,YAAe,GAAAlY,IAAI,CAAD;MACvEoU,GAAI,CAAA0E,GAAG,CAACpa,CAAI,GAAAyZ,QAAA,EAAUvZ,CAAA,GAAIqZ,OAAS,EAAAC,YAAA,EAAcM,GAAM,GAAAnY,EAAA,EAAImY,GAAM,GAAA5X,OAAA;MACjEwT,GAAI,CAAA0E,GAAG,CAACpa,CAAI,GAAA0Z,QAAA,EAAUxZ,CAAA,GAAIoZ,OAAS,EAAAE,YAAA,EAAcM,GAAA,GAAM5X,OAAS,EAAA4X,GAAA;MAChEpE,GAAI,CAAA0E,GAAG,CAACpa,CAAI,GAAAyZ,QAAA,EAAUvZ,CAAA,GAAIqZ,OAAS,EAAAC,YAAA,EAAcM,GAAA,EAAKA,GAAM,GAAA5X,OAAA;MAC5DwT,GAAI,CAAA0E,GAAG,CAACpa,CAAI,GAAA0Z,QAAA,EAAUxZ,CAAA,GAAIoZ,OAAS,EAAAE,YAAA,EAAcM,GAAM,GAAA5X,OAAA,EAAS4X,GAAM,GAAAnY,EAAA;MACtE+T,GAAA,CAAI2E,SAAS;MACb;IACF,KAAK;MACH,IAAI,CAACT,QAAU;QACbtY,IAAO,GAAAM,IAAA,CAAK4Y,OAAO,GAAGX,MAAA;QACtB/G,KAAQ,GAAAuG,CAAA,GAAIA,CAAI,OAAI/X,IAAI;QACxBoU,GAAI,CAAA+E,IAAI,CAACza,CAAI,GAAA8S,KAAA,EAAO5S,CAAA,GAAIoB,IAAM,MAAIwR,KAAA,EAAO,CAAI,GAAAxR,IAAA;QAC7C;;MAEFwY,GAAO,IAAA3X,UAAA;IACT;IACA,KAAK;MACHsX,QAAW,GAAA7X,IAAA,CAAK2K,GAAG,CAACuN,GAAA,KAAQT,CAAI,GAAAA,CAAA,GAAI,CAAI,GAAAQ,MAAM,CAAD;MAC7CP,OAAU,GAAA1X,IAAA,CAAK2K,GAAG,CAACuN,GAAO,IAAAD,MAAA;MAC1BN,OAAU,GAAA3X,IAAA,CAAK0J,GAAG,CAACwO,GAAO,IAAAD,MAAA;MAC1BH,QAAW,GAAA9X,IAAA,CAAK0J,GAAG,CAACwO,GAAA,KAAQT,CAAI,GAAAA,CAAA,GAAI,CAAI,GAAAQ,MAAM,CAAD;MAC7CnE,GAAA,CAAI4E,MAAM,CAACta,CAAI,GAAAyZ,QAAA,EAAUvZ,CAAI,GAAAqZ,OAAA;MAC7B7D,GAAA,CAAI6E,MAAM,CAACva,CAAI,GAAA0Z,QAAA,EAAUxZ,CAAI,GAAAoZ,OAAA;MAC7B5D,GAAA,CAAI6E,MAAM,CAACva,CAAI,GAAAyZ,QAAA,EAAUvZ,CAAI,GAAAqZ,OAAA;MAC7B7D,GAAA,CAAI6E,MAAM,CAACva,CAAI,GAAA0Z,QAAA,EAAUxZ,CAAI,GAAAoZ,OAAA;MAC7B5D,GAAA,CAAI2E,SAAS;MACb;IACF,KAAK;MACHP,GAAO,IAAA3X,UAAA;IACT;IACA,KAAK;MACHsX,QAAW,GAAA7X,IAAA,CAAK2K,GAAG,CAACuN,GAAA,KAAQT,CAAI,GAAAA,CAAA,GAAI,CAAI,GAAAQ,MAAM,CAAD;MAC7CP,OAAU,GAAA1X,IAAA,CAAK2K,GAAG,CAACuN,GAAO,IAAAD,MAAA;MAC1BN,OAAU,GAAA3X,IAAA,CAAK0J,GAAG,CAACwO,GAAO,IAAAD,MAAA;MAC1BH,QAAW,GAAA9X,IAAA,CAAK0J,GAAG,CAACwO,GAAA,KAAQT,CAAI,GAAAA,CAAA,GAAI,CAAI,GAAAQ,MAAM,CAAD;MAC7CnE,GAAA,CAAI4E,MAAM,CAACta,CAAI,GAAAyZ,QAAA,EAAUvZ,CAAI,GAAAqZ,OAAA;MAC7B7D,GAAA,CAAI6E,MAAM,CAACva,CAAI,GAAAyZ,QAAA,EAAUvZ,CAAI,GAAAqZ,OAAA;MAC7B7D,GAAA,CAAI4E,MAAM,CAACta,CAAI,GAAA0Z,QAAA,EAAUxZ,CAAI,GAAAoZ,OAAA;MAC7B5D,GAAA,CAAI6E,MAAM,CAACva,CAAI,GAAA0Z,QAAA,EAAUxZ,CAAI,GAAAoZ,OAAA;MAC7B;IACF,KAAK;MACHG,QAAW,GAAA7X,IAAA,CAAK2K,GAAG,CAACuN,GAAA,KAAQT,CAAI,GAAAA,CAAA,GAAI,CAAI,GAAAQ,MAAM,CAAD;MAC7CP,OAAU,GAAA1X,IAAA,CAAK2K,GAAG,CAACuN,GAAO,IAAAD,MAAA;MAC1BN,OAAU,GAAA3X,IAAA,CAAK0J,GAAG,CAACwO,GAAO,IAAAD,MAAA;MAC1BH,QAAW,GAAA9X,IAAA,CAAK0J,GAAG,CAACwO,GAAA,KAAQT,CAAI,GAAAA,CAAA,GAAI,CAAI,GAAAQ,MAAM,CAAD;MAC7CnE,GAAA,CAAI4E,MAAM,CAACta,CAAI,GAAAyZ,QAAA,EAAUvZ,CAAI,GAAAqZ,OAAA;MAC7B7D,GAAA,CAAI6E,MAAM,CAACva,CAAI,GAAAyZ,QAAA,EAAUvZ,CAAI,GAAAqZ,OAAA;MAC7B7D,GAAA,CAAI4E,MAAM,CAACta,CAAI,GAAA0Z,QAAA,EAAUxZ,CAAI,GAAAoZ,OAAA;MAC7B5D,GAAA,CAAI6E,MAAM,CAACva,CAAI,GAAA0Z,QAAA,EAAUxZ,CAAI,GAAAoZ,OAAA;MAC7BQ,GAAO,IAAA3X,UAAA;MACPsX,QAAW,GAAA7X,IAAA,CAAK2K,GAAG,CAACuN,GAAA,KAAQT,CAAI,GAAAA,CAAA,GAAI,CAAI,GAAAQ,MAAM,CAAD;MAC7CP,OAAU,GAAA1X,IAAA,CAAK2K,GAAG,CAACuN,GAAO,IAAAD,MAAA;MAC1BN,OAAU,GAAA3X,IAAA,CAAK0J,GAAG,CAACwO,GAAO,IAAAD,MAAA;MAC1BH,QAAW,GAAA9X,IAAA,CAAK0J,GAAG,CAACwO,GAAA,KAAQT,CAAI,GAAAA,CAAA,GAAI,CAAI,GAAAQ,MAAM,CAAD;MAC7CnE,GAAA,CAAI4E,MAAM,CAACta,CAAI,GAAAyZ,QAAA,EAAUvZ,CAAI,GAAAqZ,OAAA;MAC7B7D,GAAA,CAAI6E,MAAM,CAACva,CAAI,GAAAyZ,QAAA,EAAUvZ,CAAI,GAAAqZ,OAAA;MAC7B7D,GAAA,CAAI4E,MAAM,CAACta,CAAI,GAAA0Z,QAAA,EAAUxZ,CAAI,GAAAoZ,OAAA;MAC7B5D,GAAA,CAAI6E,MAAM,CAACva,CAAI,GAAA0Z,QAAA,EAAUxZ,CAAI,GAAAoZ,OAAA;MAC7B;IACF,KAAK;MACHA,OAAA,GAAUD,CAAA,GAAIA,CAAI,OAAIzX,IAAA,CAAK2K,GAAG,CAACuN,GAAA,IAAOD,MAAM;MAC5CN,OAAU,GAAA3X,IAAA,CAAK0J,GAAG,CAACwO,GAAO,IAAAD,MAAA;MAC1BnE,GAAA,CAAI4E,MAAM,CAACta,CAAI,GAAAsZ,OAAA,EAASpZ,CAAI,GAAAqZ,OAAA;MAC5B7D,GAAA,CAAI6E,MAAM,CAACva,CAAI,GAAAsZ,OAAA,EAASpZ,CAAI,GAAAqZ,OAAA;MAC5B;IACF,KAAK;MACH7D,GAAI,CAAA4E,MAAM,CAACta,CAAG,EAAAE,CAAA;MACdwV,GAAA,CAAI6E,MAAM,CAACva,CAAA,GAAI4B,IAAA,CAAK2K,GAAG,CAACuN,GAAA,KAAQT,CAAA,GAAIA,CAAI,OAAIQ,MAAM,CAAD,EAAI3Z,CAAA,GAAI0B,IAAK,CAAA0J,GAAG,CAACwO,GAAO,IAAAD,MAAA;MACzE;IACF,KAAK,KAAK;MACRnE,GAAA,CAAI2E,SAAS;MACb;EACJ;EAEA3E,GAAA,CAAIgF,IAAI;EACR,IAAI5b,OAAA,CAAQ6b,WAAW,GAAG,CAAG;IAC3BjF,GAAA,CAAIkF,MAAM;;AAEd;AAEA;;;;;;;AAOO,SAASC,cACdA,CAAAC,KAAY,EACZC,IAAU,EACVC,MAAe,EACf;EACAA,MAAS,GAAAA,MAAA,IAAU;EAEnB,OAAO,CAACD,IAAA,IAASD,KAAS,IAAAA,KAAA,CAAM9a,CAAC,GAAG+a,IAAK,CAAAzR,IAAI,GAAG0R,MAAA,IAAUF,KAAM,CAAA9a,CAAC,GAAG+a,IAAA,CAAKxR,KAAK,GAAGyR,MACjF,IAAAF,KAAA,CAAM5a,CAAC,GAAG6a,IAAK,CAAArL,GAAG,GAAGsL,MAAA,IAAUF,KAAM,CAAA5a,CAAC,GAAG6a,IAAA,CAAKpL,MAAM,GAAGqL,MAAA;AACzD;AAEO,SAASC,SAASvF,GAA6B,EAAEqF,IAAU,EAAE;EAClErF,GAAA,CAAIwC,IAAI;EACRxC,GAAA,CAAIwE,SAAS;EACbxE,GAAA,CAAI+E,IAAI,CAACM,IAAA,CAAKzR,IAAI,EAAEyR,IAAA,CAAKrL,GAAG,EAAEqL,IAAA,CAAKxR,KAAK,GAAGwR,IAAA,CAAKzR,IAAI,EAAEyR,IAAA,CAAKpL,MAAM,GAAGoL,IAAA,CAAKrL,GAAG;EAC5EgG,GAAA,CAAIzD,IAAI;AACV;AAEO,SAASiJ,UAAWA,CAAAxF,GAA6B,EAAE;EACxDA,GAAA,CAAI6C,OAAO;AACb;AAEA;;AAEC;AACM,SAAS4C,cACdA,CAAAzF,GAA6B,EAC7BhW,QAAe,EACfpB,MAAa,EACb8c,IAAc,EACdrF,IAAa,EACb;EACA,IAAI,CAACrW,QAAU;IACb,OAAOgW,GAAA,CAAI6E,MAAM,CAACjc,MAAA,CAAO0B,CAAC,EAAE1B,MAAA,CAAO4B,CAAC;;EAEtC,IAAI6V,IAAA,KAAS,QAAU;IACrB,MAAMsF,QAAA,GAAW,CAAC3b,QAAA,CAASM,CAAC,GAAG1B,MAAA,CAAO0B,CAAA,IAAK;IAC3C0V,GAAA,CAAI6E,MAAM,CAACc,QAAU,EAAA3b,QAAA,CAASQ,CAAC;IAC/BwV,GAAA,CAAI6E,MAAM,CAACc,QAAU,EAAA/c,MAAA,CAAO4B,CAAC;EAC/B,OAAO,IAAI6V,IAAA,KAAS,OAAY,MAAC,CAACqF,IAAM;IACtC1F,GAAA,CAAI6E,MAAM,CAAC7a,QAAA,CAASM,CAAC,EAAE1B,MAAA,CAAO4B,CAAC;GAC1B;IACLwV,GAAA,CAAI6E,MAAM,CAACjc,MAAA,CAAO0B,CAAC,EAAEN,QAAA,CAASQ,CAAC;;EAEjCwV,GAAA,CAAI6E,MAAM,CAACjc,MAAA,CAAO0B,CAAC,EAAE1B,MAAA,CAAO4B,CAAC;AAC/B;AAEA;;;AAGO,SAASob,eACd5F,GAA6B,EAC7BhW,QAAqB,EACrBpB,MAAmB,EACnB8c,IAAc,EACd;EACA,IAAI,CAAC1b,QAAU;IACb,OAAOgW,GAAA,CAAI6E,MAAM,CAACjc,MAAA,CAAO0B,CAAC,EAAE1B,MAAA,CAAO4B,CAAC;;EAEtCwV,GAAA,CAAI6F,aAAa,CACfH,IAAO,GAAA1b,QAAA,CAAS8b,IAAI,GAAG9b,QAAA,CAAS+b,IAAI,EACpCL,IAAA,GAAO1b,QAAS,CAAAgc,IAAI,GAAGhc,QAAA,CAASic,IAAI,EACpCP,IAAA,GAAO9c,MAAO,CAAAmd,IAAI,GAAGnd,MAAA,CAAOkd,IAAI,EAChCJ,IAAA,GAAO9c,MAAO,CAAAqd,IAAI,GAAGrd,MAAA,CAAOod,IAAI,EAChCpd,MAAA,CAAO0B,CAAC,EACR1B,MAAA,CAAO4B,CAAC;AACZ;AAEA,SAAS0b,aAAcA,CAAAlG,GAA6B,EAAEmG,IAAoB,EAAE;EAC1E,IAAIA,IAAA,CAAKC,WAAW,EAAE;IACpBpG,GAAI,CAAAqE,SAAS,CAAC8B,IAAA,CAAKC,WAAW,CAAC,EAAE,EAAED,IAAA,CAAKC,WAAW,CAAC,CAAE;;EAGxD,IAAI,CAACrgB,aAAA,CAAcogB,IAAK,CAAAjC,QAAQ,CAAG;IACjClE,GAAI,CAAAsE,MAAM,CAAC6B,IAAA,CAAKjC,QAAQ;;EAG1B,IAAIiC,IAAA,CAAKjO,KAAK,EAAE;IACd8H,GAAI,CAAAqG,SAAS,GAAGF,IAAA,CAAKjO,KAAK;;EAG5B,IAAIiO,IAAA,CAAKG,SAAS,EAAE;IAClBtG,GAAI,CAAAsG,SAAS,GAAGH,IAAA,CAAKG,SAAS;;EAGhC,IAAIH,IAAA,CAAKI,YAAY,EAAE;IACrBvG,GAAI,CAAAuG,YAAY,GAAGJ,IAAA,CAAKI,YAAY;;AAExC;AAEA,SAASC,aACPxG,GAA6B,EAC7B1V,CAAS,EACTE,CAAS,EACTic,IAAY,EACZN,IAAoB,EACpB;EACA,IAAIA,IAAK,CAAAO,aAAa,IAAIP,IAAA,CAAKQ,SAAS,EAAE;IACxC;;;;;;AAMC;IACD,MAAMC,OAAA,GAAU5G,GAAI,CAAAmC,WAAW,CAACsE,IAAA;IAChC,MAAM7S,IAAA,GAAOtJ,CAAI,GAAAsc,OAAA,CAAQC,qBAAqB;IAC9C,MAAMhT,KAAA,GAAQvJ,CAAI,GAAAsc,OAAA,CAAQE,sBAAsB;IAChD,MAAM9M,GAAA,GAAMxP,CAAI,GAAAoc,OAAA,CAAQG,uBAAuB;IAC/C,MAAM9M,MAAA,GAASzP,CAAI,GAAAoc,OAAA,CAAQI,wBAAwB;IACnD,MAAMC,WAAA,GAAcd,IAAK,CAAAO,aAAa,GAAI,CAAA1M,GAAM,GAAAC,MAAK,IAAK,IAAIA,MAAM;IAEpE+F,GAAI,CAAAkH,WAAW,GAAGlH,GAAA,CAAIqG,SAAS;IAC/BrG,GAAA,CAAIwE,SAAS;IACbxE,GAAA,CAAItD,SAAS,GAAGyJ,IAAK,CAAAgB,eAAe,IAAI;IACxCnH,GAAI,CAAA4E,MAAM,CAAChR,IAAM,EAAAqT,WAAA;IACjBjH,GAAI,CAAA6E,MAAM,CAAChR,KAAO,EAAAoT,WAAA;IAClBjH,GAAA,CAAIkF,MAAM;;AAEd;AAEA,SAASkC,YAAaA,CAAApH,GAA6B,EAAEmG,IAAqB,EAAE;EAC1E,MAAMkB,QAAA,GAAWrH,GAAA,CAAIqG,SAAS;EAE9BrG,GAAI,CAAAqG,SAAS,GAAGF,IAAA,CAAKjO,KAAK;EAC1B8H,GAAA,CAAIsH,QAAQ,CAACnB,IAAK,CAAAvS,IAAI,EAAEuS,IAAA,CAAKnM,GAAG,EAAEmM,IAAK,CAAA/I,KAAK,EAAE+I,IAAA,CAAK3C,MAAM;EACzDxD,GAAA,CAAIqG,SAAS,GAAGgB,QAAA;AAClB;AAEA;;AAEC;AACM,SAASE,WACdvH,GAA6B,EAC7B1C,IAAuB,EACvBhT,CAAS,EACTE,CAAS,EACTiV,IAAoB,EACpB0G,IAAuB,KAAE,EACzB;EACA,MAAMqB,KAAA,GAAQvhB,OAAQ,CAAAqX,IAAA,IAAQA,IAAO,IAACA,IAAA,CAAK;EAC3C,MAAM4H,MAAA,GAASiB,IAAK,CAAAsB,WAAW,GAAG,CAAK,IAAAtB,IAAA,CAAKuB,WAAW,KAAK;EAC5D,IAAI7f,CAAW,EAAA4e,IAAA;EAEfzG,GAAA,CAAIwC,IAAI;EACRxC,GAAI,CAAAP,IAAI,GAAGA,IAAA,CAAKwC,MAAM;EACtBiE,aAAA,CAAclG,GAAK,EAAAmG,IAAA;EAEnB,KAAKte,CAAA,GAAI,CAAG,EAAAA,CAAA,GAAI2f,KAAA,CAAMxf,MAAM,EAAE,EAAEH,CAAG;IACjC4e,IAAO,GAAAe,KAAK,CAAC3f,CAAE;IAEf,IAAIse,IAAA,CAAKwB,QAAQ,EAAE;MACjBP,YAAa,CAAApH,GAAA,EAAKmG,IAAA,CAAKwB,QAAQ;;IAGjC,IAAIzC,MAAQ;MACV,IAAIiB,IAAA,CAAKuB,WAAW,EAAE;QACpB1H,GAAI,CAAAkH,WAAW,GAAGf,IAAA,CAAKuB,WAAW;;MAGpC,IAAI,CAAC3hB,aAAA,CAAcogB,IAAK,CAAAsB,WAAW,CAAG;QACpCzH,GAAI,CAAAtD,SAAS,GAAGyJ,IAAA,CAAKsB,WAAW;;MAGlCzH,GAAA,CAAI4H,UAAU,CAACnB,IAAA,EAAMnc,CAAG,EAAAE,CAAA,EAAG2b,IAAA,CAAK0B,QAAQ;;IAG1C7H,GAAA,CAAI8H,QAAQ,CAACrB,IAAA,EAAMnc,CAAG,EAAAE,CAAA,EAAG2b,IAAA,CAAK0B,QAAQ;IACtCrB,YAAa,CAAAxG,GAAA,EAAK1V,CAAG,EAAAE,CAAA,EAAGic,IAAM,EAAAN,IAAA;IAE9B3b,CAAK,IAAA7D,MAAA,CAAO8Y,IAAA,CAAKG,UAAU;EAC7B;EAEAI,GAAA,CAAI6C,OAAO;AACb;AAEA;;;;AAIC;AACM,SAASkF,mBACd/H,GAA6B,EAC7B+E,IAA2C,EAC3C;EACA,MAAM;IAACza,CAAC;IAAEE,CAAC;IAAEmZ,CAAC;IAAEqE,CAAC;IAAE7D;EAAM,CAAC,GAAGY,IAAA;;EAG7B/E,GAAA,CAAI0E,GAAG,CAACpa,CAAA,GAAI6Z,MAAO,CAAA8D,OAAO,EAAEzd,CAAI,GAAA2Z,MAAA,CAAO8D,OAAO,EAAE9D,MAAA,CAAO8D,OAAO,EAAE,GAAM,GAAAhc,EAAA,EAAIA,EAAA,EAAI,IAAI;;EAGlF+T,GAAA,CAAI6E,MAAM,CAACva,CAAA,EAAGE,CAAI,GAAAwd,CAAA,GAAI7D,MAAA,CAAO+D,UAAU;;EAGvClI,GAAA,CAAI0E,GAAG,CAACpa,CAAA,GAAI6Z,MAAO,CAAA+D,UAAU,EAAE1d,CAAI,GAAAwd,CAAA,GAAI7D,MAAO,CAAA+D,UAAU,EAAE/D,MAAO,CAAA+D,UAAU,EAAEjc,EAAA,EAAIO,OAAA,EAAS,IAAI;;EAG9FwT,GAAA,CAAI6E,MAAM,CAACva,CAAA,GAAIqZ,CAAA,GAAIQ,MAAO,CAAAgE,WAAW,EAAE3d,CAAI,GAAAwd,CAAA;;EAG3ChI,GAAA,CAAI0E,GAAG,CAACpa,CAAA,GAAIqZ,CAAI,GAAAQ,MAAA,CAAOgE,WAAW,EAAE3d,CAAA,GAAIwd,CAAI,GAAA7D,MAAA,CAAOgE,WAAW,EAAEhE,MAAA,CAAOgE,WAAW,EAAE3b,OAAA,EAAS,GAAG,IAAI;;EAGpGwT,GAAA,CAAI6E,MAAM,CAACva,CAAA,GAAIqZ,CAAG,EAAAnZ,CAAA,GAAI2Z,MAAA,CAAOiE,QAAQ;;EAGrCpI,GAAA,CAAI0E,GAAG,CAACpa,CAAA,GAAIqZ,CAAI,GAAAQ,MAAA,CAAOiE,QAAQ,EAAE5d,CAAA,GAAI2Z,MAAO,CAAAiE,QAAQ,EAAEjE,MAAO,CAAAiE,QAAQ,EAAE,CAAG,GAAC5b,OAAA,EAAS,IAAI;;EAGxFwT,GAAA,CAAI6E,MAAM,CAACva,CAAI,GAAA6Z,MAAA,CAAO8D,OAAO,EAAEzd,CAAA;AACjC;ACpgBA,MAAM6d,WAAc;AACpB,MAAMC,UAAa;AAEnB;;;GAAA,C;;;;;;;AAWC;AACM,SAASC,aAAaviB,KAAsB,EAAE4F,IAAY,EAAU;EACzE,MAAM4c,OAAA,GAAU,CAAC,KAAKxiB,KAAI,EAAGyiB,KAAK,CAACJ,WAAA;EACnC,IAAI,CAACG,OAAW,IAAAA,OAAO,CAAC,EAAE,KAAK,QAAU;IACvC,OAAO5c,IAAO;;EAGhB5F,KAAQ,IAACwiB,OAAO,CAAC,CAAE;EAEnB,QAAQA,OAAO,CAAC,CAAE;IAChB,KAAK;MACH,OAAOxiB,KAAA;IACT,KAAK;MACHA,KAAS;MACT;EAGJ;EAEA,OAAO4F,IAAO,GAAA5F,KAAA;AAChB;AAEA,MAAM0iB,YAAe,GAACre,CAAe,KAACA,CAAK;AAQpC,SAASse,kBAAkB3iB,KAAsC,EAAE4iB,KAAwC,EAAE;EAClH,MAAMC,GAAA,GAAM,EAAC;EACb,MAAMC,QAAA,GAAWriB,QAAS,CAAAmiB,KAAA;EAC1B,MAAM7gB,IAAA,GAAO+gB,QAAW,GAAA1iB,MAAA,CAAO2B,IAAI,CAAC6gB,KAAA,IAASA,KAAK;EAClD,MAAMG,IAAA,GAAOtiB,QAAS,CAAAT,KAAA,IAClB8iB,QACE,GAAAE,IAAA,IAAQjiB,cAAe,CAAAf,KAAK,CAACgjB,IAAA,CAAK,EAAEhjB,KAAK,CAAC4iB,KAAK,CAACI,IAAK,EAAC,CACtD,GAAAA,IAAQ,IAAAhjB,KAAK,CAACgjB,IAAA,CAAK,GACrB,MAAMhjB,KAAK;EAEf,KAAK,MAAMgjB,IAAA,IAAQjhB,IAAM;IACvB8gB,GAAG,CAACG,IAAA,CAAK,GAAGN,YAAA,CAAaK,IAAK,CAAAC,IAAA;EAChC;EACA,OAAOH,GAAA;AACT;AAEA;;;;;;;AAOC;AACM,SAASI,MAAOA,CAAAjjB,KAA4B,EAAE;EACnD,OAAO2iB,iBAAA,CAAkB3iB,KAAO;IAACgU,GAAK;IAAKnG,KAAO;IAAKoG,MAAQ;IAAKrG,IAAM;EAAG;AAC/E;AAEA;;;;;;AAMC;AACM,SAASsV,aAAcA,CAAAljB,KAA2B,EAAE;EACzD,OAAO2iB,iBAAA,CAAkB3iB,KAAO,GAAC,WAAW,YAAY,cAAc,cAAc;AACtF;AAEA;;;;;;;AAOC;AACM,SAASmjB,SAAUA,CAAAnjB,KAAqB,EAAa;EAC1D,MAAMgF,GAAA,GAAMie,MAAO,CAAAjjB,KAAA;EAEnBgF,GAAA,CAAIoS,KAAK,GAAGpS,GAAA,CAAI4I,IAAI,GAAG5I,GAAA,CAAI6I,KAAK;EAChC7I,GAAA,CAAIwY,MAAM,GAAGxY,GAAA,CAAIgP,GAAG,GAAGhP,GAAA,CAAIiP,MAAM;EAEjC,OAAOjP,GAAA;AACT;AAEA;;;;;;AAMC;AAEM,SAASoe,OAAOhgB,OAA0B,EAAEigB,QAA4B,EAAE;EAC/EjgB,OAAA,GAAUA,OAAA,IAAW,EAAC;EACtBigB,QAAW,GAAAA,QAAA,IAAY1Q,QAAA,CAAS8G,IAAI;EAEpC,IAAI7T,IAAA,GAAO7E,cAAe,CAAAqC,OAAA,CAAQwC,IAAI,EAAEyd,QAAA,CAASzd,IAAI;EAErD,IAAI,OAAOA,IAAA,KAAS,QAAU;IAC5BA,IAAA,GAAO0d,QAAA,CAAS1d,IAAM;;EAExB,IAAI+T,KAAA,GAAQ5Y,cAAe,CAAAqC,OAAA,CAAQuW,KAAK,EAAE0J,QAAA,CAAS1J,KAAK;EACxD,IAAIA,KAAA,IAAS,CAAC,CAAC,KAAKA,KAAI,EAAG8I,KAAK,CAACH,UAAa;IAC5Cpe,OAAQ,CAAAC,IAAI,CAAC,oCAAoCwV,KAAQ;IACzDA,KAAQ,GAAA1V,SAAA;;EAGV,MAAMwV,IAAO;IACXC,MAAA,EAAQ3Y,cAAe,CAAAqC,OAAA,CAAQsW,MAAM,EAAE2J,QAAA,CAAS3J,MAAM;IACtDE,UAAA,EAAY2I,YAAA,CAAaxhB,cAAe,CAAAqC,OAAA,CAAQwW,UAAU,EAAEyJ,QAAA,CAASzJ,UAAU,CAAG,EAAAhU,IAAA;IAClFA,IAAA;IACA+T,KAAA;IACAE,MAAA,EAAQ9Y,cAAe,CAAAqC,OAAA,CAAQyW,MAAM,EAAEwJ,QAAA,CAASxJ,MAAM;IACtDoC,MAAQ;EACV;EAEAxC,IAAK,CAAAwC,MAAM,GAAGL,YAAa,CAAAnC,IAAA;EAC3B,OAAOA,IAAA;AACT;AAEA;;;;;;;;;;;AAWO,SAAS8J,QAAQC,MAAsB,EAAEpK,OAAgB,EAAE5W,KAAc,EAAEihB,IAA6B,EAAE;EAC/G,IAAIC,SAAA,GAAY,IAAI;EACpB,IAAI7hB,CAAA,EAAWO,IAAc,EAAApC,KAAA;EAE7B,KAAK6B,CAAA,GAAI,GAAGO,IAAO,GAAAohB,MAAA,CAAOxhB,MAAM,EAAEH,CAAA,GAAIO,IAAM,IAAEP,CAAG;IAC/C7B,KAAQ,GAAAwjB,MAAM,CAAC3hB,CAAE;IACjB,IAAI7B,KAAA,KAAUiE,SAAW;MACvB;;IAEF,IAAImV,OAAY,KAAAnV,SAAA,IAAa,OAAOjE,KAAA,KAAU,UAAY;MACxDA,KAAA,GAAQA,KAAM,CAAAoZ,OAAA;MACdsK,SAAA,GAAY,KAAK;;IAEnB,IAAIlhB,KAAA,KAAUyB,SAAa,IAAAhE,OAAA,CAAQD,KAAQ;MACzCA,KAAA,GAAQA,KAAK,CAACwC,KAAQ,GAAAxC,KAAA,CAAMgC,MAAM,CAAC;MACnC0hB,SAAA,GAAY,KAAK;;IAEnB,IAAI1jB,KAAA,KAAUiE,SAAW;MACvB,IAAIwf,IAAA,IAAQ,CAACC,SAAW;QACtBD,IAAK,CAAAC,SAAS,GAAG,KAAK;;MAExB,OAAO1jB,KAAA;;EAEX;AACF;AAEA;;;;;;AAMO,SAAS2jB,SAAUA,CAAAC,MAAqC,EAAEpN,KAAsB,EAAEH,WAAoB,EAAE;EAC7G,MAAM;IAAC/N,GAAA;IAAKC;EAAA,CAAI,GAAGqb,MAAA;EACnB,MAAMC,MAAA,GAASziB,WAAY,CAAAoV,KAAA,EAAO,CAACjO,GAAA,GAAMD,GAAE,IAAK;EAChD,MAAMwb,QAAA,GAAWA,CAAC9jB,KAAe,EAAA+jB,GAAA,KAAgB1N,WAAA,IAAerW,KAAU,SAAI,CAAI,GAAAA,KAAA,GAAQ+jB,GAAG;EAC7F,OAAO;IACLzb,GAAA,EAAKwb,QAAS,CAAAxb,GAAA,EAAK,CAACpC,IAAA,CAAKa,GAAG,CAAC8c,MAAA;IAC7Btb,GAAA,EAAKub,QAAA,CAASvb,GAAK,EAAAsb,MAAA;EACrB;AACF;AAUO,SAASG,cAAcC,aAAqB,EAAE7K,OAAe,EAAE;EACpE,OAAOhZ,MAAA,CAAOoP,MAAM,CAACpP,MAAO,CAAAyC,MAAM,CAACohB,aAAgB,GAAA7K,OAAA;AACrD;;AC7LA;;;;;;;;;AASC;AACM,SAAS8K,eAIdA,CAAAC,MAAS,EACTC,QAAW,IAAC,GAAG,EACfC,UAAc,EACdhB,QAA4B,EAC5BiB,SAAA,GAAYA,CAAA,KAAMH,MAAM,CAAC,EAAE,EAC3B;EACA,MAAMI,eAAA,GAAkBF,UAAc,IAAAF,MAAA;EACtC,IAAI,OAAOd,QAAA,KAAa,WAAa;IACnCA,QAAA,GAAWmB,QAAA,CAAS,WAAa,EAAAL,MAAA;;EAEnC,MAAM7H,KAA6B;IACjC,CAACmI,MAAA,CAAOC,WAAW,GAAG;IACtBC,UAAA,EAAY,IAAI;IAChBC,OAAS,EAAAT,MAAA;IACTU,WAAa,EAAAN,eAAA;IACbtR,SAAW,EAAAoQ,QAAA;IACXyB,UAAY,EAAAR,SAAA;IACZpJ,QAAU,EAACnX,KAAA,IAAqBmgB,eAAgB,EAACngB,KAAA,EAAU,GAAAogB,MAAA,CAAO,EAAEC,QAAA,EAAUG,eAAiB,EAAAlB,QAAA;EACjG;EACA,OAAO,IAAI0B,KAAA,CAAMzI,KAAO;IACtB;;AAEC;IACD0I,cAAeA,CAAApiB,MAAM,EAAEogB,IAAY,EAAE;MACnC,OAAOpgB,MAAM,CAACogB,IAAK;MACnB,OAAOpgB,MAAA,CAAOqiB,KAAK;MACnB,OAAOd,MAAM,CAAC,EAAE,CAACnB,IAAA,CAAK;MACtB,OAAO,IAAI;IACb;IAEA;;AAEC;IACDtO,GAAIA,CAAA9R,MAAM,EAAEogB,IAAY,EAAE;MACxB,OAAOkC,OAAA,CAAQtiB,MAAQ,EAAAogB,IAAA,EACrB,MAAMmC,oBAAqB,CAAAnC,IAAA,EAAMoB,QAAA,EAAUD,MAAQ,EAAAvhB,MAAA;IACvD;IAEA;;;AAGC;IACDwiB,wBAAyBA,CAAAxiB,MAAM,EAAEogB,IAAI,EAAE;MACrC,OAAOqC,OAAA,CAAQD,wBAAwB,CAACxiB,MAAA,CAAOgiB,OAAO,CAAC,EAAE,EAAE5B,IAAA;IAC7D;IAEA;;AAEC;IACDsC,cAAiBA,CAAA;MACf,OAAOD,OAAQ,CAAAC,cAAc,CAACnB,MAAM,CAAC,CAAE;IACzC;IAEA;;AAEC;IACDre,GAAIA,CAAAlD,MAAM,EAAEogB,IAAY,EAAE;MACxB,OAAOuC,oBAAA,CAAqB3iB,MAAQ,EAAAoT,QAAQ,CAACgN,IAAA;IAC/C;IAEA;;;IAGAwC,QAAQ5iB,MAAM,EAAE;MACd,OAAO2iB,oBAAqB,CAAA3iB,MAAA;IAC9B;IAEA;;AAEC;IACD0J,IAAI1J,MAAM,EAAEogB,IAAY,EAAEhjB,KAAK,EAAE;MAC/B,MAAMylB,OAAA,GAAU7iB,MAAA,CAAO8iB,QAAQ,KAAK9iB,MAAO,CAAA8iB,QAAQ,GAAGpB,SAAU;MAChE1hB,MAAM,CAACogB,IAAA,CAAK,GAAGyC,OAAO,CAACzC,IAAK,IAAGhjB,KAAA;MAC/B,OAAO4C,MAAA,CAAOqiB,KAAK;MACnB,OAAO,IAAI;IACb;EACF;AACF;AAEA;;;;;;;;AAQO,SAASU,eAIdC,KAA0B,EAC1BxM,OAAkB,EAClByM,QAA8B,EAC9BC,kBAAuC,EACvC;EACA,MAAMxJ,KAA4B;IAChCqI,UAAA,EAAY,KAAK;IACjBoB,MAAQ,EAAAH,KAAA;IACRI,QAAU,EAAA5M,OAAA;IACV6M,SAAW,EAAAJ,QAAA;IACXK,MAAA,EAAQ,IAAI3Z,GAAA;IACZuM,YAAA,EAAcA,YAAA,CAAa8M,KAAO,EAAAE,kBAAA;IAClCK,UAAA,EAAanM,GAAA,IAAmB2L,cAAe,CAAAC,KAAA,EAAO5L,GAAA,EAAK6L,QAAU,EAAAC,kBAAA;IACrE5K,QAAU,EAACnX,KAAA,IAAqB4hB,cAAe,CAAAC,KAAA,CAAM1K,QAAQ,CAACnX,KAAA,GAAQqV,OAAA,EAASyM,QAAU,EAAAC,kBAAA;EAC3F;EACA,OAAO,IAAIf,KAAA,CAAMzI,KAAO;IACtB;;AAEC;IACD0I,cAAeA,CAAApiB,MAAM,EAAEogB,IAAI,EAAE;MAC3B,OAAOpgB,MAAM,CAACogB,IAAK;MACnB,OAAO4C,KAAK,CAAC5C,IAAK;MAClB,OAAO,IAAI;IACb;IAEA;;AAEC;IACDtO,IAAI9R,MAAM,EAAEogB,IAAY,EAAEoD,QAAQ,EAAE;MAClC,OAAOlB,OAAA,CAAQtiB,MAAQ,EAAAogB,IAAA,EACrB,MAAMqD,mBAAA,CAAoBzjB,MAAA,EAAQogB,IAAM,EAAAoD,QAAA;IAC5C;IAEA;;;AAGC;IACDhB,wBAAyBA,CAAAxiB,MAAM,EAAEogB,IAAI,EAAE;MACrC,OAAOpgB,MAAA,CAAOkW,YAAY,CAACwN,OAAO,GAC9BjB,OAAQ,CAAAvf,GAAG,CAAC8f,KAAA,EAAO5C,IAAQ;QAACrX,UAAA,EAAY,IAAI;QAAED,YAAA,EAAc;MAAI,IAAIzH,SAAS,GAC7EohB,OAAA,CAAQD,wBAAwB,CAACQ,KAAA,EAAO5C,IAAK;IACnD;IAEA;;AAEC;IACDsC,cAAiBA,CAAA;MACf,OAAOD,OAAA,CAAQC,cAAc,CAACM,KAAA;IAChC;IAEA;;AAEC;IACD9f,GAAIA,CAAAlD,MAAM,EAAEogB,IAAI,EAAE;MAChB,OAAOqC,OAAA,CAAQvf,GAAG,CAAC8f,KAAO,EAAA5C,IAAA;IAC5B;IAEA;;AAEC;IACDwC,OAAUA,CAAA;MACR,OAAOH,OAAA,CAAQG,OAAO,CAACI,KAAA;IACzB;IAEA;;AAEC;IACDtZ,IAAI1J,MAAM,EAAEogB,IAAI,EAAEhjB,KAAK,EAAE;MACvB4lB,KAAK,CAAC5C,IAAA,CAAK,GAAGhjB,KAAA;MACd,OAAO4C,MAAM,CAACogB,IAAK;MACnB,OAAO,IAAI;IACb;EACF;AACF;AAEA;;AAEC;AACM,SAASlK,YACdA,CAAA8M,KAAoB,EACpBjT,QAA+B;EAAC4T,UAAA,EAAY,IAAI;EAAEC,SAAA,EAAW;AAAI,CAAC,EACtD;EACZ,MAAM;IAACrT,WAAc,GAAAR,QAAA,CAAS4T,UAAU;IAAErT,UAAa,GAAAP,QAAA,CAAS6T,SAAS;IAAEC,QAAW,GAAA9T,QAAA,CAAS2T;EAAO,CAAC,GAAGV,KAAA;EAC1G,OAAO;IACLU,OAAS,EAAAG,QAAA;IACTF,UAAY,EAAApT,WAAA;IACZqT,SAAW,EAAAtT,UAAA;IACXwT,YAAA,EAAclhB,UAAW,CAAA2N,WAAA,IAAeA,WAAc,SAAMA,WAAW;IACvEwT,WAAA,EAAanhB,UAAW,CAAA0N,UAAA,IAAcA,UAAa,SAAMA;EAC3D;AACF;AAEA,MAAM0T,OAAA,GAAUA,CAACC,MAAgB,EAAAzT,IAAA,KAAiByT,MAAA,GAASA,MAAS,GAAA1hB,WAAA,CAAYiO,IAAA,IAAQA,IAAI;AAC5F,MAAM0T,gBAAA,GAAmBA,CAAC9D,IAAA,EAAchjB,KAAA,KAAmBS,QAAS,CAAAT,KAAA,KAAUgjB,IAAA,KAAS,UACpF,KAAA5iB,MAAO,CAAAklB,cAAc,CAACtlB,KAAW,UAAI,IAAIA,KAAM,CAAA6Y,WAAW,KAAKzY,MAAK;AAEvE,SAAS8kB,QACPtiB,MAAiB,EACjBogB,IAAY,EACZO,OAAsB,EACtB;EACA,IAAInjB,MAAA,CAAOC,SAAS,CAACwD,cAAc,CAACtD,IAAI,CAACqC,MAAA,EAAQogB,IAAO;IACtD,OAAOpgB,MAAM,CAACogB,IAAK;;EAGrB,MAAMhjB,KAAQ,GAAAujB,OAAA;;EAEd3gB,MAAM,CAACogB,IAAA,CAAK,GAAGhjB,KAAA;EACf,OAAOA,KAAA;AACT;AAEA,SAASqmB,oBACPzjB,MAAoB,EACpBogB,IAAY,EACZoD,QAAmB,EACnB;EACA,MAAM;IAACL,MAAM;IAAEC,QAAQ;IAAEC,SAAA;IAAWnN,YAAA,EAAcN;EAAW,CAAC,GAAG5V,MAAA;EACjE,IAAI5C,KAAQ,GAAA+lB,MAAM,CAAC/C,IAAA,CAAK;;EAGxB,IAAIxd,UAAW,CAAAxF,KAAA,KAAUwY,WAAY,CAAAkO,YAAY,CAAC1D,IAAO;IACvDhjB,KAAQ,GAAA+mB,kBAAA,CAAmB/D,IAAM,EAAAhjB,KAAA,EAAO4C,MAAQ,EAAAwjB,QAAA;;EAElD,IAAInmB,OAAQ,CAAAD,KAAA,KAAUA,KAAM,CAAAgC,MAAM,EAAE;IAClChC,KAAA,GAAQgnB,aAAc,CAAAhE,IAAA,EAAMhjB,KAAO,EAAA4C,MAAA,EAAQ4V,WAAA,CAAYmO,WAAW;;EAEpE,IAAIG,gBAAA,CAAiB9D,IAAA,EAAMhjB,KAAQ;;IAEjCA,KAAA,GAAQ2lB,cAAA,CAAe3lB,KAAO,EAAAgmB,QAAA,EAAUC,SAAA,IAAaA,SAAS,CAACjD,IAAA,CAAK,EAAExK,WAAA;;EAExE,OAAOxY,KAAA;AACT;AAEA,SAAS+mB,mBACP/D,IAAY,EACZiE,QAAqD,EACrDrkB,MAAoB,EACpBwjB,QAAmB,EACnB;EACA,MAAM;IAACL,MAAA;IAAQC,QAAA;IAAUC,SAAS;IAAEC;EAAM,CAAC,GAAGtjB,MAAA;EAC9C,IAAIsjB,MAAA,CAAOpgB,GAAG,CAACkd,IAAO;IACpB,MAAM,IAAIkE,KAAM,0BAAyBhnB,KAAM,CAAAsM,IAAI,CAAC0Z,MAAA,EAAQiB,IAAI,CAAC,IAAQ,WAAOnE,IAAM;;EAExFkD,MAAA,CAAOnC,GAAG,CAACf,IAAA;EACX,IAAIhjB,KAAA,GAAQinB,QAAS,CAAAjB,QAAA,EAAUC,SAAa,IAAAG,QAAA;EAC5CF,MAAA,CAAOkB,MAAM,CAACpE,IAAA;EACd,IAAI8D,gBAAA,CAAiB9D,IAAA,EAAMhjB,KAAQ;;IAEjCA,KAAA,GAAQqnB,iBAAkB,CAAAtB,MAAA,CAAOnB,OAAO,EAAEmB,MAAA,EAAQ/C,IAAM,EAAAhjB,KAAA;;EAE1D,OAAOA,KAAA;AACT;AAEA,SAASgnB,cACPhE,IAAY,EACZhjB,KAAgB,EAChB4C,MAAoB,EACpB+jB,WAAqC,EACrC;EACA,MAAM;IAACZ,MAAM;IAAEC,QAAQ;IAAEC,SAAA;IAAWnN,YAAA,EAAcN;EAAW,CAAC,GAAG5V,MAAA;EAEjE,IAAI,OAAOojB,QAAS,CAAAxjB,KAAK,KAAK,eAAemkB,WAAA,CAAY3D,IAAO;IAC9D,OAAOhjB,KAAK,CAACgmB,QAAA,CAASxjB,KAAK,GAAGxC,KAAA,CAAMgC,MAAM,CAAC;EAC7C,OAAO,IAAIvB,QAAA,CAAST,KAAK,CAAC,EAAE,CAAG;;IAE7B,MAAMsnB,GAAM,GAAAtnB,KAAA;IACZ,MAAMmkB,MAAA,GAAS4B,MAAA,CAAOnB,OAAO,CAAC2C,MAAM,CAACvd,CAAA,IAAKA,CAAM,KAAAsd,GAAA;IAChDtnB,KAAA,GAAQ,EAAE;IACV,KAAK,MAAM6F,IAAA,IAAQyhB,GAAK;MACtB,MAAMpiB,QAAW,GAAAmiB,iBAAA,CAAkBlD,MAAQ,EAAA4B,MAAA,EAAQ/C,IAAM,EAAAnd,IAAA;MACzD7F,KAAM,CAAA8E,IAAI,CAAC6gB,cAAe,CAAAzgB,QAAA,EAAU8gB,QAAA,EAAUC,SAAa,IAAAA,SAAS,CAACjD,IAAA,CAAK,EAAExK,WAAA;IAC9E;;EAEF,OAAOxY,KAAA;AACT;AAEA,SAASwnB,gBACPnE,QAA8F,EAC9FL,IAAuB,EACvBhjB,KAAc,EACd;EACA,OAAOwF,UAAW,CAAA6d,QAAA,IAAYA,QAAS,CAAAL,IAAA,EAAMhjB,KAAA,IAASqjB,QAAQ;AAChE;AAEA,MAAMoE,QAAW,GAAAA,CAACxkB,GAAwB,EAAAykB,MAAA,KAAsBzkB,GAAA,KAAQ,IAAI,GAAGykB,MAC3E,UAAOzkB,GAAQ,gBAAWgC,gBAAiB,CAAAyiB,MAAA,EAAQzkB,GAAA,IAAOgB,SAAS;AAEvE,SAAS0jB,UACPrb,GAAmB,EACnBsb,YAAyB,EACzB3kB,GAAsB,EACtB4kB,cAAiC,EACjC7nB,KAAc,EACd;EACA,KAAK,MAAM0nB,MAAA,IAAUE,YAAc;IACjC,MAAM7jB,KAAA,GAAQ0jB,QAAA,CAASxkB,GAAK,EAAAykB,MAAA;IAC5B,IAAI3jB,KAAO;MACTuI,GAAA,CAAIyX,GAAG,CAAChgB,KAAA;MACR,MAAMsf,QAAW,GAAAmE,eAAA,CAAgBzjB,KAAM,CAAAkP,SAAS,EAAEhQ,GAAK,EAAAjD,KAAA;MACvD,IAAI,OAAOqjB,QAAa,oBAAeA,QAAa,KAAApgB,GAAA,IAAOogB,QAAA,KAAawE,cAAgB;;;QAGtF,OAAOxE,QAAA;;KAEJ,UAAItf,KAAA,KAAU,KAAK,IAAI,OAAO8jB,cAAmB,oBAAe5kB,GAAA,KAAQ4kB,cAAgB;;;MAG7F,OAAO,IAAI;;EAEf;EACA,OAAO,KAAK;AACd;AAEA,SAASR,kBACPO,YAAyB,EACzB1iB,QAAuB,EACvB8d,IAAuB,EACvBhjB,KAAc,EACd;EACA,MAAMqkB,UAAA,GAAanf,QAAA,CAAS2f,WAAW;EACvC,MAAMxB,QAAW,GAAAmE,eAAA,CAAgBtiB,QAAS,CAAA+N,SAAS,EAAE+P,IAAM,EAAAhjB,KAAA;EAC3D,MAAM8nB,SAAY,IAAI,GAAAF,YAAA,EAAiB,GAAAvD,UAAA,CAAW;EAClD,MAAM/X,GAAA,GAAM,IAAIC,GAAA;EAChBD,GAAA,CAAIyX,GAAG,CAAC/jB,KAAA;EACR,IAAIiD,GAAA,GAAM8kB,gBAAiB,CAAAzb,GAAA,EAAKwb,SAAW,EAAA9E,IAAA,EAAMK,QAAA,IAAYL,IAAM,EAAAhjB,KAAA;EACnE,IAAIiD,GAAA,KAAQ,IAAI,EAAE;IAChB,OAAO,KAAK;;EAEd,IAAI,OAAOogB,QAAA,KAAa,WAAe,IAAAA,QAAA,KAAaL,IAAM;IACxD/f,GAAA,GAAM8kB,gBAAiB,CAAAzb,GAAA,EAAKwb,SAAW,EAAAzE,QAAA,EAAUpgB,GAAK,EAAAjD,KAAA;IACtD,IAAIiD,GAAA,KAAQ,IAAI,EAAE;MAChB,OAAO,KAAK;;;EAGhB,OAAOihB,eAAgB,CAAAhkB,KAAA,CAAMsM,IAAI,CAACF,GAAM,IAAC,GAAG,EAAE+X,UAAY,EAAAhB,QAAA,EACxD,MAAM2E,YAAA,CAAa9iB,QAAA,EAAU8d,IAAgB,EAAAhjB,KAAA;AACjD;AAEA,SAAS+nB,iBACPzb,GAAmB,EACnBwb,SAAsB,EACtB7kB,GAAsB,EACtBogB,QAA2B,EAC3Bxd,IAAa,EACb;EACA,OAAO5C,GAAK;IACVA,GAAA,GAAM0kB,SAAU,CAAArb,GAAA,EAAKwb,SAAW,EAAA7kB,GAAA,EAAKogB,QAAU,EAAAxd,IAAA;EACjD;EACA,OAAO5C,GAAA;AACT;AAEA,SAAS+kB,aACP9iB,QAAuB,EACvB8d,IAAY,EACZhjB,KAAc,EACd;EACA,MAAM0nB,MAAA,GAASxiB,QAAA,CAAS4f,UAAU;EAClC,IAAI,EAAE9B,IAAQ,IAAA0E,MAAK,CAAI;IACrBA,MAAM,CAAC1E,IAAK,IAAG,EAAC;;EAElB,MAAMpgB,MAAA,GAAS8kB,MAAM,CAAC1E,IAAK;EAC3B,IAAI/iB,OAAA,CAAQ2C,MAAW,KAAAnC,QAAA,CAAST,KAAQ;;IAEtC,OAAOA,KAAA;;EAET,OAAO4C,MAAA,IAAU,EAAC;AACpB;AAEA,SAASuiB,qBACPnC,IAAY,EACZoB,QAAkB,EAClBD,MAAmB,EACnByB,KAAoB,EACpB;EACA,IAAI5lB,KAAA;EACJ,KAAK,MAAM6mB,MAAA,IAAUzC,QAAU;IAC7BpkB,KAAQ,GAAAwkB,QAAA,CAASoC,OAAQ,CAAAC,MAAA,EAAQ7D,IAAO,GAAAmB,MAAA;IACxC,IAAI,OAAOnkB,KAAA,KAAU,WAAa;MAChC,OAAO8mB,gBAAA,CAAiB9D,IAAA,EAAMhjB,KAC1B,IAAAqnB,iBAAA,CAAkBlD,MAAA,EAAQyB,KAAO,EAAA5C,IAAA,EAAMhjB,KAAA,IACvCA,KAAK;;EAEb;AACF;AAEA,SAASwkB,QAASA,CAAAvhB,GAAW,EAAEkhB,MAAmB,EAAE;EAClD,KAAK,MAAMpgB,KAAA,IAASogB,MAAQ;IAC1B,IAAI,CAACpgB,KAAO;MACV;;IAEF,MAAM/D,KAAA,GAAQ+D,KAAK,CAACd,GAAI;IACxB,IAAI,OAAOjD,KAAA,KAAU,WAAa;MAChC,OAAOA,KAAA;;EAEX;AACF;AAEA,SAASulB,qBAAqB3iB,MAAqB,EAAE;EACnD,IAAIb,IAAA,GAAOa,MAAA,CAAOqiB,KAAK;EACvB,IAAI,CAACljB,IAAM;IACTA,IAAA,GAAOa,MAAO,CAAAqiB,KAAK,GAAGgD,wBAAA,CAAyBrlB,MAAA,CAAOgiB,OAAO;;EAE/D,OAAO7iB,IAAA;AACT;AAEA,SAASkmB,yBAAyB9D,MAAmB,EAAE;EACrD,MAAM7X,GAAA,GAAM,IAAIC,GAAA;EAChB,KAAK,MAAMxI,KAAA,IAASogB,MAAQ;IAC1B,KAAK,MAAMlhB,GAAA,IAAO7C,MAAO,CAAA2B,IAAI,CAACgC,KAAO,EAAAwjB,MAAM,CAACxkB,CAAK,KAACA,CAAE,CAAAuV,UAAU,CAAC,GAAO;MACpEhM,GAAA,CAAIyX,GAAG,CAAC9gB,GAAA;IACV;EACF;EACA,OAAO/C,KAAA,CAAMsM,IAAI,CAACF,GAAA;AACpB;AAEO,SAAS4b,4BACdja,IAAmC,EACnC6N,IAAiB,EACjBjS,KAAa,EACbwE,KAAa,EACb;EACA,MAAM;IAACE;EAAM,CAAC,GAAGN,IAAA;EACjB,MAAM;IAAChL,GAAM;EAAA,CAAI,GAAG,IAAI,CAACklB,QAAQ;EACjC,MAAMC,MAAA,GAAS,IAAIloB,KAAoB,CAAAmO,KAAA;EACvC,IAAIxM,CAAA,EAAWO,IAAA,EAAcI,KAAe,EAAAqD,IAAA;EAE5C,KAAKhE,CAAA,GAAI,GAAGO,IAAO,GAAAiM,KAAK,EAAExM,CAAI,GAAAO,IAAA,EAAM,EAAEP,CAAG;IACvCW,KAAA,GAAQX,CAAI,GAAAgI,KAAA;IACZhE,IAAO,GAAAiW,IAAI,CAACtZ,KAAM;IAClB4lB,MAAM,CAACvmB,CAAA,CAAE,GAAG;MACVwmB,CAAA,EAAG9Z,MAAO,CAAA+Z,KAAK,CAACrjB,gBAAA,CAAiBY,IAAA,EAAM5C,GAAM,GAAAT,KAAA;IAC/C;EACF;EACA,OAAO4lB,MAAA;AACT;AClcA,MAAMG,OAAA,GAAU5nB,MAAO,CAAA4nB,OAAO,IAAI;AAGlC,MAAMC,QAAA,GAAWA,CAACta,MAAA,EAAuBrM,CAAmC,KAAAA,CAAA,GAAIqM,MAAA,CAAOlM,MAAM,IAAI,CAACkM,MAAM,CAACrM,CAAE,EAAC4mB,IAAI,IAAIva,MAAM,CAACrM,CAAE;AAC7H,MAAM6mB,YAAA,GAAgBvO,SAAA,IAAyBA,SAAc,WAAM,MAAM,GAAG;AAErE,SAASwO,YACdC,UAAuB,EACvBC,WAAwB,EACxBC,UAAuB,EACvBpZ,CAAS,EAIP;;;;EAMF,MAAM1L,QAAW,GAAA4kB,UAAA,CAAWH,IAAI,GAAGI,WAAA,GAAcD,UAAU;EAC3D,MAAMllB,OAAU,GAAAmlB,WAAA;EAChB,MAAME,IAAO,GAAAD,UAAA,CAAWL,IAAI,GAAGI,WAAA,GAAcC,UAAU;EACvD,MAAME,GAAA,GAAMzf,qBAAA,CAAsB7F,OAAS,EAAAM,QAAA;EAC3C,MAAMilB,GAAA,GAAM1f,qBAAA,CAAsBwf,IAAM,EAAArlB,OAAA;EAExC,IAAIwlB,GAAM,GAAAF,GAAA,IAAOA,GAAA,GAAMC,GAAE;EACzB,IAAIE,GAAM,GAAAF,GAAA,IAAOD,GAAA,GAAMC,GAAE;;EAGzBC,GAAM,GAAAlhB,KAAA,CAAMkhB,GAAO,QAAIA,GAAG;EAC1BC,GAAM,GAAAnhB,KAAA,CAAMmhB,GAAO,QAAIA,GAAG;EAE1B,MAAMC,EAAA,GAAK1Z,CAAI,GAAAwZ,GAAA;EACf,MAAMG,EAAA,GAAK3Z,CAAI,GAAAyZ,GAAA;EAEf,OAAO;IACLnlB,QAAU;MACRM,CAAG,EAAAZ,OAAA,CAAQY,CAAC,GAAG8kB,EAAM,IAAAL,IAAA,CAAKzkB,CAAC,GAAGN,QAAS,CAAAM,CAAC,CAAD;MACvCE,CAAG,EAAAd,OAAA,CAAQc,CAAC,GAAG4kB,EAAM,IAAAL,IAAA,CAAKvkB,CAAC,GAAGR,QAAS,CAAAQ,CAAC;IAC1C;IACAukB,IAAM;MACJzkB,CAAG,EAAAZ,OAAA,CAAQY,CAAC,GAAG+kB,EAAM,IAAAN,IAAA,CAAKzkB,CAAC,GAAGN,QAAS,CAAAM,CAAC,CAAD;MACvCE,CAAG,EAAAd,OAAA,CAAQc,CAAC,GAAG6kB,EAAM,IAAAN,IAAA,CAAKvkB,CAAC,GAAGR,QAAS,CAAAQ,CAAC;IAC1C;EACF;AACF;AAEA;;AAEC;AACD,SAAS8kB,cAAeA,CAAApb,MAAqB,EAAEqb,MAAgB,EAAEC,EAAY,EAAE;EAC7E,MAAMC,SAAA,GAAYvb,MAAA,CAAOlM,MAAM;EAE/B,IAAI0nB,MAAA,EAAgBC,KAAe,EAAAC,IAAA,EAAcC,gBAA0B,EAAAC,YAAA;EAC3E,IAAIC,UAAA,GAAavB,QAAA,CAASta,MAAQ;EAClC,KAAK,IAAIrM,CAAI,MAAGA,CAAA,GAAI4nB,SAAY,MAAG,EAAE5nB,CAAG;IACtCioB,YAAe,GAAAC,UAAA;IACfA,UAAa,GAAAvB,QAAA,CAASta,MAAA,EAAQrM,CAAI;IAClC,IAAI,CAACioB,YAAgB,KAACC,UAAY;MAChC;;IAGF,IAAIljB,YAAA,CAAa0iB,MAAM,CAAC1nB,CAAE,GAAE,GAAG0mB,OAAU;MACvCiB,EAAE,CAAC3nB,CAAE,IAAG2nB,EAAE,CAAC3nB,CAAA,GAAI,EAAE,GAAG;MACpB;;IAGF6nB,MAAA,GAASF,EAAE,CAAC3nB,CAAA,CAAE,GAAG0nB,MAAM,CAAC1nB,CAAE;IAC1B8nB,KAAA,GAAQH,EAAE,CAAC3nB,CAAA,GAAI,EAAE,GAAG0nB,MAAM,CAAC1nB,CAAE;IAC7BgoB,gBAAmB,GAAA3jB,IAAA,CAAKmB,GAAG,CAACqiB,MAAA,EAAQ,KAAKxjB,IAAK,CAAAmB,GAAG,CAACsiB,KAAO;IACzD,IAAIE,gBAAA,IAAoB,CAAG;MACzB;;IAGFD,IAAO,OAAI1jB,IAAK,CAAAyB,IAAI,CAACkiB,gBAAA;IACrBL,EAAE,CAAC3nB,CAAE,IAAG6nB,MAAA,GAASE,IAAO,GAAAL,MAAM,CAAC1nB,CAAE;IACjC2nB,EAAE,CAAC3nB,CAAA,GAAI,CAAE,IAAG8nB,KAAA,GAAQC,IAAO,GAAAL,MAAM,CAAC1nB,CAAE;EACtC;AACF;AAEA,SAASmoB,gBAAgB9b,MAAqB,EAAEsb,EAAY,EAAErP,SAAA,GAAuB,GAAG,EAAE;EACxF,MAAM8P,SAAA,GAAYvB,YAAa,CAAAvO,SAAA;EAC/B,MAAMsP,SAAA,GAAYvb,MAAA,CAAOlM,MAAM;EAC/B,IAAIsT,KAAA,EAAe4U,WAAkC,EAAAJ,YAAA;EACrD,IAAIC,UAAA,GAAavB,QAAA,CAASta,MAAQ;EAElC,KAAK,IAAIrM,CAAI,MAAGA,CAAI,GAAA4nB,SAAA,EAAW,EAAE5nB,CAAG;IAClCqoB,WAAc,GAAAJ,YAAA;IACdA,YAAe,GAAAC,UAAA;IACfA,UAAa,GAAAvB,QAAA,CAASta,MAAA,EAAQrM,CAAI;IAClC,IAAI,CAACioB,YAAc;MACjB;;IAGF,MAAMK,MAAA,GAASL,YAAY,CAAC3P,SAAU;IACtC,MAAMiQ,MAAA,GAASN,YAAY,CAACG,SAAU;IACtC,IAAIC,WAAa;MACf5U,KAAA,GAAQ,CAAC6U,MAAA,GAASD,WAAW,CAAC/P,SAAA,CAAU,IAAI;MAC5C2P,YAAY,CAAE,MAAK3P,SAAU,EAAC,CAAC,GAAGgQ,MAAS,GAAA7U,KAAA;MAC3CwU,YAAY,CAAE,MAAKG,SAAU,EAAC,CAAC,GAAGG,MAAS,GAAA9U,KAAA,GAAQkU,EAAE,CAAC3nB,CAAE;;IAE1D,IAAIkoB,UAAY;MACdzU,KAAA,GAAQ,CAACyU,UAAU,CAAC5P,SAAU,IAAGgQ,MAAK,IAAK;MAC3CL,YAAY,CAAE,MAAK3P,SAAU,EAAC,CAAC,GAAGgQ,MAAS,GAAA7U,KAAA;MAC3CwU,YAAY,CAAE,MAAKG,SAAU,EAAC,CAAC,GAAGG,MAAS,GAAA9U,KAAA,GAAQkU,EAAE,CAAC3nB,CAAE;;EAE5D;AACF;AAEA;;;;;AAKC;AACM,SAASwoB,oBAAoBnc,MAAqB,EAAEiM,SAAA,GAAuB,GAAG,EAAE;EACrF,MAAM8P,SAAA,GAAYvB,YAAa,CAAAvO,SAAA;EAC/B,MAAMsP,SAAA,GAAYvb,MAAA,CAAOlM,MAAM;EAC/B,MAAMunB,MAAmB,GAAArpB,KAAA,CAAMupB,SAAW,EAAAzK,IAAI,CAAC;EAC/C,MAAMwK,EAAA,GAAetpB,KAAM,CAAAupB,SAAA;;EAG3B,IAAI5nB,CAAA,EAAGqoB,WAAkC,EAAAJ,YAAA;EACzC,IAAIC,UAAA,GAAavB,QAAA,CAASta,MAAQ;EAElC,KAAKrM,CAAI,MAAGA,CAAI,GAAA4nB,SAAA,EAAW,EAAE5nB,CAAG;IAC9BqoB,WAAc,GAAAJ,YAAA;IACdA,YAAe,GAAAC,UAAA;IACfA,UAAa,GAAAvB,QAAA,CAASta,MAAA,EAAQrM,CAAI;IAClC,IAAI,CAACioB,YAAc;MACjB;;IAGF,IAAIC,UAAY;MACd,MAAMO,UAAA,GAAaP,UAAU,CAAC5P,SAAA,CAAU,GAAG2P,YAAY,CAAC3P,SAAU;;MAGlEoP,MAAM,CAAC1nB,CAAE,IAAGyoB,UAAe,SAAI,CAACP,UAAU,CAACE,SAAA,CAAU,GAAGH,YAAY,CAACG,SAAA,CAAU,IAAIK,UAAA,GAAa,CAAC;;IAEnGd,EAAE,CAAC3nB,CAAE,IAAG,CAACqoB,WAAc,GAAAX,MAAM,CAAC1nB,CAAE,IAC5B,CAACkoB,UAAA,GAAaR,MAAM,CAAC1nB,CAAA,GAAI,EAAE,GACxB+E,IAAA,CAAK2iB,MAAM,CAAC1nB,CAAI,KAAE,MAAM+E,IAAK,CAAA2iB,MAAM,CAAC1nB,CAAE,KAAK,IAC1C,CAAC0nB,MAAM,CAAC1nB,CAAA,GAAI,EAAE,GAAG0nB,MAAM,CAAC1nB,CAAE,CAAD,IAAK,CAAC;EACzC;EAEAynB,cAAA,CAAepb,MAAA,EAAQqb,MAAQ,EAAAC,EAAA;EAE/BQ,eAAA,CAAgB9b,MAAA,EAAQsb,EAAI,EAAArP,SAAA;AAC9B;AAEA,SAASoQ,gBAAgBC,EAAU,EAAEliB,GAAW,EAAEC,GAAW,EAAE;EAC7D,OAAOrC,IAAA,CAAKqC,GAAG,CAACrC,IAAA,CAAKoC,GAAG,CAACkiB,EAAA,EAAIjiB,GAAM,GAAAD,GAAA;AACrC;AAEA,SAASmiB,eAAgBA,CAAAvc,MAAqB,EAAEmR,IAAe,EAAE;EAC/D,IAAIxd,CAAA,EAAGO,IAAM,EAAAgd,KAAA,EAAOsL,MAAQ,EAAAC,UAAA;EAC5B,IAAIC,UAAa,GAAAzL,cAAA,CAAejR,MAAM,CAAC,EAAE,EAAEmR,IAAA;EAC3C,KAAKxd,CAAA,GAAI,GAAGO,IAAO,GAAA8L,MAAA,CAAOlM,MAAM,EAAEH,CAAA,GAAIO,IAAM,IAAEP,CAAG;IAC/C8oB,UAAa,GAAAD,MAAA;IACbA,MAAS,GAAAE,UAAA;IACTA,UAAa,GAAA/oB,CAAA,GAAIO,IAAA,GAAO,CAAK,IAAA+c,cAAA,CAAejR,MAAM,CAACrM,CAAA,GAAI,EAAE,EAAEwd,IAAA;IAC3D,IAAI,CAACqL,MAAQ;MACX;;IAEFtL,KAAQ,GAAAlR,MAAM,CAACrM,CAAE;IACjB,IAAI8oB,UAAY;MACdvL,KAAM,CAAAU,IAAI,GAAGyK,eAAA,CAAgBnL,KAAM,CAAAU,IAAI,EAAET,IAAK,CAAAzR,IAAI,EAAEyR,IAAA,CAAKxR,KAAK;MAC9DuR,KAAM,CAAAY,IAAI,GAAGuK,eAAA,CAAgBnL,KAAM,CAAAY,IAAI,EAAEX,IAAK,CAAArL,GAAG,EAAEqL,IAAA,CAAKpL,MAAM;;IAEhE,IAAI2W,UAAY;MACdxL,KAAM,CAAAW,IAAI,GAAGwK,eAAA,CAAgBnL,KAAM,CAAAW,IAAI,EAAEV,IAAK,CAAAzR,IAAI,EAAEyR,IAAA,CAAKxR,KAAK;MAC9DuR,KAAM,CAAAa,IAAI,GAAGsK,eAAA,CAAgBnL,KAAM,CAAAa,IAAI,EAAEZ,IAAK,CAAArL,GAAG,EAAEqL,IAAA,CAAKpL,MAAM;;EAElE;AACF;AAEA;;AAEC;AACM,SAAS4W,0BACdA,CAAA3c,MAAqB,EACrB9K,OAAO,EACPic,IAAe,EACfvM,IAAa,EACbqH,SAAoB,EACpB;EACA,IAAItY,CAAA,EAAWO,IAAA,EAAcgd,KAAoB,EAAA0L,aAAA;;EAGjD,IAAI1nB,OAAA,CAAQ2nB,QAAQ,EAAE;IACpB7c,MAAA,GAASA,MAAA,CAAOqZ,MAAM,CAAEiD,EAAO,KAACA,EAAA,CAAG/B,IAAI;;EAGzC,IAAIrlB,OAAA,CAAQ4nB,sBAAsB,KAAK,UAAY;IACjDX,mBAAA,CAAoBnc,MAAQ,EAAAiM,SAAA;GACvB;IACL,IAAI8Q,IAAA,GAAOnY,IAAO,GAAA5E,MAAM,CAACA,MAAA,CAAOlM,MAAM,GAAG,CAAE,IAAGkM,MAAM,CAAC,CAAE;IACvD,KAAKrM,CAAA,GAAI,GAAGO,IAAO,GAAA8L,MAAA,CAAOlM,MAAM,EAAEH,CAAA,GAAIO,IAAM,IAAEP,CAAG;MAC/Cud,KAAQ,GAAAlR,MAAM,CAACrM,CAAE;MACjBipB,aAAgB,GAAAnC,WAAA,CACdsC,IAAA,EACA7L,KACA,EAAAlR,MAAM,CAAChI,IAAK,CAAAoC,GAAG,CAACzG,CAAI,MAAGO,IAAA,IAAQ0Q,IAAA,GAAO,IAAI,MAAM1Q,IAAK,GACrDgB,OAAA,CAAQ8nB,OAAO;MAEjB9L,KAAA,CAAMU,IAAI,GAAGgL,aAAc,CAAA9mB,QAAQ,CAACM,CAAC;MACrC8a,KAAA,CAAMY,IAAI,GAAG8K,aAAc,CAAA9mB,QAAQ,CAACQ,CAAC;MACrC4a,KAAA,CAAMW,IAAI,GAAG+K,aAAc,CAAA/B,IAAI,CAACzkB,CAAC;MACjC8a,KAAA,CAAMa,IAAI,GAAG6K,aAAc,CAAA/B,IAAI,CAACvkB,CAAC;MACjCymB,IAAO,GAAA7L,KAAA;IACT;;EAGF,IAAIhc,OAAA,CAAQqnB,eAAe,EAAE;IAC3BA,eAAA,CAAgBvc,MAAQ,EAAAmR,IAAA;;AAE5B;;ACzNA;;;;;;GAAA,C;;;AAWO,SAAS8L,eAA2BA,CAAA;EACzC,OAAO,OAAOre,MAAA,KAAW,WAAe,WAAOse,QAAa;AAC9D;AAEA;;AAEC;AACM,SAASC,cAAeA,CAAAC,OAA0B,EAAqB;EAC5E,IAAI5D,MAAA,GAAS4D,OAAA,CAAQC,UAAU;EAC/B,IAAI7D,MAAU,IAAAA,MAAA,CAAOpnB,QAAQ,OAAO,qBAAuB;IACzDonB,MAAS,GAACA,MAAA,CAAsB8D,IAAI;;EAEtC,OAAO9D,MAAA;AACT;AAEA;;;AAGC;AAED,SAAS+D,aAAcA,CAAAC,UAA2B,EAAEhT,IAAiB,EAAEiT,cAAsB,EAAE;EAC7F,IAAIC,aAAA;EACJ,IAAI,OAAOF,UAAA,KAAe,QAAU;IAClCE,aAAA,GAAgBtI,QAAA,CAASoI,UAAY;IAErC,IAAIA,UAAW,CAAAxoB,OAAO,CAAC,SAAS,CAAC,CAAG;;MAElC0oB,aAAA,GAAgBA,aAAiB,SAAOlT,IAAK,CAAA6S,UAAU,CAACI,cAAe;;GAEpE;IACLC,aAAgB,GAAAF,UAAA;;EAGlB,OAAOE,aAAA;AACT;AAEA,MAAMC,gBAAA,GAAoBC,OAAA,IACxBA,OAAQ,CAAAC,aAAa,CAACC,WAAW,CAACH,gBAAgB,CAACC,OAAA,EAAS,IAAI;AAE3D,SAASG,SAASC,EAAe,EAAE7jB,QAAgB,EAAU;EAClE,OAAOwjB,gBAAA,CAAiBK,EAAI,EAAAC,gBAAgB,CAAC9jB,QAAA;AAC/C;AAEA,MAAM+jB,SAAY,IAAC,OAAO,SAAS,UAAU,OAAO;AACpD,SAASC,mBAAmBC,MAA2B,EAAE3S,KAAa,EAAE4S,MAAe,EAAa;EAClG,MAAM7kB,MAAA,GAAS,EAAC;EAChB6kB,MAAS,GAAAA,MAAA,GAAS,GAAM,GAAAA,MAAA,GAAS,EAAE;EACnC,KAAK,IAAI1qB,CAAA,GAAI,CAAG,EAAAA,CAAA,GAAI,GAAGA,CAAK;IAC1B,MAAM2qB,GAAA,GAAMJ,SAAS,CAACvqB,CAAE;IACxB6F,MAAM,CAAC8kB,GAAI,IAAGrrB,UAAW,CAAAmrB,MAAM,CAAC3S,KAAQ,SAAM6S,GAAM,GAAAD,MAAA,CAAO,CAAK;EAClE;EACA7kB,MAAA,CAAO0P,KAAK,GAAG1P,MAAA,CAAOkG,IAAI,GAAGlG,MAAA,CAAOmG,KAAK;EACzCnG,MAAA,CAAO8V,MAAM,GAAG9V,MAAA,CAAOsM,GAAG,GAAGtM,MAAA,CAAOuM,MAAM;EAC1C,OAAOvM,MAAA;AACT;AAEA,MAAM+kB,YAAA,GAAeA,CAACnoB,CAAA,EAAWE,CAAA,EAAW5B,MAC1C,KAAC,CAAA0B,CAAI,QAAKE,CAAA,GAAI,OAAO,CAAC5B,MAAA,IAAU,CAACA,MAAC,CAAuB8pB,UAAU,CAAD;AAEpE;;;;AAIC;AACD,SAASC,kBACP3mB,CAAkC,EAClCoX,MAAyB,EAKvB;EACF,MAAMwP,OAAA,GAAU5mB,CAAC,CAAiB4mB,OAAO;EACzC,MAAMlqB,MAAA,GAAUkqB,OAAA,IAAWA,OAAQ,CAAA5qB,MAAM,GAAG4qB,OAAO,CAAC,CAAE,IAAG5mB,CAAC;EAC1D,MAAM;IAAC6mB,OAAA;IAASC;EAAA,CAAQ,GAAGpqB,MAAA;EAC3B,IAAIqqB,GAAA,GAAM,KAAK;EACf,IAAIzoB,CAAG,EAAAE,CAAA;EACP,IAAIioB,YAAa,CAAAI,OAAA,EAASC,OAAS,EAAA9mB,CAAA,CAAEpD,MAAM,CAAG;IAC5C0B,CAAI,GAAAuoB,OAAA;IACJroB,CAAI,GAAAsoB,OAAA;GACC;IACL,MAAM/N,IAAA,GAAO3B,MAAA,CAAO4P,qBAAqB;IACzC1oB,CAAA,GAAI5B,MAAO,CAAAuqB,OAAO,GAAGlO,IAAA,CAAKnR,IAAI;IAC9BpJ,CAAA,GAAI9B,MAAO,CAAAwqB,OAAO,GAAGnO,IAAA,CAAK/K,GAAG;IAC7B+Y,GAAA,GAAM,IAAI;;EAEZ,OAAO;IAACzoB,CAAA;IAAGE,CAAA;IAAGuoB;EAAG;AACnB;AAEA;;;;;AAKC;AAEM,SAASI,oBACdC,KAAmD,EACnDhY,KAAY,EACc;EAC1B,IAAI,YAAYgY,KAAO;IACrB,OAAOA,KAAA;;EAGT,MAAM;IAAChQ,MAAA;IAAQH;EAAA,CAAwB,GAAG7H,KAAA;EAC1C,MAAMuE,KAAA,GAAQkS,gBAAiB,CAAAzO,MAAA;EAC/B,MAAMiQ,SAAA,GAAY1T,KAAM,CAAA2T,SAAS,KAAK;EACtC,MAAMC,QAAA,GAAWlB,kBAAA,CAAmB1S,KAAO;EAC3C,MAAM6T,OAAA,GAAUnB,kBAAmB,CAAA1S,KAAA,EAAO,QAAU;EACpD,MAAM;IAACrV,CAAA;IAAGE,CAAA;IAAGuoB;EAAG,CAAC,GAAGJ,iBAAA,CAAkBS,KAAO,EAAAhQ,MAAA;EAC7C,MAAMQ,OAAA,GAAU2P,QAAA,CAAS3f,IAAI,IAAImf,GAAO,IAAAS,OAAA,CAAQ5f,IAAI,CAAD;EACnD,MAAMiQ,OAAA,GAAU0P,QAAA,CAASvZ,GAAG,IAAI+Y,GAAO,IAAAS,OAAA,CAAQxZ,GAAG,CAAD;EAEjD,IAAI;IAACoD,KAAA;IAAOoG;EAAA,CAAO,GAAGpI,KAAA;EACtB,IAAIiY,SAAW;IACbjW,KAAA,IAASmW,QAAS,CAAAnW,KAAK,GAAGoW,OAAA,CAAQpW,KAAK;IACvCoG,MAAA,IAAU+P,QAAS,CAAA/P,MAAM,GAAGgQ,OAAA,CAAQhQ,MAAM;;EAE5C,OAAO;IACLlZ,CAAG,EAAA4B,IAAA,CAAKiB,KAAK,CAAC,CAAC7C,CAAI,GAAAsZ,OAAM,IAAKxG,KAAA,GAAQgG,MAAO,CAAAhG,KAAK,GAAG6F,uBAAA;IACrDzY,CAAG,EAAA0B,IAAA,CAAKiB,KAAK,CAAC,CAAC3C,CAAI,GAAAqZ,OAAM,IAAKL,MAAA,GAASJ,MAAO,CAAAI,MAAM,GAAGP,uBAAA;EACzD;AACF;AAEA,SAASwQ,iBAAiBrQ,MAAyB,EAAEhG,KAAa,EAAEoG,MAAc,EAAkB;EAClG,IAAIqE,QAAkB,EAAA6L,SAAA;EAEtB,IAAItW,KAAA,KAAUnT,SAAa,IAAAuZ,MAAA,KAAWvZ,SAAW;IAC/C,MAAM0pB,SAAA,GAAYtC,cAAe,CAAAjO,MAAA;IACjC,IAAI,CAACuQ,SAAW;MACdvW,KAAA,GAAQgG,MAAA,CAAOwQ,WAAW;MAC1BpQ,MAAA,GAASJ,MAAA,CAAOyQ,YAAY;KACvB;MACL,MAAM9O,IAAO,GAAA4O,SAAA,CAAUX,qBAAqB;MAC5C,MAAMc,cAAA,GAAiBjC,gBAAiB,CAAA8B,SAAA;MACxC,MAAMI,eAAA,GAAkB1B,kBAAmB,CAAAyB,cAAA,EAAgB,QAAU;MACrE,MAAME,gBAAA,GAAmB3B,kBAAA,CAAmByB,cAAgB;MAC5D1W,KAAA,GAAQ2H,IAAA,CAAK3H,KAAK,GAAG4W,gBAAA,CAAiB5W,KAAK,GAAG2W,eAAA,CAAgB3W,KAAK;MACnEoG,MAAA,GAASuB,IAAA,CAAKvB,MAAM,GAAGwQ,gBAAA,CAAiBxQ,MAAM,GAAGuQ,eAAA,CAAgBvQ,MAAM;MACvEqE,QAAA,GAAW4J,aAAc,CAAAqC,cAAA,CAAejM,QAAQ,EAAE8L,SAAW;MAC7DD,SAAA,GAAYjC,aAAc,CAAAqC,cAAA,CAAeJ,SAAS,EAAEC,SAAW;;;EAGnE,OAAO;IACLvW,KAAA;IACAoG,MAAA;IACAqE,QAAA,EAAUA,QAAY,IAAAxb,QAAA;IACtBqnB,SAAA,EAAWA,SAAa,IAAArnB;EAC1B;AACF;AAEA,MAAM4nB,MAAA,GAAU5pB,CAAA,IAAc6B,IAAA,CAAKiB,KAAK,CAAC9C,CAAA,GAAI,EAAM;AAEnD;AACO,SAAS6pB,eACd9Q,MAAyB,EACzB+Q,OAAgB,EAChBC,QAAiB,EACjBC,WAAoB,EACe;EACnC,MAAM1U,KAAA,GAAQkS,gBAAiB,CAAAzO,MAAA;EAC/B,MAAMkR,OAAA,GAAUjC,kBAAA,CAAmB1S,KAAO;EAC1C,MAAMkI,QAAA,GAAW4J,aAAc,CAAA9R,KAAA,CAAMkI,QAAQ,EAAEzE,MAAA,EAAQ,aAAkB,KAAA/W,QAAA;EACzE,MAAMqnB,SAAA,GAAYjC,aAAc,CAAA9R,KAAA,CAAM+T,SAAS,EAAEtQ,MAAA,EAAQ,cAAmB,KAAA/W,QAAA;EAC5E,MAAMkoB,aAAA,GAAgBd,gBAAiB,CAAArQ,MAAA,EAAQ+Q,OAAS,EAAAC,QAAA;EACxD,IAAI;IAAChX,KAAA;IAAOoG;EAAA,CAAO,GAAG+Q,aAAA;EAEtB,IAAI5U,KAAA,CAAM2T,SAAS,KAAK,aAAe;IACrC,MAAME,OAAA,GAAUnB,kBAAmB,CAAA1S,KAAA,EAAO,QAAU;IACpD,MAAM4T,QAAA,GAAWlB,kBAAA,CAAmB1S,KAAO;IAC3CvC,KAAA,IAASmW,QAAS,CAAAnW,KAAK,GAAGoW,OAAA,CAAQpW,KAAK;IACvCoG,MAAA,IAAU+P,QAAS,CAAA/P,MAAM,GAAGgQ,OAAA,CAAQhQ,MAAM;;EAE5CpG,KAAA,GAAQlR,IAAA,CAAKqC,GAAG,CAAC,CAAG,EAAA6O,KAAA,GAAQkX,OAAA,CAAQlX,KAAK;EACzCoG,MAAS,GAAAtX,IAAA,CAAKqC,GAAG,CAAC,GAAG8lB,WAAA,GAAcjX,KAAQ,GAAAiX,WAAA,GAAc7Q,MAAS,GAAA8Q,OAAA,CAAQ9Q,MAAM;EAChFpG,KAAA,GAAQ6W,MAAA,CAAO/nB,IAAK,CAAAoC,GAAG,CAAC8O,KAAO,EAAAyK,QAAA,EAAU0M,aAAA,CAAc1M,QAAQ;EAC/DrE,MAAA,GAASyQ,MAAA,CAAO/nB,IAAK,CAAAoC,GAAG,CAACkV,MAAQ,EAAAkQ,SAAA,EAAWa,aAAA,CAAcb,SAAS;EACnE,IAAItW,KAAA,IAAS,CAACoG,MAAQ;;;IAGpBA,MAAA,GAASyQ,MAAA,CAAO7W,KAAQ;;EAG1B,MAAMoX,cAAA,GAAiBL,OAAY,KAAAlqB,SAAA,IAAamqB,QAAa,KAAAnqB,SAAA;EAE7D,IAAIuqB,cAAA,IAAkBH,WAAA,IAAeE,aAAc,CAAA/Q,MAAM,IAAIA,MAAS,GAAA+Q,aAAA,CAAc/Q,MAAM,EAAE;IAC1FA,MAAA,GAAS+Q,aAAA,CAAc/Q,MAAM;IAC7BpG,KAAA,GAAQ6W,MAAO,CAAA/nB,IAAA,CAAKoB,KAAK,CAACkW,MAAS,GAAA6Q,WAAA;;EAGrC,OAAO;IAACjX,KAAA;IAAOoG;EAAM;AACvB;AAEA;;;;;;AAMO,SAASiR,WACdA,CAAArZ,KAAY,EACZsZ,UAAkB,EAClBC,UAAoB,EACJ;EAChB,MAAMC,UAAA,GAAaF,UAAc;EACjC,MAAMG,YAAA,GAAe3oB,IAAK,CAAAoB,KAAK,CAAC8N,KAAA,CAAMoI,MAAM,GAAGoR,UAAA;EAC/C,MAAME,WAAA,GAAc5oB,IAAK,CAAAoB,KAAK,CAAC8N,KAAA,CAAMgC,KAAK,GAAGwX,UAAA;EAE7CxZ,KAAA,CAAMoI,MAAM,GAAGtX,IAAA,CAAKoB,KAAK,CAAC8N,KAAA,CAAMoI,MAAM;EACtCpI,KAAA,CAAMgC,KAAK,GAAGlR,IAAA,CAAKoB,KAAK,CAAC8N,KAAA,CAAMgC,KAAK;EAEpC,MAAMgG,MAAA,GAAShI,KAAA,CAAMgI,MAAM;;;;EAK3B,IAAIA,MAAA,CAAOzD,KAAK,KAAKgV,UAAA,IAAe,CAACvR,MAAO,CAAAzD,KAAK,CAAC6D,MAAM,IAAI,CAACJ,MAAA,CAAOzD,KAAK,CAACvC,KAAK,CAAI;IACjFgG,MAAO,CAAAzD,KAAK,CAAC6D,MAAM,GAAI,GAAEpI,KAAM,CAAAoI,MAAO,IAAG;IACzCJ,MAAO,CAAAzD,KAAK,CAACvC,KAAK,GAAI,GAAEhC,KAAM,CAAAgC,KAAM,IAAG;;EAGzC,IAAIhC,KAAA,CAAM6H,uBAAuB,KAAK2R,UAC/B,IAAAxR,MAAA,CAAOI,MAAM,KAAKqR,YAClB,IAAAzR,MAAA,CAAOhG,KAAK,KAAK0X,WAAa;IACnC1Z,KAAA,CAAM6H,uBAAuB,GAAG2R,UAAA;IAChCxR,MAAA,CAAOI,MAAM,GAAGqR,YAAA;IAChBzR,MAAA,CAAOhG,KAAK,GAAG0X,WAAA;IACf1Z,KAAM,CAAA4E,GAAG,CAAC+U,YAAY,CAACH,UAAA,EAAY,CAAG,KAAGA,UAAA,EAAY,CAAG;IACxD,OAAO,IAAI;;EAEb,OAAO,KAAK;AACd;AAEA;;;;;AAKa,MAAAI,4BAAA,GAAgC,YAAW;EACtD,IAAIC,gBAAA,GAAmB,KAAK;EAC5B,IAAI;IACF,MAAM7rB,OAAU;MACd,IAAI8rB,OAAUA,CAAA;QACZD,gBAAA,GAAmB,IAAI;QACvB,OAAO,KAAK;MACd;IACF;IAEAniB,MAAA,CAAOqiB,gBAAgB,CAAC,MAAQ,MAAI,EAAE/rB,OAAA;IACtC0J,MAAA,CAAOsiB,mBAAmB,CAAC,MAAQ,MAAI,EAAEhsB,OAAA;EAC3C,EAAE,OAAO4C,CAAG;;;EAGZ,OAAOipB,gBAAA;AACT,CAAK;AAEL;;;;;;;;AAQC;AAEM,SAASI,aACdvD,OAAoB,EACpBzjB,QAA4B,EACR;EACpB,MAAMrI,KAAA,GAAQisB,QAAA,CAASH,OAAS,EAAAzjB,QAAA;EAChC,MAAMma,OAAU,GAAAxiB,KAAA,IAASA,KAAM,CAAAyiB,KAAK,CAAC;EACrC,OAAOD,OAAA,GAAU,CAACA,OAAO,CAAC,EAAE,GAAGve,SAAS;AAC1C;;AC/RA;;;AAGO,SAASqrB,aAAaC,EAAS,EAAEC,EAAS,EAAE9f,CAAS,EAAE2K,IAAK,EAAE;EACnE,OAAO;IACL/V,CAAG,EAAAirB,EAAA,CAAGjrB,CAAC,GAAGoL,CAAK,IAAA8f,EAAA,CAAGlrB,CAAC,GAAGirB,EAAG,CAAAjrB,CAAC,CAAD;IACzBE,CAAG,EAAA+qB,EAAA,CAAG/qB,CAAC,GAAGkL,CAAK,IAAA8f,EAAA,CAAGhrB,CAAC,GAAG+qB,EAAG,CAAA/qB,CAAC;EAC5B;AACF;AAEA;;;AAGO,SAASirB,sBACdF,EAAS,EACTC,EAAS,EACT9f,CAAS,EAAE2K,IAAkC,EAC7C;EACA,OAAO;IACL/V,CAAG,EAAAirB,EAAA,CAAGjrB,CAAC,GAAGoL,CAAK,IAAA8f,EAAA,CAAGlrB,CAAC,GAAGirB,EAAG,CAAAjrB,CAAC,CAAD;IACzBE,CAAG,EAAA6V,IAAA,KAAS,QAAW,GAAA3K,CAAA,GAAI,GAAM,GAAA6f,EAAA,CAAG/qB,CAAC,GAAGgrB,EAAG,CAAAhrB,CAAC,GACxC6V,IAAA,KAAS,OAAU,GAAA3K,CAAA,GAAI,IAAI6f,EAAG,CAAA/qB,CAAC,GAAGgrB,EAAA,CAAGhrB,CAAC,GACpCkL,CAAI,OAAI8f,EAAG,CAAAhrB,CAAC,GAAG+qB,EAAA,CAAG/qB;EAC1B;AACF;AAEA;;;AAGO,SAASkrB,qBAAqBH,EAAe,EAAEC,EAAe,EAAE9f,CAAS,EAAE2K,IAAK,EAAE;EACvF,MAAMsV,GAAM;IAACrrB,CAAA,EAAGirB,EAAA,CAAGxP,IAAI;IAAEvb,CAAA,EAAG+qB,EAAA,CAAGtP;EAAI;EACnC,MAAM2P,GAAM;IAACtrB,CAAA,EAAGkrB,EAAA,CAAG1P,IAAI;IAAEtb,CAAA,EAAGgrB,EAAA,CAAGxP;EAAI;EACnC,MAAMta,CAAA,GAAI4pB,YAAa,CAAAC,EAAA,EAAII,GAAK,EAAAjgB,CAAA;EAChC,MAAM/J,CAAA,GAAI2pB,YAAa,CAAAK,GAAA,EAAKC,GAAK,EAAAlgB,CAAA;EACjC,MAAMmgB,CAAA,GAAIP,YAAa,CAAAM,GAAA,EAAKJ,EAAI,EAAA9f,CAAA;EAChC,MAAMqC,CAAA,GAAIud,YAAa,CAAA5pB,CAAA,EAAGC,CAAG,EAAA+J,CAAA;EAC7B,MAAM1J,CAAA,GAAIspB,YAAa,CAAA3pB,CAAA,EAAGkqB,CAAG,EAAAngB,CAAA;EAC7B,OAAO4f,YAAA,CAAavd,CAAA,EAAG/L,CAAG,EAAA0J,CAAA;AAC5B;AChCA,MAAMogB,qBAAwB,YAAAA,CAASC,KAAa,EAAE3Y,KAAa,EAAc;EAC/E,OAAO;IACL9S,EAAEA,CAAC,EAAE;MACH,OAAOyrB,KAAA,GAAQA,KAAA,GAAQ3Y,KAAQ,GAAA9S,CAAA;IACjC;IACA0rB,SAASrS,CAAC,EAAE;MACVvG,KAAQ,GAAAuG,CAAA;IACV;IACA2C,UAAU7S,KAAK,EAAE;MACf,IAAIA,KAAA,KAAU,QAAU;QACtB,OAAOA,KAAA;;MAET,OAAOA,KAAA,KAAU,OAAU,YAAS,OAAO;IAC7C;IACAwiB,KAAMA,CAAA3rB,CAAC,EAAEtE,KAAK,EAAE;MACd,OAAOsE,CAAI,GAAAtE,KAAA;IACb;IACAkwB,UAAWA,CAAA5rB,CAAC,EAAE6rB,SAAS,EAAE;MACvB,OAAO7rB,CAAI,GAAA6rB,SAAA;IACb;EACF;AACF;AAEA,MAAMC,qBAAA,GAAwB,SAAAA,CAAA,EAAuB;EACnD,OAAO;IACL9rB,EAAEA,CAAC,EAAE;MACH,OAAOA,CAAA;IACT;IACA0rB,QAASA,CAAArS,CAAC,EAAE,EACZ;IACA2C,UAAU7S,KAAK,EAAE;MACf,OAAOA,KAAA;IACT;IACAwiB,KAAMA,CAAA3rB,CAAC,EAAEtE,KAAK,EAAE;MACd,OAAOsE,CAAI,GAAAtE,KAAA;IACb;IACAkwB,UAAWA,CAAA5rB,CAAC,EAAE+rB,UAAU,EAAE;MACxB,OAAO/rB,CAAA;IACT;EACF;AACF;AAEO,SAASgsB,aAAcA,CAAAxiB,GAAY,EAAEiiB,KAAa,EAAE3Y,KAAa,EAAE;EACxE,OAAOtJ,GAAM,GAAAgiB,qBAAA,CAAsBC,KAAO,EAAA3Y,KAAA,IAASgZ,qBAAuB;AAC5E;AAEO,SAASG,sBAAsBvW,GAA6B,EAAEwW,SAAwB,EAAE;EAC7F,IAAI7W,KAA4B,EAAA8W,QAAA;EAChC,IAAID,SAAA,KAAc,KAAS,IAAAA,SAAA,KAAc,KAAO;IAC9C7W,KAAQ,GAAAK,GAAA,CAAIoD,MAAM,CAACzD,KAAK;IACxB8W,QAAW,IACT9W,KAAA,CAAMwS,gBAAgB,CAAC,cACvBxS,KAAA,CAAM+W,mBAAmB,CAAC,aAC3B;IAED/W,KAAM,CAAAgX,WAAW,CAAC,aAAaH,SAAW;IACzCxW,GAAA,CAAiD4W,iBAAiB,GAAGH,QAAA;;AAE1E;AAEO,SAASI,qBAAqB7W,GAA6B,EAAEyW,QAA2B,EAAE;EAC/F,IAAIA,QAAA,KAAaxsB,SAAW;IAC1B,OAAQ+V,GAAA,CAAiD4W,iBAAiB;IAC1E5W,GAAA,CAAIoD,MAAM,CAACzD,KAAK,CAACgX,WAAW,CAAC,WAAa,EAAAF,QAAQ,CAAC,EAAE,EAAEA,QAAQ,CAAC,CAAE;;AAEtE;AC/DA,SAASK,UAAWA,CAAAzoB,QAAQ,EAAE;EAC5B,IAAIA,QAAA,KAAa,OAAS;IACxB,OAAO;MACL0oB,OAAS,EAAAnnB,aAAA;MACTonB,OAAS,EAAAtnB,UAAA;MACTunB,SAAW,EAAAtnB;IACb;;EAEF,OAAO;IACLonB,OAAS,EAAAxmB,UAAA;IACTymB,OAAS,EAAAA,CAACtrB,CAAG,EAAAC,CAAA,KAAMD,CAAI,GAAAC,CAAA;IACvBsrB,SAAA,EAAW3sB,CAAK,IAAAA;EAClB;AACF;AAEA,SAAS4sB,gBAAiBA,CAAA;EAACrnB,KAAK;EAAEC,GAAG;EAAEuE,KAAK;EAAEyE,IAAI;EAAE6G;AAAK,CAAC,EAAE;EAC1D,OAAO;IACL9P,KAAA,EAAOA,KAAQ,GAAAwE,KAAA;IACfvE,GAAA,EAAKA,GAAM,GAAAuE,KAAA;IACXyE,IAAA,EAAMA,IAAA,IAAQ,CAAChJ,GAAA,GAAMD,KAAQ,QAAKwE,KAAU;IAC5CsL;EACF;AACF;AAEA,SAASwX,WAAWC,OAAO,EAAEljB,MAAM,EAAEoI,MAAM,EAAE;EAC3C,MAAM;IAACjO,QAAA;IAAUwB,KAAA,EAAOwnB,UAAA;IAAYvnB,GAAA,EAAKwnB;EAAQ,CAAC,GAAGhb,MAAA;EACrD,MAAM;IAACya,OAAO;IAAEE;EAAS,CAAC,GAAGH,UAAW,CAAAzoB,QAAA;EACxC,MAAMgG,KAAA,GAAQH,MAAA,CAAOlM,MAAM;EAE3B,IAAI;IAAC6H,KAAK;IAAEC,GAAA;IAAKgJ;EAAA,CAAK,GAAGse,OAAA;EACzB,IAAIvvB,CAAG,EAAAO,IAAA;EAEP,IAAI0Q,IAAM;IACRjJ,KAAS,IAAAwE,KAAA;IACTvE,GAAO,IAAAuE,KAAA;IACP,KAAKxM,CAAA,GAAI,GAAGO,IAAO,GAAAiM,KAAK,EAAExM,CAAI,GAAAO,IAAA,EAAM,EAAEP,CAAG;MACvC,IAAI,CAACkvB,OAAQ,CAAAE,SAAA,CAAU/iB,MAAM,CAACrE,KAAQ,GAAAwE,KAAA,CAAM,CAAChG,QAAA,CAAS,CAAG,EAAAgpB,UAAA,EAAYC,QAAW;QAC9E;;MAEFznB,KAAA;MACAC,GAAA;IACF;IACAD,KAAS,IAAAwE,KAAA;IACTvE,GAAO,IAAAuE,KAAA;;EAGT,IAAIvE,GAAA,GAAMD,KAAO;IACfC,GAAO,IAAAuE,KAAA;;EAET,OAAO;IAACxE,KAAA;IAAOC,GAAA;IAAKgJ,IAAA;IAAM6G,KAAA,EAAOyX,OAAA,CAAQzX;EAAK;AAChD;AAgBO,SAAS4X,aAAcA,CAAAH,OAAO,EAAEljB,MAAM,EAAEoI,MAAM,EAAE;EACrD,IAAI,CAACA,MAAQ;IACX,OAAO,CAAC8a,OAAA,CAAQ;;EAGlB,MAAM;IAAC/oB,QAAA;IAAUwB,KAAA,EAAOwnB,UAAA;IAAYvnB,GAAA,EAAKwnB;EAAQ,CAAC,GAAGhb,MAAA;EACrD,MAAMjI,KAAA,GAAQH,MAAA,CAAOlM,MAAM;EAC3B,MAAM;IAACgvB,OAAA;IAASD,OAAA;IAASE;EAAS,CAAC,GAAGH,UAAW,CAAAzoB,QAAA;EACjD,MAAM;IAACwB,KAAA;IAAOC,GAAA;IAAKgJ,IAAA;IAAM6G;EAAA,CAAM,GAAGwX,UAAW,CAAAC,OAAA,EAASljB,MAAQ,EAAAoI,MAAA;EAE9D,MAAM5O,MAAA,GAAS,EAAE;EACjB,IAAI8pB,MAAA,GAAS,KAAK;EAClB,IAAIC,QAAA,GAAW,IAAI;EACnB,IAAIzxB,KAAA,EAAOof,KAAO,EAAAsS,SAAA;EAElB,MAAMC,aAAA,GAAgBA,CAAA,KAAMZ,OAAQ,CAAAM,UAAA,EAAYK,SAAA,EAAW1xB,KAAU,KAAAgxB,OAAA,CAAQK,UAAA,EAAYK,SAAe;EACxG,MAAME,WAAA,GAAcA,CAAA,KAAMZ,OAAQ,CAAAM,QAAA,EAAUtxB,KAAA,MAAW,CAAK,IAAA+wB,OAAA,CAAQO,QAAA,EAAUI,SAAW,EAAA1xB,KAAA;EACzF,MAAM6xB,WAAA,GAAcA,CAAA,KAAML,MAAU,IAAAG,aAAA;EACpC,MAAMG,UAAA,GAAaA,CAAA,KAAM,CAACN,MAAU,IAAAI,WAAA;EAEpC,KAAK,IAAI/vB,CAAA,GAAIgI,KAAO,EAAAohB,IAAA,GAAOphB,KAAA,EAAOhI,CAAK,IAAAiI,GAAA,EAAK,EAAEjI,CAAG;IAC/Cud,KAAQ,GAAAlR,MAAM,CAACrM,CAAA,GAAIwM,KAAM;IAEzB,IAAI+Q,KAAA,CAAMqJ,IAAI,EAAE;MACd;;IAGFzoB,KAAQ,GAAAixB,SAAA,CAAU7R,KAAK,CAAC/W,QAAS;IAEjC,IAAIrI,KAAA,KAAU0xB,SAAW;MACvB;;IAGFF,MAAS,GAAAT,OAAA,CAAQ/wB,KAAA,EAAOqxB,UAAY,EAAAC,QAAA;IAEpC,IAAIG,QAAA,KAAa,IAAI,IAAII,WAAe;MACtCJ,QAAA,GAAWT,OAAQ,CAAAhxB,KAAA,EAAOqxB,UAAgB,UAAIxvB,CAAA,GAAIopB,IAAI;;IAGxD,IAAIwG,QAAA,KAAa,IAAI,IAAIK,UAAc;MACrCpqB,MAAO,CAAA5C,IAAI,CAACosB,gBAAiB;QAACrnB,KAAO,EAAA4nB,QAAA;QAAU3nB,GAAK,EAAAjI,CAAA;QAAGiR,IAAA;QAAMzE,KAAA;QAAOsL;MAAK;MACzE8X,QAAA,GAAW,IAAI;;IAEjBxG,IAAO,GAAAppB,CAAA;IACP6vB,SAAY,GAAA1xB,KAAA;EACd;EAEA,IAAIyxB,QAAA,KAAa,IAAI,EAAE;IACrB/pB,MAAO,CAAA5C,IAAI,CAACosB,gBAAiB;MAACrnB,KAAO,EAAA4nB,QAAA;MAAU3nB,GAAA;MAAKgJ,IAAA;MAAMzE,KAAA;MAAOsL;IAAK;;EAGxE,OAAOjS,MAAA;AACT;AAYO,SAASqqB,eAAetR,IAAI,EAAEnK,MAAM,EAAE;EAC3C,MAAM5O,MAAA,GAAS,EAAE;EACjB,MAAMsqB,QAAA,GAAWvR,IAAA,CAAKuR,QAAQ;EAE9B,KAAK,IAAInwB,CAAI,MAAGA,CAAA,GAAImwB,QAAS,CAAAhwB,MAAM,EAAEH,CAAK;IACxC,MAAMowB,GAAA,GAAMV,aAAA,CAAcS,QAAQ,CAACnwB,CAAA,CAAE,EAAE4e,IAAA,CAAKvS,MAAM,EAAEoI,MAAA;IACpD,IAAI2b,GAAA,CAAIjwB,MAAM,EAAE;MACd0F,MAAA,CAAO5C,IAAI,CAAI,GAAAmtB,GAAA;;EAEnB;EACA,OAAOvqB,MAAA;AACT;AAKA,SAASwqB,gBAAgBhkB,MAAM,EAAEG,KAAK,EAAEyE,IAAI,EAAEiY,QAAQ,EAAE;EACtD,IAAIlhB,KAAQ;EACZ,IAAIC,GAAA,GAAMuE,KAAQ;EAElB,IAAIyE,IAAA,IAAQ,CAACiY,QAAU;IAErB,OAAOlhB,KAAA,GAAQwE,KAAA,IAAS,CAACH,MAAM,CAACrE,KAAM,EAAC4e,IAAI,EAAE;MAC3C5e,KAAA;IACF;;EAIF,OAAOA,KAAA,GAAQwE,KAAS,IAAAH,MAAM,CAACrE,KAAM,EAAC4e,IAAI,EAAE;IAC1C5e,KAAA;EACF;EAGAA,KAAS,IAAAwE,KAAA;EAET,IAAIyE,IAAM;IAERhJ,GAAO,IAAAD,KAAA;;EAGT,OAAOC,GAAA,GAAMD,KAAA,IAASqE,MAAM,CAACpE,GAAA,GAAMuE,KAAM,EAACoa,IAAI,EAAE;IAC9C3e,GAAA;EACF;EAGAA,GAAO,IAAAuE,KAAA;EAEP,OAAO;IAACxE,KAAA;IAAOC;EAAG;AACpB;AASA,SAASqoB,cAAcjkB,MAAM,EAAErE,KAAK,EAAEtB,GAAG,EAAEuK,IAAI,EAAE;EAC/C,MAAMzE,KAAA,GAAQH,MAAA,CAAOlM,MAAM;EAC3B,MAAM0F,MAAA,GAAS,EAAE;EACjB,IAAIqD,IAAO,GAAAlB,KAAA;EACX,IAAIohB,IAAA,GAAO/c,MAAM,CAACrE,KAAM;EACxB,IAAIC,GAAA;EAEJ,KAAKA,GAAA,GAAMD,KAAQ,MAAGC,GAAO,IAAAvB,GAAA,EAAK,EAAEuB,GAAK;IACvC,MAAMsoB,GAAM,GAAAlkB,MAAM,CAACpE,GAAA,GAAMuE,KAAM;IAC/B,IAAI+jB,GAAI,CAAA3J,IAAI,IAAI2J,GAAA,CAAIC,IAAI,EAAE;MACxB,IAAI,CAACpH,IAAK,CAAAxC,IAAI,EAAE;QACd3V,IAAA,GAAO,KAAK;QACZpL,MAAA,CAAO5C,IAAI,CAAC;UAAC+E,KAAA,EAAOA,KAAQ,GAAAwE,KAAA;UAAOvE,GAAA,EAAK,CAACA,GAAM,QAAKuE,KAAA;UAAOyE;QAAI;QAE/DjJ,KAAA,GAAQkB,IAAO,GAAAqnB,GAAA,CAAIC,IAAI,GAAGvoB,GAAA,GAAM,IAAI;;KAEjC;MACLiB,IAAO,GAAAjB,GAAA;MACP,IAAImhB,IAAA,CAAKxC,IAAI,EAAE;QACb5e,KAAQ,GAAAC,GAAA;;;IAGZmhB,IAAO,GAAAmH,GAAA;EACT;EAEA,IAAIrnB,IAAA,KAAS,IAAI,EAAE;IACjBrD,MAAA,CAAO5C,IAAI,CAAC;MAAC+E,KAAA,EAAOA,KAAQ,GAAAwE,KAAA;MAAOvE,GAAA,EAAKiB,IAAO,GAAAsD,KAAA;MAAOyE;IAAI;;EAG5D,OAAOpL,MAAA;AACT;AAUO,SAAS4qB,iBAAiB7R,IAAI,EAAE8R,cAAc,EAAE;EACrD,MAAMrkB,MAAA,GAASuS,IAAA,CAAKvS,MAAM;EAC1B,MAAM6c,QAAW,GAAAtK,IAAA,CAAKrd,OAAO,CAAC2nB,QAAQ;EACtC,MAAM1c,KAAA,GAAQH,MAAA,CAAOlM,MAAM;EAE3B,IAAI,CAACqM,KAAO;IACV,OAAO,EAAE;;EAGX,MAAMyE,IAAO,IAAC,CAAC2N,IAAA,CAAK+R,KAAK;EACzB,MAAM;IAAC3oB,KAAA;IAAOC;EAAA,CAAI,GAAGooB,eAAA,CAAgBhkB,MAAQ,EAAAG,KAAA,EAAOyE,IAAM,EAAAiY,QAAA;EAE1D,IAAIA,QAAA,KAAa,IAAI,EAAE;IACrB,OAAO0H,aAAA,CAAchS,IAAM,GAAC;MAAC5W,KAAA;MAAOC,GAAA;MAAKgJ;IAAI,EAAE,EAAE5E,MAAQ,EAAAqkB,cAAA;;EAG3D,MAAMhqB,GAAM,GAAAuB,GAAA,GAAMD,KAAQ,GAAAC,GAAA,GAAMuE,KAAA,GAAQvE,GAAG;EAC3C,MAAM4oB,YAAA,GAAe,CAAC,CAACjS,IAAA,CAAKkS,SAAS,IAAI9oB,KAAA,KAAU,CAAK,IAAAC,GAAA,KAAQuE,KAAQ;EACxE,OAAOokB,aAAA,CAAchS,IAAM,EAAA0R,aAAA,CAAcjkB,MAAA,EAAQrE,KAAO,EAAAtB,GAAA,EAAKmqB,YAAA,GAAexkB,MAAQ,EAAAqkB,cAAA;AACtF;AAQA,SAASE,cAAchS,IAAI,EAAEuR,QAAQ,EAAE9jB,MAAM,EAAEqkB,cAAc,EAAE;EAC7D,IAAI,CAACA,cAAkB,KAACA,cAAA,CAAepM,UAAU,IAAI,CAACjY,MAAQ;IAC5D,OAAO8jB,QAAA;;EAET,OAAOY,eAAA,CAAgBnS,IAAM,EAAAuR,QAAA,EAAU9jB,MAAQ,EAAAqkB,cAAA;AACjD;AASA,SAASK,gBAAgBnS,IAAI,EAAEuR,QAAQ,EAAE9jB,MAAM,EAAEqkB,cAAc,EAAE;EAC/D,MAAMM,YAAe,GAAApS,IAAA,CAAKqS,MAAM,CAACzV,UAAU;EAC3C,MAAM0V,SAAA,GAAYC,SAAU,CAAAvS,IAAA,CAAKrd,OAAO;EACxC,MAAM;IAAC6vB,aAAe,EAAA1wB,YAAA;IAAca,OAAA,EAAS;MAAC2nB;IAAQ;EAAC,CAAC,GAAGtK,IAAA;EAC3D,MAAMpS,KAAA,GAAQH,MAAA,CAAOlM,MAAM;EAC3B,MAAM0F,MAAA,GAAS,EAAE;EACjB,IAAIwrB,SAAY,GAAAH,SAAA;EAChB,IAAIlpB,KAAQ,GAAAmoB,QAAQ,CAAC,EAAE,CAACnoB,KAAK;EAC7B,IAAIhI,CAAI,GAAAgI,KAAA;EAER,SAASspB,SAASnpB,CAAC,EAAEhE,CAAC,EAAEotB,CAAC,EAAEC,EAAE,EAAE;IAC7B,MAAMC,GAAM,GAAAvI,QAAA,GAAW,CAAC,IAAI,CAAC;IAC7B,IAAI/gB,CAAA,KAAMhE,CAAG;MACX;;IAGFgE,CAAK,IAAAqE,KAAA;IACL,OAAOH,MAAM,CAAClE,CAAA,GAAIqE,KAAM,EAACoa,IAAI,EAAE;MAC7Bze,CAAK,IAAAspB,GAAA;IACP;IACA,OAAOplB,MAAM,CAAClI,CAAA,GAAIqI,KAAM,EAACoa,IAAI,EAAE;MAC7BziB,CAAK,IAAAstB,GAAA;IACP;IACA,IAAItpB,CAAA,GAAIqE,KAAU,KAAArI,CAAA,GAAIqI,KAAO;MAC3B3G,MAAA,CAAO5C,IAAI,CAAC;QAAC+E,KAAA,EAAOG,CAAI,GAAAqE,KAAA;QAAOvE,GAAA,EAAK9D,CAAI,GAAAqI,KAAA;QAAOyE,IAAM,EAAAsgB,CAAA;QAAGzZ,KAAO,EAAA0Z;MAAE;MACjEH,SAAY,GAAAG,EAAA;MACZxpB,KAAA,GAAQ7D,CAAI,GAAAqI,KAAA;;EAEhB;EAEA,KAAK,MAAM+iB,OAAA,IAAWY,QAAU;IAC9BnoB,KAAQ,GAAAkhB,QAAA,GAAWlhB,KAAQ,GAAAunB,OAAA,CAAQvnB,KAAK;IACxC,IAAIohB,IAAO,GAAA/c,MAAM,CAACrE,KAAA,GAAQwE,KAAM;IAChC,IAAIsL,KAAA;IACJ,KAAK9X,CAAA,GAAIgI,KAAQ,MAAGhI,CAAA,IAAKuvB,OAAQ,CAAAtnB,GAAG,EAAEjI,CAAK;MACzC,MAAM2oB,EAAK,GAAAtc,MAAM,CAACrM,CAAA,GAAIwM,KAAM;MAC5BsL,KAAA,GAAQqZ,SAAU,CAAAT,cAAA,CAAepM,UAAU,CAACnC,aAAA,CAAc6O,YAAc;QACtE1yB,IAAM;QACNozB,EAAI,EAAAtI,IAAA;QACJsE,EAAI,EAAA/E,EAAA;QACJgJ,WAAA,EAAa,CAAC3xB,CAAI,QAAKwM,KAAA;QACvBolB,WAAA,EAAa5xB,CAAI,GAAAwM,KAAA;QACjB9L;MACF;MACA,IAAImxB,YAAA,CAAa/Z,KAAA,EAAOuZ,SAAY;QAClCC,QAAA,CAAStpB,KAAO,EAAAhI,CAAA,GAAI,CAAG,EAAAuvB,OAAA,CAAQte,IAAI,EAAEogB,SAAA;;MAEvCjI,IAAO,GAAAT,EAAA;MACP0I,SAAY,GAAAvZ,KAAA;IACd;IACA,IAAI9P,KAAA,GAAQhI,CAAA,GAAI,CAAG;MACjBsxB,QAAA,CAAStpB,KAAO,EAAAhI,CAAA,GAAI,CAAG,EAAAuvB,OAAA,CAAQte,IAAI,EAAEogB,SAAA;;EAEzC;EAEA,OAAOxrB,MAAA;AACT;AAEA,SAASsrB,UAAU5vB,OAAO,EAAE;EAC1B,OAAO;IACL4V,eAAA,EAAiB5V,OAAA,CAAQ4V,eAAe;IACxC2a,cAAA,EAAgBvwB,OAAA,CAAQuwB,cAAc;IACtCC,UAAA,EAAYxwB,OAAA,CAAQwwB,UAAU;IAC9BC,gBAAA,EAAkBzwB,OAAA,CAAQywB,gBAAgB;IAC1CC,eAAA,EAAiB1wB,OAAA,CAAQ0wB,eAAe;IACxC7U,WAAA,EAAa7b,OAAA,CAAQ6b,WAAW;IAChChG,WAAA,EAAa7V,OAAA,CAAQ6V;EACvB;AACF;AAEA,SAASya,YAAaA,CAAA/Z,KAAK,EAAEuZ,SAAS,EAAE;EACtC,IAAI,CAACA,SAAW;IACd,OAAO,KAAK;;EAEd,MAAM5W,KAAA,GAAQ,EAAE;EAChB,MAAMyX,QAAW,YAAAA,CAAS9wB,GAAG,EAAEjD,KAAK,EAAE;IACpC,IAAI,CAACiS,mBAAA,CAAoBjS,KAAQ;MAC/B,OAAOA,KAAA;;IAET,IAAI,CAACsc,KAAA,CAAMtG,QAAQ,CAAChW,KAAQ;MAC1Bsc,KAAA,CAAMxX,IAAI,CAAC9E,KAAA;;IAEb,OAAOsc,KAAA,CAAMpZ,OAAO,CAAClD,KAAA;EACvB;EACA,OAAOuU,IAAA,CAAKC,SAAS,CAACmF,KAAA,EAAOoa,QAAA,MAAcxf,IAAK,CAAAC,SAAS,CAAC0e,SAAW,EAAAa,QAAA;AACvE"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}