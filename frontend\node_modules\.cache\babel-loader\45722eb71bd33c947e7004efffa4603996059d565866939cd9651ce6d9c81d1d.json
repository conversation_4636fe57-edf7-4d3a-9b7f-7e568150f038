{"ast": null, "code": "import { onNextFrame, defaultTimestep } from './on-next-frame.mjs';\nimport { createRenderStep } from './create-render-step.mjs';\nconst maxElapsed = 40;\nlet useDefaultElapsed = true;\nlet runNextFrame = false;\nlet isProcessing = false;\nconst frame = {\n  delta: 0,\n  timestamp: 0\n};\nconst stepsOrder = [\"read\", \"update\", \"preRender\", \"render\", \"postRender\"];\nconst steps = stepsOrder.reduce((acc, key) => {\n  acc[key] = createRenderStep(() => runNextFrame = true);\n  return acc;\n}, {});\nconst sync = stepsOrder.reduce((acc, key) => {\n  const step = steps[key];\n  acc[key] = (process, keepAlive = false, immediate = false) => {\n    if (!runNextFrame) startLoop();\n    return step.schedule(process, keepAlive, immediate);\n  };\n  return acc;\n}, {});\nconst cancelSync = stepsOrder.reduce((acc, key) => {\n  acc[key] = steps[key].cancel;\n  return acc;\n}, {});\nconst flushSync = stepsOrder.reduce((acc, key) => {\n  acc[key] = () => steps[key].process(frame);\n  return acc;\n}, {});\nconst processStep = stepId => steps[stepId].process(frame);\nconst processFrame = timestamp => {\n  runNextFrame = false;\n  frame.delta = useDefaultElapsed ? defaultTimestep : Math.max(Math.min(timestamp - frame.timestamp, maxElapsed), 1);\n  frame.timestamp = timestamp;\n  isProcessing = true;\n  stepsOrder.forEach(processStep);\n  isProcessing = false;\n  if (runNextFrame) {\n    useDefaultElapsed = false;\n    onNextFrame(processFrame);\n  }\n};\nconst startLoop = () => {\n  runNextFrame = true;\n  useDefaultElapsed = true;\n  if (!isProcessing) onNextFrame(processFrame);\n};\nconst getFrameData = () => frame;\nexport default sync;\nexport { cancelSync, flushSync, getFrameData };", "map": {"version": 3, "names": ["onNextFrame", "defaultTimestep", "createRenderStep", "maxElapsed", "useDefaultElapsed", "runNextFrame", "isProcessing", "frame", "delta", "timestamp", "stepsOrder", "steps", "reduce", "acc", "key", "sync", "step", "process", "keepAlive", "immediate", "startLoop", "schedule", "cancelSync", "cancel", "flushSync", "processStep", "stepId", "processFrame", "Math", "max", "min", "for<PERSON>ach", "getFrameData"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/framesync/dist/es/index.mjs"], "sourcesContent": ["import { onNextFrame, defaultTimestep } from './on-next-frame.mjs';\nimport { createRenderStep } from './create-render-step.mjs';\n\nconst maxElapsed = 40;\nlet useDefaultElapsed = true;\nlet runNextFrame = false;\nlet isProcessing = false;\nconst frame = {\n    delta: 0,\n    timestamp: 0,\n};\nconst stepsOrder = [\n    \"read\",\n    \"update\",\n    \"preRender\",\n    \"render\",\n    \"postRender\",\n];\nconst steps = stepsOrder.reduce((acc, key) => {\n    acc[key] = createRenderStep(() => (runNextFrame = true));\n    return acc;\n}, {});\nconst sync = stepsOrder.reduce((acc, key) => {\n    const step = steps[key];\n    acc[key] = (process, keepAlive = false, immediate = false) => {\n        if (!runNextFrame)\n            startLoop();\n        return step.schedule(process, keepAlive, immediate);\n    };\n    return acc;\n}, {});\nconst cancelSync = stepsOrder.reduce((acc, key) => {\n    acc[key] = steps[key].cancel;\n    return acc;\n}, {});\nconst flushSync = stepsOrder.reduce((acc, key) => {\n    acc[key] = () => steps[key].process(frame);\n    return acc;\n}, {});\nconst processStep = (stepId) => steps[stepId].process(frame);\nconst processFrame = (timestamp) => {\n    runNextFrame = false;\n    frame.delta = useDefaultElapsed\n        ? defaultTimestep\n        : Math.max(Math.min(timestamp - frame.timestamp, maxElapsed), 1);\n    frame.timestamp = timestamp;\n    isProcessing = true;\n    stepsOrder.forEach(processStep);\n    isProcessing = false;\n    if (runNextFrame) {\n        useDefaultElapsed = false;\n        onNextFrame(processFrame);\n    }\n};\nconst startLoop = () => {\n    runNextFrame = true;\n    useDefaultElapsed = true;\n    if (!isProcessing)\n        onNextFrame(processFrame);\n};\nconst getFrameData = () => frame;\n\nexport default sync;\nexport { cancelSync, flushSync, getFrameData };\n"], "mappings": "AAAA,SAASA,WAAW,EAAEC,eAAe,QAAQ,qBAAqB;AAClE,SAASC,gBAAgB,QAAQ,0BAA0B;AAE3D,MAAMC,UAAU,GAAG,EAAE;AACrB,IAAIC,iBAAiB,GAAG,IAAI;AAC5B,IAAIC,YAAY,GAAG,KAAK;AACxB,IAAIC,YAAY,GAAG,KAAK;AACxB,MAAMC,KAAK,GAAG;EACVC,KAAK,EAAE,CAAC;EACRC,SAAS,EAAE;AACf,CAAC;AACD,MAAMC,UAAU,GAAG,CACf,MAAM,EACN,QAAQ,EACR,WAAW,EACX,QAAQ,EACR,YAAY,CACf;AACD,MAAMC,KAAK,GAAGD,UAAU,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAK;EAC1CD,GAAG,CAACC,GAAG,CAAC,GAAGZ,gBAAgB,CAAC,MAAOG,YAAY,GAAG,IAAK,CAAC;EACxD,OAAOQ,GAAG;AACd,CAAC,EAAE,CAAC,CAAC,CAAC;AACN,MAAME,IAAI,GAAGL,UAAU,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAK;EACzC,MAAME,IAAI,GAAGL,KAAK,CAACG,GAAG,CAAC;EACvBD,GAAG,CAACC,GAAG,CAAC,GAAG,CAACG,OAAO,EAAEC,SAAS,GAAG,KAAK,EAAEC,SAAS,GAAG,KAAK,KAAK;IAC1D,IAAI,CAACd,YAAY,EACbe,SAAS,CAAC,CAAC;IACf,OAAOJ,IAAI,CAACK,QAAQ,CAACJ,OAAO,EAAEC,SAAS,EAAEC,SAAS,CAAC;EACvD,CAAC;EACD,OAAON,GAAG;AACd,CAAC,EAAE,CAAC,CAAC,CAAC;AACN,MAAMS,UAAU,GAAGZ,UAAU,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAK;EAC/CD,GAAG,CAACC,GAAG,CAAC,GAAGH,KAAK,CAACG,GAAG,CAAC,CAACS,MAAM;EAC5B,OAAOV,GAAG;AACd,CAAC,EAAE,CAAC,CAAC,CAAC;AACN,MAAMW,SAAS,GAAGd,UAAU,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAK;EAC9CD,GAAG,CAACC,GAAG,CAAC,GAAG,MAAMH,KAAK,CAACG,GAAG,CAAC,CAACG,OAAO,CAACV,KAAK,CAAC;EAC1C,OAAOM,GAAG;AACd,CAAC,EAAE,CAAC,CAAC,CAAC;AACN,MAAMY,WAAW,GAAIC,MAAM,IAAKf,KAAK,CAACe,MAAM,CAAC,CAACT,OAAO,CAACV,KAAK,CAAC;AAC5D,MAAMoB,YAAY,GAAIlB,SAAS,IAAK;EAChCJ,YAAY,GAAG,KAAK;EACpBE,KAAK,CAACC,KAAK,GAAGJ,iBAAiB,GACzBH,eAAe,GACf2B,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACrB,SAAS,GAAGF,KAAK,CAACE,SAAS,EAAEN,UAAU,CAAC,EAAE,CAAC,CAAC;EACpEI,KAAK,CAACE,SAAS,GAAGA,SAAS;EAC3BH,YAAY,GAAG,IAAI;EACnBI,UAAU,CAACqB,OAAO,CAACN,WAAW,CAAC;EAC/BnB,YAAY,GAAG,KAAK;EACpB,IAAID,YAAY,EAAE;IACdD,iBAAiB,GAAG,KAAK;IACzBJ,WAAW,CAAC2B,YAAY,CAAC;EAC7B;AACJ,CAAC;AACD,MAAMP,SAAS,GAAGA,CAAA,KAAM;EACpBf,YAAY,GAAG,IAAI;EACnBD,iBAAiB,GAAG,IAAI;EACxB,IAAI,CAACE,YAAY,EACbN,WAAW,CAAC2B,YAAY,CAAC;AACjC,CAAC;AACD,MAAMK,YAAY,GAAGA,CAAA,KAAMzB,KAAK;AAEhC,eAAeQ,IAAI;AACnB,SAASO,UAAU,EAAEE,SAAS,EAAEQ,YAAY"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}