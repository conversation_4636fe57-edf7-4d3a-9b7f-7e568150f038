{"ast": null, "code": "/**\n * A simple guard function:\n *\n * ```js\n * Math.min(Math.max(low, value), high)\n * ```\n */\nfunction guard(low, high, value) {\n  return Math.min(Math.max(low, value), high);\n}\nclass ColorError extends Error {\n  constructor(color) {\n    super(`Failed to parse color: \"${color}\"`);\n  }\n}\nvar ColorError$1 = ColorError;\n\n/**\n * Parses a color into red, gree, blue, alpha parts\n *\n * @param color the input color. Can be a RGB, RBGA, HSL, HSLA, or named color\n */\nfunction parseToRgba(color) {\n  if (typeof color !== 'string') throw new ColorError$1(color);\n  if (color.trim().toLowerCase() === 'transparent') return [0, 0, 0, 0];\n  let normalizedColor = color.trim();\n  normalizedColor = namedColorRegex.test(color) ? nameToHex(color) : color;\n  const reducedHexMatch = reducedHexRegex.exec(normalizedColor);\n  if (reducedHexMatch) {\n    const arr = Array.from(reducedHexMatch).slice(1);\n    return [...arr.slice(0, 3).map(x => parseInt(r(x, 2), 16)), parseInt(r(arr[3] || 'f', 2), 16) / 255];\n  }\n  const hexMatch = hexRegex.exec(normalizedColor);\n  if (hexMatch) {\n    const arr = Array.from(hexMatch).slice(1);\n    return [...arr.slice(0, 3).map(x => parseInt(x, 16)), parseInt(arr[3] || 'ff', 16) / 255];\n  }\n  const rgbaMatch = rgbaRegex.exec(normalizedColor);\n  if (rgbaMatch) {\n    const arr = Array.from(rgbaMatch).slice(1);\n    return [...arr.slice(0, 3).map(x => parseInt(x, 10)), parseFloat(arr[3] || '1')];\n  }\n  const hslaMatch = hslaRegex.exec(normalizedColor);\n  if (hslaMatch) {\n    const [h, s, l, a] = Array.from(hslaMatch).slice(1).map(parseFloat);\n    if (guard(0, 100, s) !== s) throw new ColorError$1(color);\n    if (guard(0, 100, l) !== l) throw new ColorError$1(color);\n    return [...hslToRgb(h, s, l), Number.isNaN(a) ? 1 : a];\n  }\n  throw new ColorError$1(color);\n}\nfunction hash(str) {\n  let hash = 5381;\n  let i = str.length;\n  while (i) {\n    hash = hash * 33 ^ str.charCodeAt(--i);\n  }\n\n  /* JavaScript does bitwise operations (like XOR, above) on 32-bit signed\n   * integers. Since we want the results to be always positive, convert the\n   * signed int to an unsigned by doing an unsigned bitshift. */\n  return (hash >>> 0) % 2341;\n}\nconst colorToInt = x => parseInt(x.replace(/_/g, ''), 36);\nconst compressedColorMap = '1q29ehhb 1n09sgk7 1kl1ekf_ _yl4zsno 16z9eiv3 1p29lhp8 _bd9zg04 17u0____ _iw9zhe5 _to73___ _r45e31e _7l6g016 _jh8ouiv _zn3qba8 1jy4zshs 11u87k0u 1ro9yvyo 1aj3xael 1gz9zjz0 _3w8l4xo 1bf1ekf_ _ke3v___ _4rrkb__ 13j776yz _646mbhl _nrjr4__ _le6mbhl 1n37ehkb _m75f91n _qj3bzfz 1939yygw 11i5z6x8 _1k5f8xs 1509441m 15t5lwgf _ae2th1n _tg1ugcv 1lp1ugcv 16e14up_ _h55rw7n _ny9yavn _7a11xb_ 1ih442g9 _pv442g9 1mv16xof 14e6y7tu 1oo9zkds 17d1cisi _4v9y70f _y98m8kc 1019pq0v 12o9zda8 _348j4f4 1et50i2o _8epa8__ _ts6senj 1o350i2o 1mi9eiuo 1259yrp0 1ln80gnw _632xcoy 1cn9zldc _f29edu4 1n490c8q _9f9ziet 1b94vk74 _m49zkct 1kz6s73a 1eu9dtog _q58s1rz 1dy9sjiq __u89jo3 _aj5nkwg _ld89jo3 13h9z6wx _qa9z2ii _l119xgq _bs5arju 1hj4nwk9 1qt4nwk9 1ge6wau6 14j9zlcw 11p1edc_ _ms1zcxe _439shk6 _jt9y70f _754zsow 1la40eju _oq5p___ _x279qkz 1fa5r3rv _yd2d9ip _424tcku _8y1di2_ _zi2uabw _yy7rn9h 12yz980_ __39ljp6 1b59zg0x _n39zfzp 1fy9zest _b33k___ _hp9wq92 1il50hz4 _io472ub _lj9z3eo 19z9ykg0 _8t8iu3a 12b9bl4a 1ak5yw0o _896v4ku _tb8k8lv _s59zi6t _c09ze0p 1lg80oqn 1id9z8wb _238nba5 1kq6wgdi _154zssg _tn3zk49 _da9y6tc 1sg7cv4f _r12jvtt 1gq5fmkz 1cs9rvci _lp9jn1c _xw1tdnb 13f9zje6 16f6973h _vo7ir40 _bt5arjf _rc45e4t _hr4e100 10v4e100 _hc9zke2 _w91egv_ _sj2r1kk 13c87yx8 _vqpds__ _ni8ggk8 _tj9yqfb 1ia2j4r4 _7x9b10u 1fc9ld4j 1eq9zldr _5j9lhpx _ez9zl6o _md61fzm'.split(' ').reduce((acc, next) => {\n  const key = colorToInt(next.substring(0, 3));\n  const hex = colorToInt(next.substring(3)).toString(16);\n\n  // NOTE: padStart could be used here but it breaks Node 6 compat\n  // https://github.com/ricokahler/color2k/issues/351\n  let prefix = '';\n  for (let i = 0; i < 6 - hex.length; i++) {\n    prefix += '0';\n  }\n  acc[key] = `${prefix}${hex}`;\n  return acc;\n}, {});\n\n/**\n * Checks if a string is a CSS named color and returns its equivalent hex value, otherwise returns the original color.\n */\nfunction nameToHex(color) {\n  const normalizedColorName = color.toLowerCase().trim();\n  const result = compressedColorMap[hash(normalizedColorName)];\n  if (!result) throw new ColorError$1(color);\n  return `#${result}`;\n}\nconst r = (str, amount) => Array.from(Array(amount)).map(() => str).join('');\nconst reducedHexRegex = new RegExp(`^#${r('([a-f0-9])', 3)}([a-f0-9])?$`, 'i');\nconst hexRegex = new RegExp(`^#${r('([a-f0-9]{2})', 3)}([a-f0-9]{2})?$`, 'i');\nconst rgbaRegex = new RegExp(`^rgba?\\\\(\\\\s*(\\\\d+)\\\\s*${r(',\\\\s*(\\\\d+)\\\\s*', 2)}(?:,\\\\s*([\\\\d.]+))?\\\\s*\\\\)$`, 'i');\nconst hslaRegex = /^hsla?\\(\\s*([\\d.]+)\\s*,\\s*([\\d.]+)%\\s*,\\s*([\\d.]+)%(?:\\s*,\\s*([\\d.]+))?\\s*\\)$/i;\nconst namedColorRegex = /^[a-z]+$/i;\nconst roundColor = color => {\n  return Math.round(color * 255);\n};\nconst hslToRgb = (hue, saturation, lightness) => {\n  let l = lightness / 100;\n  if (saturation === 0) {\n    // achromatic\n    return [l, l, l].map(roundColor);\n  }\n\n  // formulae from https://en.wikipedia.org/wiki/HSL_and_HSV\n  const huePrime = (hue % 360 + 360) % 360 / 60;\n  const chroma = (1 - Math.abs(2 * l - 1)) * (saturation / 100);\n  const secondComponent = chroma * (1 - Math.abs(huePrime % 2 - 1));\n  let red = 0;\n  let green = 0;\n  let blue = 0;\n  if (huePrime >= 0 && huePrime < 1) {\n    red = chroma;\n    green = secondComponent;\n  } else if (huePrime >= 1 && huePrime < 2) {\n    red = secondComponent;\n    green = chroma;\n  } else if (huePrime >= 2 && huePrime < 3) {\n    green = chroma;\n    blue = secondComponent;\n  } else if (huePrime >= 3 && huePrime < 4) {\n    green = secondComponent;\n    blue = chroma;\n  } else if (huePrime >= 4 && huePrime < 5) {\n    red = secondComponent;\n    blue = chroma;\n  } else if (huePrime >= 5 && huePrime < 6) {\n    red = chroma;\n    blue = secondComponent;\n  }\n  const lightnessModification = l - chroma / 2;\n  const finalRed = red + lightnessModification;\n  const finalGreen = green + lightnessModification;\n  const finalBlue = blue + lightnessModification;\n  return [finalRed, finalGreen, finalBlue].map(roundColor);\n};\n\n// taken from:\n\n/**\n * Parses a color in hue, saturation, lightness, and the alpha channel.\n *\n * Hue is a number between 0 and 360, saturation, lightness, and alpha are\n * decimal percentages between 0 and 1\n */\nfunction parseToHsla(color) {\n  const [red, green, blue, alpha] = parseToRgba(color).map((value, index) =>\n  // 3rd index is alpha channel which is already normalized\n  index === 3 ? value : value / 255);\n  const max = Math.max(red, green, blue);\n  const min = Math.min(red, green, blue);\n  const lightness = (max + min) / 2;\n\n  // achromatic\n  if (max === min) return [0, 0, lightness, alpha];\n  const delta = max - min;\n  const saturation = lightness > 0.5 ? delta / (2 - max - min) : delta / (max + min);\n  const hue = 60 * (red === max ? (green - blue) / delta + (green < blue ? 6 : 0) : green === max ? (blue - red) / delta + 2 : (red - green) / delta + 4);\n  return [hue, saturation, lightness, alpha];\n}\n\n/**\n * Takes in hsla parts and constructs an hsla string\n *\n * @param hue The color circle (from 0 to 360) - 0 (or 360) is red, 120 is green, 240 is blue\n * @param saturation Percentage of saturation, given as a decimal between 0 and 1\n * @param lightness Percentage of lightness, given as a decimal between 0 and 1\n * @param alpha Percentage of opacity, given as a decimal between 0 and 1\n */\nfunction hsla(hue, saturation, lightness, alpha) {\n  return `hsla(${(hue % 360).toFixed()}, ${guard(0, 100, saturation * 100).toFixed()}%, ${guard(0, 100, lightness * 100).toFixed()}%, ${parseFloat(guard(0, 1, alpha).toFixed(3))})`;\n}\n\n/**\n * Adjusts the current hue of the color by the given degrees. Wraps around when\n * over 360.\n *\n * @param color input color\n * @param degrees degrees to adjust the input color, accepts degree integers\n * (0 - 360) and wraps around on overflow\n */\nfunction adjustHue(color, degrees) {\n  const [h, s, l, a] = parseToHsla(color);\n  return hsla(h + degrees, s, l, a);\n}\n\n/**\n * Darkens using lightness. This is equivalent to subtracting the lightness\n * from the L in HSL.\n *\n * @param amount The amount to darken, given as a decimal between 0 and 1\n */\nfunction darken(color, amount) {\n  const [hue, saturation, lightness, alpha] = parseToHsla(color);\n  return hsla(hue, saturation, lightness - amount, alpha);\n}\n\n/**\n * Desaturates the input color by the given amount via subtracting from the `s`\n * in `hsla`.\n *\n * @param amount The amount to desaturate, given as a decimal between 0 and 1\n */\nfunction desaturate(color, amount) {\n  const [h, s, l, a] = parseToHsla(color);\n  return hsla(h, s - amount, l, a);\n}\n\n// taken from:\n// https://github.com/styled-components/polished/blob/0764c982551b487469043acb56281b0358b3107b/src/color/getLuminance.js\n\n/**\n * Returns a number (float) representing the luminance of a color.\n */\nfunction getLuminance(color) {\n  if (color === 'transparent') return 0;\n  function f(x) {\n    const channel = x / 255;\n    return channel <= 0.03928 ? channel / 12.92 : Math.pow((channel + 0.055) / 1.055, 2.4);\n  }\n  const [r, g, b] = parseToRgba(color);\n  return 0.2126 * f(r) + 0.7152 * f(g) + 0.0722 * f(b);\n}\n\n// taken from:\n\n/**\n * Returns the contrast ratio between two colors based on\n * [W3's recommended equation for calculating contrast](http://www.w3.org/TR/WCAG20/#contrast-ratiodef).\n */\nfunction getContrast(color1, color2) {\n  const luminance1 = getLuminance(color1);\n  const luminance2 = getLuminance(color2);\n  return luminance1 > luminance2 ? (luminance1 + 0.05) / (luminance2 + 0.05) : (luminance2 + 0.05) / (luminance1 + 0.05);\n}\n\n/**\n * Takes in rgba parts and returns an rgba string\n *\n * @param red The amount of red in the red channel, given in a number between 0 and 255 inclusive\n * @param green The amount of green in the red channel, given in a number between 0 and 255 inclusive\n * @param blue The amount of blue in the red channel, given in a number between 0 and 255 inclusive\n * @param alpha Percentage of opacity, given as a decimal between 0 and 1\n */\nfunction rgba(red, green, blue, alpha) {\n  return `rgba(${guard(0, 255, red).toFixed()}, ${guard(0, 255, green).toFixed()}, ${guard(0, 255, blue).toFixed()}, ${parseFloat(guard(0, 1, alpha).toFixed(3))})`;\n}\n\n/**\n * Mixes two colors together. Taken from sass's implementation.\n */\nfunction mix(color1, color2, weight) {\n  const normalize = (n, index) =>\n  // 3rd index is alpha channel which is already normalized\n  index === 3 ? n : n / 255;\n  const [r1, g1, b1, a1] = parseToRgba(color1).map(normalize);\n  const [r2, g2, b2, a2] = parseToRgba(color2).map(normalize);\n\n  // The formula is copied from the original Sass implementation:\n  // http://sass-lang.com/documentation/Sass/Script/Functions.html#mix-instance_method\n  const alphaDelta = a2 - a1;\n  const normalizedWeight = weight * 2 - 1;\n  const combinedWeight = normalizedWeight * alphaDelta === -1 ? normalizedWeight : normalizedWeight + alphaDelta / (1 + normalizedWeight * alphaDelta);\n  const weight2 = (combinedWeight + 1) / 2;\n  const weight1 = 1 - weight2;\n  const r = (r1 * weight1 + r2 * weight2) * 255;\n  const g = (g1 * weight1 + g2 * weight2) * 255;\n  const b = (b1 * weight1 + b2 * weight2) * 255;\n  const a = a2 * weight + a1 * (1 - weight);\n  return rgba(r, g, b, a);\n}\n\n/**\n * Given a series colors, this function will return a `scale(x)` function that\n * accepts a percentage as a decimal between 0 and 1 and returns the color at\n * that percentage in the scale.\n *\n * ```js\n * const scale = getScale('red', 'yellow', 'green');\n * console.log(scale(0)); // rgba(255, 0, 0, 1)\n * console.log(scale(0.5)); // rgba(255, 255, 0, 1)\n * console.log(scale(1)); // rgba(0, 128, 0, 1)\n * ```\n *\n * If you'd like to limit the domain and range like chroma-js, we recommend\n * wrapping scale again.\n *\n * ```js\n * const _scale = getScale('red', 'yellow', 'green');\n * const scale = x => _scale(x / 100);\n *\n * console.log(scale(0)); // rgba(255, 0, 0, 1)\n * console.log(scale(50)); // rgba(255, 255, 0, 1)\n * console.log(scale(100)); // rgba(0, 128, 0, 1)\n * ```\n */\nfunction getScale(...colors) {\n  return n => {\n    const lastIndex = colors.length - 1;\n    const lowIndex = guard(0, lastIndex, Math.floor(n * lastIndex));\n    const highIndex = guard(0, lastIndex, Math.ceil(n * lastIndex));\n    const color1 = colors[lowIndex];\n    const color2 = colors[highIndex];\n    const unit = 1 / lastIndex;\n    const weight = (n - unit * lowIndex) / unit;\n    return mix(color1, color2, weight);\n  };\n}\nconst guidelines = {\n  decorative: 1.5,\n  readable: 3,\n  aa: 4.5,\n  aaa: 7\n};\n\n/**\n * Returns whether or not a color has bad contrast against a background\n * according to a given standard.\n */\nfunction hasBadContrast(color, standard = 'aa', background = '#fff') {\n  return getContrast(color, background) < guidelines[standard];\n}\n\n/**\n * Lightens a color by a given amount. This is equivalent to\n * `darken(color, -amount)`\n *\n * @param amount The amount to darken, given as a decimal between 0 and 1\n */\nfunction lighten(color, amount) {\n  return darken(color, -amount);\n}\n\n/**\n * Takes in a color and makes it more transparent by convert to `rgba` and\n * decreasing the amount in the alpha channel.\n *\n * @param amount The amount to increase the transparency by, given as a decimal between 0 and 1\n */\nfunction transparentize(color, amount) {\n  const [r, g, b, a] = parseToRgba(color);\n  return rgba(r, g, b, a - amount);\n}\n\n/**\n * Takes a color and un-transparentizes it. Equivalent to\n * `transparentize(color, -amount)`\n *\n * @param amount The amount to increase the opacity by, given as a decimal between 0 and 1\n */\nfunction opacify(color, amount) {\n  return transparentize(color, -amount);\n}\n\n/**\n * An alternative function to `readableColor`. Returns whether or not the \n * readable color (i.e. the color to be place on top the input color) should be\n * black.\n */\nfunction readableColorIsBlack(color) {\n  return getLuminance(color) > 0.179;\n}\n\n/**\n * Returns black or white for best contrast depending on the luminosity of the\n * given color.\n */\nfunction readableColor(color) {\n  return readableColorIsBlack(color) ? '#000' : '#fff';\n}\n\n/**\n * Saturates a color by converting it to `hsl` and increasing the saturation\n * amount. Equivalent to `desaturate(color, -amount)`\n * \n * @param color Input color\n * @param amount The amount to darken, given as a decimal between 0 and 1\n */\nfunction saturate(color, amount) {\n  return desaturate(color, -amount);\n}\n\n/**\n * Takes in any color and returns it as a hex code.\n */\nfunction toHex(color) {\n  const [r, g, b, a] = parseToRgba(color);\n  let hex = x => {\n    const h = guard(0, 255, x).toString(16);\n    // NOTE: padStart could be used here but it breaks Node 6 compat\n    // https://github.com/ricokahler/color2k/issues/351\n    return h.length === 1 ? `0${h}` : h;\n  };\n  return `#${hex(r)}${hex(g)}${hex(b)}${a < 1 ? hex(Math.round(a * 255)) : ''}`;\n}\n\n/**\n * Takes in any color and returns it as an rgba string.\n */\nfunction toRgba(color) {\n  return rgba(...parseToRgba(color));\n}\n\n/**\n * Takes in any color and returns it as an hsla string.\n */\nfunction toHsla(color) {\n  return hsla(...parseToHsla(color));\n}\nexport { ColorError$1 as ColorError, adjustHue, darken, desaturate, getContrast, getLuminance, getScale, guard, hasBadContrast, hsla, lighten, mix, opacify, parseToHsla, parseToRgba, readableColor, readableColorIsBlack, rgba, saturate, toHex, toHsla, toRgba, transparentize };", "map": {"version": 3, "names": ["guard", "low", "high", "value", "Math", "min", "max", "ColorError", "Error", "constructor", "color", "ColorError$1", "parseToRgba", "trim", "toLowerCase", "normalizedColor", "namedColorRegex", "test", "nameToHex", "reducedHexMatch", "reducedHexRegex", "exec", "arr", "Array", "from", "slice", "map", "x", "parseInt", "r", "hexMatch", "hexRegex", "rgbaMatch", "rgbaRegex", "parseFloat", "hslaMatch", "hslaRegex", "h", "s", "l", "a", "hslToRgb", "Number", "isNaN", "hash", "str", "i", "length", "charCodeAt", "colorToInt", "replace", "compressedColorMap", "split", "reduce", "acc", "next", "key", "substring", "hex", "toString", "prefix", "normalizedColorName", "result", "amount", "join", "RegExp", "roundColor", "round", "hue", "saturation", "lightness", "huePrime", "chroma", "abs", "secondComponent", "red", "green", "blue", "lightnessModification", "finalRed", "finalGreen", "finalBlue", "parseToHsla", "alpha", "index", "delta", "hsla", "toFixed", "adjustHue", "degrees", "darken", "desaturate", "getLuminance", "f", "channel", "pow", "g", "b", "getContrast", "color1", "color2", "luminance1", "luminance2", "rgba", "mix", "weight", "normalize", "n", "r1", "g1", "b1", "a1", "r2", "g2", "b2", "a2", "alphaDelta", "normalizedWeight", "combinedWeight", "weight2", "weight1", "getScale", "colors", "lastIndex", "lowIndex", "floor", "highIndex", "ceil", "unit", "guidelines", "decorative", "readable", "aa", "aaa", "hasBadContrast", "standard", "background", "lighten", "transparentize", "opacify", "readableColorIsBlack", "readableColor", "saturate", "toHex", "toRgba", "toHsla"], "sources": ["C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\color2k\\src\\guard.ts", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\color2k\\src\\ColorError.ts", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\color2k\\src\\parseToRgba.ts", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\color2k\\src\\parseToHsla.ts", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\color2k\\src\\hsla.ts", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\color2k\\src\\adjustHue.ts", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\color2k\\src\\darken.ts", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\color2k\\src\\desaturate.ts", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\color2k\\src\\getLuminance.ts", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\color2k\\src\\getContrast.ts", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\color2k\\src\\rgba.ts", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\color2k\\src\\mix.ts", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\color2k\\src\\getScale.ts", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\color2k\\src\\hasBadContrast.ts", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\color2k\\src\\lighten.ts", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\color2k\\src\\transparentize.ts", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\color2k\\src\\opacify.ts", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\color2k\\src\\readableColorIsBlack.ts", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\color2k\\src\\readableColor.ts", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\color2k\\src\\saturate.ts", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\color2k\\src\\toHex.ts", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\color2k\\src\\toRgba.ts", "C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\color2k\\src\\toHsla.ts"], "sourcesContent": ["/**\n * A simple guard function:\n *\n * ```js\n * Math.min(Math.max(low, value), high)\n * ```\n */\nfunction guard(low: number, high: number, value: number): number {\n  return Math.min(Math.max(low, value), high);\n}\n\nexport default guard;\n", "class ColorError extends Error {\n  constructor(color: string) {\n    super(`Failed to parse color: \"${color}\"`);\n  }\n}\n\nexport default ColorError;\n", "import guard from './guard';\nimport ColorError from './ColorError';\n\n/**\n * Parses a color into red, gree, blue, alpha parts\n *\n * @param color the input color. Can be a RGB, RBGA, HSL, HSLA, or named color\n */\nfunction parseToRgba(color: string): [number, number, number, number] {\n  if (typeof color !== 'string') throw new ColorError(color);\n  if (color.trim().toLowerCase() === 'transparent') return [0, 0, 0, 0];\n\n  let normalizedColor = color.trim();\n  normalizedColor = namedColorRegex.test(color) ? nameToHex(color) : color;\n\n  const reducedHexMatch = reducedHexRegex.exec(normalizedColor);\n  if (reducedHexMatch) {\n    const arr = Array.from(reducedHexMatch).slice(1);\n    return [\n      ...arr.slice(0, 3).map((x) => parseInt(r(x, 2), 16)),\n      parseInt(r(arr[3] || 'f', 2), 16) / 255,\n    ] as [number, number, number, number];\n  }\n\n  const hexMatch = hexRegex.exec(normalizedColor);\n  if (hexMatch) {\n    const arr = Array.from(hexMatch).slice(1);\n    return [\n      ...arr.slice(0, 3).map((x) => parseInt(x, 16)),\n      parseInt(arr[3] || 'ff', 16) / 255,\n    ] as [number, number, number, number];\n  }\n\n  const rgbaMatch = rgbaRegex.exec(normalizedColor);\n  if (rgbaMatch) {\n    const arr = Array.from(rgbaMatch).slice(1);\n    return [\n      ...arr.slice(0, 3).map((x) => parseInt(x, 10)),\n      parseFloat(arr[3] || '1'),\n    ] as [number, number, number, number];\n  }\n\n  const hslaMatch = hslaRegex.exec(normalizedColor);\n  if (hslaMatch) {\n    const [h, s, l, a] = Array.from(hslaMatch).slice(1).map(parseFloat);\n    if (guard(0, 100, s) !== s) throw new ColorError(color);\n    if (guard(0, 100, l) !== l) throw new ColorError(color);\n    return [...hslToRgb(h, s, l), Number.isNaN(a) ? 1 : a] as [\n      number,\n      number,\n      number,\n      number\n    ];\n  }\n\n  throw new ColorError(color);\n}\n\nfunction hash(str: string) {\n  let hash = 5381;\n  let i = str.length;\n\n  while (i) {\n    hash = (hash * 33) ^ str.charCodeAt(--i);\n  }\n\n  /* JavaScript does bitwise operations (like XOR, above) on 32-bit signed\n   * integers. Since we want the results to be always positive, convert the\n   * signed int to an unsigned by doing an unsigned bitshift. */\n  return (hash >>> 0) % 2341;\n}\n\nconst colorToInt = (x: string) => parseInt(x.replace(/_/g, ''), 36);\n\nconst compressedColorMap =\n  '1q29ehhb 1n09sgk7 1kl1ekf_ _yl4zsno 16z9eiv3 1p29lhp8 _bd9zg04 17u0____ _iw9zhe5 _to73___ _r45e31e _7l6g016 _jh8ouiv _zn3qba8 1jy4zshs 11u87k0u 1ro9yvyo 1aj3xael 1gz9zjz0 _3w8l4xo 1bf1ekf_ _ke3v___ _4rrkb__ 13j776yz _646mbhl _nrjr4__ _le6mbhl 1n37ehkb _m75f91n _qj3bzfz 1939yygw 11i5z6x8 _1k5f8xs 1509441m 15t5lwgf _ae2th1n _tg1ugcv 1lp1ugcv 16e14up_ _h55rw7n _ny9yavn _7a11xb_ 1ih442g9 _pv442g9 1mv16xof 14e6y7tu 1oo9zkds 17d1cisi _4v9y70f _y98m8kc 1019pq0v 12o9zda8 _348j4f4 1et50i2o _8epa8__ _ts6senj 1o350i2o 1mi9eiuo 1259yrp0 1ln80gnw _632xcoy 1cn9zldc _f29edu4 1n490c8q _9f9ziet 1b94vk74 _m49zkct 1kz6s73a 1eu9dtog _q58s1rz 1dy9sjiq __u89jo3 _aj5nkwg _ld89jo3 13h9z6wx _qa9z2ii _l119xgq _bs5arju 1hj4nwk9 1qt4nwk9 1ge6wau6 14j9zlcw 11p1edc_ _ms1zcxe _439shk6 _jt9y70f _754zsow 1la40eju _oq5p___ _x279qkz 1fa5r3rv _yd2d9ip _424tcku _8y1di2_ _zi2uabw _yy7rn9h 12yz980_ __39ljp6 1b59zg0x _n39zfzp 1fy9zest _b33k___ _hp9wq92 1il50hz4 _io472ub _lj9z3eo 19z9ykg0 _8t8iu3a 12b9bl4a 1ak5yw0o _896v4ku _tb8k8lv _s59zi6t _c09ze0p 1lg80oqn 1id9z8wb _238nba5 1kq6wgdi _154zssg _tn3zk49 _da9y6tc 1sg7cv4f _r12jvtt 1gq5fmkz 1cs9rvci _lp9jn1c _xw1tdnb 13f9zje6 16f6973h _vo7ir40 _bt5arjf _rc45e4t _hr4e100 10v4e100 _hc9zke2 _w91egv_ _sj2r1kk 13c87yx8 _vqpds__ _ni8ggk8 _tj9yqfb 1ia2j4r4 _7x9b10u 1fc9ld4j 1eq9zldr _5j9lhpx _ez9zl6o _md61fzm'\n    .split(' ')\n    .reduce((acc, next) => {\n      const key = colorToInt(next.substring(0, 3));\n      const hex = colorToInt(next.substring(3)).toString(16);\n\n      // NOTE: padStart could be used here but it breaks Node 6 compat\n      // https://github.com/ricokahler/color2k/issues/351\n      let prefix = '';\n      for (let i = 0; i < 6 - hex.length; i++) {\n        prefix += '0';\n      }\n\n      acc[key] = `${prefix}${hex}`;\n      return acc;\n    }, {} as { [key: string]: string });\n\n/**\n * Checks if a string is a CSS named color and returns its equivalent hex value, otherwise returns the original color.\n */\nfunction nameToHex(color: string): string {\n  const normalizedColorName = color.toLowerCase().trim();\n  const result = compressedColorMap[hash(normalizedColorName)];\n  if (!result) throw new ColorError(color);\n  return `#${result}`;\n}\n\nconst r = (str: string, amount: number) =>\n  Array.from(Array(amount))\n    .map(() => str)\n    .join('');\n\nconst reducedHexRegex = new RegExp(`^#${r('([a-f0-9])', 3)}([a-f0-9])?$`, 'i');\nconst hexRegex = new RegExp(`^#${r('([a-f0-9]{2})', 3)}([a-f0-9]{2})?$`, 'i');\nconst rgbaRegex = new RegExp(\n  `^rgba?\\\\(\\\\s*(\\\\d+)\\\\s*${r(\n    ',\\\\s*(\\\\d+)\\\\s*',\n    2\n  )}(?:,\\\\s*([\\\\d.]+))?\\\\s*\\\\)$`,\n  'i'\n);\nconst hslaRegex =\n  /^hsla?\\(\\s*([\\d.]+)\\s*,\\s*([\\d.]+)%\\s*,\\s*([\\d.]+)%(?:\\s*,\\s*([\\d.]+))?\\s*\\)$/i;\nconst namedColorRegex = /^[a-z]+$/i;\n\nconst roundColor = (color: number): number => {\n  return Math.round(color * 255);\n};\n\nconst hslToRgb = (\n  hue: number,\n  saturation: number,\n  lightness: number\n): [number, number, number] => {\n  let l = lightness / 100;\n  if (saturation === 0) {\n    // achromatic\n    return [l, l, l].map(roundColor) as [number, number, number];\n  }\n\n  // formulae from https://en.wikipedia.org/wiki/HSL_and_HSV\n  const huePrime = (((hue % 360) + 360) % 360) / 60;\n  const chroma = (1 - Math.abs(2 * l - 1)) * (saturation / 100);\n  const secondComponent = chroma * (1 - Math.abs((huePrime % 2) - 1));\n\n  let red = 0;\n  let green = 0;\n  let blue = 0;\n\n  if (huePrime >= 0 && huePrime < 1) {\n    red = chroma;\n    green = secondComponent;\n  } else if (huePrime >= 1 && huePrime < 2) {\n    red = secondComponent;\n    green = chroma;\n  } else if (huePrime >= 2 && huePrime < 3) {\n    green = chroma;\n    blue = secondComponent;\n  } else if (huePrime >= 3 && huePrime < 4) {\n    green = secondComponent;\n    blue = chroma;\n  } else if (huePrime >= 4 && huePrime < 5) {\n    red = secondComponent;\n    blue = chroma;\n  } else if (huePrime >= 5 && huePrime < 6) {\n    red = chroma;\n    blue = secondComponent;\n  }\n\n  const lightnessModification = l - chroma / 2;\n  const finalRed = red + lightnessModification;\n  const finalGreen = green + lightnessModification;\n  const finalBlue = blue + lightnessModification;\n\n  return [finalRed, finalGreen, finalBlue].map(roundColor) as [\n    number,\n    number,\n    number\n  ];\n};\n\nexport default parseToRgba;\n", "// taken from:\n// https://github.com/styled-components/polished/blob/a23a6a2bb26802b3d922d9c3b67bac3f3a54a310/src/internalHelpers/_rgbToHsl.js\nimport parseToRgba from './parseToRgba';\n\n/**\n * Parses a color in hue, saturation, lightness, and the alpha channel.\n *\n * Hue is a number between 0 and 360, saturation, lightness, and alpha are\n * decimal percentages between 0 and 1\n */\nfunction parseToHsla(color: string): [number, number, number, number] {\n  const [red, green, blue, alpha] = parseToRgba(color).map((value, index) =>\n    // 3rd index is alpha channel which is already normalized\n    index === 3 ? value : value / 255\n  );\n\n  const max = Math.max(red, green, blue);\n  const min = Math.min(red, green, blue);\n  const lightness = (max + min) / 2;\n\n  // achromatic\n  if (max === min) return [0, 0, lightness, alpha];\n\n  const delta = max - min;\n  const saturation =\n    lightness > 0.5 ? delta / (2 - max - min) : delta / (max + min);\n\n  const hue =\n    60 *\n    (red === max\n      ? (green - blue) / delta + (green < blue ? 6 : 0)\n      : green === max\n      ? (blue - red) / delta + 2\n      : (red - green) / delta + 4);\n\n  return [hue, saturation, lightness, alpha];\n}\n\nexport default parseToHsla;\n", "import guard from './guard';\n\n/**\n * Takes in hsla parts and constructs an hsla string\n *\n * @param hue The color circle (from 0 to 360) - 0 (or 360) is red, 120 is green, 240 is blue\n * @param saturation Percentage of saturation, given as a decimal between 0 and 1\n * @param lightness Percentage of lightness, given as a decimal between 0 and 1\n * @param alpha Percentage of opacity, given as a decimal between 0 and 1\n */\nfunction hsla(\n  hue: number,\n  saturation: number,\n  lightness: number,\n  alpha: number\n): string {\n  return `hsla(${(hue % 360).toFixed()}, ${guard(\n    0,\n    100,\n    saturation * 100\n  ).toFixed()}%, ${guard(0, 100, lightness * 100).toFixed()}%, ${parseFloat(\n    guard(0, 1, alpha).toFixed(3)\n  )})`;\n}\n\nexport default hsla;\n", "import parseToHsla from './parseToHsla';\nimport hsla from './hsla';\n\n/**\n * Adjusts the current hue of the color by the given degrees. Wraps around when\n * over 360.\n *\n * @param color input color\n * @param degrees degrees to adjust the input color, accepts degree integers\n * (0 - 360) and wraps around on overflow\n */\nfunction adjustHue(color: string, degrees: number): string {\n  const [h, s, l, a] = parseToHsla(color);\n  return hsla(h + degrees, s, l, a);\n}\n\nexport default adjustHue;\n", "import parseToHsla from './parseToHsla';\nimport hsla from './hsla';\n\n/**\n * Darkens using lightness. This is equivalent to subtracting the lightness\n * from the L in HSL.\n *\n * @param amount The amount to darken, given as a decimal between 0 and 1\n */\nfunction darken(color: string, amount: number): string {\n  const [hue, saturation, lightness, alpha] = parseToHsla(color);\n  return hsla(hue, saturation, lightness - amount, alpha);\n}\n\nexport default darken;\n", "import parseToHsla from './parseToHsla';\nimport hsla from './hsla';\n\n/**\n * Desaturates the input color by the given amount via subtracting from the `s`\n * in `hsla`.\n *\n * @param amount The amount to desaturate, given as a decimal between 0 and 1\n */\nfunction desaturate(color: string, amount: number): string {\n  const [h, s, l, a] = parseToHsla(color);\n  return hsla(h, s - amount, l, a);\n}\n\nexport default desaturate;\n", "import parseToRgba from './parseToRgba';\n// taken from:\n// https://github.com/styled-components/polished/blob/0764c982551b487469043acb56281b0358b3107b/src/color/getLuminance.js\n\n/**\n * Returns a number (float) representing the luminance of a color.\n */\nfunction getLuminance(color: string): number {\n  if (color === 'transparent') return 0;\n\n  function f(x: number) {\n    const channel = x / 255;\n    return channel <= 0.03928\n      ? channel / 12.92\n      : Math.pow(((channel + 0.055) / 1.055), 2.4);\n  }\n\n  const [r, g, b] = parseToRgba(color);\n  return 0.2126 * f(r) + 0.7152 * f(g) + 0.0722 * f(b);\n}\n\nexport default getLuminance;\n", "// taken from:\n// https://github.com/styled-components/polished/blob/0764c982551b487469043acb56281b0358b3107b/src/color/getContrast.js\nimport getLuminance from './getLuminance';\n\n/**\n * Returns the contrast ratio between two colors based on\n * [W3's recommended equation for calculating contrast](http://www.w3.org/TR/WCAG20/#contrast-ratiodef).\n */\nfunction getContrast(color1: string, color2: string): number {\n  const luminance1 = getLuminance(color1);\n  const luminance2 = getLuminance(color2);\n\n  return luminance1 > luminance2\n    ? (luminance1 + 0.05) / (luminance2 + 0.05)\n    : (luminance2 + 0.05) / (luminance1 + 0.05);\n}\n\nexport default getContrast;\n", "import guard from './guard';\n\n/**\n * Takes in rgba parts and returns an rgba string\n *\n * @param red The amount of red in the red channel, given in a number between 0 and 255 inclusive\n * @param green The amount of green in the red channel, given in a number between 0 and 255 inclusive\n * @param blue The amount of blue in the red channel, given in a number between 0 and 255 inclusive\n * @param alpha Percentage of opacity, given as a decimal between 0 and 1\n */\nfunction rgba(red: number, green: number, blue: number, alpha: number): string {\n  return `rgba(${guard(0, 255, red).toFixed()}, ${guard(\n    0,\n    255,\n    green\n  ).toFixed()}, ${guard(0, 255, blue).toFixed()}, ${parseFloat(\n    guard(0, 1, alpha).toFixed(3)\n  )})`;\n}\n\nexport default rgba;\n", "import parseToRgba from './parseToRgba';\nimport rgba from './rgba';\n\n/**\n * Mixes two colors together. Taken from sass's implementation.\n */\nfunction mix(color1: string, color2: string, weight: number): string {\n  const normalize = (n: number, index: number) =>\n    // 3rd index is alpha channel which is already normalized\n    index === 3 ? n : n / 255;\n\n  const [r1, g1, b1, a1] = parseToRgba(color1).map(normalize);\n  const [r2, g2, b2, a2] = parseToRgba(color2).map(normalize);\n\n  // The formula is copied from the original Sass implementation:\n  // http://sass-lang.com/documentation/Sass/Script/Functions.html#mix-instance_method\n  const alphaDelta = a2 - a1;\n  const normalizedWeight = weight * 2 - 1;\n  const combinedWeight =\n    normalizedWeight * alphaDelta === -1\n      ? normalizedWeight\n      : normalizedWeight + alphaDelta / (1 + normalizedWeight * alphaDelta);\n  const weight2 = (combinedWeight + 1) / 2;\n  const weight1 = 1 - weight2;\n\n  const r = (r1 * weight1 + r2 * weight2) * 255;\n  const g = (g1 * weight1 + g2 * weight2) * 255;\n  const b = (b1 * weight1 + b2 * weight2) * 255;\n  const a = a2 * weight + a1 * (1 - weight);\n\n  return rgba(r, g, b, a);\n}\n\nexport default mix;\n", "import mix from './mix';\nimport guard from './guard';\n\n/**\n * Given a series colors, this function will return a `scale(x)` function that\n * accepts a percentage as a decimal between 0 and 1 and returns the color at\n * that percentage in the scale.\n *\n * ```js\n * const scale = getScale('red', 'yellow', 'green');\n * console.log(scale(0)); // rgba(255, 0, 0, 1)\n * console.log(scale(0.5)); // rgba(255, 255, 0, 1)\n * console.log(scale(1)); // rgba(0, 128, 0, 1)\n * ```\n *\n * If you'd like to limit the domain and range like chroma-js, we recommend\n * wrapping scale again.\n *\n * ```js\n * const _scale = getScale('red', 'yellow', 'green');\n * const scale = x => _scale(x / 100);\n *\n * console.log(scale(0)); // rgba(255, 0, 0, 1)\n * console.log(scale(50)); // rgba(255, 255, 0, 1)\n * console.log(scale(100)); // rgba(0, 128, 0, 1)\n * ```\n */\nfunction getScale(...colors: string[]): (n: number) => string {\n  return (n) => {\n    const lastIndex = colors.length - 1;\n    const lowIndex = guard(0, lastIndex, Math.floor(n * lastIndex));\n    const highIndex = guard(0, lastIndex, Math.ceil(n * lastIndex));\n\n    const color1 = colors[lowIndex];\n    const color2 = colors[highIndex];\n\n    const unit = 1 / lastIndex;\n    const weight = (n - unit * lowIndex) / unit;\n\n    return mix(color1, color2, weight);\n  };\n}\n\nexport default getScale;\n", "import getContrast from './getContrast';\n\nconst guidelines = {\n  decorative: 1.5,\n  readable: 3,\n  aa: 4.5,\n  aaa: 7,\n};\n\n/**\n * Returns whether or not a color has bad contrast against a background\n * according to a given standard.\n */\nfunction hasBadContrast(\n  color: string,\n  standard: 'decorative' | 'readable' | 'aa' | 'aaa' = 'aa',\n  background: string = '#fff'\n): boolean {\n  return getContrast(color, background) < guidelines[standard];\n}\n\nexport default hasBadContrast;\n", "import darken from './darken';\n/**\n * Lightens a color by a given amount. This is equivalent to\n * `darken(color, -amount)`\n *\n * @param amount The amount to darken, given as a decimal between 0 and 1\n */\nfunction lighten(color: string, amount: number): string {\n  return darken(color, -amount);\n}\n\nexport default lighten;\n", "import parseToRgba from './parseToRgba';\nimport rgba from './rgba';\n\n/**\n * Takes in a color and makes it more transparent by convert to `rgba` and\n * decreasing the amount in the alpha channel.\n *\n * @param amount The amount to increase the transparency by, given as a decimal between 0 and 1\n */\nfunction transparentize(color: string, amount: number): string {\n  const [r, g, b, a] = parseToRgba(color);\n  return rgba(r, g, b, a - amount);\n}\n\nexport default transparentize;\n", "import transparentize from './transparentize';\n\n/**\n * Takes a color and un-transparentizes it. Equivalent to\n * `transparentize(color, -amount)`\n *\n * @param amount The amount to increase the opacity by, given as a decimal between 0 and 1\n */\nfunction opacify(color: string, amount: number): string {\n  return transparentize(color, -amount);\n}\n\nexport default opacify;\n", "import getLuminance from './getLuminance';\n\n/**\n * An alternative function to `readableColor`. Returns whether or not the \n * readable color (i.e. the color to be place on top the input color) should be\n * black.\n */\nfunction readableColorIsBlack(color: string): boolean {\n  return getLuminance(color) > 0.179;\n}\n\nexport default readableColorIsBlack;\n", "import readableColorIsBlack from './readableColorIsBlack';\n\n/**\n * Returns black or white for best contrast depending on the luminosity of the\n * given color.\n */\nfunction readableColor(color: string): string {\n  return readableColorIsBlack(color) ? '#000' : '#fff';\n}\n\nexport default readableColor;\n", "import desaturate from './desaturate';\n\n/**\n * Saturates a color by converting it to `hsl` and increasing the saturation\n * amount. Equivalent to `desaturate(color, -amount)`\n * \n * @param color Input color\n * @param amount The amount to darken, given as a decimal between 0 and 1\n */\nfunction saturate(color: string, amount: number): string {\n  return desaturate(color, -amount);\n}\n\nexport default saturate;\n", "import parseToRgba from './parseToRgba';\nimport guard from './guard';\n\n/**\n * Takes in any color and returns it as a hex code.\n */\nfunction toHex(color: string): string {\n  const [r, g, b, a] = parseToRgba(color);\n\n  let hex = (x: number) => {\n    const h = guard(0, 255, x).toString(16);\n    // NOTE: padStart could be used here but it breaks Node 6 compat\n    // https://github.com/ricokahler/color2k/issues/351\n    return h.length === 1 ? `0${h}` : h;\n  };\n\n  return `#${hex(r)}${hex(g)}${hex(b)}${a < 1 ? hex(Math.round(a * 255)) : ''}`;\n}\n\nexport default toHex;\n", "import parseToRgba from './parseToRgba';\nimport rgba from './rgba';\n\n/**\n * Takes in any color and returns it as an rgba string.\n */\nfunction toRgba(color: string): string {\n  return rgba(...parseToRgba(color));\n}\n\nexport default toRgba;\n", "import parseToHsla from './parseToHsla';\nimport hsla from './hsla';\n\n/**\n * Takes in any color and returns it as an hsla string.\n */\nfunction toHsla(color: string): string {\n  return hsla(...parseToHsla(color));\n}\n\nexport default toHsla;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,KAAKA,CAACC,GAAW,EAAEC,IAAY,EAAEC,KAAa,EAAU;EAC/D,OAAOC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACL,GAAG,EAAEE,KAAK,CAAC,EAAED,IAAI,CAAC;AAC7C;ACTA,MAAMK,UAAU,SAASC,KAAK,CAAC;EAC7BC,WAAWA,CAACC,KAAa,EAAE;IACzB,KAAK,CAAE,2BAA0BA,KAAM,GAAE,CAAC;EAC5C;AACF;AAEA,IAAAC,YAAA,GAAeJ,UAAU;;ACHzB;AACA;AACA;AACA;AACA;AACA,SAASK,WAAWA,CAACF,KAAa,EAAoC;EACpE,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE,MAAM,IAAIC,YAAU,CAACD,KAAK,CAAC;EAC1D,IAAIA,KAAK,CAACG,IAAI,EAAE,CAACC,WAAW,EAAE,KAAK,aAAa,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAErE,IAAIC,eAAe,GAAGL,KAAK,CAACG,IAAI,EAAE;EAClCE,eAAe,GAAGC,eAAe,CAACC,IAAI,CAACP,KAAK,CAAC,GAAGQ,SAAS,CAACR,KAAK,CAAC,GAAGA,KAAK;EAExE,MAAMS,eAAe,GAAGC,eAAe,CAACC,IAAI,CAACN,eAAe,CAAC;EAC7D,IAAII,eAAe,EAAE;IACnB,MAAMG,GAAG,GAAGC,KAAK,CAACC,IAAI,CAACL,eAAe,CAAC,CAACM,KAAK,CAAC,CAAC,CAAC;IAChD,OAAO,CACL,GAAGH,GAAG,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAEC,CAAC,IAAKC,QAAQ,CAACC,CAAC,CAACF,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EACpDC,QAAQ,CAACC,CAAC,CAACP,GAAG,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CACxC;EACH;EAEA,MAAMQ,QAAQ,GAAGC,QAAQ,CAACV,IAAI,CAACN,eAAe,CAAC;EAC/C,IAAIe,QAAQ,EAAE;IACZ,MAAMR,GAAG,GAAGC,KAAK,CAACC,IAAI,CAACM,QAAQ,CAAC,CAACL,KAAK,CAAC,CAAC,CAAC;IACzC,OAAO,CACL,GAAGH,GAAG,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAEC,CAAC,IAAKC,QAAQ,CAACD,CAAC,EAAE,EAAE,CAAC,CAAC,EAC9CC,QAAQ,CAACN,GAAG,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,CACnC;EACH;EAEA,MAAMU,SAAS,GAAGC,SAAS,CAACZ,IAAI,CAACN,eAAe,CAAC;EACjD,IAAIiB,SAAS,EAAE;IACb,MAAMV,GAAG,GAAGC,KAAK,CAACC,IAAI,CAACQ,SAAS,CAAC,CAACP,KAAK,CAAC,CAAC,CAAC;IAC1C,OAAO,CACL,GAAGH,GAAG,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAEC,CAAC,IAAKC,QAAQ,CAACD,CAAC,EAAE,EAAE,CAAC,CAAC,EAC9CO,UAAU,CAACZ,GAAG,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAC1B;EACH;EAEA,MAAMa,SAAS,GAAGC,SAAS,CAACf,IAAI,CAACN,eAAe,CAAC;EACjD,IAAIoB,SAAS,EAAE;IACb,MAAM,CAACE,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,GAAGjB,KAAK,CAACC,IAAI,CAACW,SAAS,CAAC,CAACV,KAAK,CAAC,CAAC,CAAC,CAACC,GAAG,CAACQ,UAAU,CAAC;IACnE,IAAIlC,KAAK,CAAC,CAAC,EAAE,GAAG,EAAEsC,CAAC,CAAC,KAAKA,CAAC,EAAE,MAAM,IAAI3B,YAAU,CAACD,KAAK,CAAC;IACvD,IAAIV,KAAK,CAAC,CAAC,EAAE,GAAG,EAAEuC,CAAC,CAAC,KAAKA,CAAC,EAAE,MAAM,IAAI5B,YAAU,CAACD,KAAK,CAAC;IACvD,OAAO,CAAC,GAAG+B,QAAQ,CAACJ,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,EAAEG,MAAM,CAACC,KAAK,CAACH,CAAC,CAAC,GAAG,CAAC,GAAGA,CAAC,CAAC;EAMxD;EAEA,MAAM,IAAI7B,YAAU,CAACD,KAAK,CAAC;AAC7B;AAEA,SAASkC,IAAIA,CAACC,GAAW,EAAE;EACzB,IAAID,IAAI,GAAG,IAAI;EACf,IAAIE,CAAC,GAAGD,GAAG,CAACE,MAAM;EAElB,OAAOD,CAAC,EAAE;IACRF,IAAI,GAAIA,IAAI,GAAG,EAAE,GAAIC,GAAG,CAACG,UAAU,CAAC,EAAEF,CAAC,CAAC;EAC1C;;EAEA;AACF;AACA;EACE,OAAO,CAACF,IAAI,KAAK,CAAC,IAAI,IAAI;AAC5B;AAEA,MAAMK,UAAU,GAAItB,CAAS,IAAKC,QAAQ,CAACD,CAAC,CAACuB,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;AAEnE,MAAMC,kBAAkB,GACtB,qzCAAqzC,CAClzCC,KAAK,CAAC,GAAG,CAAC,CACVC,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAK;EACrB,MAAMC,GAAG,GAAGP,UAAU,CAACM,IAAI,CAACE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAC5C,MAAMC,GAAG,GAAGT,UAAU,CAACM,IAAI,CAACE,SAAS,CAAC,CAAC,CAAC,CAAC,CAACE,QAAQ,CAAC,EAAE,CAAC;;EAEtD;EACA;EACA,IAAIC,MAAM,GAAG,EAAE;EACf,KAAK,IAAId,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,GAAGY,GAAG,CAACX,MAAM,EAAED,CAAC,EAAE,EAAE;IACvCc,MAAM,IAAI,GAAG;EACf;EAEAN,GAAG,CAACE,GAAG,CAAC,GAAI,GAAEI,MAAO,GAAEF,GAAK;EAC5B,OAAOJ,GAAG;AACZ,CAAC,EAAE,EAAE,CAA8B;;AAEvC;AACA;AACA;AACA,SAASpC,SAASA,CAACR,KAAa,EAAU;EACxC,MAAMmD,mBAAmB,GAAGnD,KAAK,CAACI,WAAW,EAAE,CAACD,IAAI,EAAE;EACtD,MAAMiD,MAAM,GAAGX,kBAAkB,CAACP,IAAI,CAACiB,mBAAmB,CAAC,CAAC;EAC5D,IAAI,CAACC,MAAM,EAAE,MAAM,IAAInD,YAAU,CAACD,KAAK,CAAC;EACxC,OAAQ,IAAGoD,MAAQ;AACrB;AAEA,MAAMjC,CAAC,GAAGA,CAACgB,GAAW,EAAEkB,MAAc,KACpCxC,KAAK,CAACC,IAAI,CAACD,KAAK,CAACwC,MAAM,CAAC,CAAC,CACtBrC,GAAG,CAAC,MAAMmB,GAAG,CAAC,CACdmB,IAAI,CAAC,EAAE,CAAC;AAEb,MAAM5C,eAAe,GAAG,IAAI6C,MAAM,CAAM,KAAApC,CAAC,CAAC,YAAY,EAAE,CAAC,CAAe,gBAAE,GAAG,CAAC;AAC9E,MAAME,QAAQ,GAAG,IAAIkC,MAAM,CAAM,KAAApC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAkB,mBAAE,GAAG,CAAC;AAC7E,MAAMI,SAAS,GAAG,IAAIgC,MAAM,CACA,0BAAApC,CAAC,CACzB,iBAAiB,EACjB,CAAC,CAC2B,+BAC9B,GAAG,CACJ;AACD,MAAMO,SAAS,GACb,gFAAgF;AAClF,MAAMpB,eAAe,GAAG,WAAW;AAEnC,MAAMkD,UAAU,GAAIxD,KAAa,IAAa;EAC5C,OAAON,IAAI,CAAC+D,KAAK,CAACzD,KAAK,GAAG,GAAG,CAAC;AAChC,CAAC;AAED,MAAM+B,QAAQ,GAAGA,CACf2B,GAAW,EACXC,UAAkB,EAClBC,SAAiB,KACY;EAC7B,IAAI/B,CAAC,GAAG+B,SAAS,GAAG,GAAG;EACvB,IAAID,UAAU,KAAK,CAAC,EAAE;IACpB;IACA,OAAO,CAAC9B,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,CAACb,GAAG,CAACwC,UAAU,CAAC;EAClC;;EAEA;EACA,MAAMK,QAAQ,GAAI,CAAEH,GAAG,GAAG,GAAG,GAAI,GAAG,IAAI,GAAG,GAAI,EAAE;EACjD,MAAMI,MAAM,GAAG,CAAC,CAAC,GAAGpE,IAAI,CAACqE,GAAG,CAAC,CAAC,GAAGlC,CAAC,GAAG,CAAC,CAAC,KAAK8B,UAAU,GAAG,GAAG,CAAC;EAC7D,MAAMK,eAAe,GAAGF,MAAM,IAAI,CAAC,GAAGpE,IAAI,CAACqE,GAAG,CAAEF,QAAQ,GAAG,CAAC,GAAI,CAAC,CAAC,CAAC;EAEnE,IAAII,GAAG,GAAG,CAAC;EACX,IAAIC,KAAK,GAAG,CAAC;EACb,IAAIC,IAAI,GAAG,CAAC;EAEZ,IAAIN,QAAQ,IAAI,CAAC,IAAIA,QAAQ,GAAG,CAAC,EAAE;IACjCI,GAAG,GAAGH,MAAM;IACZI,KAAK,GAAGF,eAAe;GACxB,MAAM,IAAIH,QAAQ,IAAI,CAAC,IAAIA,QAAQ,GAAG,CAAC,EAAE;IACxCI,GAAG,GAAGD,eAAe;IACrBE,KAAK,GAAGJ,MAAM;GACf,MAAM,IAAID,QAAQ,IAAI,CAAC,IAAIA,QAAQ,GAAG,CAAC,EAAE;IACxCK,KAAK,GAAGJ,MAAM;IACdK,IAAI,GAAGH,eAAe;GACvB,MAAM,IAAIH,QAAQ,IAAI,CAAC,IAAIA,QAAQ,GAAG,CAAC,EAAE;IACxCK,KAAK,GAAGF,eAAe;IACvBG,IAAI,GAAGL,MAAM;GACd,MAAM,IAAID,QAAQ,IAAI,CAAC,IAAIA,QAAQ,GAAG,CAAC,EAAE;IACxCI,GAAG,GAAGD,eAAe;IACrBG,IAAI,GAAGL,MAAM;GACd,MAAM,IAAID,QAAQ,IAAI,CAAC,IAAIA,QAAQ,GAAG,CAAC,EAAE;IACxCI,GAAG,GAAGH,MAAM;IACZK,IAAI,GAAGH,eAAe;EACxB;EAEA,MAAMI,qBAAqB,GAAGvC,CAAC,GAAGiC,MAAM,GAAG,CAAC;EAC5C,MAAMO,QAAQ,GAAGJ,GAAG,GAAGG,qBAAqB;EAC5C,MAAME,UAAU,GAAGJ,KAAK,GAAGE,qBAAqB;EAChD,MAAMG,SAAS,GAAGJ,IAAI,GAAGC,qBAAqB;EAE9C,OAAO,CAACC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,CAAC,CAACvD,GAAG,CAACwC,UAAU,CAAC;AAK1D,CAAC;;AC9KD;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA,SAASgB,WAAWA,CAACxE,KAAa,EAAoC;EACpE,MAAM,CAACiE,GAAG,EAAEC,KAAK,EAAEC,IAAI,EAAEM,KAAK,CAAC,GAAGvE,WAAW,CAACF,KAAK,CAAC,CAACgB,GAAG,CAAC,CAACvB,KAAK,EAAEiF,KAAK;EACpE;EACAA,KAAK,KAAK,CAAC,GAAGjF,KAAK,GAAGA,KAAK,GAAG,GAAG,CAClC;EAED,MAAMG,GAAG,GAAGF,IAAI,CAACE,GAAG,CAACqE,GAAG,EAAEC,KAAK,EAAEC,IAAI,CAAC;EACtC,MAAMxE,GAAG,GAAGD,IAAI,CAACC,GAAG,CAACsE,GAAG,EAAEC,KAAK,EAAEC,IAAI,CAAC;EACtC,MAAMP,SAAS,GAAG,CAAChE,GAAG,GAAGD,GAAG,IAAI,CAAC;;EAEjC;EACA,IAAIC,GAAG,KAAKD,GAAG,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,EAAEiE,SAAS,EAAEa,KAAK,CAAC;EAEhD,MAAME,KAAK,GAAG/E,GAAG,GAAGD,GAAG;EACvB,MAAMgE,UAAU,GACdC,SAAS,GAAG,GAAG,GAAGe,KAAK,IAAI,CAAC,GAAG/E,GAAG,GAAGD,GAAG,CAAC,GAAGgF,KAAK,IAAI/E,GAAG,GAAGD,GAAG,CAAC;EAEjE,MAAM+D,GAAG,GACP,EAAE,IACDO,GAAG,KAAKrE,GAAG,GACR,CAACsE,KAAK,GAAGC,IAAI,IAAIQ,KAAK,IAAIT,KAAK,GAAGC,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,GAC/CD,KAAK,KAAKtE,GAAG,GACb,CAACuE,IAAI,GAAGF,GAAG,IAAIU,KAAK,GAAG,CAAC,GACxB,CAACV,GAAG,GAAGC,KAAK,IAAIS,KAAK,GAAG,CAAC,CAAC;EAEhC,OAAO,CAACjB,GAAG,EAAEC,UAAU,EAAEC,SAAS,EAAEa,KAAK,CAAC;AAC5C;;AClCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,IAAIA,CACXlB,GAAW,EACXC,UAAkB,EAClBC,SAAiB,EACjBa,KAAa,EACL;EACR,OAAQ,QAAO,CAACf,GAAG,GAAG,GAAG,EAAEmB,OAAO,EAAO,KAAAvF,KAAK,CAC5C,CAAC,EACD,GAAG,EACHqE,UAAU,GAAG,GAAG,CACjB,CAACkB,OAAO,EAAQ,MAAAvF,KAAK,CAAC,CAAC,EAAE,GAAG,EAAEsE,SAAS,GAAG,GAAG,CAAC,CAACiB,OAAO,EAAQ,MAAArD,UAAU,CACvElC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAEmF,KAAK,CAAC,CAACI,OAAO,CAAC,CAAC,CAAC,CAC3B;AACN;;ACpBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAAC9E,KAAa,EAAE+E,OAAe,EAAU;EACzD,MAAM,CAACpD,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,GAAG0C,WAAW,CAACxE,KAAK,CAAC;EACvC,OAAO4E,IAAI,CAACjD,CAAC,GAAGoD,OAAO,EAAEnD,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;AACnC;;ACXA;AACA;AACA;AACA;AACA;AACA;AACA,SAASkD,MAAMA,CAAChF,KAAa,EAAEqD,MAAc,EAAU;EACrD,MAAM,CAACK,GAAG,EAAEC,UAAU,EAAEC,SAAS,EAAEa,KAAK,CAAC,GAAGD,WAAW,CAACxE,KAAK,CAAC;EAC9D,OAAO4E,IAAI,CAAClB,GAAG,EAAEC,UAAU,EAAEC,SAAS,GAAGP,MAAM,EAAEoB,KAAK,CAAC;AACzD;;ACTA;AACA;AACA;AACA;AACA;AACA;AACA,SAASQ,UAAUA,CAACjF,KAAa,EAAEqD,MAAc,EAAU;EACzD,MAAM,CAAC1B,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,GAAG0C,WAAW,CAACxE,KAAK,CAAC;EACvC,OAAO4E,IAAI,CAACjD,CAAC,EAAEC,CAAC,GAAGyB,MAAM,EAAExB,CAAC,EAAEC,CAAC,CAAC;AAClC;;ACXA;AACA;;AAEA;AACA;AACA;AACA,SAASoD,YAAYA,CAAClF,KAAa,EAAU;EAC3C,IAAIA,KAAK,KAAK,aAAa,EAAE,OAAO,CAAC;EAErC,SAASmF,CAACA,CAAClE,CAAS,EAAE;IACpB,MAAMmE,OAAO,GAAGnE,CAAC,GAAG,GAAG;IACvB,OAAOmE,OAAO,IAAI,OAAO,GACrBA,OAAO,GAAG,KAAK,GACf1F,IAAI,CAAC2F,GAAG,CAAE,CAACD,OAAO,GAAG,KAAK,IAAI,KAAK,EAAG,GAAG,CAAC;EAChD;EAEA,MAAM,CAACjE,CAAC,EAAEmE,CAAC,EAAEC,CAAC,CAAC,GAAGrF,WAAW,CAACF,KAAK,CAAC;EACpC,OAAO,MAAM,GAAGmF,CAAC,CAAChE,CAAC,CAAC,GAAG,MAAM,GAAGgE,CAAC,CAACG,CAAC,CAAC,GAAG,MAAM,GAAGH,CAAC,CAACI,CAAC,CAAC;AACtD;;ACnBA;;AAIA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACC,MAAc,EAAEC,MAAc,EAAU;EAC3D,MAAMC,UAAU,GAAGT,YAAY,CAACO,MAAM,CAAC;EACvC,MAAMG,UAAU,GAAGV,YAAY,CAACQ,MAAM,CAAC;EAEvC,OAAOC,UAAU,GAAGC,UAAU,GAC1B,CAACD,UAAU,GAAG,IAAI,KAAKC,UAAU,GAAG,IAAI,CAAC,GACzC,CAACA,UAAU,GAAG,IAAI,KAAKD,UAAU,GAAG,IAAI,CAAC;AAC/C;;ACbA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,IAAIA,CAAC5B,GAAW,EAAEC,KAAa,EAAEC,IAAY,EAAEM,KAAa,EAAU;EAC7E,OAAQ,QAAOnF,KAAK,CAAC,CAAC,EAAE,GAAG,EAAE2E,GAAG,CAAC,CAACY,OAAO,EAAG,KAAIvF,KAAK,CACnD,CAAC,EACD,GAAG,EACH4E,KAAK,CACN,CAACW,OAAO,EAAO,KAAAvF,KAAK,CAAC,CAAC,EAAE,GAAG,EAAE6E,IAAI,CAAC,CAACU,OAAO,EAAO,KAAArD,UAAU,CAC1DlC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAEmF,KAAK,CAAC,CAACI,OAAO,CAAC,CAAC,CAAC,CAC3B;AACN;;ACfA;AACA;AACA;AACA,SAASiB,GAAGA,CAACL,MAAc,EAAEC,MAAc,EAAEK,MAAc,EAAU;EACnE,MAAMC,SAAS,GAAGA,CAACC,CAAS,EAAEvB,KAAa;EACzC;EACAA,KAAK,KAAK,CAAC,GAAGuB,CAAC,GAAGA,CAAC,GAAG,GAAG;EAE3B,MAAM,CAACC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC,GAAGnG,WAAW,CAACuF,MAAM,CAAC,CAACzE,GAAG,CAACgF,SAAS,CAAC;EAC3D,MAAM,CAACM,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC,GAAGvG,WAAW,CAACwF,MAAM,CAAC,CAAC1E,GAAG,CAACgF,SAAS,CAAC;;EAE3D;EACA;EACA,MAAMU,UAAU,GAAGD,EAAE,GAAGJ,EAAE;EAC1B,MAAMM,gBAAgB,GAAGZ,MAAM,GAAG,CAAC,GAAG,CAAC;EACvC,MAAMa,cAAc,GAClBD,gBAAgB,GAAGD,UAAU,KAAK,CAAC,CAAC,GAChCC,gBAAgB,GAChBA,gBAAgB,GAAGD,UAAU,IAAI,CAAC,GAAGC,gBAAgB,GAAGD,UAAU,CAAC;EACzE,MAAMG,OAAO,GAAG,CAACD,cAAc,GAAG,CAAC,IAAI,CAAC;EACxC,MAAME,OAAO,GAAG,CAAC,GAAGD,OAAO;EAE3B,MAAM1F,CAAC,GAAG,CAAC+E,EAAE,GAAGY,OAAO,GAAGR,EAAE,GAAGO,OAAO,IAAI,GAAG;EAC7C,MAAMvB,CAAC,GAAG,CAACa,EAAE,GAAGW,OAAO,GAAGP,EAAE,GAAGM,OAAO,IAAI,GAAG;EAC7C,MAAMtB,CAAC,GAAG,CAACa,EAAE,GAAGU,OAAO,GAAGN,EAAE,GAAGK,OAAO,IAAI,GAAG;EAC7C,MAAM/E,CAAC,GAAG2E,EAAE,GAAGV,MAAM,GAAGM,EAAE,IAAI,CAAC,GAAGN,MAAM,CAAC;EAEzC,OAAOF,IAAI,CAAC1E,CAAC,EAAEmE,CAAC,EAAEC,CAAC,EAAEzD,CAAC,CAAC;AACzB;;AC5BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASiF,QAAQA,CAAC,GAAGC,MAAgB,EAAyB;EAC5D,OAAQf,CAAC,IAAK;IACZ,MAAMgB,SAAS,GAAGD,MAAM,CAAC3E,MAAM,GAAG,CAAC;IACnC,MAAM6E,QAAQ,GAAG5H,KAAK,CAAC,CAAC,EAAE2H,SAAS,EAAEvH,IAAI,CAACyH,KAAK,CAAClB,CAAC,GAAGgB,SAAS,CAAC,CAAC;IAC/D,MAAMG,SAAS,GAAG9H,KAAK,CAAC,CAAC,EAAE2H,SAAS,EAAEvH,IAAI,CAAC2H,IAAI,CAACpB,CAAC,GAAGgB,SAAS,CAAC,CAAC;IAE/D,MAAMxB,MAAM,GAAGuB,MAAM,CAACE,QAAQ,CAAC;IAC/B,MAAMxB,MAAM,GAAGsB,MAAM,CAACI,SAAS,CAAC;IAEhC,MAAME,IAAI,GAAG,CAAC,GAAGL,SAAS;IAC1B,MAAMlB,MAAM,GAAG,CAACE,CAAC,GAAGqB,IAAI,GAAGJ,QAAQ,IAAII,IAAI;IAE3C,OAAOxB,GAAG,CAACL,MAAM,EAAEC,MAAM,EAAEK,MAAM,CAAC;GACnC;AACH;ACvCA,MAAMwB,UAAU,GAAG;EACjBC,UAAU,EAAE,GAAG;EACfC,QAAQ,EAAE,CAAC;EACXC,EAAE,EAAE,GAAG;EACPC,GAAG,EAAE;AACP,CAAC;;AAED;AACA;AACA;AACA;AACA,SAASC,cAAcA,CACrB5H,KAAa,EACb6H,QAAkD,GAAG,IAAI,EACzDC,UAAkB,GAAG,MAAM,EAClB;EACT,OAAOtC,WAAW,CAACxF,KAAK,EAAE8H,UAAU,CAAC,GAAGP,UAAU,CAACM,QAAQ,CAAC;AAC9D;;AClBA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,OAAOA,CAAC/H,KAAa,EAAEqD,MAAc,EAAU;EACtD,OAAO2B,MAAM,CAAChF,KAAK,EAAE,CAACqD,MAAM,CAAC;AAC/B;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS2E,cAAcA,CAAChI,KAAa,EAAEqD,MAAc,EAAU;EAC7D,MAAM,CAAClC,CAAC,EAAEmE,CAAC,EAAEC,CAAC,EAAEzD,CAAC,CAAC,GAAG5B,WAAW,CAACF,KAAK,CAAC;EACvC,OAAO6F,IAAI,CAAC1E,CAAC,EAAEmE,CAAC,EAAEC,CAAC,EAAEzD,CAAC,GAAGuB,MAAM,CAAC;AAClC;;ACVA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS4E,OAAOA,CAACjI,KAAa,EAAEqD,MAAc,EAAU;EACtD,OAAO2E,cAAc,CAAChI,KAAK,EAAE,CAACqD,MAAM,CAAC;AACvC;;ACRA;AACA;AACA;AACA;AACA;AACA,SAAS6E,oBAAoBA,CAAClI,KAAa,EAAW;EACpD,OAAOkF,YAAY,CAAClF,KAAK,CAAC,GAAG,KAAK;AACpC;;ACPA;AACA;AACA;AACA;AACA,SAASmI,aAAaA,CAACnI,KAAa,EAAU;EAC5C,OAAOkI,oBAAoB,CAAClI,KAAK,CAAC,GAAG,MAAM,GAAG,MAAM;AACtD;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASoI,QAAQA,CAACpI,KAAa,EAAEqD,MAAc,EAAU;EACvD,OAAO4B,UAAU,CAACjF,KAAK,EAAE,CAACqD,MAAM,CAAC;AACnC;;ACRA;AACA;AACA;AACA,SAASgF,KAAKA,CAACrI,KAAa,EAAU;EACpC,MAAM,CAACmB,CAAC,EAAEmE,CAAC,EAAEC,CAAC,EAAEzD,CAAC,CAAC,GAAG5B,WAAW,CAACF,KAAK,CAAC;EAEvC,IAAIgD,GAAG,GAAI/B,CAAS,IAAK;IACvB,MAAMU,CAAC,GAAGrC,KAAK,CAAC,CAAC,EAAE,GAAG,EAAE2B,CAAC,CAAC,CAACgC,QAAQ,CAAC,EAAE,CAAC;IACvC;IACA;IACA,OAAOtB,CAAC,CAACU,MAAM,KAAK,CAAC,GAAO,IAAAV,CAAG,KAAGA,CAAC;GACpC;EAED,OAAW,IAAAqB,GAAG,CAAC7B,CAAC,CAAI,GAAA6B,GAAG,CAACsC,CAAC,CAAE,GAAEtC,GAAG,CAACuC,CAAC,CAAE,GAAEzD,CAAC,GAAG,CAAC,GAAGkB,GAAG,CAACtD,IAAI,CAAC+D,KAAK,CAAC3B,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,EAAI;AAC/E;;ACdA;AACA;AACA;AACA,SAASwG,MAAMA,CAACtI,KAAa,EAAU;EACrC,OAAO6F,IAAI,CAAC,GAAG3F,WAAW,CAACF,KAAK,CAAC,CAAC;AACpC;;ACLA;AACA;AACA;AACA,SAASuI,MAAMA,CAACvI,KAAa,EAAU;EACrC,OAAO4E,IAAI,CAAC,GAAGJ,WAAW,CAACxE,KAAK,CAAC,CAAC;AACpC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}