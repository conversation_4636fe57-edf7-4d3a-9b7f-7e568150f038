{"ast": null, "code": "import * as React from 'react';\nimport PropTypes from 'prop-types';\nexport var hiddenGuard = {\n  width: '1px',\n  height: '0px',\n  padding: 0,\n  overflow: 'hidden',\n  position: 'fixed',\n  top: '1px',\n  left: '1px'\n};\nvar InFocusGuard = function InFocusGuard(_ref) {\n  var children = _ref.children;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"div\", {\n    key: \"guard-first\",\n    \"data-focus-guard\": true,\n    \"data-focus-auto-guard\": true,\n    style: hiddenGuard\n  }), children, children && /*#__PURE__*/React.createElement(\"div\", {\n    key: \"guard-last\",\n    \"data-focus-guard\": true,\n    \"data-focus-auto-guard\": true,\n    style: hiddenGuard\n  }));\n};\nInFocusGuard.propTypes = process.env.NODE_ENV !== \"production\" ? {\n  children: PropTypes.node\n} : {};\nInFocusGuard.defaultProps = {\n  children: null\n};\nexport default InFocusGuard;", "map": {"version": 3, "names": ["React", "PropTypes", "<PERSON><PERSON><PERSON>", "width", "height", "padding", "overflow", "position", "top", "left", "InFocusGuard", "_ref", "children", "createElement", "Fragment", "key", "style", "propTypes", "process", "env", "NODE_ENV", "node", "defaultProps"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/react-focus-lock/dist/es2015/FocusGuard.js"], "sourcesContent": ["import * as React from 'react';\nimport PropTypes from 'prop-types';\nexport var hiddenGuard = {\n  width: '1px',\n  height: '0px',\n  padding: 0,\n  overflow: 'hidden',\n  position: 'fixed',\n  top: '1px',\n  left: '1px'\n};\n\nvar InFocusGuard = function InFocusGuard(_ref) {\n  var children = _ref.children;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"div\", {\n    key: \"guard-first\",\n    \"data-focus-guard\": true,\n    \"data-focus-auto-guard\": true,\n    style: hiddenGuard\n  }), children, children && /*#__PURE__*/React.createElement(\"div\", {\n    key: \"guard-last\",\n    \"data-focus-guard\": true,\n    \"data-focus-auto-guard\": true,\n    style: hiddenGuard\n  }));\n};\n\nInFocusGuard.propTypes = process.env.NODE_ENV !== \"production\" ? {\n  children: PropTypes.node\n} : {};\nInFocusGuard.defaultProps = {\n  children: null\n};\nexport default InFocusGuard;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAO,IAAIC,WAAW,GAAG;EACvBC,KAAK,EAAE,KAAK;EACZC,MAAM,EAAE,KAAK;EACbC,OAAO,EAAE,CAAC;EACVC,QAAQ,EAAE,QAAQ;EAClBC,QAAQ,EAAE,OAAO;EACjBC,GAAG,EAAE,KAAK;EACVC,IAAI,EAAE;AACR,CAAC;AAED,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAACC,IAAI,EAAE;EAC7C,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;EAC5B,OAAO,aAAaZ,KAAK,CAACa,aAAa,CAACb,KAAK,CAACc,QAAQ,EAAE,IAAI,EAAE,aAAad,KAAK,CAACa,aAAa,CAAC,KAAK,EAAE;IACpGE,GAAG,EAAE,aAAa;IAClB,kBAAkB,EAAE,IAAI;IACxB,uBAAuB,EAAE,IAAI;IAC7BC,KAAK,EAAEd;EACT,CAAC,CAAC,EAAEU,QAAQ,EAAEA,QAAQ,IAAI,aAAaZ,KAAK,CAACa,aAAa,CAAC,KAAK,EAAE;IAChEE,GAAG,EAAE,YAAY;IACjB,kBAAkB,EAAE,IAAI;IACxB,uBAAuB,EAAE,IAAI;IAC7BC,KAAK,EAAEd;EACT,CAAC,CAAC,CAAC;AACL,CAAC;AAEDQ,YAAY,CAACO,SAAS,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG;EAC/DR,QAAQ,EAAEX,SAAS,CAACoB;AACtB,CAAC,GAAG,CAAC,CAAC;AACNX,YAAY,CAACY,YAAY,GAAG;EAC1BV,QAAQ,EAAE;AACZ,CAAC;AACD,eAAeF,YAAY"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}