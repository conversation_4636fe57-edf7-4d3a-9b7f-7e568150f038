{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport * as constants from 'focus-lock/constants';\nimport { inlineProp } from './util';\nimport { mediumEffect } from './medium';\nexport var useFocusInside = function useFocusInside(observedRef) {\n  React.useEffect(function () {\n    var enabled = true;\n    mediumEffect.useMedium(function (car) {\n      var observed = observedRef && observedRef.current;\n      if (enabled && observed) {\n        if (!car.focusInside(observed)) {\n          car.moveFocusInside(observed, null);\n        }\n      }\n    });\n    return function () {\n      enabled = false;\n    };\n  }, [observedRef]);\n};\nfunction MoveFocusInside(_ref) {\n  var isDisabled = _ref.disabled,\n    className = _ref.className,\n    children = _ref.children;\n  var ref = React.useRef(null);\n  useFocusInside(isDisabled ? undefined : ref);\n  return /*#__PURE__*/React.createElement(\"div\", _extends({}, inlineProp(constants.FOCUS_AUTO, !isDisabled), {\n    ref: ref,\n    className: className\n  }), children);\n}\nMoveFocusInside.propTypes = process.env.NODE_ENV !== \"production\" ? {\n  children: PropTypes.node.isRequired,\n  disabled: PropTypes.bool,\n  className: PropTypes.string\n} : {};\nMoveFocusInside.defaultProps = {\n  disabled: false,\n  className: undefined\n};\nexport default MoveFocusInside;", "map": {"version": 3, "names": ["_extends", "React", "PropTypes", "constants", "inlineProp", "mediumEffect", "useFocusInside", "observedRef", "useEffect", "enabled", "useMedium", "car", "observed", "current", "focusInside", "moveFocusInside", "MoveFocusInside", "_ref", "isDisabled", "disabled", "className", "children", "ref", "useRef", "undefined", "createElement", "FOCUS_AUTO", "propTypes", "process", "env", "NODE_ENV", "node", "isRequired", "bool", "string", "defaultProps"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/react-focus-lock/dist/es2015/MoveFocusInside.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport * as constants from 'focus-lock/constants';\nimport { inlineProp } from './util';\nimport { mediumEffect } from './medium';\nexport var useFocusInside = function useFocusInside(observedRef) {\n  React.useEffect(function () {\n    var enabled = true;\n    mediumEffect.useMedium(function (car) {\n      var observed = observedRef && observedRef.current;\n\n      if (enabled && observed) {\n        if (!car.focusInside(observed)) {\n          car.moveFocusInside(observed, null);\n        }\n      }\n    });\n    return function () {\n      enabled = false;\n    };\n  }, [observedRef]);\n};\n\nfunction MoveFocusInside(_ref) {\n  var isDisabled = _ref.disabled,\n      className = _ref.className,\n      children = _ref.children;\n  var ref = React.useRef(null);\n  useFocusInside(isDisabled ? undefined : ref);\n  return /*#__PURE__*/React.createElement(\"div\", _extends({}, inlineProp(constants.FOCUS_AUTO, !isDisabled), {\n    ref: ref,\n    className: className\n  }), children);\n}\n\nMoveFocusInside.propTypes = process.env.NODE_ENV !== \"production\" ? {\n  children: PropTypes.node.isRequired,\n  disabled: PropTypes.bool,\n  className: PropTypes.string\n} : {};\nMoveFocusInside.defaultProps = {\n  disabled: false,\n  className: undefined\n};\nexport default MoveFocusInside;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAO,KAAKC,SAAS,MAAM,sBAAsB;AACjD,SAASC,UAAU,QAAQ,QAAQ;AACnC,SAASC,YAAY,QAAQ,UAAU;AACvC,OAAO,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,WAAW,EAAE;EAC/DN,KAAK,CAACO,SAAS,CAAC,YAAY;IAC1B,IAAIC,OAAO,GAAG,IAAI;IAClBJ,YAAY,CAACK,SAAS,CAAC,UAAUC,GAAG,EAAE;MACpC,IAAIC,QAAQ,GAAGL,WAAW,IAAIA,WAAW,CAACM,OAAO;MAEjD,IAAIJ,OAAO,IAAIG,QAAQ,EAAE;QACvB,IAAI,CAACD,GAAG,CAACG,WAAW,CAACF,QAAQ,CAAC,EAAE;UAC9BD,GAAG,CAACI,eAAe,CAACH,QAAQ,EAAE,IAAI,CAAC;QACrC;MACF;IACF,CAAC,CAAC;IACF,OAAO,YAAY;MACjBH,OAAO,GAAG,KAAK;IACjB,CAAC;EACH,CAAC,EAAE,CAACF,WAAW,CAAC,CAAC;AACnB,CAAC;AAED,SAASS,eAAeA,CAACC,IAAI,EAAE;EAC7B,IAAIC,UAAU,GAAGD,IAAI,CAACE,QAAQ;IAC1BC,SAAS,GAAGH,IAAI,CAACG,SAAS;IAC1BC,QAAQ,GAAGJ,IAAI,CAACI,QAAQ;EAC5B,IAAIC,GAAG,GAAGrB,KAAK,CAACsB,MAAM,CAAC,IAAI,CAAC;EAC5BjB,cAAc,CAACY,UAAU,GAAGM,SAAS,GAAGF,GAAG,CAAC;EAC5C,OAAO,aAAarB,KAAK,CAACwB,aAAa,CAAC,KAAK,EAAEzB,QAAQ,CAAC,CAAC,CAAC,EAAEI,UAAU,CAACD,SAAS,CAACuB,UAAU,EAAE,CAACR,UAAU,CAAC,EAAE;IACzGI,GAAG,EAAEA,GAAG;IACRF,SAAS,EAAEA;EACb,CAAC,CAAC,EAAEC,QAAQ,CAAC;AACf;AAEAL,eAAe,CAACW,SAAS,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG;EAClET,QAAQ,EAAEnB,SAAS,CAAC6B,IAAI,CAACC,UAAU;EACnCb,QAAQ,EAAEjB,SAAS,CAAC+B,IAAI;EACxBb,SAAS,EAAElB,SAAS,CAACgC;AACvB,CAAC,GAAG,CAAC,CAAC;AACNlB,eAAe,CAACmB,YAAY,GAAG;EAC7BhB,QAAQ,EAAE,KAAK;EACfC,SAAS,EAAEI;AACb,CAAC;AACD,eAAeR,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}