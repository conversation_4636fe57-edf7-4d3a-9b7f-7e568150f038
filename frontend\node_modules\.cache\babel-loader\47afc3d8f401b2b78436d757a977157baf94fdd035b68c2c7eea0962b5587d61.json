{"ast": null, "code": "/* eslint-disable no-mixed-operators */\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport withSideEffect from 'react-clientside-effect';\nimport { moveFocusInside, focusInside, focusIsHidden, expandFocusableNodes } from 'focus-lock';\nimport { deferAction } from './util';\nimport { mediumFocus, mediumBlur, mediumEffect } from './medium';\nvar focusOnBody = function focusOnBody() {\n  return document && document.activeElement === document.body;\n};\nvar isFreeFocus = function isFreeFocus() {\n  return focusOnBody() || focusIsHidden();\n};\nvar lastActiveTrap = null;\nvar lastActiveFocus = null;\nvar lastPortaledElement = null;\nvar focusWasOutsideWindow = false;\nvar defaultWhitelist = function defaultWhitelist() {\n  return true;\n};\nvar focusWhitelisted = function focusWhitelisted(activeElement) {\n  return (lastActiveTrap.whiteList || defaultWhitelist)(activeElement);\n};\nvar recordPortal = function recordPortal(observerNode, portaledElement) {\n  lastPortaledElement = {\n    observerNode: observerNode,\n    portaledElement: portaledElement\n  };\n};\nvar focusIsPortaledPair = function focusIsPortaledPair(element) {\n  return lastPortaledElement && lastPortaledElement.portaledElement === element;\n};\nfunction autoGuard(startIndex, end, step, allNodes) {\n  var lastGuard = null;\n  var i = startIndex;\n  do {\n    var item = allNodes[i];\n    if (item.guard) {\n      if (item.node.dataset.focusAutoGuard) {\n        lastGuard = item;\n      }\n    } else if (item.lockItem) {\n      if (i !== startIndex) {\n        // we will tab to the next element\n        return;\n      }\n      lastGuard = null;\n    } else {\n      break;\n    }\n  } while ((i += step) !== end);\n  if (lastGuard) {\n    lastGuard.node.tabIndex = 0;\n  }\n}\nvar extractRef = function extractRef(ref) {\n  return ref && 'current' in ref ? ref.current : ref;\n};\nvar focusWasOutside = function focusWasOutside(crossFrameOption) {\n  if (crossFrameOption) {\n    // with cross frame return true for any value\n    return Boolean(focusWasOutsideWindow);\n  } // in other case return only of focus went a while aho\n\n  return focusWasOutsideWindow === 'meanwhile';\n};\nvar checkInHost = function checkInHost(check, el, boundary) {\n  return el && (\n  // find host equal to active element and check nested active element\n  el.host === check && (!el.activeElement || boundary.contains(el.activeElement)) // dive up\n  || el.parentNode && checkInHost(check, el.parentNode, boundary));\n};\nvar withinHost = function withinHost(activeElement, workingArea) {\n  return workingArea.some(function (area) {\n    return checkInHost(activeElement, area, area);\n  });\n};\nvar activateTrap = function activateTrap() {\n  var result = false;\n  if (lastActiveTrap) {\n    var _lastActiveTrap = lastActiveTrap,\n      observed = _lastActiveTrap.observed,\n      persistentFocus = _lastActiveTrap.persistentFocus,\n      autoFocus = _lastActiveTrap.autoFocus,\n      shards = _lastActiveTrap.shards,\n      crossFrame = _lastActiveTrap.crossFrame,\n      focusOptions = _lastActiveTrap.focusOptions;\n    var workingNode = observed || lastPortaledElement && lastPortaledElement.portaledElement;\n    var activeElement = document && document.activeElement;\n    if (workingNode) {\n      var workingArea = [workingNode].concat(shards.map(extractRef).filter(Boolean));\n      if (!activeElement || focusWhitelisted(activeElement)) {\n        if (persistentFocus || focusWasOutside(crossFrame) || !isFreeFocus() || !lastActiveFocus && autoFocus) {\n          if (workingNode && !(\n          // active element is \"inside\" working area\n          focusInside(workingArea) ||\n          // check for shadow-dom contained elements\n          activeElement && withinHost(activeElement, workingArea) || focusIsPortaledPair(activeElement, workingNode))) {\n            if (document && !lastActiveFocus && activeElement && !autoFocus) {\n              // Check if blur() exists, which is missing on certain elements on IE\n              if (activeElement.blur) {\n                activeElement.blur();\n              }\n              document.body.focus();\n            } else {\n              result = moveFocusInside(workingArea, lastActiveFocus, {\n                focusOptions: focusOptions\n              });\n              lastPortaledElement = {};\n            }\n          }\n          focusWasOutsideWindow = false;\n          lastActiveFocus = document && document.activeElement;\n        }\n      }\n      if (document) {\n        var newActiveElement = document && document.activeElement;\n        var allNodes = expandFocusableNodes(workingArea);\n        var focusedIndex = allNodes.map(function (_ref) {\n          var node = _ref.node;\n          return node;\n        }).indexOf(newActiveElement);\n        if (focusedIndex > -1) {\n          // remove old focus\n          allNodes.filter(function (_ref2) {\n            var guard = _ref2.guard,\n              node = _ref2.node;\n            return guard && node.dataset.focusAutoGuard;\n          }).forEach(function (_ref3) {\n            var node = _ref3.node;\n            return node.removeAttribute('tabIndex');\n          });\n          autoGuard(focusedIndex, allNodes.length, +1, allNodes);\n          autoGuard(focusedIndex, -1, -1, allNodes);\n        }\n      }\n    }\n  }\n  return result;\n};\nvar onTrap = function onTrap(event) {\n  if (activateTrap() && event) {\n    // prevent scroll jump\n    event.stopPropagation();\n    event.preventDefault();\n  }\n};\nvar onBlur = function onBlur() {\n  return deferAction(activateTrap);\n};\nvar onFocus = function onFocus(event) {\n  // detect portal\n  var source = event.target;\n  var currentNode = event.currentTarget;\n  if (!currentNode.contains(source)) {\n    recordPortal(currentNode, source);\n  }\n};\nvar FocusWatcher = function FocusWatcher() {\n  return null;\n};\nvar FocusTrap = function FocusTrap(_ref4) {\n  var children = _ref4.children;\n  return /*#__PURE__*/React.createElement(\"div\", {\n    onBlur: onBlur,\n    onFocus: onFocus\n  }, children);\n};\nFocusTrap.propTypes = process.env.NODE_ENV !== \"production\" ? {\n  children: PropTypes.node.isRequired\n} : {};\nvar onWindowBlur = function onWindowBlur() {\n  focusWasOutsideWindow = 'just'; // using setTimeout to set  this variable after React/sidecar reaction\n\n  deferAction(function () {\n    focusWasOutsideWindow = 'meanwhile';\n  });\n};\nvar attachHandler = function attachHandler() {\n  document.addEventListener('focusin', onTrap);\n  document.addEventListener('focusout', onBlur);\n  window.addEventListener('blur', onWindowBlur);\n};\nvar detachHandler = function detachHandler() {\n  document.removeEventListener('focusin', onTrap);\n  document.removeEventListener('focusout', onBlur);\n  window.removeEventListener('blur', onWindowBlur);\n};\nfunction reducePropsToState(propsList) {\n  return propsList.filter(function (_ref5) {\n    var disabled = _ref5.disabled;\n    return !disabled;\n  });\n}\nfunction handleStateChangeOnClient(traps) {\n  var trap = traps.slice(-1)[0];\n  if (trap && !lastActiveTrap) {\n    attachHandler();\n  }\n  var lastTrap = lastActiveTrap;\n  var sameTrap = lastTrap && trap && trap.id === lastTrap.id;\n  lastActiveTrap = trap;\n  if (lastTrap && !sameTrap) {\n    lastTrap.onDeactivation(); // return focus only of last trap was removed\n\n    if (!traps.filter(function (_ref6) {\n      var id = _ref6.id;\n      return id === lastTrap.id;\n    }).length) {\n      // allow defer is no other trap is awaiting restore\n      lastTrap.returnFocus(!trap);\n    }\n  }\n  if (trap) {\n    lastActiveFocus = null;\n    if (!sameTrap || lastTrap.observed !== trap.observed) {\n      trap.onActivation();\n    }\n    activateTrap(true);\n    deferAction(activateTrap);\n  } else {\n    detachHandler();\n    lastActiveFocus = null;\n  }\n} // bind medium\n\nmediumFocus.assignSyncMedium(onFocus);\nmediumBlur.assignMedium(onBlur);\nmediumEffect.assignMedium(function (cb) {\n  return cb({\n    moveFocusInside: moveFocusInside,\n    focusInside: focusInside\n  });\n});\nexport default withSideEffect(reducePropsToState, handleStateChangeOnClient)(FocusWatcher);", "map": {"version": 3, "names": ["React", "PropTypes", "withSideEffect", "moveFocusInside", "focusInside", "focusIsHidden", "expandFocusableNodes", "deferAction", "mediumFocus", "mediumBlur", "mediumEffect", "focusOnBody", "document", "activeElement", "body", "isFreeFocus", "lastActiveTrap", "lastActiveFocus", "lastPortaledElement", "focusWasOutsideWindow", "defaultW<PERSON>elist", "focus<PERSON><PERSON><PERSON><PERSON>", "whiteList", "recordPortal", "observerNode", "portaledElement", "focusIsPortaledPair", "element", "autoGuard", "startIndex", "end", "step", "allNodes", "<PERSON><PERSON><PERSON>", "i", "item", "guard", "node", "dataset", "focusAutoGuard", "lockItem", "tabIndex", "extractRef", "ref", "current", "focusWasOutside", "crossFrameOption", "Boolean", "checkInHost", "check", "el", "boundary", "host", "contains", "parentNode", "withinHost", "workingArea", "some", "area", "activateTrap", "result", "_lastActiveTrap", "observed", "persistentFocus", "autoFocus", "shards", "crossFrame", "focusOptions", "workingNode", "concat", "map", "filter", "blur", "focus", "newActiveElement", "focusedIndex", "_ref", "indexOf", "_ref2", "for<PERSON>ach", "_ref3", "removeAttribute", "length", "onTrap", "event", "stopPropagation", "preventDefault", "onBlur", "onFocus", "source", "target", "currentNode", "currentTarget", "FocusWatcher", "FocusTrap", "_ref4", "children", "createElement", "propTypes", "process", "env", "NODE_ENV", "isRequired", "onWindowBlur", "attach<PERSON><PERSON><PERSON>", "addEventListener", "window", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "reducePropsToState", "propsList", "_ref5", "disabled", "handleStateChangeOnClient", "traps", "trap", "slice", "lastTrap", "sameTrap", "id", "onDeactivation", "_ref6", "returnFocus", "onActivation", "assignSyncMedium", "assignMedium", "cb"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/react-focus-lock/dist/es2015/Trap.js"], "sourcesContent": ["/* eslint-disable no-mixed-operators */\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport withSideEffect from 'react-clientside-effect';\nimport { moveFocusInside, focusInside, focusIsHidden, expandFocusableNodes } from 'focus-lock';\nimport { deferAction } from './util';\nimport { mediumFocus, mediumBlur, mediumEffect } from './medium';\n\nvar focusOnBody = function focusOnBody() {\n  return document && document.activeElement === document.body;\n};\n\nvar isFreeFocus = function isFreeFocus() {\n  return focusOnBody() || focusIsHidden();\n};\n\nvar lastActiveTrap = null;\nvar lastActiveFocus = null;\nvar lastPortaledElement = null;\nvar focusWasOutsideWindow = false;\n\nvar defaultWhitelist = function defaultWhitelist() {\n  return true;\n};\n\nvar focusWhitelisted = function focusWhitelisted(activeElement) {\n  return (lastActiveTrap.whiteList || defaultWhitelist)(activeElement);\n};\n\nvar recordPortal = function recordPortal(observerNode, portaledElement) {\n  lastPortaledElement = {\n    observerNode: observerNode,\n    portaledElement: portaledElement\n  };\n};\n\nvar focusIsPortaledPair = function focusIsPortaledPair(element) {\n  return lastPortaledElement && lastPortaledElement.portaledElement === element;\n};\n\nfunction autoGuard(startIndex, end, step, allNodes) {\n  var lastGuard = null;\n  var i = startIndex;\n\n  do {\n    var item = allNodes[i];\n\n    if (item.guard) {\n      if (item.node.dataset.focusAutoGuard) {\n        lastGuard = item;\n      }\n    } else if (item.lockItem) {\n      if (i !== startIndex) {\n        // we will tab to the next element\n        return;\n      }\n\n      lastGuard = null;\n    } else {\n      break;\n    }\n  } while ((i += step) !== end);\n\n  if (lastGuard) {\n    lastGuard.node.tabIndex = 0;\n  }\n}\n\nvar extractRef = function extractRef(ref) {\n  return ref && 'current' in ref ? ref.current : ref;\n};\n\nvar focusWasOutside = function focusWasOutside(crossFrameOption) {\n  if (crossFrameOption) {\n    // with cross frame return true for any value\n    return Boolean(focusWasOutsideWindow);\n  } // in other case return only of focus went a while aho\n\n\n  return focusWasOutsideWindow === 'meanwhile';\n};\n\nvar checkInHost = function checkInHost(check, el, boundary) {\n  return el && ( // find host equal to active element and check nested active element\n  el.host === check && (!el.activeElement || boundary.contains(el.activeElement)) // dive up\n  || el.parentNode && checkInHost(check, el.parentNode, boundary));\n};\n\nvar withinHost = function withinHost(activeElement, workingArea) {\n  return workingArea.some(function (area) {\n    return checkInHost(activeElement, area, area);\n  });\n};\n\nvar activateTrap = function activateTrap() {\n  var result = false;\n\n  if (lastActiveTrap) {\n    var _lastActiveTrap = lastActiveTrap,\n        observed = _lastActiveTrap.observed,\n        persistentFocus = _lastActiveTrap.persistentFocus,\n        autoFocus = _lastActiveTrap.autoFocus,\n        shards = _lastActiveTrap.shards,\n        crossFrame = _lastActiveTrap.crossFrame,\n        focusOptions = _lastActiveTrap.focusOptions;\n    var workingNode = observed || lastPortaledElement && lastPortaledElement.portaledElement;\n    var activeElement = document && document.activeElement;\n\n    if (workingNode) {\n      var workingArea = [workingNode].concat(shards.map(extractRef).filter(Boolean));\n\n      if (!activeElement || focusWhitelisted(activeElement)) {\n        if (persistentFocus || focusWasOutside(crossFrame) || !isFreeFocus() || !lastActiveFocus && autoFocus) {\n          if (workingNode && !( // active element is \"inside\" working area\n          focusInside(workingArea) || // check for shadow-dom contained elements\n          activeElement && withinHost(activeElement, workingArea) || focusIsPortaledPair(activeElement, workingNode))) {\n            if (document && !lastActiveFocus && activeElement && !autoFocus) {\n              // Check if blur() exists, which is missing on certain elements on IE\n              if (activeElement.blur) {\n                activeElement.blur();\n              }\n\n              document.body.focus();\n            } else {\n              result = moveFocusInside(workingArea, lastActiveFocus, {\n                focusOptions: focusOptions\n              });\n              lastPortaledElement = {};\n            }\n          }\n\n          focusWasOutsideWindow = false;\n          lastActiveFocus = document && document.activeElement;\n        }\n      }\n\n      if (document) {\n        var newActiveElement = document && document.activeElement;\n        var allNodes = expandFocusableNodes(workingArea);\n        var focusedIndex = allNodes.map(function (_ref) {\n          var node = _ref.node;\n          return node;\n        }).indexOf(newActiveElement);\n\n        if (focusedIndex > -1) {\n          // remove old focus\n          allNodes.filter(function (_ref2) {\n            var guard = _ref2.guard,\n                node = _ref2.node;\n            return guard && node.dataset.focusAutoGuard;\n          }).forEach(function (_ref3) {\n            var node = _ref3.node;\n            return node.removeAttribute('tabIndex');\n          });\n          autoGuard(focusedIndex, allNodes.length, +1, allNodes);\n          autoGuard(focusedIndex, -1, -1, allNodes);\n        }\n      }\n    }\n  }\n\n  return result;\n};\n\nvar onTrap = function onTrap(event) {\n  if (activateTrap() && event) {\n    // prevent scroll jump\n    event.stopPropagation();\n    event.preventDefault();\n  }\n};\n\nvar onBlur = function onBlur() {\n  return deferAction(activateTrap);\n};\n\nvar onFocus = function onFocus(event) {\n  // detect portal\n  var source = event.target;\n  var currentNode = event.currentTarget;\n\n  if (!currentNode.contains(source)) {\n    recordPortal(currentNode, source);\n  }\n};\n\nvar FocusWatcher = function FocusWatcher() {\n  return null;\n};\n\nvar FocusTrap = function FocusTrap(_ref4) {\n  var children = _ref4.children;\n  return /*#__PURE__*/React.createElement(\"div\", {\n    onBlur: onBlur,\n    onFocus: onFocus\n  }, children);\n};\n\nFocusTrap.propTypes = process.env.NODE_ENV !== \"production\" ? {\n  children: PropTypes.node.isRequired\n} : {};\n\nvar onWindowBlur = function onWindowBlur() {\n  focusWasOutsideWindow = 'just'; // using setTimeout to set  this variable after React/sidecar reaction\n\n  deferAction(function () {\n    focusWasOutsideWindow = 'meanwhile';\n  });\n};\n\nvar attachHandler = function attachHandler() {\n  document.addEventListener('focusin', onTrap);\n  document.addEventListener('focusout', onBlur);\n  window.addEventListener('blur', onWindowBlur);\n};\n\nvar detachHandler = function detachHandler() {\n  document.removeEventListener('focusin', onTrap);\n  document.removeEventListener('focusout', onBlur);\n  window.removeEventListener('blur', onWindowBlur);\n};\n\nfunction reducePropsToState(propsList) {\n  return propsList.filter(function (_ref5) {\n    var disabled = _ref5.disabled;\n    return !disabled;\n  });\n}\n\nfunction handleStateChangeOnClient(traps) {\n  var trap = traps.slice(-1)[0];\n\n  if (trap && !lastActiveTrap) {\n    attachHandler();\n  }\n\n  var lastTrap = lastActiveTrap;\n  var sameTrap = lastTrap && trap && trap.id === lastTrap.id;\n  lastActiveTrap = trap;\n\n  if (lastTrap && !sameTrap) {\n    lastTrap.onDeactivation(); // return focus only of last trap was removed\n\n    if (!traps.filter(function (_ref6) {\n      var id = _ref6.id;\n      return id === lastTrap.id;\n    }).length) {\n      // allow defer is no other trap is awaiting restore\n      lastTrap.returnFocus(!trap);\n    }\n  }\n\n  if (trap) {\n    lastActiveFocus = null;\n\n    if (!sameTrap || lastTrap.observed !== trap.observed) {\n      trap.onActivation();\n    }\n\n    activateTrap(true);\n    deferAction(activateTrap);\n  } else {\n    detachHandler();\n    lastActiveFocus = null;\n  }\n} // bind medium\n\n\nmediumFocus.assignSyncMedium(onFocus);\nmediumBlur.assignMedium(onBlur);\nmediumEffect.assignMedium(function (cb) {\n  return cb({\n    moveFocusInside: moveFocusInside,\n    focusInside: focusInside\n  });\n});\nexport default withSideEffect(reducePropsToState, handleStateChangeOnClient)(FocusWatcher);"], "mappings": "AAAA;AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,cAAc,MAAM,yBAAyB;AACpD,SAASC,eAAe,EAAEC,WAAW,EAAEC,aAAa,EAAEC,oBAAoB,QAAQ,YAAY;AAC9F,SAASC,WAAW,QAAQ,QAAQ;AACpC,SAASC,WAAW,EAAEC,UAAU,EAAEC,YAAY,QAAQ,UAAU;AAEhE,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAAA,EAAG;EACvC,OAAOC,QAAQ,IAAIA,QAAQ,CAACC,aAAa,KAAKD,QAAQ,CAACE,IAAI;AAC7D,CAAC;AAED,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAAA,EAAG;EACvC,OAAOJ,WAAW,CAAC,CAAC,IAAIN,aAAa,CAAC,CAAC;AACzC,CAAC;AAED,IAAIW,cAAc,GAAG,IAAI;AACzB,IAAIC,eAAe,GAAG,IAAI;AAC1B,IAAIC,mBAAmB,GAAG,IAAI;AAC9B,IAAIC,qBAAqB,GAAG,KAAK;AAEjC,IAAIC,gBAAgB,GAAG,SAASA,gBAAgBA,CAAA,EAAG;EACjD,OAAO,IAAI;AACb,CAAC;AAED,IAAIC,gBAAgB,GAAG,SAASA,gBAAgBA,CAACR,aAAa,EAAE;EAC9D,OAAO,CAACG,cAAc,CAACM,SAAS,IAAIF,gBAAgB,EAAEP,aAAa,CAAC;AACtE,CAAC;AAED,IAAIU,YAAY,GAAG,SAASA,YAAYA,CAACC,YAAY,EAAEC,eAAe,EAAE;EACtEP,mBAAmB,GAAG;IACpBM,YAAY,EAAEA,YAAY;IAC1BC,eAAe,EAAEA;EACnB,CAAC;AACH,CAAC;AAED,IAAIC,mBAAmB,GAAG,SAASA,mBAAmBA,CAACC,OAAO,EAAE;EAC9D,OAAOT,mBAAmB,IAAIA,mBAAmB,CAACO,eAAe,KAAKE,OAAO;AAC/E,CAAC;AAED,SAASC,SAASA,CAACC,UAAU,EAAEC,GAAG,EAAEC,IAAI,EAAEC,QAAQ,EAAE;EAClD,IAAIC,SAAS,GAAG,IAAI;EACpB,IAAIC,CAAC,GAAGL,UAAU;EAElB,GAAG;IACD,IAAIM,IAAI,GAAGH,QAAQ,CAACE,CAAC,CAAC;IAEtB,IAAIC,IAAI,CAACC,KAAK,EAAE;MACd,IAAID,IAAI,CAACE,IAAI,CAACC,OAAO,CAACC,cAAc,EAAE;QACpCN,SAAS,GAAGE,IAAI;MAClB;IACF,CAAC,MAAM,IAAIA,IAAI,CAACK,QAAQ,EAAE;MACxB,IAAIN,CAAC,KAAKL,UAAU,EAAE;QACpB;QACA;MACF;MAEAI,SAAS,GAAG,IAAI;IAClB,CAAC,MAAM;MACL;IACF;EACF,CAAC,QAAQ,CAACC,CAAC,IAAIH,IAAI,MAAMD,GAAG;EAE5B,IAAIG,SAAS,EAAE;IACbA,SAAS,CAACI,IAAI,CAACI,QAAQ,GAAG,CAAC;EAC7B;AACF;AAEA,IAAIC,UAAU,GAAG,SAASA,UAAUA,CAACC,GAAG,EAAE;EACxC,OAAOA,GAAG,IAAI,SAAS,IAAIA,GAAG,GAAGA,GAAG,CAACC,OAAO,GAAGD,GAAG;AACpD,CAAC;AAED,IAAIE,eAAe,GAAG,SAASA,eAAeA,CAACC,gBAAgB,EAAE;EAC/D,IAAIA,gBAAgB,EAAE;IACpB;IACA,OAAOC,OAAO,CAAC5B,qBAAqB,CAAC;EACvC,CAAC,CAAC;;EAGF,OAAOA,qBAAqB,KAAK,WAAW;AAC9C,CAAC;AAED,IAAI6B,WAAW,GAAG,SAASA,WAAWA,CAACC,KAAK,EAAEC,EAAE,EAAEC,QAAQ,EAAE;EAC1D,OAAOD,EAAE;EAAM;EACfA,EAAE,CAACE,IAAI,KAAKH,KAAK,KAAK,CAACC,EAAE,CAACrC,aAAa,IAAIsC,QAAQ,CAACE,QAAQ,CAACH,EAAE,CAACrC,aAAa,CAAC,CAAC,CAAC;EAAA,GAC7EqC,EAAE,CAACI,UAAU,IAAIN,WAAW,CAACC,KAAK,EAAEC,EAAE,CAACI,UAAU,EAAEH,QAAQ,CAAC,CAAC;AAClE,CAAC;AAED,IAAII,UAAU,GAAG,SAASA,UAAUA,CAAC1C,aAAa,EAAE2C,WAAW,EAAE;EAC/D,OAAOA,WAAW,CAACC,IAAI,CAAC,UAAUC,IAAI,EAAE;IACtC,OAAOV,WAAW,CAACnC,aAAa,EAAE6C,IAAI,EAAEA,IAAI,CAAC;EAC/C,CAAC,CAAC;AACJ,CAAC;AAED,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAG;EACzC,IAAIC,MAAM,GAAG,KAAK;EAElB,IAAI5C,cAAc,EAAE;IAClB,IAAI6C,eAAe,GAAG7C,cAAc;MAChC8C,QAAQ,GAAGD,eAAe,CAACC,QAAQ;MACnCC,eAAe,GAAGF,eAAe,CAACE,eAAe;MACjDC,SAAS,GAAGH,eAAe,CAACG,SAAS;MACrCC,MAAM,GAAGJ,eAAe,CAACI,MAAM;MAC/BC,UAAU,GAAGL,eAAe,CAACK,UAAU;MACvCC,YAAY,GAAGN,eAAe,CAACM,YAAY;IAC/C,IAAIC,WAAW,GAAGN,QAAQ,IAAI5C,mBAAmB,IAAIA,mBAAmB,CAACO,eAAe;IACxF,IAAIZ,aAAa,GAAGD,QAAQ,IAAIA,QAAQ,CAACC,aAAa;IAEtD,IAAIuD,WAAW,EAAE;MACf,IAAIZ,WAAW,GAAG,CAACY,WAAW,CAAC,CAACC,MAAM,CAACJ,MAAM,CAACK,GAAG,CAAC5B,UAAU,CAAC,CAAC6B,MAAM,CAACxB,OAAO,CAAC,CAAC;MAE9E,IAAI,CAAClC,aAAa,IAAIQ,gBAAgB,CAACR,aAAa,CAAC,EAAE;QACrD,IAAIkD,eAAe,IAAIlB,eAAe,CAACqB,UAAU,CAAC,IAAI,CAACnD,WAAW,CAAC,CAAC,IAAI,CAACE,eAAe,IAAI+C,SAAS,EAAE;UACrG,IAAII,WAAW,IAAI;UAAG;UACtBhE,WAAW,CAACoD,WAAW,CAAC;UAAI;UAC5B3C,aAAa,IAAI0C,UAAU,CAAC1C,aAAa,EAAE2C,WAAW,CAAC,IAAI9B,mBAAmB,CAACb,aAAa,EAAEuD,WAAW,CAAC,CAAC,EAAE;YAC3G,IAAIxD,QAAQ,IAAI,CAACK,eAAe,IAAIJ,aAAa,IAAI,CAACmD,SAAS,EAAE;cAC/D;cACA,IAAInD,aAAa,CAAC2D,IAAI,EAAE;gBACtB3D,aAAa,CAAC2D,IAAI,CAAC,CAAC;cACtB;cAEA5D,QAAQ,CAACE,IAAI,CAAC2D,KAAK,CAAC,CAAC;YACvB,CAAC,MAAM;cACLb,MAAM,GAAGzD,eAAe,CAACqD,WAAW,EAAEvC,eAAe,EAAE;gBACrDkD,YAAY,EAAEA;cAChB,CAAC,CAAC;cACFjD,mBAAmB,GAAG,CAAC,CAAC;YAC1B;UACF;UAEAC,qBAAqB,GAAG,KAAK;UAC7BF,eAAe,GAAGL,QAAQ,IAAIA,QAAQ,CAACC,aAAa;QACtD;MACF;MAEA,IAAID,QAAQ,EAAE;QACZ,IAAI8D,gBAAgB,GAAG9D,QAAQ,IAAIA,QAAQ,CAACC,aAAa;QACzD,IAAImB,QAAQ,GAAG1B,oBAAoB,CAACkD,WAAW,CAAC;QAChD,IAAImB,YAAY,GAAG3C,QAAQ,CAACsC,GAAG,CAAC,UAAUM,IAAI,EAAE;UAC9C,IAAIvC,IAAI,GAAGuC,IAAI,CAACvC,IAAI;UACpB,OAAOA,IAAI;QACb,CAAC,CAAC,CAACwC,OAAO,CAACH,gBAAgB,CAAC;QAE5B,IAAIC,YAAY,GAAG,CAAC,CAAC,EAAE;UACrB;UACA3C,QAAQ,CAACuC,MAAM,CAAC,UAAUO,KAAK,EAAE;YAC/B,IAAI1C,KAAK,GAAG0C,KAAK,CAAC1C,KAAK;cACnBC,IAAI,GAAGyC,KAAK,CAACzC,IAAI;YACrB,OAAOD,KAAK,IAAIC,IAAI,CAACC,OAAO,CAACC,cAAc;UAC7C,CAAC,CAAC,CAACwC,OAAO,CAAC,UAAUC,KAAK,EAAE;YAC1B,IAAI3C,IAAI,GAAG2C,KAAK,CAAC3C,IAAI;YACrB,OAAOA,IAAI,CAAC4C,eAAe,CAAC,UAAU,CAAC;UACzC,CAAC,CAAC;UACFrD,SAAS,CAAC+C,YAAY,EAAE3C,QAAQ,CAACkD,MAAM,EAAE,CAAC,CAAC,EAAElD,QAAQ,CAAC;UACtDJ,SAAS,CAAC+C,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE3C,QAAQ,CAAC;QAC3C;MACF;IACF;EACF;EAEA,OAAO4B,MAAM;AACf,CAAC;AAED,IAAIuB,MAAM,GAAG,SAASA,MAAMA,CAACC,KAAK,EAAE;EAClC,IAAIzB,YAAY,CAAC,CAAC,IAAIyB,KAAK,EAAE;IAC3B;IACAA,KAAK,CAACC,eAAe,CAAC,CAAC;IACvBD,KAAK,CAACE,cAAc,CAAC,CAAC;EACxB;AACF,CAAC;AAED,IAAIC,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,OAAOhF,WAAW,CAACoD,YAAY,CAAC;AAClC,CAAC;AAED,IAAI6B,OAAO,GAAG,SAASA,OAAOA,CAACJ,KAAK,EAAE;EACpC;EACA,IAAIK,MAAM,GAAGL,KAAK,CAACM,MAAM;EACzB,IAAIC,WAAW,GAAGP,KAAK,CAACQ,aAAa;EAErC,IAAI,CAACD,WAAW,CAACtC,QAAQ,CAACoC,MAAM,CAAC,EAAE;IACjClE,YAAY,CAACoE,WAAW,EAAEF,MAAM,CAAC;EACnC;AACF,CAAC;AAED,IAAII,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAG;EACzC,OAAO,IAAI;AACb,CAAC;AAED,IAAIC,SAAS,GAAG,SAASA,SAASA,CAACC,KAAK,EAAE;EACxC,IAAIC,QAAQ,GAAGD,KAAK,CAACC,QAAQ;EAC7B,OAAO,aAAahG,KAAK,CAACiG,aAAa,CAAC,KAAK,EAAE;IAC7CV,MAAM,EAAEA,MAAM;IACdC,OAAO,EAAEA;EACX,CAAC,EAAEQ,QAAQ,CAAC;AACd,CAAC;AAEDF,SAAS,CAACI,SAAS,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG;EAC5DL,QAAQ,EAAE/F,SAAS,CAACoC,IAAI,CAACiE;AAC3B,CAAC,GAAG,CAAC,CAAC;AAEN,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAG;EACzCpF,qBAAqB,GAAG,MAAM,CAAC,CAAC;;EAEhCZ,WAAW,CAAC,YAAY;IACtBY,qBAAqB,GAAG,WAAW;EACrC,CAAC,CAAC;AACJ,CAAC;AAED,IAAIqF,aAAa,GAAG,SAASA,aAAaA,CAAA,EAAG;EAC3C5F,QAAQ,CAAC6F,gBAAgB,CAAC,SAAS,EAAEtB,MAAM,CAAC;EAC5CvE,QAAQ,CAAC6F,gBAAgB,CAAC,UAAU,EAAElB,MAAM,CAAC;EAC7CmB,MAAM,CAACD,gBAAgB,CAAC,MAAM,EAAEF,YAAY,CAAC;AAC/C,CAAC;AAED,IAAII,aAAa,GAAG,SAASA,aAAaA,CAAA,EAAG;EAC3C/F,QAAQ,CAACgG,mBAAmB,CAAC,SAAS,EAAEzB,MAAM,CAAC;EAC/CvE,QAAQ,CAACgG,mBAAmB,CAAC,UAAU,EAAErB,MAAM,CAAC;EAChDmB,MAAM,CAACE,mBAAmB,CAAC,MAAM,EAAEL,YAAY,CAAC;AAClD,CAAC;AAED,SAASM,kBAAkBA,CAACC,SAAS,EAAE;EACrC,OAAOA,SAAS,CAACvC,MAAM,CAAC,UAAUwC,KAAK,EAAE;IACvC,IAAIC,QAAQ,GAAGD,KAAK,CAACC,QAAQ;IAC7B,OAAO,CAACA,QAAQ;EAClB,CAAC,CAAC;AACJ;AAEA,SAASC,yBAAyBA,CAACC,KAAK,EAAE;EACxC,IAAIC,IAAI,GAAGD,KAAK,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAE7B,IAAID,IAAI,IAAI,CAACnG,cAAc,EAAE;IAC3BwF,aAAa,CAAC,CAAC;EACjB;EAEA,IAAIa,QAAQ,GAAGrG,cAAc;EAC7B,IAAIsG,QAAQ,GAAGD,QAAQ,IAAIF,IAAI,IAAIA,IAAI,CAACI,EAAE,KAAKF,QAAQ,CAACE,EAAE;EAC1DvG,cAAc,GAAGmG,IAAI;EAErB,IAAIE,QAAQ,IAAI,CAACC,QAAQ,EAAE;IACzBD,QAAQ,CAACG,cAAc,CAAC,CAAC,CAAC,CAAC;;IAE3B,IAAI,CAACN,KAAK,CAAC3C,MAAM,CAAC,UAAUkD,KAAK,EAAE;MACjC,IAAIF,EAAE,GAAGE,KAAK,CAACF,EAAE;MACjB,OAAOA,EAAE,KAAKF,QAAQ,CAACE,EAAE;IAC3B,CAAC,CAAC,CAACrC,MAAM,EAAE;MACT;MACAmC,QAAQ,CAACK,WAAW,CAAC,CAACP,IAAI,CAAC;IAC7B;EACF;EAEA,IAAIA,IAAI,EAAE;IACRlG,eAAe,GAAG,IAAI;IAEtB,IAAI,CAACqG,QAAQ,IAAID,QAAQ,CAACvD,QAAQ,KAAKqD,IAAI,CAACrD,QAAQ,EAAE;MACpDqD,IAAI,CAACQ,YAAY,CAAC,CAAC;IACrB;IAEAhE,YAAY,CAAC,IAAI,CAAC;IAClBpD,WAAW,CAACoD,YAAY,CAAC;EAC3B,CAAC,MAAM;IACLgD,aAAa,CAAC,CAAC;IACf1F,eAAe,GAAG,IAAI;EACxB;AACF,CAAC,CAAC;;AAGFT,WAAW,CAACoH,gBAAgB,CAACpC,OAAO,CAAC;AACrC/E,UAAU,CAACoH,YAAY,CAACtC,MAAM,CAAC;AAC/B7E,YAAY,CAACmH,YAAY,CAAC,UAAUC,EAAE,EAAE;EACtC,OAAOA,EAAE,CAAC;IACR3H,eAAe,EAAEA,eAAe;IAChCC,WAAW,EAAEA;EACf,CAAC,CAAC;AACJ,CAAC,CAAC;AACF,eAAeF,cAAc,CAAC2G,kBAAkB,EAAEI,yBAAyB,CAAC,CAACpB,YAAY,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}