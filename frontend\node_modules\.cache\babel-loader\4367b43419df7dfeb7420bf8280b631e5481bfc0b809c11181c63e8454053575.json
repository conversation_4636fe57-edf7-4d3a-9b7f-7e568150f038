{"ast": null, "code": "import * as allConstants from './constants';\nimport { focusInside } from './focusInside';\nimport { focusIsHidden } from './focusIsHidden';\nimport { focusSolver } from './focusSolver';\nimport { expandFocusableNodes } from './focusables';\nimport { moveFocusInside } from './moveFocusInside';\nimport { focusNextElement, focusPrevElement, getRelativeFocusable } from './sibling';\nimport { getFocusableNodes, getTabbableNodes } from './utils/DOMutils';\n/**\n * magic symbols to control focus behavior from DOM\n * see description of every particular one\n */\nvar constants = allConstants;\nexport { constants,\n//\nfocusInside, focusIsHidden,\n//\nmoveFocusInside, focusSolver,\n//\nexpandFocusableNodes, getFocusableNodes, getTabbableNodes,\n//\nfocusNextElement, focusPrevElement, getRelativeFocusable };\n/**\n * @deprecated - please use {@link moveFocusInside} named export\n */\nvar deprecated_default_moveFocusInside = moveFocusInside;\nexport default deprecated_default_moveFocusInside;\n//", "map": {"version": 3, "names": ["allConstants", "focusInside", "focusIsHidden", "focusSolver", "expandFocusableNodes", "moveFocusInside", "focusNextElement", "focusPrevElement", "getRelativeFocusable", "getFocusableNodes", "getTabbableNodes", "constants", "deprecated_default_moveFocusInside"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/focus-lock/dist/es2015/index.js"], "sourcesContent": ["import * as allConstants from './constants';\nimport { focusInside } from './focusInside';\nimport { focusIsHidden } from './focusIsHidden';\nimport { focusSolver } from './focusSolver';\nimport { expandFocusableNodes } from './focusables';\nimport { moveFocusInside } from './moveFocusInside';\nimport { focusNextElement, focusPrevElement, getRelativeFocusable } from './sibling';\nimport { getFocusableNodes, getTabbableNodes } from './utils/DOMutils';\n/**\n * magic symbols to control focus behavior from DOM\n * see description of every particular one\n */\nvar constants = allConstants;\nexport { constants, \n//\nfocusInside, focusIsHidden, \n//\nmoveFocusInside, focusSolver, \n//\nexpandFocusableNodes, getFocusableNodes, getTabbableNodes, \n//\nfocusNextElement, focusPrevElement, getRelativeFocusable, };\n/**\n * @deprecated - please use {@link moveFocusInside} named export\n */\nvar deprecated_default_moveFocusInside = moveFocusInside;\nexport default deprecated_default_moveFocusInside;\n//\n"], "mappings": "AAAA,OAAO,KAAKA,YAAY,MAAM,aAAa;AAC3C,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,oBAAoB,QAAQ,cAAc;AACnD,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,gBAAgB,EAAEC,gBAAgB,EAAEC,oBAAoB,QAAQ,WAAW;AACpF,SAASC,iBAAiB,EAAEC,gBAAgB,QAAQ,kBAAkB;AACtE;AACA;AACA;AACA;AACA,IAAIC,SAAS,GAAGX,YAAY;AAC5B,SAASW,SAAS;AAClB;AACAV,WAAW,EAAEC,aAAa;AAC1B;AACAG,eAAe,EAAEF,WAAW;AAC5B;AACAC,oBAAoB,EAAEK,iBAAiB,EAAEC,gBAAgB;AACzD;AACAJ,gBAAgB,EAAEC,gBAAgB,EAAEC,oBAAoB;AACxD;AACA;AACA;AACA,IAAII,kCAAkC,GAAGP,eAAe;AACxD,eAAeO,kCAAkC;AACjD"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}