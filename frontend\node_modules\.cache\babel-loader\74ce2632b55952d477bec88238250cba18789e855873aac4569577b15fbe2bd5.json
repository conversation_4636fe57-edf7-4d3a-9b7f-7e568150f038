{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport * as constants from 'focus-lock/constants';\nimport { inlineProp } from './util';\nvar FreeFocusInside = function FreeFocusInside(_ref) {\n  var children = _ref.children,\n    className = _ref.className;\n  return /*#__PURE__*/React.createElement(\"div\", _extends({}, inlineProp(constants.FOCUS_ALLOW, true), {\n    className: className\n  }), children);\n};\nFreeFocusInside.propTypes = process.env.NODE_ENV !== \"production\" ? {\n  children: PropTypes.node.isRequired,\n  className: PropTypes.string\n} : {};\nFreeFocusInside.defaultProps = {\n  className: undefined\n};\nexport default FreeFocusInside;", "map": {"version": 3, "names": ["_extends", "React", "PropTypes", "constants", "inlineProp", "FreeFocusInside", "_ref", "children", "className", "createElement", "FOCUS_ALLOW", "propTypes", "process", "env", "NODE_ENV", "node", "isRequired", "string", "defaultProps", "undefined"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/react-focus-lock/dist/es2015/FreeFocusInside.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport * as constants from 'focus-lock/constants';\nimport { inlineProp } from './util';\n\nvar FreeFocusInside = function FreeFocusInside(_ref) {\n  var children = _ref.children,\n      className = _ref.className;\n  return /*#__PURE__*/React.createElement(\"div\", _extends({}, inlineProp(constants.FOCUS_ALLOW, true), {\n    className: className\n  }), children);\n};\n\nFreeFocusInside.propTypes = process.env.NODE_ENV !== \"production\" ? {\n  children: PropTypes.node.isRequired,\n  className: PropTypes.string\n} : {};\nFreeFocusInside.defaultProps = {\n  className: undefined\n};\nexport default FreeFocusInside;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAO,KAAKC,SAAS,MAAM,sBAAsB;AACjD,SAASC,UAAU,QAAQ,QAAQ;AAEnC,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAACC,IAAI,EAAE;EACnD,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;IACxBC,SAAS,GAAGF,IAAI,CAACE,SAAS;EAC9B,OAAO,aAAaP,KAAK,CAACQ,aAAa,CAAC,KAAK,EAAET,QAAQ,CAAC,CAAC,CAAC,EAAEI,UAAU,CAACD,SAAS,CAACO,WAAW,EAAE,IAAI,CAAC,EAAE;IACnGF,SAAS,EAAEA;EACb,CAAC,CAAC,EAAED,QAAQ,CAAC;AACf,CAAC;AAEDF,eAAe,CAACM,SAAS,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG;EAClEP,QAAQ,EAAEL,SAAS,CAACa,IAAI,CAACC,UAAU;EACnCR,SAAS,EAAEN,SAAS,CAACe;AACvB,CAAC,GAAG,CAAC,CAAC;AACNZ,eAAe,CAACa,YAAY,GAAG;EAC7BV,SAAS,EAAEW;AACb,CAAC;AACD,eAAed,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}