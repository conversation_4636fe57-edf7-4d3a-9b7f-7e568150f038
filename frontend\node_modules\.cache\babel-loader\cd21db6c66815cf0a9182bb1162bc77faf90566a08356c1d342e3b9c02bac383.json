{"ast": null, "code": "export { assignRef } from './assignRef';\n// callback ref\nexport { useCallbackRef } from './useRef';\nexport { createCallbackRef } from './createRef';\n// merge ref\nexport { mergeRefs } from './mergeRef';\nexport { useMergeRefs } from './useMergeRef';\n// transform ref\nexport { useTransformRef } from './useTransformRef';\nexport { transformRef } from './transformRef';\n// refToCallback\nexport { refToCallback, useRefToCallback } from './refToCallback';", "map": {"version": 3, "names": ["assignRef", "useCallbackRef", "createCallbackRef", "mergeRefs", "useMergeRefs", "useTransformRef", "transformRef", "ref<PERSON><PERSON><PERSON><PERSON><PERSON>", "useRefToCallback"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/use-callback-ref/dist/es2015/index.js"], "sourcesContent": ["export { assignRef } from './assignRef';\n// callback ref\nexport { useCallbackRef } from './useRef';\nexport { createCallbackRef } from './createRef';\n// merge ref\nexport { mergeRefs } from './mergeRef';\nexport { useMergeRefs } from './useMergeRef';\n// transform ref\nexport { useTransformRef } from './useTransformRef';\nexport { transformRef } from './transformRef';\n// refToCallback\nexport { refToCallback, useRefToCallback } from './refToCallback';\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,aAAa;AACvC;AACA,SAASC,cAAc,QAAQ,UAAU;AACzC,SAASC,iBAAiB,QAAQ,aAAa;AAC/C;AACA,SAASC,SAAS,QAAQ,YAAY;AACtC,SAASC,YAAY,QAAQ,eAAe;AAC5C;AACA,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C;AACA,SAASC,aAAa,EAAEC,gBAAgB,QAAQ,iBAAiB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}