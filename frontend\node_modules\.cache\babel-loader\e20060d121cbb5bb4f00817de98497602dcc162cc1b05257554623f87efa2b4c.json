{"ast": null, "code": "import { Socket as Engine, installTimerFunctions, nextTick } from \"engine.io-client\";\nimport { Socket } from \"./socket.js\";\nimport * as parser from \"socket.io-parser\";\nimport { on } from \"./on.js\";\nimport { Backoff } from \"./contrib/backo2.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nexport class Manager extends Emitter {\n  constructor(uri, opts) {\n    var _a;\n    super();\n    this.nsps = {};\n    this.subs = [];\n    if (uri && \"object\" === typeof uri) {\n      opts = uri;\n      uri = undefined;\n    }\n    opts = opts || {};\n    opts.path = opts.path || \"/socket.io\";\n    this.opts = opts;\n    installTimerFunctions(this, opts);\n    this.reconnection(opts.reconnection !== false);\n    this.reconnectionAttempts(opts.reconnectionAttempts || Infinity);\n    this.reconnectionDelay(opts.reconnectionDelay || 1000);\n    this.reconnectionDelayMax(opts.reconnectionDelayMax || 5000);\n    this.randomizationFactor((_a = opts.randomizationFactor) !== null && _a !== void 0 ? _a : 0.5);\n    this.backoff = new Backoff({\n      min: this.reconnectionDelay(),\n      max: this.reconnectionDelayMax(),\n      jitter: this.randomizationFactor()\n    });\n    this.timeout(null == opts.timeout ? 20000 : opts.timeout);\n    this._readyState = \"closed\";\n    this.uri = uri;\n    const _parser = opts.parser || parser;\n    this.encoder = new _parser.Encoder();\n    this.decoder = new _parser.Decoder();\n    this._autoConnect = opts.autoConnect !== false;\n    if (this._autoConnect) this.open();\n  }\n  reconnection(v) {\n    if (!arguments.length) return this._reconnection;\n    this._reconnection = !!v;\n    return this;\n  }\n  reconnectionAttempts(v) {\n    if (v === undefined) return this._reconnectionAttempts;\n    this._reconnectionAttempts = v;\n    return this;\n  }\n  reconnectionDelay(v) {\n    var _a;\n    if (v === undefined) return this._reconnectionDelay;\n    this._reconnectionDelay = v;\n    (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMin(v);\n    return this;\n  }\n  randomizationFactor(v) {\n    var _a;\n    if (v === undefined) return this._randomizationFactor;\n    this._randomizationFactor = v;\n    (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setJitter(v);\n    return this;\n  }\n  reconnectionDelayMax(v) {\n    var _a;\n    if (v === undefined) return this._reconnectionDelayMax;\n    this._reconnectionDelayMax = v;\n    (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMax(v);\n    return this;\n  }\n  timeout(v) {\n    if (!arguments.length) return this._timeout;\n    this._timeout = v;\n    return this;\n  }\n  /**\n   * Starts trying to reconnect if reconnection is enabled and we have not\n   * started reconnecting yet\n   *\n   * @private\n   */\n  maybeReconnectOnOpen() {\n    // Only try to reconnect if it's the first time we're connecting\n    if (!this._reconnecting && this._reconnection && this.backoff.attempts === 0) {\n      // keeps reconnection from firing twice for the same reconnection loop\n      this.reconnect();\n    }\n  }\n  /**\n   * Sets the current transport `socket`.\n   *\n   * @param {Function} fn - optional, callback\n   * @return self\n   * @public\n   */\n  open(fn) {\n    if (~this._readyState.indexOf(\"open\")) return this;\n    this.engine = new Engine(this.uri, this.opts);\n    const socket = this.engine;\n    const self = this;\n    this._readyState = \"opening\";\n    this.skipReconnect = false;\n    // emit `open`\n    const openSubDestroy = on(socket, \"open\", function () {\n      self.onopen();\n      fn && fn();\n    });\n    const onError = err => {\n      this.cleanup();\n      this._readyState = \"closed\";\n      this.emitReserved(\"error\", err);\n      if (fn) {\n        fn(err);\n      } else {\n        // Only do this if there is no fn to handle the error\n        this.maybeReconnectOnOpen();\n      }\n    };\n    // emit `error`\n    const errorSub = on(socket, \"error\", onError);\n    if (false !== this._timeout) {\n      const timeout = this._timeout;\n      // set timer\n      const timer = this.setTimeoutFn(() => {\n        openSubDestroy();\n        onError(new Error(\"timeout\"));\n        socket.close();\n      }, timeout);\n      if (this.opts.autoUnref) {\n        timer.unref();\n      }\n      this.subs.push(() => {\n        this.clearTimeoutFn(timer);\n      });\n    }\n    this.subs.push(openSubDestroy);\n    this.subs.push(errorSub);\n    return this;\n  }\n  /**\n   * Alias for open()\n   *\n   * @return self\n   * @public\n   */\n  connect(fn) {\n    return this.open(fn);\n  }\n  /**\n   * Called upon transport open.\n   *\n   * @private\n   */\n  onopen() {\n    // clear old subs\n    this.cleanup();\n    // mark as open\n    this._readyState = \"open\";\n    this.emitReserved(\"open\");\n    // add new subs\n    const socket = this.engine;\n    this.subs.push(on(socket, \"ping\", this.onping.bind(this)), on(socket, \"data\", this.ondata.bind(this)), on(socket, \"error\", this.onerror.bind(this)), on(socket, \"close\", this.onclose.bind(this)), on(this.decoder, \"decoded\", this.ondecoded.bind(this)));\n  }\n  /**\n   * Called upon a ping.\n   *\n   * @private\n   */\n  onping() {\n    this.emitReserved(\"ping\");\n  }\n  /**\n   * Called with data.\n   *\n   * @private\n   */\n  ondata(data) {\n    try {\n      this.decoder.add(data);\n    } catch (e) {\n      this.onclose(\"parse error\", e);\n    }\n  }\n  /**\n   * Called when parser fully decodes a packet.\n   *\n   * @private\n   */\n  ondecoded(packet) {\n    // the nextTick call prevents an exception in a user-provided event listener from triggering a disconnection due to a \"parse error\"\n    nextTick(() => {\n      this.emitReserved(\"packet\", packet);\n    }, this.setTimeoutFn);\n  }\n  /**\n   * Called upon socket error.\n   *\n   * @private\n   */\n  onerror(err) {\n    this.emitReserved(\"error\", err);\n  }\n  /**\n   * Creates a new socket for the given `nsp`.\n   *\n   * @return {Socket}\n   * @public\n   */\n  socket(nsp, opts) {\n    let socket = this.nsps[nsp];\n    if (!socket) {\n      socket = new Socket(this, nsp, opts);\n      this.nsps[nsp] = socket;\n    } else if (this._autoConnect && !socket.active) {\n      socket.connect();\n    }\n    return socket;\n  }\n  /**\n   * Called upon a socket close.\n   *\n   * @param socket\n   * @private\n   */\n  _destroy(socket) {\n    const nsps = Object.keys(this.nsps);\n    for (const nsp of nsps) {\n      const socket = this.nsps[nsp];\n      if (socket.active) {\n        return;\n      }\n    }\n    this._close();\n  }\n  /**\n   * Writes a packet.\n   *\n   * @param packet\n   * @private\n   */\n  _packet(packet) {\n    const encodedPackets = this.encoder.encode(packet);\n    for (let i = 0; i < encodedPackets.length; i++) {\n      this.engine.write(encodedPackets[i], packet.options);\n    }\n  }\n  /**\n   * Clean up transport subscriptions and packet buffer.\n   *\n   * @private\n   */\n  cleanup() {\n    this.subs.forEach(subDestroy => subDestroy());\n    this.subs.length = 0;\n    this.decoder.destroy();\n  }\n  /**\n   * Close the current socket.\n   *\n   * @private\n   */\n  _close() {\n    this.skipReconnect = true;\n    this._reconnecting = false;\n    this.onclose(\"forced close\");\n    if (this.engine) this.engine.close();\n  }\n  /**\n   * Alias for close()\n   *\n   * @private\n   */\n  disconnect() {\n    return this._close();\n  }\n  /**\n   * Called upon engine close.\n   *\n   * @private\n   */\n  onclose(reason, description) {\n    this.cleanup();\n    this.backoff.reset();\n    this._readyState = \"closed\";\n    this.emitReserved(\"close\", reason, description);\n    if (this._reconnection && !this.skipReconnect) {\n      this.reconnect();\n    }\n  }\n  /**\n   * Attempt a reconnection.\n   *\n   * @private\n   */\n  reconnect() {\n    if (this._reconnecting || this.skipReconnect) return this;\n    const self = this;\n    if (this.backoff.attempts >= this._reconnectionAttempts) {\n      this.backoff.reset();\n      this.emitReserved(\"reconnect_failed\");\n      this._reconnecting = false;\n    } else {\n      const delay = this.backoff.duration();\n      this._reconnecting = true;\n      const timer = this.setTimeoutFn(() => {\n        if (self.skipReconnect) return;\n        this.emitReserved(\"reconnect_attempt\", self.backoff.attempts);\n        // check again for the case socket closed in above events\n        if (self.skipReconnect) return;\n        self.open(err => {\n          if (err) {\n            self._reconnecting = false;\n            self.reconnect();\n            this.emitReserved(\"reconnect_error\", err);\n          } else {\n            self.onreconnect();\n          }\n        });\n      }, delay);\n      if (this.opts.autoUnref) {\n        timer.unref();\n      }\n      this.subs.push(() => {\n        this.clearTimeoutFn(timer);\n      });\n    }\n  }\n  /**\n   * Called upon successful reconnect.\n   *\n   * @private\n   */\n  onreconnect() {\n    const attempt = this.backoff.attempts;\n    this._reconnecting = false;\n    this.backoff.reset();\n    this.emitReserved(\"reconnect\", attempt);\n  }\n}", "map": {"version": 3, "names": ["Socket", "Engine", "installTimerFunctions", "nextTick", "parser", "on", "Backoff", "Emitter", "Manager", "constructor", "uri", "opts", "_a", "nsps", "subs", "undefined", "path", "reconnection", "reconnectionAttempts", "Infinity", "reconnectionDelay", "reconnectionDelayMax", "randomizationFactor", "backoff", "min", "max", "jitter", "timeout", "_readyState", "_parser", "encoder", "Encoder", "decoder", "Decoder", "_autoConnect", "autoConnect", "open", "v", "arguments", "length", "_reconnection", "_reconnectionAttempts", "_reconnectionDelay", "setMin", "_randomizationFactor", "setJitter", "_reconnectionDelayMax", "setMax", "_timeout", "maybeReconnectOnOpen", "_reconnecting", "attempts", "reconnect", "fn", "indexOf", "engine", "socket", "self", "skipReconnect", "openSubDestroy", "onopen", "onError", "err", "cleanup", "emit<PERSON><PERSON><PERSON><PERSON>", "errorSub", "timer", "setTimeoutFn", "Error", "close", "autoUnref", "unref", "push", "clearTimeoutFn", "connect", "onping", "bind", "ondata", "onerror", "onclose", "ondecoded", "data", "add", "e", "packet", "nsp", "active", "_destroy", "Object", "keys", "_close", "_packet", "encodedPackets", "encode", "i", "write", "options", "for<PERSON>ach", "subDestroy", "destroy", "disconnect", "reason", "description", "reset", "delay", "duration", "onreconnect", "attempt"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/socket.io-client/build/esm/manager.js"], "sourcesContent": ["import { Socket as Engine, installTimerFunctions, nextTick, } from \"engine.io-client\";\nimport { Socket } from \"./socket.js\";\nimport * as parser from \"socket.io-parser\";\nimport { on } from \"./on.js\";\nimport { Backoff } from \"./contrib/backo2.js\";\nimport { Emitter, } from \"@socket.io/component-emitter\";\nexport class Manager extends Emitter {\n    constructor(uri, opts) {\n        var _a;\n        super();\n        this.nsps = {};\n        this.subs = [];\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = undefined;\n        }\n        opts = opts || {};\n        opts.path = opts.path || \"/socket.io\";\n        this.opts = opts;\n        installTimerFunctions(this, opts);\n        this.reconnection(opts.reconnection !== false);\n        this.reconnectionAttempts(opts.reconnectionAttempts || Infinity);\n        this.reconnectionDelay(opts.reconnectionDelay || 1000);\n        this.reconnectionDelayMax(opts.reconnectionDelayMax || 5000);\n        this.randomizationFactor((_a = opts.randomizationFactor) !== null && _a !== void 0 ? _a : 0.5);\n        this.backoff = new Backoff({\n            min: this.reconnectionDelay(),\n            max: this.reconnectionDelayMax(),\n            jitter: this.randomizationFactor(),\n        });\n        this.timeout(null == opts.timeout ? 20000 : opts.timeout);\n        this._readyState = \"closed\";\n        this.uri = uri;\n        const _parser = opts.parser || parser;\n        this.encoder = new _parser.Encoder();\n        this.decoder = new _parser.Decoder();\n        this._autoConnect = opts.autoConnect !== false;\n        if (this._autoConnect)\n            this.open();\n    }\n    reconnection(v) {\n        if (!arguments.length)\n            return this._reconnection;\n        this._reconnection = !!v;\n        return this;\n    }\n    reconnectionAttempts(v) {\n        if (v === undefined)\n            return this._reconnectionAttempts;\n        this._reconnectionAttempts = v;\n        return this;\n    }\n    reconnectionDelay(v) {\n        var _a;\n        if (v === undefined)\n            return this._reconnectionDelay;\n        this._reconnectionDelay = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMin(v);\n        return this;\n    }\n    randomizationFactor(v) {\n        var _a;\n        if (v === undefined)\n            return this._randomizationFactor;\n        this._randomizationFactor = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setJitter(v);\n        return this;\n    }\n    reconnectionDelayMax(v) {\n        var _a;\n        if (v === undefined)\n            return this._reconnectionDelayMax;\n        this._reconnectionDelayMax = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMax(v);\n        return this;\n    }\n    timeout(v) {\n        if (!arguments.length)\n            return this._timeout;\n        this._timeout = v;\n        return this;\n    }\n    /**\n     * Starts trying to reconnect if reconnection is enabled and we have not\n     * started reconnecting yet\n     *\n     * @private\n     */\n    maybeReconnectOnOpen() {\n        // Only try to reconnect if it's the first time we're connecting\n        if (!this._reconnecting &&\n            this._reconnection &&\n            this.backoff.attempts === 0) {\n            // keeps reconnection from firing twice for the same reconnection loop\n            this.reconnect();\n        }\n    }\n    /**\n     * Sets the current transport `socket`.\n     *\n     * @param {Function} fn - optional, callback\n     * @return self\n     * @public\n     */\n    open(fn) {\n        if (~this._readyState.indexOf(\"open\"))\n            return this;\n        this.engine = new Engine(this.uri, this.opts);\n        const socket = this.engine;\n        const self = this;\n        this._readyState = \"opening\";\n        this.skipReconnect = false;\n        // emit `open`\n        const openSubDestroy = on(socket, \"open\", function () {\n            self.onopen();\n            fn && fn();\n        });\n        const onError = (err) => {\n            this.cleanup();\n            this._readyState = \"closed\";\n            this.emitReserved(\"error\", err);\n            if (fn) {\n                fn(err);\n            }\n            else {\n                // Only do this if there is no fn to handle the error\n                this.maybeReconnectOnOpen();\n            }\n        };\n        // emit `error`\n        const errorSub = on(socket, \"error\", onError);\n        if (false !== this._timeout) {\n            const timeout = this._timeout;\n            // set timer\n            const timer = this.setTimeoutFn(() => {\n                openSubDestroy();\n                onError(new Error(\"timeout\"));\n                socket.close();\n            }, timeout);\n            if (this.opts.autoUnref) {\n                timer.unref();\n            }\n            this.subs.push(() => {\n                this.clearTimeoutFn(timer);\n            });\n        }\n        this.subs.push(openSubDestroy);\n        this.subs.push(errorSub);\n        return this;\n    }\n    /**\n     * Alias for open()\n     *\n     * @return self\n     * @public\n     */\n    connect(fn) {\n        return this.open(fn);\n    }\n    /**\n     * Called upon transport open.\n     *\n     * @private\n     */\n    onopen() {\n        // clear old subs\n        this.cleanup();\n        // mark as open\n        this._readyState = \"open\";\n        this.emitReserved(\"open\");\n        // add new subs\n        const socket = this.engine;\n        this.subs.push(on(socket, \"ping\", this.onping.bind(this)), on(socket, \"data\", this.ondata.bind(this)), on(socket, \"error\", this.onerror.bind(this)), on(socket, \"close\", this.onclose.bind(this)), on(this.decoder, \"decoded\", this.ondecoded.bind(this)));\n    }\n    /**\n     * Called upon a ping.\n     *\n     * @private\n     */\n    onping() {\n        this.emitReserved(\"ping\");\n    }\n    /**\n     * Called with data.\n     *\n     * @private\n     */\n    ondata(data) {\n        try {\n            this.decoder.add(data);\n        }\n        catch (e) {\n            this.onclose(\"parse error\", e);\n        }\n    }\n    /**\n     * Called when parser fully decodes a packet.\n     *\n     * @private\n     */\n    ondecoded(packet) {\n        // the nextTick call prevents an exception in a user-provided event listener from triggering a disconnection due to a \"parse error\"\n        nextTick(() => {\n            this.emitReserved(\"packet\", packet);\n        }, this.setTimeoutFn);\n    }\n    /**\n     * Called upon socket error.\n     *\n     * @private\n     */\n    onerror(err) {\n        this.emitReserved(\"error\", err);\n    }\n    /**\n     * Creates a new socket for the given `nsp`.\n     *\n     * @return {Socket}\n     * @public\n     */\n    socket(nsp, opts) {\n        let socket = this.nsps[nsp];\n        if (!socket) {\n            socket = new Socket(this, nsp, opts);\n            this.nsps[nsp] = socket;\n        }\n        else if (this._autoConnect && !socket.active) {\n            socket.connect();\n        }\n        return socket;\n    }\n    /**\n     * Called upon a socket close.\n     *\n     * @param socket\n     * @private\n     */\n    _destroy(socket) {\n        const nsps = Object.keys(this.nsps);\n        for (const nsp of nsps) {\n            const socket = this.nsps[nsp];\n            if (socket.active) {\n                return;\n            }\n        }\n        this._close();\n    }\n    /**\n     * Writes a packet.\n     *\n     * @param packet\n     * @private\n     */\n    _packet(packet) {\n        const encodedPackets = this.encoder.encode(packet);\n        for (let i = 0; i < encodedPackets.length; i++) {\n            this.engine.write(encodedPackets[i], packet.options);\n        }\n    }\n    /**\n     * Clean up transport subscriptions and packet buffer.\n     *\n     * @private\n     */\n    cleanup() {\n        this.subs.forEach((subDestroy) => subDestroy());\n        this.subs.length = 0;\n        this.decoder.destroy();\n    }\n    /**\n     * Close the current socket.\n     *\n     * @private\n     */\n    _close() {\n        this.skipReconnect = true;\n        this._reconnecting = false;\n        this.onclose(\"forced close\");\n        if (this.engine)\n            this.engine.close();\n    }\n    /**\n     * Alias for close()\n     *\n     * @private\n     */\n    disconnect() {\n        return this._close();\n    }\n    /**\n     * Called upon engine close.\n     *\n     * @private\n     */\n    onclose(reason, description) {\n        this.cleanup();\n        this.backoff.reset();\n        this._readyState = \"closed\";\n        this.emitReserved(\"close\", reason, description);\n        if (this._reconnection && !this.skipReconnect) {\n            this.reconnect();\n        }\n    }\n    /**\n     * Attempt a reconnection.\n     *\n     * @private\n     */\n    reconnect() {\n        if (this._reconnecting || this.skipReconnect)\n            return this;\n        const self = this;\n        if (this.backoff.attempts >= this._reconnectionAttempts) {\n            this.backoff.reset();\n            this.emitReserved(\"reconnect_failed\");\n            this._reconnecting = false;\n        }\n        else {\n            const delay = this.backoff.duration();\n            this._reconnecting = true;\n            const timer = this.setTimeoutFn(() => {\n                if (self.skipReconnect)\n                    return;\n                this.emitReserved(\"reconnect_attempt\", self.backoff.attempts);\n                // check again for the case socket closed in above events\n                if (self.skipReconnect)\n                    return;\n                self.open((err) => {\n                    if (err) {\n                        self._reconnecting = false;\n                        self.reconnect();\n                        this.emitReserved(\"reconnect_error\", err);\n                    }\n                    else {\n                        self.onreconnect();\n                    }\n                });\n            }, delay);\n            if (this.opts.autoUnref) {\n                timer.unref();\n            }\n            this.subs.push(() => {\n                this.clearTimeoutFn(timer);\n            });\n        }\n    }\n    /**\n     * Called upon successful reconnect.\n     *\n     * @private\n     */\n    onreconnect() {\n        const attempt = this.backoff.attempts;\n        this._reconnecting = false;\n        this.backoff.reset();\n        this.emitReserved(\"reconnect\", attempt);\n    }\n}\n"], "mappings": "AAAA,SAASA,MAAM,IAAIC,MAAM,EAAEC,qBAAqB,EAAEC,QAAQ,QAAS,kBAAkB;AACrF,SAASH,MAAM,QAAQ,aAAa;AACpC,OAAO,KAAKI,MAAM,MAAM,kBAAkB;AAC1C,SAASC,EAAE,QAAQ,SAAS;AAC5B,SAASC,OAAO,QAAQ,qBAAqB;AAC7C,SAASC,OAAO,QAAS,8BAA8B;AACvD,OAAO,MAAMC,OAAO,SAASD,OAAO,CAAC;EACjCE,WAAWA,CAACC,GAAG,EAAEC,IAAI,EAAE;IACnB,IAAIC,EAAE;IACN,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,IAAI,GAAG,CAAC,CAAC;IACd,IAAI,CAACC,IAAI,GAAG,EAAE;IACd,IAAIJ,GAAG,IAAI,QAAQ,KAAK,OAAOA,GAAG,EAAE;MAChCC,IAAI,GAAGD,GAAG;MACVA,GAAG,GAAGK,SAAS;IACnB;IACAJ,IAAI,GAAGA,IAAI,IAAI,CAAC,CAAC;IACjBA,IAAI,CAACK,IAAI,GAAGL,IAAI,CAACK,IAAI,IAAI,YAAY;IACrC,IAAI,CAACL,IAAI,GAAGA,IAAI;IAChBT,qBAAqB,CAAC,IAAI,EAAES,IAAI,CAAC;IACjC,IAAI,CAACM,YAAY,CAACN,IAAI,CAACM,YAAY,KAAK,KAAK,CAAC;IAC9C,IAAI,CAACC,oBAAoB,CAACP,IAAI,CAACO,oBAAoB,IAAIC,QAAQ,CAAC;IAChE,IAAI,CAACC,iBAAiB,CAACT,IAAI,CAACS,iBAAiB,IAAI,IAAI,CAAC;IACtD,IAAI,CAACC,oBAAoB,CAACV,IAAI,CAACU,oBAAoB,IAAI,IAAI,CAAC;IAC5D,IAAI,CAACC,mBAAmB,CAAC,CAACV,EAAE,GAAGD,IAAI,CAACW,mBAAmB,MAAM,IAAI,IAAIV,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,GAAG,CAAC;IAC9F,IAAI,CAACW,OAAO,GAAG,IAAIjB,OAAO,CAAC;MACvBkB,GAAG,EAAE,IAAI,CAACJ,iBAAiB,CAAC,CAAC;MAC7BK,GAAG,EAAE,IAAI,CAACJ,oBAAoB,CAAC,CAAC;MAChCK,MAAM,EAAE,IAAI,CAACJ,mBAAmB,CAAC;IACrC,CAAC,CAAC;IACF,IAAI,CAACK,OAAO,CAAC,IAAI,IAAIhB,IAAI,CAACgB,OAAO,GAAG,KAAK,GAAGhB,IAAI,CAACgB,OAAO,CAAC;IACzD,IAAI,CAACC,WAAW,GAAG,QAAQ;IAC3B,IAAI,CAAClB,GAAG,GAAGA,GAAG;IACd,MAAMmB,OAAO,GAAGlB,IAAI,CAACP,MAAM,IAAIA,MAAM;IACrC,IAAI,CAAC0B,OAAO,GAAG,IAAID,OAAO,CAACE,OAAO,CAAC,CAAC;IACpC,IAAI,CAACC,OAAO,GAAG,IAAIH,OAAO,CAACI,OAAO,CAAC,CAAC;IACpC,IAAI,CAACC,YAAY,GAAGvB,IAAI,CAACwB,WAAW,KAAK,KAAK;IAC9C,IAAI,IAAI,CAACD,YAAY,EACjB,IAAI,CAACE,IAAI,CAAC,CAAC;EACnB;EACAnB,YAAYA,CAACoB,CAAC,EAAE;IACZ,IAAI,CAACC,SAAS,CAACC,MAAM,EACjB,OAAO,IAAI,CAACC,aAAa;IAC7B,IAAI,CAACA,aAAa,GAAG,CAAC,CAACH,CAAC;IACxB,OAAO,IAAI;EACf;EACAnB,oBAAoBA,CAACmB,CAAC,EAAE;IACpB,IAAIA,CAAC,KAAKtB,SAAS,EACf,OAAO,IAAI,CAAC0B,qBAAqB;IACrC,IAAI,CAACA,qBAAqB,GAAGJ,CAAC;IAC9B,OAAO,IAAI;EACf;EACAjB,iBAAiBA,CAACiB,CAAC,EAAE;IACjB,IAAIzB,EAAE;IACN,IAAIyB,CAAC,KAAKtB,SAAS,EACf,OAAO,IAAI,CAAC2B,kBAAkB;IAClC,IAAI,CAACA,kBAAkB,GAAGL,CAAC;IAC3B,CAACzB,EAAE,GAAG,IAAI,CAACW,OAAO,MAAM,IAAI,IAAIX,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC+B,MAAM,CAACN,CAAC,CAAC;IACrE,OAAO,IAAI;EACf;EACAf,mBAAmBA,CAACe,CAAC,EAAE;IACnB,IAAIzB,EAAE;IACN,IAAIyB,CAAC,KAAKtB,SAAS,EACf,OAAO,IAAI,CAAC6B,oBAAoB;IACpC,IAAI,CAACA,oBAAoB,GAAGP,CAAC;IAC7B,CAACzB,EAAE,GAAG,IAAI,CAACW,OAAO,MAAM,IAAI,IAAIX,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACiC,SAAS,CAACR,CAAC,CAAC;IACxE,OAAO,IAAI;EACf;EACAhB,oBAAoBA,CAACgB,CAAC,EAAE;IACpB,IAAIzB,EAAE;IACN,IAAIyB,CAAC,KAAKtB,SAAS,EACf,OAAO,IAAI,CAAC+B,qBAAqB;IACrC,IAAI,CAACA,qBAAqB,GAAGT,CAAC;IAC9B,CAACzB,EAAE,GAAG,IAAI,CAACW,OAAO,MAAM,IAAI,IAAIX,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACmC,MAAM,CAACV,CAAC,CAAC;IACrE,OAAO,IAAI;EACf;EACAV,OAAOA,CAACU,CAAC,EAAE;IACP,IAAI,CAACC,SAAS,CAACC,MAAM,EACjB,OAAO,IAAI,CAACS,QAAQ;IACxB,IAAI,CAACA,QAAQ,GAAGX,CAAC;IACjB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;EACIY,oBAAoBA,CAAA,EAAG;IACnB;IACA,IAAI,CAAC,IAAI,CAACC,aAAa,IACnB,IAAI,CAACV,aAAa,IAClB,IAAI,CAACjB,OAAO,CAAC4B,QAAQ,KAAK,CAAC,EAAE;MAC7B;MACA,IAAI,CAACC,SAAS,CAAC,CAAC;IACpB;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIhB,IAAIA,CAACiB,EAAE,EAAE;IACL,IAAI,CAAC,IAAI,CAACzB,WAAW,CAAC0B,OAAO,CAAC,MAAM,CAAC,EACjC,OAAO,IAAI;IACf,IAAI,CAACC,MAAM,GAAG,IAAItD,MAAM,CAAC,IAAI,CAACS,GAAG,EAAE,IAAI,CAACC,IAAI,CAAC;IAC7C,MAAM6C,MAAM,GAAG,IAAI,CAACD,MAAM;IAC1B,MAAME,IAAI,GAAG,IAAI;IACjB,IAAI,CAAC7B,WAAW,GAAG,SAAS;IAC5B,IAAI,CAAC8B,aAAa,GAAG,KAAK;IAC1B;IACA,MAAMC,cAAc,GAAGtD,EAAE,CAACmD,MAAM,EAAE,MAAM,EAAE,YAAY;MAClDC,IAAI,CAACG,MAAM,CAAC,CAAC;MACbP,EAAE,IAAIA,EAAE,CAAC,CAAC;IACd,CAAC,CAAC;IACF,MAAMQ,OAAO,GAAIC,GAAG,IAAK;MACrB,IAAI,CAACC,OAAO,CAAC,CAAC;MACd,IAAI,CAACnC,WAAW,GAAG,QAAQ;MAC3B,IAAI,CAACoC,YAAY,CAAC,OAAO,EAAEF,GAAG,CAAC;MAC/B,IAAIT,EAAE,EAAE;QACJA,EAAE,CAACS,GAAG,CAAC;MACX,CAAC,MACI;QACD;QACA,IAAI,CAACb,oBAAoB,CAAC,CAAC;MAC/B;IACJ,CAAC;IACD;IACA,MAAMgB,QAAQ,GAAG5D,EAAE,CAACmD,MAAM,EAAE,OAAO,EAAEK,OAAO,CAAC;IAC7C,IAAI,KAAK,KAAK,IAAI,CAACb,QAAQ,EAAE;MACzB,MAAMrB,OAAO,GAAG,IAAI,CAACqB,QAAQ;MAC7B;MACA,MAAMkB,KAAK,GAAG,IAAI,CAACC,YAAY,CAAC,MAAM;QAClCR,cAAc,CAAC,CAAC;QAChBE,OAAO,CAAC,IAAIO,KAAK,CAAC,SAAS,CAAC,CAAC;QAC7BZ,MAAM,CAACa,KAAK,CAAC,CAAC;MAClB,CAAC,EAAE1C,OAAO,CAAC;MACX,IAAI,IAAI,CAAChB,IAAI,CAAC2D,SAAS,EAAE;QACrBJ,KAAK,CAACK,KAAK,CAAC,CAAC;MACjB;MACA,IAAI,CAACzD,IAAI,CAAC0D,IAAI,CAAC,MAAM;QACjB,IAAI,CAACC,cAAc,CAACP,KAAK,CAAC;MAC9B,CAAC,CAAC;IACN;IACA,IAAI,CAACpD,IAAI,CAAC0D,IAAI,CAACb,cAAc,CAAC;IAC9B,IAAI,CAAC7C,IAAI,CAAC0D,IAAI,CAACP,QAAQ,CAAC;IACxB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;EACIS,OAAOA,CAACrB,EAAE,EAAE;IACR,OAAO,IAAI,CAACjB,IAAI,CAACiB,EAAE,CAAC;EACxB;EACA;AACJ;AACA;AACA;AACA;EACIO,MAAMA,CAAA,EAAG;IACL;IACA,IAAI,CAACG,OAAO,CAAC,CAAC;IACd;IACA,IAAI,CAACnC,WAAW,GAAG,MAAM;IACzB,IAAI,CAACoC,YAAY,CAAC,MAAM,CAAC;IACzB;IACA,MAAMR,MAAM,GAAG,IAAI,CAACD,MAAM;IAC1B,IAAI,CAACzC,IAAI,CAAC0D,IAAI,CAACnE,EAAE,CAACmD,MAAM,EAAE,MAAM,EAAE,IAAI,CAACmB,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAEvE,EAAE,CAACmD,MAAM,EAAE,MAAM,EAAE,IAAI,CAACqB,MAAM,CAACD,IAAI,CAAC,IAAI,CAAC,CAAC,EAAEvE,EAAE,CAACmD,MAAM,EAAE,OAAO,EAAE,IAAI,CAACsB,OAAO,CAACF,IAAI,CAAC,IAAI,CAAC,CAAC,EAAEvE,EAAE,CAACmD,MAAM,EAAE,OAAO,EAAE,IAAI,CAACuB,OAAO,CAACH,IAAI,CAAC,IAAI,CAAC,CAAC,EAAEvE,EAAE,CAAC,IAAI,CAAC2B,OAAO,EAAE,SAAS,EAAE,IAAI,CAACgD,SAAS,CAACJ,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;EAC9P;EACA;AACJ;AACA;AACA;AACA;EACID,MAAMA,CAAA,EAAG;IACL,IAAI,CAACX,YAAY,CAAC,MAAM,CAAC;EAC7B;EACA;AACJ;AACA;AACA;AACA;EACIa,MAAMA,CAACI,IAAI,EAAE;IACT,IAAI;MACA,IAAI,CAACjD,OAAO,CAACkD,GAAG,CAACD,IAAI,CAAC;IAC1B,CAAC,CACD,OAAOE,CAAC,EAAE;MACN,IAAI,CAACJ,OAAO,CAAC,aAAa,EAAEI,CAAC,CAAC;IAClC;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIH,SAASA,CAACI,MAAM,EAAE;IACd;IACAjF,QAAQ,CAAC,MAAM;MACX,IAAI,CAAC6D,YAAY,CAAC,QAAQ,EAAEoB,MAAM,CAAC;IACvC,CAAC,EAAE,IAAI,CAACjB,YAAY,CAAC;EACzB;EACA;AACJ;AACA;AACA;AACA;EACIW,OAAOA,CAAChB,GAAG,EAAE;IACT,IAAI,CAACE,YAAY,CAAC,OAAO,EAAEF,GAAG,CAAC;EACnC;EACA;AACJ;AACA;AACA;AACA;AACA;EACIN,MAAMA,CAAC6B,GAAG,EAAE1E,IAAI,EAAE;IACd,IAAI6C,MAAM,GAAG,IAAI,CAAC3C,IAAI,CAACwE,GAAG,CAAC;IAC3B,IAAI,CAAC7B,MAAM,EAAE;MACTA,MAAM,GAAG,IAAIxD,MAAM,CAAC,IAAI,EAAEqF,GAAG,EAAE1E,IAAI,CAAC;MACpC,IAAI,CAACE,IAAI,CAACwE,GAAG,CAAC,GAAG7B,MAAM;IAC3B,CAAC,MACI,IAAI,IAAI,CAACtB,YAAY,IAAI,CAACsB,MAAM,CAAC8B,MAAM,EAAE;MAC1C9B,MAAM,CAACkB,OAAO,CAAC,CAAC;IACpB;IACA,OAAOlB,MAAM;EACjB;EACA;AACJ;AACA;AACA;AACA;AACA;EACI+B,QAAQA,CAAC/B,MAAM,EAAE;IACb,MAAM3C,IAAI,GAAG2E,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC5E,IAAI,CAAC;IACnC,KAAK,MAAMwE,GAAG,IAAIxE,IAAI,EAAE;MACpB,MAAM2C,MAAM,GAAG,IAAI,CAAC3C,IAAI,CAACwE,GAAG,CAAC;MAC7B,IAAI7B,MAAM,CAAC8B,MAAM,EAAE;QACf;MACJ;IACJ;IACA,IAAI,CAACI,MAAM,CAAC,CAAC;EACjB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,OAAOA,CAACP,MAAM,EAAE;IACZ,MAAMQ,cAAc,GAAG,IAAI,CAAC9D,OAAO,CAAC+D,MAAM,CAACT,MAAM,CAAC;IAClD,KAAK,IAAIU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,cAAc,CAACrD,MAAM,EAAEuD,CAAC,EAAE,EAAE;MAC5C,IAAI,CAACvC,MAAM,CAACwC,KAAK,CAACH,cAAc,CAACE,CAAC,CAAC,EAAEV,MAAM,CAACY,OAAO,CAAC;IACxD;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIjC,OAAOA,CAAA,EAAG;IACN,IAAI,CAACjD,IAAI,CAACmF,OAAO,CAAEC,UAAU,IAAKA,UAAU,CAAC,CAAC,CAAC;IAC/C,IAAI,CAACpF,IAAI,CAACyB,MAAM,GAAG,CAAC;IACpB,IAAI,CAACP,OAAO,CAACmE,OAAO,CAAC,CAAC;EAC1B;EACA;AACJ;AACA;AACA;AACA;EACIT,MAAMA,CAAA,EAAG;IACL,IAAI,CAAChC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACR,aAAa,GAAG,KAAK;IAC1B,IAAI,CAAC6B,OAAO,CAAC,cAAc,CAAC;IAC5B,IAAI,IAAI,CAACxB,MAAM,EACX,IAAI,CAACA,MAAM,CAACc,KAAK,CAAC,CAAC;EAC3B;EACA;AACJ;AACA;AACA;AACA;EACI+B,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAACV,MAAM,CAAC,CAAC;EACxB;EACA;AACJ;AACA;AACA;AACA;EACIX,OAAOA,CAACsB,MAAM,EAAEC,WAAW,EAAE;IACzB,IAAI,CAACvC,OAAO,CAAC,CAAC;IACd,IAAI,CAACxC,OAAO,CAACgF,KAAK,CAAC,CAAC;IACpB,IAAI,CAAC3E,WAAW,GAAG,QAAQ;IAC3B,IAAI,CAACoC,YAAY,CAAC,OAAO,EAAEqC,MAAM,EAAEC,WAAW,CAAC;IAC/C,IAAI,IAAI,CAAC9D,aAAa,IAAI,CAAC,IAAI,CAACkB,aAAa,EAAE;MAC3C,IAAI,CAACN,SAAS,CAAC,CAAC;IACpB;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIA,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAACF,aAAa,IAAI,IAAI,CAACQ,aAAa,EACxC,OAAO,IAAI;IACf,MAAMD,IAAI,GAAG,IAAI;IACjB,IAAI,IAAI,CAAClC,OAAO,CAAC4B,QAAQ,IAAI,IAAI,CAACV,qBAAqB,EAAE;MACrD,IAAI,CAAClB,OAAO,CAACgF,KAAK,CAAC,CAAC;MACpB,IAAI,CAACvC,YAAY,CAAC,kBAAkB,CAAC;MACrC,IAAI,CAACd,aAAa,GAAG,KAAK;IAC9B,CAAC,MACI;MACD,MAAMsD,KAAK,GAAG,IAAI,CAACjF,OAAO,CAACkF,QAAQ,CAAC,CAAC;MACrC,IAAI,CAACvD,aAAa,GAAG,IAAI;MACzB,MAAMgB,KAAK,GAAG,IAAI,CAACC,YAAY,CAAC,MAAM;QAClC,IAAIV,IAAI,CAACC,aAAa,EAClB;QACJ,IAAI,CAACM,YAAY,CAAC,mBAAmB,EAAEP,IAAI,CAAClC,OAAO,CAAC4B,QAAQ,CAAC;QAC7D;QACA,IAAIM,IAAI,CAACC,aAAa,EAClB;QACJD,IAAI,CAACrB,IAAI,CAAE0B,GAAG,IAAK;UACf,IAAIA,GAAG,EAAE;YACLL,IAAI,CAACP,aAAa,GAAG,KAAK;YAC1BO,IAAI,CAACL,SAAS,CAAC,CAAC;YAChB,IAAI,CAACY,YAAY,CAAC,iBAAiB,EAAEF,GAAG,CAAC;UAC7C,CAAC,MACI;YACDL,IAAI,CAACiD,WAAW,CAAC,CAAC;UACtB;QACJ,CAAC,CAAC;MACN,CAAC,EAAEF,KAAK,CAAC;MACT,IAAI,IAAI,CAAC7F,IAAI,CAAC2D,SAAS,EAAE;QACrBJ,KAAK,CAACK,KAAK,CAAC,CAAC;MACjB;MACA,IAAI,CAACzD,IAAI,CAAC0D,IAAI,CAAC,MAAM;QACjB,IAAI,CAACC,cAAc,CAACP,KAAK,CAAC;MAC9B,CAAC,CAAC;IACN;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIwC,WAAWA,CAAA,EAAG;IACV,MAAMC,OAAO,GAAG,IAAI,CAACpF,OAAO,CAAC4B,QAAQ;IACrC,IAAI,CAACD,aAAa,GAAG,KAAK;IAC1B,IAAI,CAAC3B,OAAO,CAACgF,KAAK,CAAC,CAAC;IACpB,IAAI,CAACvC,YAAY,CAAC,WAAW,EAAE2C,OAAO,CAAC;EAC3C;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}