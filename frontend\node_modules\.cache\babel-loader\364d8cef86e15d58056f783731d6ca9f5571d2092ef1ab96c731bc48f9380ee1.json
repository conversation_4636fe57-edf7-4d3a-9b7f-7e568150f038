{"ast": null, "code": "import * as React from 'react';\nimport { style<PERSON><PERSON>leton } from 'react-style-singleton';\nimport { fullWidthClassName, zeroRightClassName, noScrollbarsClassName, removedBarSizeVariable } from './constants';\nimport { getGapWidth } from './utils';\nvar Style = styleSingleton();\n// important tip - once we measure scrollBar width and remove them\n// we could not repeat this operation\n// thus we are using style-singleton - only the first \"yet correct\" style will be applied.\nvar getStyles = function (_a, allowRelative, gapMode, important) {\n  var left = _a.left,\n    top = _a.top,\n    right = _a.right,\n    gap = _a.gap;\n  if (gapMode === void 0) {\n    gapMode = 'margin';\n  }\n  return \"\\n  .\".concat(noScrollbarsClassName, \" {\\n   overflow: hidden \").concat(important, \";\\n   padding-right: \").concat(gap, \"px \").concat(important, \";\\n  }\\n  body {\\n    overflow: hidden \").concat(important, \";\\n    overscroll-behavior: contain;\\n    \").concat([allowRelative && \"position: relative \".concat(important, \";\"), gapMode === 'margin' && \"\\n    padding-left: \".concat(left, \"px;\\n    padding-top: \").concat(top, \"px;\\n    padding-right: \").concat(right, \"px;\\n    margin-left:0;\\n    margin-top:0;\\n    margin-right: \").concat(gap, \"px \").concat(important, \";\\n    \"), gapMode === 'padding' && \"padding-right: \".concat(gap, \"px \").concat(important, \";\")].filter(Boolean).join(''), \"\\n  }\\n  \\n  .\").concat(zeroRightClassName, \" {\\n    right: \").concat(gap, \"px \").concat(important, \";\\n  }\\n  \\n  .\").concat(fullWidthClassName, \" {\\n    margin-right: \").concat(gap, \"px \").concat(important, \";\\n  }\\n  \\n  .\").concat(zeroRightClassName, \" .\").concat(zeroRightClassName, \" {\\n    right: 0 \").concat(important, \";\\n  }\\n  \\n  .\").concat(fullWidthClassName, \" .\").concat(fullWidthClassName, \" {\\n    margin-right: 0 \").concat(important, \";\\n  }\\n  \\n  body {\\n    \").concat(removedBarSizeVariable, \": \").concat(gap, \"px;\\n  }\\n\");\n};\n/**\n * Removes page scrollbar and blocks page scroll when mounted\n */\nexport var RemoveScrollBar = function (props) {\n  var noRelative = props.noRelative,\n    noImportant = props.noImportant,\n    _a = props.gapMode,\n    gapMode = _a === void 0 ? 'margin' : _a;\n  /*\n   gap will be measured on every component mount\n   however it will be used only by the \"first\" invocation\n   due to singleton nature of <Style\n   */\n  var gap = React.useMemo(function () {\n    return getGapWidth(gapMode);\n  }, [gapMode]);\n  return React.createElement(Style, {\n    styles: getStyles(gap, !noRelative, gapMode, !noImportant ? '!important' : '')\n  });\n};", "map": {"version": 3, "names": ["React", "styleSingleton", "fullWidthClassName", "zeroRightClassName", "noScrollbarsClassName", "removedBarSizeVariable", "getGapWidth", "Style", "getStyles", "_a", "allowRelative", "gapMode", "important", "left", "top", "right", "gap", "concat", "filter", "Boolean", "join", "RemoveScrollBar", "props", "noRelative", "noImportant", "useMemo", "createElement", "styles"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/react-remove-scroll-bar/dist/es2015/component.js"], "sourcesContent": ["import * as React from 'react';\nimport { style<PERSON><PERSON>leton } from 'react-style-singleton';\nimport { fullWidthClassName, zeroRightClassName, noScrollbarsClassName, removedBarSizeVariable } from './constants';\nimport { getGapWidth } from './utils';\nvar Style = styleSingleton();\n// important tip - once we measure scrollBar width and remove them\n// we could not repeat this operation\n// thus we are using style-singleton - only the first \"yet correct\" style will be applied.\nvar getStyles = function (_a, allowRelative, gapMode, important) {\n    var left = _a.left, top = _a.top, right = _a.right, gap = _a.gap;\n    if (gapMode === void 0) { gapMode = 'margin'; }\n    return \"\\n  .\".concat(noScrollbarsClassName, \" {\\n   overflow: hidden \").concat(important, \";\\n   padding-right: \").concat(gap, \"px \").concat(important, \";\\n  }\\n  body {\\n    overflow: hidden \").concat(important, \";\\n    overscroll-behavior: contain;\\n    \").concat([\n        allowRelative && \"position: relative \".concat(important, \";\"),\n        gapMode === 'margin' &&\n            \"\\n    padding-left: \".concat(left, \"px;\\n    padding-top: \").concat(top, \"px;\\n    padding-right: \").concat(right, \"px;\\n    margin-left:0;\\n    margin-top:0;\\n    margin-right: \").concat(gap, \"px \").concat(important, \";\\n    \"),\n        gapMode === 'padding' && \"padding-right: \".concat(gap, \"px \").concat(important, \";\"),\n    ]\n        .filter(Boolean)\n        .join(''), \"\\n  }\\n  \\n  .\").concat(zeroRightClassName, \" {\\n    right: \").concat(gap, \"px \").concat(important, \";\\n  }\\n  \\n  .\").concat(fullWidthClassName, \" {\\n    margin-right: \").concat(gap, \"px \").concat(important, \";\\n  }\\n  \\n  .\").concat(zeroRightClassName, \" .\").concat(zeroRightClassName, \" {\\n    right: 0 \").concat(important, \";\\n  }\\n  \\n  .\").concat(fullWidthClassName, \" .\").concat(fullWidthClassName, \" {\\n    margin-right: 0 \").concat(important, \";\\n  }\\n  \\n  body {\\n    \").concat(removedBarSizeVariable, \": \").concat(gap, \"px;\\n  }\\n\");\n};\n/**\n * Removes page scrollbar and blocks page scroll when mounted\n */\nexport var RemoveScrollBar = function (props) {\n    var noRelative = props.noRelative, noImportant = props.noImportant, _a = props.gapMode, gapMode = _a === void 0 ? 'margin' : _a;\n    /*\n     gap will be measured on every component mount\n     however it will be used only by the \"first\" invocation\n     due to singleton nature of <Style\n     */\n    var gap = React.useMemo(function () { return getGapWidth(gapMode); }, [gapMode]);\n    return React.createElement(Style, { styles: getStyles(gap, !noRelative, gapMode, !noImportant ? '!important' : '') });\n};\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,cAAc,QAAQ,uBAAuB;AACtD,SAASC,kBAAkB,EAAEC,kBAAkB,EAAEC,qBAAqB,EAAEC,sBAAsB,QAAQ,aAAa;AACnH,SAASC,WAAW,QAAQ,SAAS;AACrC,IAAIC,KAAK,GAAGN,cAAc,CAAC,CAAC;AAC5B;AACA;AACA;AACA,IAAIO,SAAS,GAAG,SAAAA,CAAUC,EAAE,EAAEC,aAAa,EAAEC,OAAO,EAAEC,SAAS,EAAE;EAC7D,IAAIC,IAAI,GAAGJ,EAAE,CAACI,IAAI;IAAEC,GAAG,GAAGL,EAAE,CAACK,GAAG;IAAEC,KAAK,GAAGN,EAAE,CAACM,KAAK;IAAEC,GAAG,GAAGP,EAAE,CAACO,GAAG;EAChE,IAAIL,OAAO,KAAK,KAAK,CAAC,EAAE;IAAEA,OAAO,GAAG,QAAQ;EAAE;EAC9C,OAAO,OAAO,CAACM,MAAM,CAACb,qBAAqB,EAAE,0BAA0B,CAAC,CAACa,MAAM,CAACL,SAAS,EAAE,uBAAuB,CAAC,CAACK,MAAM,CAACD,GAAG,EAAE,KAAK,CAAC,CAACC,MAAM,CAACL,SAAS,EAAE,yCAAyC,CAAC,CAACK,MAAM,CAACL,SAAS,EAAE,4CAA4C,CAAC,CAACK,MAAM,CAAC,CACvQP,aAAa,IAAI,qBAAqB,CAACO,MAAM,CAACL,SAAS,EAAE,GAAG,CAAC,EAC7DD,OAAO,KAAK,QAAQ,IAChB,sBAAsB,CAACM,MAAM,CAACJ,IAAI,EAAE,wBAAwB,CAAC,CAACI,MAAM,CAACH,GAAG,EAAE,0BAA0B,CAAC,CAACG,MAAM,CAACF,KAAK,EAAE,gEAAgE,CAAC,CAACE,MAAM,CAACD,GAAG,EAAE,KAAK,CAAC,CAACC,MAAM,CAACL,SAAS,EAAE,SAAS,CAAC,EACzOD,OAAO,KAAK,SAAS,IAAI,iBAAiB,CAACM,MAAM,CAACD,GAAG,EAAE,KAAK,CAAC,CAACC,MAAM,CAACL,SAAS,EAAE,GAAG,CAAC,CACvF,CACIM,MAAM,CAACC,OAAO,CAAC,CACfC,IAAI,CAAC,EAAE,CAAC,EAAE,gBAAgB,CAAC,CAACH,MAAM,CAACd,kBAAkB,EAAE,iBAAiB,CAAC,CAACc,MAAM,CAACD,GAAG,EAAE,KAAK,CAAC,CAACC,MAAM,CAACL,SAAS,EAAE,iBAAiB,CAAC,CAACK,MAAM,CAACf,kBAAkB,EAAE,wBAAwB,CAAC,CAACe,MAAM,CAACD,GAAG,EAAE,KAAK,CAAC,CAACC,MAAM,CAACL,SAAS,EAAE,iBAAiB,CAAC,CAACK,MAAM,CAACd,kBAAkB,EAAE,IAAI,CAAC,CAACc,MAAM,CAACd,kBAAkB,EAAE,mBAAmB,CAAC,CAACc,MAAM,CAACL,SAAS,EAAE,iBAAiB,CAAC,CAACK,MAAM,CAACf,kBAAkB,EAAE,IAAI,CAAC,CAACe,MAAM,CAACf,kBAAkB,EAAE,0BAA0B,CAAC,CAACe,MAAM,CAACL,SAAS,EAAE,4BAA4B,CAAC,CAACK,MAAM,CAACZ,sBAAsB,EAAE,IAAI,CAAC,CAACY,MAAM,CAACD,GAAG,EAAE,YAAY,CAAC;AACpjB,CAAC;AACD;AACA;AACA;AACA,OAAO,IAAIK,eAAe,GAAG,SAAAA,CAAUC,KAAK,EAAE;EAC1C,IAAIC,UAAU,GAAGD,KAAK,CAACC,UAAU;IAAEC,WAAW,GAAGF,KAAK,CAACE,WAAW;IAAEf,EAAE,GAAGa,KAAK,CAACX,OAAO;IAAEA,OAAO,GAAGF,EAAE,KAAK,KAAK,CAAC,GAAG,QAAQ,GAAGA,EAAE;EAC/H;AACJ;AACA;AACA;AACA;EACI,IAAIO,GAAG,GAAGhB,KAAK,CAACyB,OAAO,CAAC,YAAY;IAAE,OAAOnB,WAAW,CAACK,OAAO,CAAC;EAAE,CAAC,EAAE,CAACA,OAAO,CAAC,CAAC;EAChF,OAAOX,KAAK,CAAC0B,aAAa,CAACnB,KAAK,EAAE;IAAEoB,MAAM,EAAEnB,SAAS,CAACQ,GAAG,EAAE,CAACO,UAAU,EAAEZ,OAAO,EAAE,CAACa,WAAW,GAAG,YAAY,GAAG,EAAE;EAAE,CAAC,CAAC;AACzH,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}