{"ast": null, "code": "const compareByDepth = (a, b) => a.depth - b.depth;\nexport { compareByDepth };", "map": {"version": 3, "names": ["compareByDepth", "a", "b", "depth"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/framer-motion/dist/es/render/utils/compare-by-depth.mjs"], "sourcesContent": ["const compareByDepth = (a, b) => a.depth - b.depth;\n\nexport { compareByDepth };\n"], "mappings": "AAAA,MAAMA,cAAc,GAAGA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACE,KAAK,GAAGD,CAAC,CAACC,KAAK;AAElD,SAASH,cAAc"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}