{"ast": null, "code": "import { FOCUS_ALLOW } from './constants';\nimport { contains } from './utils/DOMutils';\nimport { toArray } from './utils/array';\nimport { getActiveElement } from './utils/getActiveElement';\n/**\n * checks if focus is hidden FROM the focus-lock\n * ie contained inside a node focus-lock shall ignore\n *\n * This is a utility function coupled with {@link FOCUS_ALLOW} constant\n *\n * @returns {boolean} focus is currently is in \"allow\" area\n */\nexport var focusIsHidden = function (inDocument) {\n  if (inDocument === void 0) {\n    inDocument = document;\n  }\n  var activeElement = getActiveElement(inDocument);\n  if (!activeElement) {\n    return false;\n  }\n  // this does not support setting FOCUS_ALLOW within shadow dom\n  return toArray(inDocument.querySelectorAll(\"[\".concat(FOCUS_ALLOW, \"]\"))).some(function (node) {\n    return contains(node, activeElement);\n  });\n};", "map": {"version": 3, "names": ["FOCUS_ALLOW", "contains", "toArray", "getActiveElement", "focusIsHidden", "inDocument", "document", "activeElement", "querySelectorAll", "concat", "some", "node"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/focus-lock/dist/es2015/focusIsHidden.js"], "sourcesContent": ["import { FOCUS_ALLOW } from './constants';\nimport { contains } from './utils/DOMutils';\nimport { toArray } from './utils/array';\nimport { getActiveElement } from './utils/getActiveElement';\n/**\n * checks if focus is hidden FROM the focus-lock\n * ie contained inside a node focus-lock shall ignore\n *\n * This is a utility function coupled with {@link FOCUS_ALLOW} constant\n *\n * @returns {boolean} focus is currently is in \"allow\" area\n */\nexport var focusIsHidden = function (inDocument) {\n    if (inDocument === void 0) { inDocument = document; }\n    var activeElement = getActiveElement(inDocument);\n    if (!activeElement) {\n        return false;\n    }\n    // this does not support setting FOCUS_ALLOW within shadow dom\n    return toArray(inDocument.querySelectorAll(\"[\".concat(FOCUS_ALLOW, \"]\"))).some(function (node) { return contains(node, activeElement); });\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,aAAa;AACzC,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,SAASC,OAAO,QAAQ,eAAe;AACvC,SAASC,gBAAgB,QAAQ,0BAA0B;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,aAAa,GAAG,SAAAA,CAAUC,UAAU,EAAE;EAC7C,IAAIA,UAAU,KAAK,KAAK,CAAC,EAAE;IAAEA,UAAU,GAAGC,QAAQ;EAAE;EACpD,IAAIC,aAAa,GAAGJ,gBAAgB,CAACE,UAAU,CAAC;EAChD,IAAI,CAACE,aAAa,EAAE;IAChB,OAAO,KAAK;EAChB;EACA;EACA,OAAOL,OAAO,CAACG,UAAU,CAACG,gBAAgB,CAAC,GAAG,CAACC,MAAM,CAACT,WAAW,EAAE,GAAG,CAAC,CAAC,CAAC,CAACU,IAAI,CAAC,UAAUC,IAAI,EAAE;IAAE,OAAOV,QAAQ,CAACU,IAAI,EAAEJ,aAAa,CAAC;EAAE,CAAC,CAAC;AAC7I,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}