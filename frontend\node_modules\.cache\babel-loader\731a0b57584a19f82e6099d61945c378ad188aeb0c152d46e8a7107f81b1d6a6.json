{"ast": null, "code": "import { RemoveScrollBar } from './component';\nimport { zeroRightClassName, fullWidthClassName, noScrollbarsClassName, removedBarSizeVariable } from './constants';\nimport { getGapWidth } from './utils';\nexport { RemoveScrollBar, zeroRightClassName, fullWidthClassName, noScrollbarsClassName, removedBarSizeVariable, getGapWidth };", "map": {"version": 3, "names": ["RemoveScrollBar", "zeroRightClassName", "fullWidthClassName", "noScrollbarsClassName", "removedBarSizeVariable", "getGapWidth"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/react-remove-scroll-bar/dist/es2015/index.js"], "sourcesContent": ["import { RemoveScrollBar } from './component';\nimport { zeroRightClassName, fullWidthClassName, noScrollbarsClassName, removedBarSizeVariable } from './constants';\nimport { getGapWidth } from './utils';\nexport { RemoveScrollBar, zeroRightClassName, fullWidthClassName, noScrollbarsClassName, removedBarSizeVariable, getGapWidth, };\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,aAAa;AAC7C,SAASC,kBAAkB,EAAEC,kBAAkB,EAAEC,qBAAqB,EAAEC,sBAAsB,QAAQ,aAAa;AACnH,SAASC,WAAW,QAAQ,SAAS;AACrC,SAASL,eAAe,EAAEC,kBAAkB,EAAEC,kBAAkB,EAAEC,qBAAqB,EAAEC,sBAAsB,EAAEC,WAAW"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}