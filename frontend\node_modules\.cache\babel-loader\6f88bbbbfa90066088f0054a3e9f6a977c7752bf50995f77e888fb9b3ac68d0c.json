{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\RecipeHub-Recipe-Sharing-Platform\\\\frontend\\\\src\\\\components\\\\Feed\\\\MiniCard.jsx\",\n  _s = $RefreshSig$();\nimport { Box, Button, Card, CardBody, Flex, Heading, Divider, Center, Text, HStack, useDisclosure, Modal, ModalOverlay, ModalContent, ModalHeader, ModalCloseButton, ModalBody, ModalFooter, Textarea, VStack } from \"@chakra-ui/react\";\nimport { ChatIcon } from \"@chakra-ui/icons\";\nimport React, { useEffect, useRef, useState } from \"react\";\nimport { Avatar, AvatarBadge, AvatarGroup } from \"@chakra-ui/react\";\nimport { io } from \"socket.io-client\";\nimport { AiOutlineUser } from \"react-icons/ai\";\nimport { styled } from \"styled-components\";\nimport { useSelector } from \"react-redux\";\nimport axios from \"axios\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MiniCard_Friends = ({\n  userId,\n  friend,\n  addRequestHandler\n}) => {\n  return /*#__PURE__*/_jsxDEV(Card, {\n    maxW: \"md\",\n    mb: \"1rem\",\n    borderRadius: \"lg\",\n    overflow: \"hidden\",\n    boxShadow: \"md\",\n    p: \"1rem\",\n    children: /*#__PURE__*/_jsxDEV(Flex, {\n      direction: {\n        base: \"column\",\n        sm: \"row\"\n      },\n      gap: \"1rem\",\n      alignItems: \"center\",\n      children: [/*#__PURE__*/_jsxDEV(Avatar, {\n        size: \"md\",\n        name: friend.name,\n        src: friend.profileImage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Center, {\n        height: \"50px\",\n        children: /*#__PURE__*/_jsxDEV(Divider, {\n          orientation: \"vertical\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CardBody, {\n        flexGrow: 1,\n        p: 0,\n        children: [/*#__PURE__*/_jsxDEV(Flex, {\n          mb: 2,\n          justifyContent: \"space-between\",\n          alignItems: \"center\",\n          w: \"100%\",\n          children: [/*#__PURE__*/_jsxDEV(Heading, {\n            size: \"sm\",\n            fontWeight: \"500\",\n            children: friend.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 13\n          }, this), !friend.requests.includes(userId) ? /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outline\",\n            size: \"sm\",\n            colorScheme: \"primary\",\n            onClick: () => addRequestHandler(friend._id, friend.requests),\n            children: \"Add Friend\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Button, {\n            colorScheme: \"primary\",\n            size: \"sm\",\n            disabled: true,\n            children: \"Friend Request Sent\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Text, {\n          noOfLines: 2,\n          isTruncated: true,\n          children: friend.bio.slice(0, 40) + \"...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 34,\n    columnNumber: 5\n  }, this);\n};\n_c = MiniCard_Friends;\nconst MiniCard_Request = ({\n  friend,\n  acceptRequestHandler,\n  rejectRequestHandler\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: /*#__PURE__*/_jsxDEV(Card, {\n      maxW: \"md\",\n      px: \"4px\",\n      borderRadius: \"none\",\n      boxShadow: \"none\",\n      children: /*#__PURE__*/_jsxDEV(Flex, {\n        justify: \"space-between\",\n        align: \"center\",\n        children: [/*#__PURE__*/_jsxDEV(Avatar, {\n          size: \"md\",\n          name: friend.name,\n          src: friend.profileImage\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          flexGrow: 1,\n          px: 4,\n          children: /*#__PURE__*/_jsxDEV(Heading, {\n            size: \"sm\",\n            textAlign: \"left\",\n            fontWeight: \"500\",\n            children: friend.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Flex, {\n          gap: 2,\n          justifyContent: \"flex-end\",\n          alignItems: \"center\",\n          children: [\" \", /*#__PURE__*/_jsxDEV(Button, {\n            colorScheme: \"primary.500\",\n            size: \"xs\",\n            onClick: () => acceptRequestHandler(friend._id),\n            children: \"Accept\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outline\",\n            colorScheme: \"primary\",\n            size: \"xs\",\n            onClick: () => rejectRequestHandler(friend._id),\n            children: \"Reject\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 91,\n    columnNumber: 5\n  }, this);\n};\n_c2 = MiniCard_Request;\nconst socket = io.connect(process.env.NODE_ENV === 'production' ? \"https://concerned-picture-9849.onrender.com\" : \"http://localhost:3001\", {\n  withCredentials: true\n});\nsocket.on(\"connect\", () => {\n  console.log(\"Connected to the Socket.io server\");\n});\nconst FriendCard = ({\n  friend\n}) => {\n  _s();\n  const {\n    isOpen,\n    onOpen,\n    onClose\n  } = useDisclosure();\n  const [chat, setChat] = useState([]);\n  const [message, setMessage] = useState(\"\");\n  const [refresh, setRefresh] = useState(false);\n  const user = useSelector(store => store.authReducer.loggedInUser);\n  const chatContainerRef = useRef();\n  const lastMessageRef = useRef();\n  const sendMessage = () => {\n    const now = new Date();\n    const options = {\n      hour: \"2-digit\",\n      minute: \"2-digit\",\n      second: \"2-digit\"\n    };\n    const formattedTime = now.toLocaleDateString(\"en-US\", options);\n    if (message === \"\") {\n      return;\n    }\n    const data = {\n      sender: user._id,\n      receiver: friend._id,\n      message: message,\n      time: formattedTime\n    };\n    axios.post(`${process.env.REACT_APP_API_URL}/chat/addmessage`, data).then(res => {\n      console.log(res.data);\n      socket.emit(\"message\");\n      setRefresh(!refresh);\n    }).catch(err => {\n      console.log(err);\n    });\n    setMessage(\"\");\n  };\n  useEffect(() => {\n    if (lastMessageRef.current) {\n      lastMessageRef.current.scrollIntoView({\n        behavior: \"smooth\"\n      });\n    }\n  }, [chat]);\n  useEffect(() => {\n    axios.get(`${process.env.REACT_APP_API_URL}/chat/getmessage/${user._id}/${friend._id}`).then(res => {\n      console.log(res.data);\n      setChat(res.data);\n    }).catch(err => {\n      console.log(err);\n    });\n  }, [refresh]);\n  useEffect(() => {\n    socket.on(\"sendMessage\", data => {\n      console.log(\"Refresh message\");\n      axios.get(`${process.env.REACT_APP_API_URL}/chat/getmessage/${user._id}/${friend._id}`).then(res => {\n        setChat(res.data);\n      }).catch(err => {\n        console.log(err);\n      });\n    });\n  }, [socket]);\n  console.log(chat, \"chat\");\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      maxW: \"md\",\n      p: \"1rem\",\n      borderRadius: \"4px\",\n      boxShadow: \"none\",\n      bor: true,\n      children: /*#__PURE__*/_jsxDEV(Flex, {\n        justifyContent: \"space-between\",\n        alignItems: \"center\",\n        width: \"100%\",\n        children: [/*#__PURE__*/_jsxDEV(Avatar, {\n          src: friend.profileImage,\n          mr: \"1rem\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Center, {\n          height: \"50px\",\n          children: /*#__PURE__*/_jsxDEV(Divider, {\n            orientation: \"vertical\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Flex, {\n          justifyContent: \"space-between\",\n          alignItems: \"center\",\n          flexGrow: 1,\n          children: [/*#__PURE__*/_jsxDEV(Heading, {\n            ml: \"1rem\",\n            size: \"sm\",\n            justifySelf: \"flex-start\",\n            fontWeight: \"500\",\n            children: friend.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ChatIcon, {\n            cursor: \"pointer\",\n            onClick: onOpen\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 212,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      isOpen: isOpen,\n      onClose: onClose,\n      children: [/*#__PURE__*/_jsxDEV(ModalOverlay, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ModalContent, {\n        children: [/*#__PURE__*/_jsxDEV(ModalHeader, {\n          children: /*#__PURE__*/_jsxDEV(Flex, {\n            width: \"100%\",\n            alignItems: \"center\",\n            children: [/*#__PURE__*/_jsxDEV(Avatar, {\n              src: friend.profileImage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Heading, {\n              ml: \"20px\",\n              size: \"sm\",\n              justifySelf: \"flex-start\",\n              children: friend.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ModalCloseButton, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ModalBody, {\n          children: /*#__PURE__*/_jsxDEV(CHATBOX, {\n            children: /*#__PURE__*/_jsxDEV(\"main\", {\n              className: \"msger-chat\",\n              ref: chatContainerRef,\n              children: chat.length > 0 && chat.map((ele, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                ref: index === chat.length - 1 ? lastMessageRef : null,\n                className: ele.sender !== user._id ? \"msg left-msg\" : \"msg right-msg\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"msg-bubble\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"msg-info\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"msg-info-name\",\n                      children: ele.sender !== user._id ? friend.name : user.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 259,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"msg-info-time\",\n                      children: ele.time\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 262,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 258,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"msg-text\",\n                    children: ele.message\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 265,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 257,\n                  columnNumber: 23\n                }, this)\n              }, ele._id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ModalFooter, {\n          children: /*#__PURE__*/_jsxDEV(Flex, {\n            flexDir: \"column\",\n            w: \"100%\",\n            children: [/*#__PURE__*/_jsxDEV(Textarea, {\n              value: message,\n              required: true,\n              onChange: e => setMessage(e.target.value),\n              placeholder: \"Enter your message\",\n              width: \"100%\",\n              mb: \"1rem\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              onClick: () => sendMessage(),\n              variant: \"solid\",\n              alignSelf: \"flex-end\",\n              children: \"Send message\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 231,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 211,\n    columnNumber: 5\n  }, this);\n};\n_s(FriendCard, \"1FsoTSFVEjMwkAqxWqhJEMrOXkQ=\", false, function () {\n  return [useDisclosure, useSelector];\n});\n_c3 = FriendCard;\nexport { MiniCard_Request, MiniCard_Friends, FriendCard };\nconst CHATBOX = styled.div`\n  .msger {\n    display: flex;\n    flex-flow: column wrap;\n    justify-content: space-between;\n    width: 100%;\n    max-width: 867px;\n    margin: 25px 10px;\n    height: calc(100% - 50px);\n    border: 2px solid #ddd;\n    border-radius: 5px;\n    background: #fff;\n    box-shadow: 0 15px 15px -5px rgba(0, 0, 0, 0.2);\n  }\n\n  .msger-header {\n    display: flex;\n    justify-content: space-between;\n    padding: 10px;\n    border-bottom: 2px solid #ddd;\n    background: #eee;\n    color: #666;\n  }\n\n  .msger-chat {\n    height: 400px;\n    overflow-y: scroll;\n  }\n  .msger-chat::-webkit-scrollbar {\n    width: 6px;\n  }\n  .msger-chat::-webkit-scrollbar-track {\n    background: #ddd;\n  }\n  .msger-chat::-webkit-scrollbar-thumb {\n    background: #bdbdbd;\n  }\n  .msg {\n    display: flex;\n    align-items: flex-end;\n    margin-bottom: 10px;\n  }\n  .msg:last-of-type {\n    margin: 0;\n  }\n  .msg-img {\n    width: 50px;\n    height: 50px;\n    margin-right: 10px;\n    background: #ddd;\n    background-repeat: no-repeat;\n    background-position: center;\n    background-size: cover;\n    border-radius: 50%;\n  }\n  .msg-bubble {\n    max-width: 450px;\n    padding: 15px;\n    border-radius: 15px;\n    background: #ececec;\n  }\n  .msg-info {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 1rem;\n  }\n  .msg-info-name {\n    margin-right: 10px;\n    font-weight: bold;\n  }\n  .msg-info-time {\n    font-size: 0.85em;\n  }\n\n  .left-msg .msg-bubble {\n    border-bottom-left-radius: 0;\n  }\n\n  .right-msg {\n    flex-direction: row-reverse;\n  }\n  .right-msg .msg-bubble {\n    background: #e89c45;\n    color: #fff;\n    border-bottom-right-radius: 0;\n  }\n  .right-msg .msg-img {\n    margin: 0 0 0 10px;\n  }\n\n  .msger-chat {\n    background-color: #fcfcfe;\n    background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='260' height='260' viewBox='0 0 260 260'%3E%3Cg fill-rule='evenodd'%3E%3Cg fill='%23dddddd' fill-opacity='0.4'%3E%3Cpath d='M24.37 16c.2.65.39 1.32.54 2H21.17l1.17 2.34.45.9-.24.11V28a5 5 0 0 1-2.23 8.94l-.02.06a8 8 0 0 1-7.75 6h-20a8 8 0 0 1-7.74-6l-.02-.06A5 5 0 0 1-17.45 28v-6.76l-.79-1.58-.44-.9.9-.44.63-.32H-20a23.01 23.01 0 0 1 44.37-2zm-36.82 2a1 1 0 0 0-.44.1l-3.1 1.56.89 1.79 1.31-.66a3 3 0 0 1 2.69 0l2.2 1.1a1 1 0 0 0 .9 0l2.21-1.1a3 3 0 0 1 2.69 0l2.2 1.1a1 1 0 0 0 .9 0l2.21-1.1a3 3 0 0 1 2.69 0l2.2 1.1a1 1 0 0 0 .86.02l2.88-1.27a3 3 0 0 1 2.43 0l2.88 1.27a1 1 0 0 0 .85-.02l3.1-1.55-.89-1.79-1.42.71a3 3 0 0 1-2.56.06l-2.77-1.23a1 1 0 0 0-.4-.09h-.01a1 1 0 0 0-.4.09l-2.78 1.23a3 3 0 0 1-2.56-.06l-2.3-1.15a1 1 0 0 0-.45-.11h-.01a1 1 0 0 0-.44.1L.9 19.22a3 3 0 0 1-2.69 0l-2.2-1.1a1 1 0 0 0-.45-.11h-.01a1 1 0 0 0-.44.1l-2.21 1.11a3 3 0 0 1-2.69 0l-2.2-1.1a1 1 0 0 0-.45-.11h-.01zm0-2h-4.9a21.01 21.01 0 0 1 39.61 0h-2.09l-.06-.13-.26.13h-32.31zm30.35 7.68l1.36-.68h1.3v2h-36v-1.15l.34-.17 1.36-.68h2.59l1.36.68a3 3 0 0 0 2.69 0l1.36-.68h2.59l1.36.68a3 3 0 0 0 2.69 0L2.26 23h2.59l1.36.68a3 3 0 0 0 2.56.06l1.67-.74h3.23l1.67.74a3 3 0 0 0 2.56-.06zM-13.82 27l16.37 4.91L18.93 27h-32.75zm-.63 2h.34l16.66 5 16.67-5h.33a3 3 0 1 1 0 6h-34a3 3 0 1 1 0-6zm1.35 8a6 6 0 0 0 5.65 4h20a6 6 0 0 0 5.66-4H-13.1z'/%3E%3Cpath id='path6_fill-copy' d='M284.37 16c.2.65.39 1.32.54 2H281.17l1.17 2.34.45.9-.24.11V28a5 5 0 0 1-2.23 8.94l-.02.06a8 8 0 0 1-7.75 6h-20a8 8 0 0 1-7.74-6l-.02-.06a5 5 0 0 1-2.24-8.94v-6.76l-.79-1.58-.44-.9.9-.44.63-.32H240a23.01 23.01 0 0 1 44.37-2zm-36.82 2a1 1 0 0 0-.44.1l-3.1 1.56.89 1.79 1.31-.66a3 3 0 0 1 2.69 0l2.2 1.1a1 1 0 0 0 .9 0l2.21-1.1a3 3 0 0 1 2.69 0l2.2 1.1a1 1 0 0 0 .9 0l2.21-1.1a3 3 0 0 1 2.69 0l2.2 1.1a1 1 0 0 0 .86.02l2.88-1.27a3 3 0 0 1 2.43 0l2.88 1.27a1 1 0 0 0 .85-.02l3.1-1.55-.89-1.79-1.42.71a3 3 0 0 1-2.56.06l-2.77-1.23a1 1 0 0 0-.4-.09h-.01a1 1 0 0 0-.4.09l-2.78 1.23a3 3 0 0 1-2.56-.06l-2.3-1.15a1 1 0 0 0-.45-.11h-.01a1 1 0 0 0-.44.1l-2.21 1.11a3 3 0 0 1-2.69 0l-2.2-1.1a1 1 0 0 0-.45-.11h-.01a1 1 0 0 0-.44.1l-2.21 1.11a3 3 0 0 1-2.69 0l-2.2-1.1a1 1 0 0 0-.45-.11h-.01zm0-2h-4.9a21.01 21.01 0 0 1 39.61 0h-2.09l-.06-.13-.26.13h-32.31zm30.35 7.68l1.36-.68h1.3v2h-36v-1.15l.34-.17 1.36-.68h2.59l1.36.68a3 3 0 0 0 2.69 0l1.36-.68h2.59l1.36.68a3 3 0 0 0 2.69 0l1.36-.68h2.59l1.36.68a3 3 0 0 0 2.56.06l1.67-.74h3.23l1.67.74a3 3 0 0 0 2.56-.06zM246.18 27l16.37 4.91L278.93 27h-32.75zm-.63 2h.34l16.66 5 16.67-5h.33a3 3 0 1 1 0 6h-34a3 3 0 1 1 0-6zm1.35 8a6 6 0 0 0 5.65 4h20a6 6 0 0 0 5.66-4H246.9z'/%3E%3Cpath d='M159.5 21.02A9 9 0 0 0 151 15h-42a9 9 0 0 0-8.5 6.02 6 6 0 0 0 .02 11.96A8.99 8.99 0 0 0 109 45h42a9 9 0 0 0 8.48-12.02 6 6 0 0 0 .02-11.96zM151 17h-42a7 7 0 0 0-6.33 4h54.66a7 7 0 0 0-6.33-4zm-9.34 26a8.98 8.98 0 0 0 3.34-7h-2a7 7 0 0 1-7 7h-4.34a8.98 8.98 0 0 0 3.34-7h-2a7 7 0 0 1-7 7h-4.34a8.98 8.98 0 0 0 3.34-7h-2a7 7 0 0 1-7 7h-7a7 7 0 1 1 0-14h42a7 7 0 1 1 0 14h-9.34zM109 27a9 9 0 0 0-7.48 4H101a4 4 0 1 1 0-8h58a4 4 0 0 1 0 8h-.52a9 9 0 0 0-7.48-4h-42z'/%3E%3Cpath d='M39 115a8 8 0 1 0 0-16 8 8 0 0 0 0 16zm6-8a6 6 0 1 1-12 0 6 6 0 0 1 12 0zm-3-29v-2h8v-6H40a4 4 0 0 0-4 4v10H22l-1.33 4-.67 2h2.19L26 130h26l3.81-40H58l-.67-2L56 84H42v-6zm-4-4v10h2V74h8v-2h-8a2 2 0 0 0-2 2zm2 12h14.56l.67 2H22.77l.67-2H40zm13.8 4H24.2l3.62 38h22.36l3.62-38z'/%3E%3Cpath d='M129 92h-6v4h-6v4h-6v14h-3l.24 2 3.76 32h36l3.76-32 .24-2h-3v-14h-6v-4h-6v-4h-8zm18 22v-12h-4v4h3v8h1zm-3 0v-6h-4v6h4zm-6 6v-16h-4v19.17c1.6-.7 2.97-1.8 4-3.17zm-6 3.8V100h-4v23.8a10.04 10.04 0 0 0 4 0zm-6-.63V104h-4v16a10.04 10.04 0 0 0 4 3.17zm-6-9.17v-6h-4v6h4zm-6 0v-8h3v-4h-4v12h1zm27-12v-4h-4v4h3v4h1v-4zm-6 0v-8h-4v4h3v4h1zm-6-4v-4h-4v8h1v-4h3zm-6 4v-4h-4v8h1v-4h3zm7 24a12 12 0 0 0 11.83-10h7.92l-3.53 30h-32.44l-3.53-30h7.92A12 12 0 0 0 130 126z'/%3E%3Cpath d='M212 86v2h-4v-2h4zm4 0h-2v2h2v-2zm-20 0v.1a5 5 0 0 0-.56 9.65l.06.25 1.12 4.48a2 2 0 0 0 1.94 1.52h.01l7.02 24.55a2 2 0 0 0 1.92 1.45h4.98a2 2 0 0 0 1.92-1.45l7.02-24.55a2 2 0 0 0 1.95-1.52L224.5 96l.06-.25a5 5 0 0 0-.56-9.65V86a14 14 0 0 0-28 0zm4 0h6v2h-9a3 3 0 1 0 0 6H223a3 3 0 1 0 0-6H220v-2h2a12 12 0 1 0-24 0h2zm-1.44 14l-1-4h24.88l-1 4h-22.88zm8.95 26l-6.86-24h18.7l-6.86 24h-4.98zM150 242a22 22 0 1 0 0-44 22 22 0 0 0 0 44zm24-22a24 24 0 1 1-48 0 24 24 0 0 1 48 0zm-28.38 17.73l2.04-.87a6 6 0 0 1 4.68 0l2.04.87a2 2 0 0 0 2.5-.82l1.14-1.9a6 6 0 0 1 3.79-2.75l2.15-.5a2 2 0 0 0 1.54-2.12l-.19-2.2a6 6 0 0 1 1.45-4.46l1.45-1.67a2 2 0 0 0 0-2.62l-1.45-1.67a6 6 0 0 1-1.45-4.46l.2-2.2a2 2 0 0 0-1.55-2.13l-2.15-.5a6 6 0 0 1-3.8-2.75l-1.13-1.9a2 2 0 0 0-2.5-.8l-2.04.86a6 6 0 0 1-4.68 0l-2.04-.87a2 2 0 0 0-2.5.82l-1.14 1.9a6 6 0 0 1-3.79 2.75l-2.15.5a2 2 0 0 0-1.54 2.12l.19 2.2a6 6 0 0 1-1.45 4.46l-1.45 1.67a2 2 0 0 0 0 2.62l1.45 1.67a6 6 0 0 1 1.45 4.46l-.2 2.2a2 2 0 0 0 1.55 2.13l2.15.5a6 6 0 0 1 3.8 2.75l1.13 1.9a2 2 0 0 0 2.5.8zm2.82.97a4 4 0 0 1 3.12 0l2.04.87a4 4 0 0 0 4.99-1.62l1.14-1.9a4 4 0 0 1 2.53-1.84l2.15-.5a4 4 0 0 0 3.09-4.24l-.2-2.2a4 4 0 0 1 .97-2.98l1.45-1.67a4 4 0 0 0 0-5.24l-1.45-1.67a4 4 0 0 1-.97-2.97l.2-2.2a4 4 0 0 0-3.09-4.25l-2.15-.5a4 4 0 0 1-2.53-1.84l-1.14-1.9a4 4 0 0 0-5-1.62l-2.03.87a4 4 0 0 1-3.12 0l-2.04-.87a4 4 0 0 0-4.99 1.62l-1.14 1.9a4 4 0 0 1-2.53 1.84l-2.15.5a4 4 0 0 0-3.09 4.24l.2 2.2a4 4 0 0 1-.97 2.98l-1.45 1.67a4 4 0 0 0 0 5.24l1.45 1.67a4 4 0 0 1 .97 2.97l-.2 2.2a4 4 0 0 0 3.09 4.25l2.15.5a4 4 0 0 1 2.53 1.84l1.14 1.9a4 4 0 0 0 5 1.62l2.03-.87zM152 207a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm6 2a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm-11 1a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm-6 0a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm3-5a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm-8 8a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm3 6a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm0 6a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm4 7a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm5-2a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm5 4a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm4-6a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm6-4a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm-4-3a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm4-3a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm-5-4a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm-24 6a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm16 5a5 5 0 1 0 0-10 5 5 0 0 0 0 10zm7-5a7 7 0 1 1-14 0 7 7 0 0 1 14 0zm86-29a1 1 0 0 0 0 2h2a1 1 0 0 0 0-2h-2zm19 9a1 1 0 0 1 1-1h2a1 1 0 0 1 0 2h-2a1 1 0 0 1-1-1zm-14 5a1 1 0 0 0 0 2h2a1 1 0 0 0 0-2h-2zm-25 1a1 1 0 0 0 0 2h2a1 1 0 0 0 0-2h-2zm5 4a1 1 0 0 0 0 2h2a1 1 0 0 0 0-2h-2zm9 0a1 1 0 0 1 1-1h2a1 1 0 0 1 0 2h-2a1 1 0 0 1-1-1zm15 1a1 1 0 0 1 1-1h2a1 1 0 0 1 0 2h-2a1 1 0 0 1-1-1zm12-2a1 1 0 0 0 0 2h2a1 1 0 0 0 0-2h-2zm-11-14a1 1 0 0 1 1-1h2a1 1 0 0 1 0 2h-2a1 1 0 0 1-1-1zm-19 0a1 1 0 0 0 0 2h2a1 1 0 0 0 0-2h-2zm6 5a1 1 0 0 1 1-1h2a1 1 0 0 1 0 2h-2a1 1 0 0 1-1-1zm-25 15c0-.47.01-.94.03-1.4a5 5 0 0 1-1.7-8 3.99 3.99 0 0 1 1.88-5.18 5 5 0 0 1 3.4-6.22 3 3 0 0 1 1.46-1.05 5 5 0 0 1 7.76-3.27A30.86 30.86 0 0 1 246 184c6.79 0 13.06 2.18 18.17 5.88a5 5 0 0 1 7.76 3.27 3 3 0 0 1 1.47 1.05 5 5 0 0 1 3.4 6.22 4 4 0 0 1 1.87 5.18 4.98 4.98 0 0 1-1.7 8c.02.46.03.93.03 1.4v1h-62v-1zm.83-7.17a30.9 30.9 0 0 0-.62 3.57 3 3 0 0 1-.61-4.2c.37.28.78.49 1.23.63zm1.49-4.61c-.36.87-.68 1.76-.96 2.68a2 2 0 0 1-.21-3.71c.33.4.73.75 1.17 1.03zm2.32-4.54c-.54.86-1.03 1.76-1.49 2.68a3 3 0 0 1-.07-4.67 3 3 0 0 0 1.56 1.99zm1.14-1.7c.35-.5.72-.98 1.1-1.46a1 1 0 1 0-1.1 1.45zm5.34-5.77c-1.03.86-2 1.79-2.9 2.77a3 3 0 0 0-1.11-.77 3 3 0 0 1 4-2zm42.66 2.77c-.9-.98-1.87-1.9-2.9-2.77a3 3 0 0 1 4.01 2 3 3 0 0 0-1.1.77zm1.34 1.54c.38.48.75.96 1.1 1.45a1 1 0 1 0-1.1-1.45zm3.73 5.84c-.46-.92-.95-1.82-1.5-2.68a3 3 0 0 0 1.57-1.99 3 3 0 0 1-.07 4.67zm1.8 4.53c-.29-.9-.6-1.8-.97-2.67.44-.28.84-.63 1.17-1.03a2 2 0 0 1-.2 3.7zm1.14 5.51c-.14-1.21-.35-2.4-.62-3.57.45-.14.86-.35 1.23-.63a2.99 2.99 0 0 1-.6 4.2zM275 214a29 29 0 0 0-57.97 0h57.96zM72.33 198.12c-.21-.32-.34-.7-.34-1.12v-12h-2v12a4.01 4.01 0 0 0 7.09 2.54c.57-.69.91-1.57.91-2.54v-12h-2v12a1.99 1.99 0 0 1-2 2 2 2 0 0 1-1.66-.88zM75 176c.38 0 .74-.04 1.1-.12a4 4 0 0 0 6.19 2.4A13.94 13.94 0 0 1 84 185v24a6 6 0 0 1-6 6h-3v9a5 5 0 1 1-10 0v-9h-3a6 6 0 0 1-6-6v-24a14 14 0 0 1 14-14 5 5 0 0 0 5 5zm-17 15v12a1.99 1.99 0 0 0 1.22 1.84 2 2 0 0 0 2.44-.72c.21-.32.34-.7.34-1.12v-12h2v12a3.98 3.98 0 0 1-5.35 3.77 3.98 3.98 0 0 1-.65-.3V209a4 4 0 0 0 4 4h16a4 4 0 0 0 4-4v-24c.01-1.53-.23-2.88-.72-4.17-.43.1-.87.16-1.28.17a6 6 0 0 1-5.2-3 7 7 0 0 1-6.47-4.88A12 12 0 0 0 58 185v6zm9 24v9a3 3 0 1 0 6 0v-9h-6z'/%3E%3Cpath d='M-17 191a1 1 0 0 0 0 2h2a1 1 0 0 0 0-2h-2zm19 9a1 1 0 0 1 1-1h2a1 1 0 0 1 0 2H3a1 1 0 0 1-1-1zm-14 5a1 1 0 0 0 0 2h2a1 1 0 0 0 0-2h-2zm-25 1a1 1 0 0 0 0 2h2a1 1 0 0 0 0-2h-2zm5 4a1 1 0 0 0 0 2h2a1 1 0 0 0 0-2h-2zm9 0a1 1 0 0 1 1-1h2a1 1 0 0 1 0 2h-2a1 1 0 0 1-1-1zm15 1a1 1 0 0 1 1-1h2a1 1 0 0 1 0 2h-2a1 1 0 0 1-1-1zm12-2a1 1 0 0 0 0 2h2a1 1 0 0 0 0-2H4zm-11-14a1 1 0 0 1 1-1h2a1 1 0 0 1 0 2h-2a1 1 0 0 1-1-1zm-19 0a1 1 0 0 0 0 2h2a1 1 0 0 0 0-2h-2zm6 5a1 1 0 0 1 1-1h2a1 1 0 0 1 0 2h-2a1 1 0 0 1-1-1zm-25 15c0-.47.01-.94.03-1.4a5 5 0 0 1-1.7-8 3.99 3.99 0 0 1 1.88-5.18 5 5 0 0 1 3.4-6.22 3 3 0 0 1 1.46-1.05 5 5 0 0 1 7.76-3.27A30.86 30.86 0 0 1-14 184c6.79 0 13.06 2.18 18.17 5.88a5 5 0 0 1 7.76 3.27 3 3 0 0 1 1.47 1.05 5 5 0 0 1 3.4 6.22 4 4 0 0 1 1.87 5.18 4.98 4.98 0 0 1-1.7 8c.02.46.03.93.03 1.4v1h-62v-1zm.83-7.17a30.9 30.9 0 0 0-.62 3.57 3 3 0 0 1-.61-4.2c.37.28.78.49 1.23.63zm1.49-4.61c-.36.87-.68 1.76-.96 2.68a2 2 0 0 1-.21-3.71c.33.4.73.75 1.17 1.03zm2.32-4.54c-.54.86-1.03 1.76-1.49 2.68a3 3 0 0 1-.07-4.67 3 3 0 0 0 1.56 1.99zm1.14-1.7c.35-.5.72-.98 1.1-1.46a1 1 0 1 0-1.1 1.45zm5.34-5.77c-1.03.86-2 1.79-2.9 2.77a3 3 0 0 0-1.11-.77 3 3 0 0 1 4-2zm42.66 2.77c-.9-.98-1.87-1.9-2.9-2.77a3 3 0 0 1 4.01 2 3 3 0 0 0-1.1.77zm1.34 1.54c.38.48.75.96 1.1 1.45a1 1 0 1 0-1.1-1.45zm3.73 5.84c-.46-.92-.95-1.82-1.5-2.68a3 3 0 0 0 1.57-1.99 3 3 0 0 1-.07 4.67zm1.8 4.53c-.29-.9-.6-1.8-.97-2.67.44-.28.84-.63 1.17-1.03a2 2 0 0 1-.2 3.7zm1.14 5.51c-.14-1.21-.35-2.4-.62-3.57.45-.14.86-.35 1.23-.63a2.99 2.99 0 0 1-.6 4.2zM15 214a29 29 0 0 0-57.97 0h57.96z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\");\n  }\n`;\n_c4 = CHATBOX;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"MiniCard_Friends\");\n$RefreshReg$(_c2, \"MiniCard_Request\");\n$RefreshReg$(_c3, \"FriendCard\");\n$RefreshReg$(_c4, \"CHATBOX\");", "map": {"version": 3, "names": ["Box", "<PERSON><PERSON>", "Card", "CardBody", "Flex", "Heading", "Divider", "Center", "Text", "HStack", "useDisclosure", "Modal", "ModalOverlay", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ModalCloseButton", "ModalBody", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Textarea", "VStack", "ChatIcon", "React", "useEffect", "useRef", "useState", "Avatar", "AvatarBadge", "AvatarGroup", "io", "AiOutlineUser", "styled", "useSelector", "axios", "jsxDEV", "_jsxDEV", "MiniCard_Friends", "userId", "friend", "addRequestHandler", "maxW", "mb", "borderRadius", "overflow", "boxShadow", "p", "children", "direction", "base", "sm", "gap", "alignItems", "size", "name", "src", "profileImage", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "height", "orientation", "flexGrow", "justifyContent", "w", "fontWeight", "requests", "includes", "variant", "colorScheme", "onClick", "_id", "disabled", "noOfLines", "isTruncated", "bio", "slice", "_c", "MiniCard_Request", "acceptRequestHandler", "rejectRequestHandler", "px", "justify", "align", "textAlign", "_c2", "socket", "connect", "process", "env", "NODE_ENV", "withCredentials", "on", "console", "log", "FriendCard", "_s", "isOpen", "onOpen", "onClose", "chat", "setChat", "message", "setMessage", "refresh", "setRefresh", "user", "store", "authReducer", "loggedInUser", "chatContainerRef", "lastMessageRef", "sendMessage", "now", "Date", "options", "hour", "minute", "second", "formattedTime", "toLocaleDateString", "data", "sender", "receiver", "time", "post", "REACT_APP_API_URL", "then", "res", "emit", "catch", "err", "current", "scrollIntoView", "behavior", "get", "bor", "width", "mr", "ml", "justifySelf", "cursor", "CHATBOX", "className", "ref", "length", "map", "ele", "index", "flexDir", "value", "required", "onChange", "e", "target", "placeholder", "alignSelf", "_c3", "div", "_c4", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/src/components/Feed/MiniCard.jsx"], "sourcesContent": ["import {\r\n  Box,\r\n  Button,\r\n  Card,\r\n  CardBody,\r\n  Flex,\r\n  Heading,\r\n  Divider,\r\n  Center,\r\n  Text,\r\n  HStack,\r\n  useDisclosure,\r\n  Modal,\r\n  ModalOverlay,\r\n  ModalContent,\r\n  ModalHeader,\r\n  ModalCloseButton,\r\n  ModalBody,\r\n  ModalFooter,\r\n  Textarea,\r\n  VStack,\r\n} from \"@chakra-ui/react\";\r\nimport { ChatIcon } from \"@chakra-ui/icons\";\r\nimport React, { useEffect, useRef, useState } from \"react\";\r\nimport { Avatar, AvatarBadge, AvatarGroup } from \"@chakra-ui/react\";\r\nimport { io } from \"socket.io-client\";\r\n\r\nimport { AiOutlineUser } from \"react-icons/ai\";\r\nimport { styled } from \"styled-components\";\r\nimport { useSelector } from \"react-redux\";\r\nimport axios from \"axios\";\r\nconst MiniCard_Friends = ({ userId, friend, addRequestHandler }) => {\r\n  return (\r\n    <Card\r\n      maxW=\"md\"\r\n      mb=\"1rem\"\r\n      borderRadius=\"lg\"\r\n      overflow=\"hidden\"\r\n      boxShadow={\"md\"}\r\n      p=\"1rem\"\r\n    >\r\n      <Flex\r\n        direction={{ base: \"column\", sm: \"row\" }}\r\n        gap=\"1rem\"\r\n        alignItems=\"center\"\r\n      >\r\n        <Avatar size=\"md\" name={friend.name} src={friend.profileImage} />\r\n        <Center height=\"50px\">\r\n          <Divider orientation=\"vertical\" />\r\n        </Center>\r\n        <CardBody flexGrow={1} p={0}>\r\n          <Flex\r\n            mb={2}\r\n            justifyContent=\"space-between\"\r\n            alignItems=\"center\"\r\n            w=\"100%\"\r\n          >\r\n            <Heading size=\"sm\" fontWeight=\"500\">\r\n              {friend.name}\r\n            </Heading>\r\n            {!friend.requests.includes(userId) ? (\r\n              <Button\r\n                variant=\"outline\"\r\n                size=\"sm\"\r\n                colorScheme=\"primary\"\r\n                onClick={() => addRequestHandler(friend._id, friend.requests)}\r\n              >\r\n                Add Friend\r\n              </Button>\r\n            ) : (\r\n              <Button colorScheme=\"primary\" size=\"sm\" disabled>\r\n                Friend Request Sent\r\n              </Button>\r\n            )}\r\n          </Flex>\r\n          <Text noOfLines={2} isTruncated>\r\n            {friend.bio.slice(0, 40) + \"...\"}\r\n          </Text>\r\n        </CardBody>\r\n      </Flex>\r\n    </Card>\r\n  );\r\n};\r\n\r\nconst MiniCard_Request = ({\r\n  friend,\r\n  acceptRequestHandler,\r\n  rejectRequestHandler,\r\n}) => {\r\n  return (\r\n    <div>\r\n      <Card maxW=\"md\" px=\"4px\" borderRadius=\"none\" boxShadow=\"none\">\r\n        <Flex justify=\"space-between\" align=\"center\">\r\n          <Avatar size=\"md\" name={friend.name} src={friend.profileImage} />\r\n          <Box flexGrow={1} px={4}>\r\n            <Heading size=\"sm\" textAlign={\"left\"} fontWeight={\"500\"}>\r\n              {friend.name}\r\n            </Heading>\r\n          </Box>\r\n          <Flex gap={2} justifyContent=\"flex-end\" alignItems=\"center\">\r\n            {\" \"}\r\n            <Button\r\n              colorScheme=\"primary.500\"\r\n              size=\"xs\"\r\n              onClick={() => acceptRequestHandler(friend._id)}\r\n            >\r\n              Accept\r\n            </Button>\r\n            <Button\r\n              variant=\"outline\"\r\n              colorScheme=\"primary\"\r\n              size=\"xs\"\r\n              onClick={() => rejectRequestHandler(friend._id)}\r\n            >\r\n              Reject\r\n            </Button>\r\n          </Flex>\r\n        </Flex>\r\n      </Card>\r\n    </div>\r\n  );\r\n};\r\n\r\nconst socket = io.connect(\r\n  process.env.NODE_ENV === 'production'\r\n    ? \"https://concerned-picture-9849.onrender.com\"\r\n    : \"http://localhost:3001\",\r\n  {\r\n    withCredentials: true\r\n  }\r\n);\r\n\r\nsocket.on(\"connect\", () => {\r\n  console.log(\"Connected to the Socket.io server\");\r\n});\r\n\r\nconst FriendCard = ({ friend }) => {\r\n  const { isOpen, onOpen, onClose } = useDisclosure();\r\n  const [chat, setChat] = useState([]);\r\n  const [message, setMessage] = useState(\"\");\r\n  const [refresh, setRefresh] = useState(false);\r\n  const user = useSelector((store) => store.authReducer.loggedInUser);\r\n  const chatContainerRef = useRef();\r\n  const lastMessageRef = useRef();\r\n\r\n  const sendMessage = () => {\r\n    const now = new Date();\r\n    const options = {\r\n      hour: \"2-digit\",\r\n      minute: \"2-digit\",\r\n      second: \"2-digit\",\r\n    };\r\n    const formattedTime = now.toLocaleDateString(\"en-US\", options);\r\n    if (message === \"\") {\r\n      return;\r\n    }\r\n    const data = {\r\n      sender: user._id,\r\n      receiver: friend._id,\r\n      message: message,\r\n      time: formattedTime,\r\n    };\r\n\r\n    axios\r\n      .post(`${process.env.REACT_APP_API_URL}/chat/addmessage`, data)\r\n      .then((res) => {\r\n        console.log(res.data);\r\n        socket.emit(\"message\");\r\n        setRefresh(!refresh);\r\n      })\r\n      .catch((err) => {\r\n        console.log(err);\r\n      });\r\n    setMessage(\"\");\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (lastMessageRef.current) {\r\n      lastMessageRef.current.scrollIntoView({ behavior: \"smooth\" });\r\n    }\r\n  }, [chat]);\r\n\r\n  useEffect(() => {\r\n    axios\r\n      .get(\r\n        `${process.env.REACT_APP_API_URL}/chat/getmessage/${user._id}/${friend._id}`\r\n      )\r\n      .then((res) => {\r\n        console.log(res.data);\r\n        setChat(res.data);\r\n      })\r\n      .catch((err) => {\r\n        console.log(err);\r\n      });\r\n  }, [refresh]);\r\n\r\n  useEffect(() => {\r\n    socket.on(\"sendMessage\", (data) => {\r\n      console.log(\"Refresh message\")\r\n      axios.get(`${process.env.REACT_APP_API_URL}/chat/getmessage/${user._id}/${friend._id}`).then((res) => {\r\n        setChat(res.data)\r\n      }).catch((err) => {\r\n        console.log(err)\r\n      })\r\n    });\r\n  }, [socket]);\r\n\r\n  console.log(chat, \"chat\");\r\n\r\n  return (\r\n    <div>\r\n      <Card maxW=\"md\" p=\"1rem\" borderRadius=\"4px\" boxShadow=\"none\" bor>\r\n        <Flex justifyContent=\"space-between\" alignItems=\"center\" width=\"100%\">\r\n          <Avatar src={friend.profileImage} mr={\"1rem\"} />\r\n          <Center height=\"50px\">\r\n            <Divider orientation=\"vertical\" />\r\n          </Center>\r\n          <Flex justifyContent=\"space-between\" alignItems=\"center\" flexGrow={1}>\r\n            <Heading\r\n              ml=\"1rem\"\r\n              size=\"sm\"\r\n              justifySelf=\"flex-start\"\r\n              fontWeight=\"500\"\r\n            >\r\n              {friend.name}\r\n            </Heading>\r\n            <ChatIcon cursor={\"pointer\"} onClick={onOpen} />\r\n          </Flex>\r\n        </Flex>\r\n      </Card>\r\n      <Modal isOpen={isOpen} onClose={onClose}>\r\n        <ModalOverlay />\r\n        <ModalContent>\r\n          <ModalHeader>\r\n            <Flex width=\"100%\" alignItems={\"center\"}>\r\n              <Avatar src={friend.profileImage} />\r\n              <Heading ml=\"20px\" size=\"sm\" justifySelf=\"flex-start\">\r\n                {friend.name}\r\n              </Heading>\r\n            </Flex>\r\n          </ModalHeader>\r\n          <ModalCloseButton />\r\n          <ModalBody>\r\n            <CHATBOX>\r\n              <main className=\"msger-chat\" ref={chatContainerRef}>\r\n                {chat.length > 0 &&\r\n                  chat.map((ele, index) => (\r\n                    <div\r\n                      ref={index === chat.length - 1 ? lastMessageRef : null}\r\n                      className={\r\n                        ele.sender !== user._id\r\n                          ? \"msg left-msg\"\r\n                          : \"msg right-msg\"\r\n                      }\r\n                      key={ele._id}\r\n                    >\r\n                      <div className=\"msg-bubble\">\r\n                        <div className=\"msg-info\">\r\n                          <div className=\"msg-info-name\">\r\n                            {ele.sender !== user._id ? friend.name : user.name}\r\n                          </div>\r\n                          <div className=\"msg-info-time\">{ele.time}</div>\r\n                        </div>\r\n\r\n                        <div className=\"msg-text\">{ele.message}</div>\r\n                      </div>\r\n                    </div>\r\n                  ))}\r\n              </main>\r\n            </CHATBOX>\r\n          </ModalBody>\r\n\r\n          <ModalFooter>\r\n            <Flex flexDir={\"column\"} w=\"100%\">\r\n              <Textarea\r\n                value={message}\r\n                required={true}\r\n                onChange={(e) => setMessage(e.target.value)}\r\n                placeholder=\"Enter your message\"\r\n                width=\"100%\"\r\n                mb=\"1rem\"\r\n              ></Textarea>\r\n              <Button\r\n                onClick={() => sendMessage()}\r\n                variant=\"solid\"\r\n                alignSelf={\"flex-end\"}\r\n              >\r\n                Send message\r\n              </Button>\r\n            </Flex>\r\n          </ModalFooter>\r\n        </ModalContent>\r\n      </Modal>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport { MiniCard_Request, MiniCard_Friends, FriendCard };\r\n\r\nconst CHATBOX = styled.div`\r\n  .msger {\r\n    display: flex;\r\n    flex-flow: column wrap;\r\n    justify-content: space-between;\r\n    width: 100%;\r\n    max-width: 867px;\r\n    margin: 25px 10px;\r\n    height: calc(100% - 50px);\r\n    border: 2px solid #ddd;\r\n    border-radius: 5px;\r\n    background: #fff;\r\n    box-shadow: 0 15px 15px -5px rgba(0, 0, 0, 0.2);\r\n  }\r\n\r\n  .msger-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    padding: 10px;\r\n    border-bottom: 2px solid #ddd;\r\n    background: #eee;\r\n    color: #666;\r\n  }\r\n\r\n  .msger-chat {\r\n    height: 400px;\r\n    overflow-y: scroll;\r\n  }\r\n  .msger-chat::-webkit-scrollbar {\r\n    width: 6px;\r\n  }\r\n  .msger-chat::-webkit-scrollbar-track {\r\n    background: #ddd;\r\n  }\r\n  .msger-chat::-webkit-scrollbar-thumb {\r\n    background: #bdbdbd;\r\n  }\r\n  .msg {\r\n    display: flex;\r\n    align-items: flex-end;\r\n    margin-bottom: 10px;\r\n  }\r\n  .msg:last-of-type {\r\n    margin: 0;\r\n  }\r\n  .msg-img {\r\n    width: 50px;\r\n    height: 50px;\r\n    margin-right: 10px;\r\n    background: #ddd;\r\n    background-repeat: no-repeat;\r\n    background-position: center;\r\n    background-size: cover;\r\n    border-radius: 50%;\r\n  }\r\n  .msg-bubble {\r\n    max-width: 450px;\r\n    padding: 15px;\r\n    border-radius: 15px;\r\n    background: #ececec;\r\n  }\r\n  .msg-info {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-bottom: 1rem;\r\n  }\r\n  .msg-info-name {\r\n    margin-right: 10px;\r\n    font-weight: bold;\r\n  }\r\n  .msg-info-time {\r\n    font-size: 0.85em;\r\n  }\r\n\r\n  .left-msg .msg-bubble {\r\n    border-bottom-left-radius: 0;\r\n  }\r\n\r\n  .right-msg {\r\n    flex-direction: row-reverse;\r\n  }\r\n  .right-msg .msg-bubble {\r\n    background: #e89c45;\r\n    color: #fff;\r\n    border-bottom-right-radius: 0;\r\n  }\r\n  .right-msg .msg-img {\r\n    margin: 0 0 0 10px;\r\n  }\r\n\r\n  .msger-chat {\r\n    background-color: #fcfcfe;\r\n    background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='260' height='260' viewBox='0 0 260 260'%3E%3Cg fill-rule='evenodd'%3E%3Cg fill='%23dddddd' fill-opacity='0.4'%3E%3Cpath d='M24.37 16c.2.65.39 1.32.54 2H21.17l1.17 2.34.45.9-.24.11V28a5 5 0 0 1-2.23 8.94l-.02.06a8 8 0 0 1-7.75 6h-20a8 8 0 0 1-7.74-6l-.02-.06A5 5 0 0 1-17.45 28v-6.76l-.79-1.58-.44-.9.9-.44.63-.32H-20a23.01 23.01 0 0 1 44.37-2zm-36.82 2a1 1 0 0 0-.44.1l-3.1 1.56.89 1.79 1.31-.66a3 3 0 0 1 2.69 0l2.2 1.1a1 1 0 0 0 .9 0l2.21-1.1a3 3 0 0 1 2.69 0l2.2 1.1a1 1 0 0 0 .9 0l2.21-1.1a3 3 0 0 1 2.69 0l2.2 1.1a1 1 0 0 0 .86.02l2.88-1.27a3 3 0 0 1 2.43 0l2.88 1.27a1 1 0 0 0 .85-.02l3.1-1.55-.89-1.79-1.42.71a3 3 0 0 1-2.56.06l-2.77-1.23a1 1 0 0 0-.4-.09h-.01a1 1 0 0 0-.4.09l-2.78 1.23a3 3 0 0 1-2.56-.06l-2.3-1.15a1 1 0 0 0-.45-.11h-.01a1 1 0 0 0-.44.1L.9 19.22a3 3 0 0 1-2.69 0l-2.2-1.1a1 1 0 0 0-.45-.11h-.01a1 1 0 0 0-.44.1l-2.21 1.11a3 3 0 0 1-2.69 0l-2.2-1.1a1 1 0 0 0-.45-.11h-.01zm0-2h-4.9a21.01 21.01 0 0 1 39.61 0h-2.09l-.06-.13-.26.13h-32.31zm30.35 7.68l1.36-.68h1.3v2h-36v-1.15l.34-.17 1.36-.68h2.59l1.36.68a3 3 0 0 0 2.69 0l1.36-.68h2.59l1.36.68a3 3 0 0 0 2.69 0L2.26 23h2.59l1.36.68a3 3 0 0 0 2.56.06l1.67-.74h3.23l1.67.74a3 3 0 0 0 2.56-.06zM-13.82 27l16.37 4.91L18.93 27h-32.75zm-.63 2h.34l16.66 5 16.67-5h.33a3 3 0 1 1 0 6h-34a3 3 0 1 1 0-6zm1.35 8a6 6 0 0 0 5.65 4h20a6 6 0 0 0 5.66-4H-13.1z'/%3E%3Cpath id='path6_fill-copy' d='M284.37 16c.2.65.39 1.32.54 2H281.17l1.17 2.34.45.9-.24.11V28a5 5 0 0 1-2.23 8.94l-.02.06a8 8 0 0 1-7.75 6h-20a8 8 0 0 1-7.74-6l-.02-.06a5 5 0 0 1-2.24-8.94v-6.76l-.79-1.58-.44-.9.9-.44.63-.32H240a23.01 23.01 0 0 1 44.37-2zm-36.82 2a1 1 0 0 0-.44.1l-3.1 1.56.89 1.79 1.31-.66a3 3 0 0 1 2.69 0l2.2 1.1a1 1 0 0 0 .9 0l2.21-1.1a3 3 0 0 1 2.69 0l2.2 1.1a1 1 0 0 0 .9 0l2.21-1.1a3 3 0 0 1 2.69 0l2.2 1.1a1 1 0 0 0 .86.02l2.88-1.27a3 3 0 0 1 2.43 0l2.88 1.27a1 1 0 0 0 .85-.02l3.1-1.55-.89-1.79-1.42.71a3 3 0 0 1-2.56.06l-2.77-1.23a1 1 0 0 0-.4-.09h-.01a1 1 0 0 0-.4.09l-2.78 1.23a3 3 0 0 1-2.56-.06l-2.3-1.15a1 1 0 0 0-.45-.11h-.01a1 1 0 0 0-.44.1l-2.21 1.11a3 3 0 0 1-2.69 0l-2.2-1.1a1 1 0 0 0-.45-.11h-.01a1 1 0 0 0-.44.1l-2.21 1.11a3 3 0 0 1-2.69 0l-2.2-1.1a1 1 0 0 0-.45-.11h-.01zm0-2h-4.9a21.01 21.01 0 0 1 39.61 0h-2.09l-.06-.13-.26.13h-32.31zm30.35 7.68l1.36-.68h1.3v2h-36v-1.15l.34-.17 1.36-.68h2.59l1.36.68a3 3 0 0 0 2.69 0l1.36-.68h2.59l1.36.68a3 3 0 0 0 2.69 0l1.36-.68h2.59l1.36.68a3 3 0 0 0 2.56.06l1.67-.74h3.23l1.67.74a3 3 0 0 0 2.56-.06zM246.18 27l16.37 4.91L278.93 27h-32.75zm-.63 2h.34l16.66 5 16.67-5h.33a3 3 0 1 1 0 6h-34a3 3 0 1 1 0-6zm1.35 8a6 6 0 0 0 5.65 4h20a6 6 0 0 0 5.66-4H246.9z'/%3E%3Cpath d='M159.5 21.02A9 9 0 0 0 151 15h-42a9 9 0 0 0-8.5 6.02 6 6 0 0 0 .02 11.96A8.99 8.99 0 0 0 109 45h42a9 9 0 0 0 8.48-12.02 6 6 0 0 0 .02-11.96zM151 17h-42a7 7 0 0 0-6.33 4h54.66a7 7 0 0 0-6.33-4zm-9.34 26a8.98 8.98 0 0 0 3.34-7h-2a7 7 0 0 1-7 7h-4.34a8.98 8.98 0 0 0 3.34-7h-2a7 7 0 0 1-7 7h-4.34a8.98 8.98 0 0 0 3.34-7h-2a7 7 0 0 1-7 7h-7a7 7 0 1 1 0-14h42a7 7 0 1 1 0 14h-9.34zM109 27a9 9 0 0 0-7.48 4H101a4 4 0 1 1 0-8h58a4 4 0 0 1 0 8h-.52a9 9 0 0 0-7.48-4h-42z'/%3E%3Cpath d='M39 115a8 8 0 1 0 0-16 8 8 0 0 0 0 16zm6-8a6 6 0 1 1-12 0 6 6 0 0 1 12 0zm-3-29v-2h8v-6H40a4 4 0 0 0-4 4v10H22l-1.33 4-.67 2h2.19L26 130h26l3.81-40H58l-.67-2L56 84H42v-6zm-4-4v10h2V74h8v-2h-8a2 2 0 0 0-2 2zm2 12h14.56l.67 2H22.77l.67-2H40zm13.8 4H24.2l3.62 38h22.36l3.62-38z'/%3E%3Cpath d='M129 92h-6v4h-6v4h-6v14h-3l.24 2 3.76 32h36l3.76-32 .24-2h-3v-14h-6v-4h-6v-4h-8zm18 22v-12h-4v4h3v8h1zm-3 0v-6h-4v6h4zm-6 6v-16h-4v19.17c1.6-.7 2.97-1.8 4-3.17zm-6 3.8V100h-4v23.8a10.04 10.04 0 0 0 4 0zm-6-.63V104h-4v16a10.04 10.04 0 0 0 4 3.17zm-6-9.17v-6h-4v6h4zm-6 0v-8h3v-4h-4v12h1zm27-12v-4h-4v4h3v4h1v-4zm-6 0v-8h-4v4h3v4h1zm-6-4v-4h-4v8h1v-4h3zm-6 4v-4h-4v8h1v-4h3zm7 24a12 12 0 0 0 11.83-10h7.92l-3.53 30h-32.44l-3.53-30h7.92A12 12 0 0 0 130 126z'/%3E%3Cpath d='M212 86v2h-4v-2h4zm4 0h-2v2h2v-2zm-20 0v.1a5 5 0 0 0-.56 9.65l.06.25 1.12 4.48a2 2 0 0 0 1.94 1.52h.01l7.02 24.55a2 2 0 0 0 1.92 1.45h4.98a2 2 0 0 0 1.92-1.45l7.02-24.55a2 2 0 0 0 1.95-1.52L224.5 96l.06-.25a5 5 0 0 0-.56-9.65V86a14 14 0 0 0-28 0zm4 0h6v2h-9a3 3 0 1 0 0 6H223a3 3 0 1 0 0-6H220v-2h2a12 12 0 1 0-24 0h2zm-1.44 14l-1-4h24.88l-1 4h-22.88zm8.95 26l-6.86-24h18.7l-6.86 24h-4.98zM150 242a22 22 0 1 0 0-44 22 22 0 0 0 0 44zm24-22a24 24 0 1 1-48 0 24 24 0 0 1 48 0zm-28.38 17.73l2.04-.87a6 6 0 0 1 4.68 0l2.04.87a2 2 0 0 0 2.5-.82l1.14-1.9a6 6 0 0 1 3.79-2.75l2.15-.5a2 2 0 0 0 1.54-2.12l-.19-2.2a6 6 0 0 1 1.45-4.46l1.45-1.67a2 2 0 0 0 0-2.62l-1.45-1.67a6 6 0 0 1-1.45-4.46l.2-2.2a2 2 0 0 0-1.55-2.13l-2.15-.5a6 6 0 0 1-3.8-2.75l-1.13-1.9a2 2 0 0 0-2.5-.8l-2.04.86a6 6 0 0 1-4.68 0l-2.04-.87a2 2 0 0 0-2.5.82l-1.14 1.9a6 6 0 0 1-3.79 2.75l-2.15.5a2 2 0 0 0-1.54 2.12l.19 2.2a6 6 0 0 1-1.45 4.46l-1.45 1.67a2 2 0 0 0 0 2.62l1.45 1.67a6 6 0 0 1 1.45 4.46l-.2 2.2a2 2 0 0 0 1.55 2.13l2.15.5a6 6 0 0 1 3.8 2.75l1.13 1.9a2 2 0 0 0 2.5.8zm2.82.97a4 4 0 0 1 3.12 0l2.04.87a4 4 0 0 0 4.99-1.62l1.14-1.9a4 4 0 0 1 2.53-1.84l2.15-.5a4 4 0 0 0 3.09-4.24l-.2-2.2a4 4 0 0 1 .97-2.98l1.45-1.67a4 4 0 0 0 0-5.24l-1.45-1.67a4 4 0 0 1-.97-2.97l.2-2.2a4 4 0 0 0-3.09-4.25l-2.15-.5a4 4 0 0 1-2.53-1.84l-1.14-1.9a4 4 0 0 0-5-1.62l-2.03.87a4 4 0 0 1-3.12 0l-2.04-.87a4 4 0 0 0-4.99 1.62l-1.14 1.9a4 4 0 0 1-2.53 1.84l-2.15.5a4 4 0 0 0-3.09 4.24l.2 2.2a4 4 0 0 1-.97 2.98l-1.45 1.67a4 4 0 0 0 0 5.24l1.45 1.67a4 4 0 0 1 .97 2.97l-.2 2.2a4 4 0 0 0 3.09 4.25l2.15.5a4 4 0 0 1 2.53 1.84l1.14 1.9a4 4 0 0 0 5 1.62l2.03-.87zM152 207a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm6 2a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm-11 1a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm-6 0a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm3-5a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm-8 8a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm3 6a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm0 6a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm4 7a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm5-2a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm5 4a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm4-6a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm6-4a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm-4-3a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm4-3a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm-5-4a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm-24 6a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm16 5a5 5 0 1 0 0-10 5 5 0 0 0 0 10zm7-5a7 7 0 1 1-14 0 7 7 0 0 1 14 0zm86-29a1 1 0 0 0 0 2h2a1 1 0 0 0 0-2h-2zm19 9a1 1 0 0 1 1-1h2a1 1 0 0 1 0 2h-2a1 1 0 0 1-1-1zm-14 5a1 1 0 0 0 0 2h2a1 1 0 0 0 0-2h-2zm-25 1a1 1 0 0 0 0 2h2a1 1 0 0 0 0-2h-2zm5 4a1 1 0 0 0 0 2h2a1 1 0 0 0 0-2h-2zm9 0a1 1 0 0 1 1-1h2a1 1 0 0 1 0 2h-2a1 1 0 0 1-1-1zm15 1a1 1 0 0 1 1-1h2a1 1 0 0 1 0 2h-2a1 1 0 0 1-1-1zm12-2a1 1 0 0 0 0 2h2a1 1 0 0 0 0-2h-2zm-11-14a1 1 0 0 1 1-1h2a1 1 0 0 1 0 2h-2a1 1 0 0 1-1-1zm-19 0a1 1 0 0 0 0 2h2a1 1 0 0 0 0-2h-2zm6 5a1 1 0 0 1 1-1h2a1 1 0 0 1 0 2h-2a1 1 0 0 1-1-1zm-25 15c0-.47.01-.94.03-1.4a5 5 0 0 1-1.7-8 3.99 3.99 0 0 1 1.88-5.18 5 5 0 0 1 3.4-6.22 3 3 0 0 1 1.46-1.05 5 5 0 0 1 7.76-3.27A30.86 30.86 0 0 1 246 184c6.79 0 13.06 2.18 18.17 5.88a5 5 0 0 1 7.76 3.27 3 3 0 0 1 1.47 1.05 5 5 0 0 1 3.4 6.22 4 4 0 0 1 1.87 5.18 4.98 4.98 0 0 1-1.7 8c.02.46.03.93.03 1.4v1h-62v-1zm.83-7.17a30.9 30.9 0 0 0-.62 3.57 3 3 0 0 1-.61-4.2c.37.28.78.49 1.23.63zm1.49-4.61c-.36.87-.68 1.76-.96 2.68a2 2 0 0 1-.21-3.71c.33.4.73.75 1.17 1.03zm2.32-4.54c-.54.86-1.03 1.76-1.49 2.68a3 3 0 0 1-.07-4.67 3 3 0 0 0 1.56 1.99zm1.14-1.7c.35-.5.72-.98 1.1-1.46a1 1 0 1 0-1.1 1.45zm5.34-5.77c-1.03.86-2 1.79-2.9 2.77a3 3 0 0 0-1.11-.77 3 3 0 0 1 4-2zm42.66 2.77c-.9-.98-1.87-1.9-2.9-2.77a3 3 0 0 1 4.01 2 3 3 0 0 0-1.1.77zm1.34 1.54c.38.48.75.96 1.1 1.45a1 1 0 1 0-1.1-1.45zm3.73 5.84c-.46-.92-.95-1.82-1.5-2.68a3 3 0 0 0 1.57-1.99 3 3 0 0 1-.07 4.67zm1.8 4.53c-.29-.9-.6-1.8-.97-2.67.44-.28.84-.63 1.17-1.03a2 2 0 0 1-.2 3.7zm1.14 5.51c-.14-1.21-.35-2.4-.62-3.57.45-.14.86-.35 1.23-.63a2.99 2.99 0 0 1-.6 4.2zM275 214a29 29 0 0 0-57.97 0h57.96zM72.33 198.12c-.21-.32-.34-.7-.34-1.12v-12h-2v12a4.01 4.01 0 0 0 7.09 2.54c.57-.69.91-1.57.91-2.54v-12h-2v12a1.99 1.99 0 0 1-2 2 2 2 0 0 1-1.66-.88zM75 176c.38 0 .74-.04 1.1-.12a4 4 0 0 0 6.19 2.4A13.94 13.94 0 0 1 84 185v24a6 6 0 0 1-6 6h-3v9a5 5 0 1 1-10 0v-9h-3a6 6 0 0 1-6-6v-24a14 14 0 0 1 14-14 5 5 0 0 0 5 5zm-17 15v12a1.99 1.99 0 0 0 1.22 1.84 2 2 0 0 0 2.44-.72c.21-.32.34-.7.34-1.12v-12h2v12a3.98 3.98 0 0 1-5.35 3.77 3.98 3.98 0 0 1-.65-.3V209a4 4 0 0 0 4 4h16a4 4 0 0 0 4-4v-24c.01-1.53-.23-2.88-.72-4.17-.43.1-.87.16-1.28.17a6 6 0 0 1-5.2-3 7 7 0 0 1-6.47-4.88A12 12 0 0 0 58 185v6zm9 24v9a3 3 0 1 0 6 0v-9h-6z'/%3E%3Cpath d='M-17 191a1 1 0 0 0 0 2h2a1 1 0 0 0 0-2h-2zm19 9a1 1 0 0 1 1-1h2a1 1 0 0 1 0 2H3a1 1 0 0 1-1-1zm-14 5a1 1 0 0 0 0 2h2a1 1 0 0 0 0-2h-2zm-25 1a1 1 0 0 0 0 2h2a1 1 0 0 0 0-2h-2zm5 4a1 1 0 0 0 0 2h2a1 1 0 0 0 0-2h-2zm9 0a1 1 0 0 1 1-1h2a1 1 0 0 1 0 2h-2a1 1 0 0 1-1-1zm15 1a1 1 0 0 1 1-1h2a1 1 0 0 1 0 2h-2a1 1 0 0 1-1-1zm12-2a1 1 0 0 0 0 2h2a1 1 0 0 0 0-2H4zm-11-14a1 1 0 0 1 1-1h2a1 1 0 0 1 0 2h-2a1 1 0 0 1-1-1zm-19 0a1 1 0 0 0 0 2h2a1 1 0 0 0 0-2h-2zm6 5a1 1 0 0 1 1-1h2a1 1 0 0 1 0 2h-2a1 1 0 0 1-1-1zm-25 15c0-.47.01-.94.03-1.4a5 5 0 0 1-1.7-8 3.99 3.99 0 0 1 1.88-5.18 5 5 0 0 1 3.4-6.22 3 3 0 0 1 1.46-1.05 5 5 0 0 1 7.76-3.27A30.86 30.86 0 0 1-14 184c6.79 0 13.06 2.18 18.17 5.88a5 5 0 0 1 7.76 3.27 3 3 0 0 1 1.47 1.05 5 5 0 0 1 3.4 6.22 4 4 0 0 1 1.87 5.18 4.98 4.98 0 0 1-1.7 8c.02.46.03.93.03 1.4v1h-62v-1zm.83-7.17a30.9 30.9 0 0 0-.62 3.57 3 3 0 0 1-.61-4.2c.37.28.78.49 1.23.63zm1.49-4.61c-.36.87-.68 1.76-.96 2.68a2 2 0 0 1-.21-3.71c.33.4.73.75 1.17 1.03zm2.32-4.54c-.54.86-1.03 1.76-1.49 2.68a3 3 0 0 1-.07-4.67 3 3 0 0 0 1.56 1.99zm1.14-1.7c.35-.5.72-.98 1.1-1.46a1 1 0 1 0-1.1 1.45zm5.34-5.77c-1.03.86-2 1.79-2.9 2.77a3 3 0 0 0-1.11-.77 3 3 0 0 1 4-2zm42.66 2.77c-.9-.98-1.87-1.9-2.9-2.77a3 3 0 0 1 4.01 2 3 3 0 0 0-1.1.77zm1.34 1.54c.38.48.75.96 1.1 1.45a1 1 0 1 0-1.1-1.45zm3.73 5.84c-.46-.92-.95-1.82-1.5-2.68a3 3 0 0 0 1.57-1.99 3 3 0 0 1-.07 4.67zm1.8 4.53c-.29-.9-.6-1.8-.97-2.67.44-.28.84-.63 1.17-1.03a2 2 0 0 1-.2 3.7zm1.14 5.51c-.14-1.21-.35-2.4-.62-3.57.45-.14.86-.35 1.23-.63a2.99 2.99 0 0 1-.6 4.2zM15 214a29 29 0 0 0-57.97 0h57.96z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\");\r\n  }\r\n`;\r\n"], "mappings": ";;AAAA,SACEA,GAAG,EACHC,MAAM,EACNC,IAAI,EACJC,QAAQ,EACRC,IAAI,EACJC,OAAO,EACPC,OAAO,EACPC,MAAM,EACNC,IAAI,EACJC,MAAM,EACNC,aAAa,EACbC,KAAK,EACLC,YAAY,EACZC,YAAY,EACZC,WAAW,EACXC,gBAAgB,EAChBC,SAAS,EACTC,WAAW,EACXC,QAAQ,EACRC,MAAM,QACD,kBAAkB;AACzB,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,OAAOC,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,SAASC,MAAM,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACnE,SAASC,EAAE,QAAQ,kBAAkB;AAErC,SAASC,aAAa,QAAQ,gBAAgB;AAC9C,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,WAAW,QAAQ,aAAa;AACzC,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAC1B,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC,MAAM;EAAEC,MAAM;EAAEC;AAAkB,CAAC,KAAK;EAClE,oBACEJ,OAAA,CAAChC,IAAI;IACHqC,IAAI,EAAC,IAAI;IACTC,EAAE,EAAC,MAAM;IACTC,YAAY,EAAC,IAAI;IACjBC,QAAQ,EAAC,QAAQ;IACjBC,SAAS,EAAE,IAAK;IAChBC,CAAC,EAAC,MAAM;IAAAC,QAAA,eAERX,OAAA,CAAC9B,IAAI;MACH0C,SAAS,EAAE;QAAEC,IAAI,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAM,CAAE;MACzCC,GAAG,EAAC,MAAM;MACVC,UAAU,EAAC,QAAQ;MAAAL,QAAA,gBAEnBX,OAAA,CAACT,MAAM;QAAC0B,IAAI,EAAC,IAAI;QAACC,IAAI,EAAEf,MAAM,CAACe,IAAK;QAACC,GAAG,EAAEhB,MAAM,CAACiB;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACjExB,OAAA,CAAC3B,MAAM;QAACoD,MAAM,EAAC,MAAM;QAAAd,QAAA,eACnBX,OAAA,CAAC5B,OAAO;UAACsD,WAAW,EAAC;QAAU;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC,eACTxB,OAAA,CAAC/B,QAAQ;QAAC0D,QAAQ,EAAE,CAAE;QAACjB,CAAC,EAAE,CAAE;QAAAC,QAAA,gBAC1BX,OAAA,CAAC9B,IAAI;UACHoC,EAAE,EAAE,CAAE;UACNsB,cAAc,EAAC,eAAe;UAC9BZ,UAAU,EAAC,QAAQ;UACnBa,CAAC,EAAC,MAAM;UAAAlB,QAAA,gBAERX,OAAA,CAAC7B,OAAO;YAAC8C,IAAI,EAAC,IAAI;YAACa,UAAU,EAAC,KAAK;YAAAnB,QAAA,EAChCR,MAAM,CAACe;UAAI;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,EACT,CAACrB,MAAM,CAAC4B,QAAQ,CAACC,QAAQ,CAAC9B,MAAM,CAAC,gBAChCF,OAAA,CAACjC,MAAM;YACLkE,OAAO,EAAC,SAAS;YACjBhB,IAAI,EAAC,IAAI;YACTiB,WAAW,EAAC,SAAS;YACrBC,OAAO,EAAEA,CAAA,KAAM/B,iBAAiB,CAACD,MAAM,CAACiC,GAAG,EAAEjC,MAAM,CAAC4B,QAAQ,CAAE;YAAApB,QAAA,EAC/D;UAED;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,gBAETxB,OAAA,CAACjC,MAAM;YAACmE,WAAW,EAAC,SAAS;YAACjB,IAAI,EAAC,IAAI;YAACoB,QAAQ;YAAA1B,QAAA,EAAC;UAEjD;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eACPxB,OAAA,CAAC1B,IAAI;UAACgE,SAAS,EAAE,CAAE;UAACC,WAAW;UAAA5B,QAAA,EAC5BR,MAAM,CAACqC,GAAG,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG;QAAK;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEX,CAAC;AAACkB,EAAA,GAnDIzC,gBAAgB;AAqDtB,MAAM0C,gBAAgB,GAAGA,CAAC;EACxBxC,MAAM;EACNyC,oBAAoB;EACpBC;AACF,CAAC,KAAK;EACJ,oBACE7C,OAAA;IAAAW,QAAA,eACEX,OAAA,CAAChC,IAAI;MAACqC,IAAI,EAAC,IAAI;MAACyC,EAAE,EAAC,KAAK;MAACvC,YAAY,EAAC,MAAM;MAACE,SAAS,EAAC,MAAM;MAAAE,QAAA,eAC3DX,OAAA,CAAC9B,IAAI;QAAC6E,OAAO,EAAC,eAAe;QAACC,KAAK,EAAC,QAAQ;QAAArC,QAAA,gBAC1CX,OAAA,CAACT,MAAM;UAAC0B,IAAI,EAAC,IAAI;UAACC,IAAI,EAAEf,MAAM,CAACe,IAAK;UAACC,GAAG,EAAEhB,MAAM,CAACiB;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjExB,OAAA,CAAClC,GAAG;UAAC6D,QAAQ,EAAE,CAAE;UAACmB,EAAE,EAAE,CAAE;UAAAnC,QAAA,eACtBX,OAAA,CAAC7B,OAAO;YAAC8C,IAAI,EAAC,IAAI;YAACgC,SAAS,EAAE,MAAO;YAACnB,UAAU,EAAE,KAAM;YAAAnB,QAAA,EACrDR,MAAM,CAACe;UAAI;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACNxB,OAAA,CAAC9B,IAAI;UAAC6C,GAAG,EAAE,CAAE;UAACa,cAAc,EAAC,UAAU;UAACZ,UAAU,EAAC,QAAQ;UAAAL,QAAA,GACxD,GAAG,eACJX,OAAA,CAACjC,MAAM;YACLmE,WAAW,EAAC,aAAa;YACzBjB,IAAI,EAAC,IAAI;YACTkB,OAAO,EAAEA,CAAA,KAAMS,oBAAoB,CAACzC,MAAM,CAACiC,GAAG,CAAE;YAAAzB,QAAA,EACjD;UAED;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTxB,OAAA,CAACjC,MAAM;YACLkE,OAAO,EAAC,SAAS;YACjBC,WAAW,EAAC,SAAS;YACrBjB,IAAI,EAAC,IAAI;YACTkB,OAAO,EAAEA,CAAA,KAAMU,oBAAoB,CAAC1C,MAAM,CAACiC,GAAG,CAAE;YAAAzB,QAAA,EACjD;UAED;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC0B,GAAA,GArCIP,gBAAgB;AAuCtB,MAAMQ,MAAM,GAAGzD,EAAE,CAAC0D,OAAO,CACvBC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GACjC,6CAA6C,GAC7C,uBAAuB,EAC3B;EACEC,eAAe,EAAE;AACnB,CACF,CAAC;AAEDL,MAAM,CAACM,EAAE,CAAC,SAAS,EAAE,MAAM;EACzBC,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;AAClD,CAAC,CAAC;AAEF,MAAMC,UAAU,GAAGA,CAAC;EAAEzD;AAAO,CAAC,KAAK;EAAA0D,EAAA;EACjC,MAAM;IAAEC,MAAM;IAAEC,MAAM;IAAEC;EAAQ,CAAC,GAAGxF,aAAa,CAAC,CAAC;EACnD,MAAM,CAACyF,IAAI,EAAEC,OAAO,CAAC,GAAG5E,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAAC6E,OAAO,EAAEC,UAAU,CAAC,GAAG9E,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC+E,OAAO,EAAEC,UAAU,CAAC,GAAGhF,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAMiF,IAAI,GAAG1E,WAAW,CAAE2E,KAAK,IAAKA,KAAK,CAACC,WAAW,CAACC,YAAY,CAAC;EACnE,MAAMC,gBAAgB,GAAGtF,MAAM,CAAC,CAAC;EACjC,MAAMuF,cAAc,GAAGvF,MAAM,CAAC,CAAC;EAE/B,MAAMwF,WAAW,GAAGA,CAAA,KAAM;IACxB,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,MAAMC,OAAO,GAAG;MACdC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE,SAAS;MACjBC,MAAM,EAAE;IACV,CAAC;IACD,MAAMC,aAAa,GAAGN,GAAG,CAACO,kBAAkB,CAAC,OAAO,EAAEL,OAAO,CAAC;IAC9D,IAAIb,OAAO,KAAK,EAAE,EAAE;MAClB;IACF;IACA,MAAMmB,IAAI,GAAG;MACXC,MAAM,EAAEhB,IAAI,CAACnC,GAAG;MAChBoD,QAAQ,EAAErF,MAAM,CAACiC,GAAG;MACpB+B,OAAO,EAAEA,OAAO;MAChBsB,IAAI,EAAEL;IACR,CAAC;IAEDtF,KAAK,CACF4F,IAAI,CAAE,GAAErC,OAAO,CAACC,GAAG,CAACqC,iBAAkB,kBAAiB,EAAEL,IAAI,CAAC,CAC9DM,IAAI,CAAEC,GAAG,IAAK;MACbnC,OAAO,CAACC,GAAG,CAACkC,GAAG,CAACP,IAAI,CAAC;MACrBnC,MAAM,CAAC2C,IAAI,CAAC,SAAS,CAAC;MACtBxB,UAAU,CAAC,CAACD,OAAO,CAAC;IACtB,CAAC,CAAC,CACD0B,KAAK,CAAEC,GAAG,IAAK;MACdtC,OAAO,CAACC,GAAG,CAACqC,GAAG,CAAC;IAClB,CAAC,CAAC;IACJ5B,UAAU,CAAC,EAAE,CAAC;EAChB,CAAC;EAEDhF,SAAS,CAAC,MAAM;IACd,IAAIwF,cAAc,CAACqB,OAAO,EAAE;MAC1BrB,cAAc,CAACqB,OAAO,CAACC,cAAc,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAC,CAAC;IAC/D;EACF,CAAC,EAAE,CAAClC,IAAI,CAAC,CAAC;EAEV7E,SAAS,CAAC,MAAM;IACdU,KAAK,CACFsG,GAAG,CACD,GAAE/C,OAAO,CAACC,GAAG,CAACqC,iBAAkB,oBAAmBpB,IAAI,CAACnC,GAAI,IAAGjC,MAAM,CAACiC,GAAI,EAC7E,CAAC,CACAwD,IAAI,CAAEC,GAAG,IAAK;MACbnC,OAAO,CAACC,GAAG,CAACkC,GAAG,CAACP,IAAI,CAAC;MACrBpB,OAAO,CAAC2B,GAAG,CAACP,IAAI,CAAC;IACnB,CAAC,CAAC,CACDS,KAAK,CAAEC,GAAG,IAAK;MACdtC,OAAO,CAACC,GAAG,CAACqC,GAAG,CAAC;IAClB,CAAC,CAAC;EACN,CAAC,EAAE,CAAC3B,OAAO,CAAC,CAAC;EAEbjF,SAAS,CAAC,MAAM;IACd+D,MAAM,CAACM,EAAE,CAAC,aAAa,EAAG6B,IAAI,IAAK;MACjC5B,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;MAC9B7D,KAAK,CAACsG,GAAG,CAAE,GAAE/C,OAAO,CAACC,GAAG,CAACqC,iBAAkB,oBAAmBpB,IAAI,CAACnC,GAAI,IAAGjC,MAAM,CAACiC,GAAI,EAAC,CAAC,CAACwD,IAAI,CAAEC,GAAG,IAAK;QACpG3B,OAAO,CAAC2B,GAAG,CAACP,IAAI,CAAC;MACnB,CAAC,CAAC,CAACS,KAAK,CAAEC,GAAG,IAAK;QAChBtC,OAAO,CAACC,GAAG,CAACqC,GAAG,CAAC;MAClB,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC7C,MAAM,CAAC,CAAC;EAEZO,OAAO,CAACC,GAAG,CAACM,IAAI,EAAE,MAAM,CAAC;EAEzB,oBACEjE,OAAA;IAAAW,QAAA,gBACEX,OAAA,CAAChC,IAAI;MAACqC,IAAI,EAAC,IAAI;MAACK,CAAC,EAAC,MAAM;MAACH,YAAY,EAAC,KAAK;MAACE,SAAS,EAAC,MAAM;MAAC4F,GAAG;MAAA1F,QAAA,eAC9DX,OAAA,CAAC9B,IAAI;QAAC0D,cAAc,EAAC,eAAe;QAACZ,UAAU,EAAC,QAAQ;QAACsF,KAAK,EAAC,MAAM;QAAA3F,QAAA,gBACnEX,OAAA,CAACT,MAAM;UAAC4B,GAAG,EAAEhB,MAAM,CAACiB,YAAa;UAACmF,EAAE,EAAE;QAAO;UAAAlF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChDxB,OAAA,CAAC3B,MAAM;UAACoD,MAAM,EAAC,MAAM;UAAAd,QAAA,eACnBX,OAAA,CAAC5B,OAAO;YAACsD,WAAW,EAAC;UAAU;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACTxB,OAAA,CAAC9B,IAAI;UAAC0D,cAAc,EAAC,eAAe;UAACZ,UAAU,EAAC,QAAQ;UAACW,QAAQ,EAAE,CAAE;UAAAhB,QAAA,gBACnEX,OAAA,CAAC7B,OAAO;YACNqI,EAAE,EAAC,MAAM;YACTvF,IAAI,EAAC,IAAI;YACTwF,WAAW,EAAC,YAAY;YACxB3E,UAAU,EAAC,KAAK;YAAAnB,QAAA,EAEfR,MAAM,CAACe;UAAI;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACVxB,OAAA,CAACd,QAAQ;YAACwH,MAAM,EAAE,SAAU;YAACvE,OAAO,EAAE4B;UAAO;YAAA1C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACPxB,OAAA,CAACvB,KAAK;MAACqF,MAAM,EAAEA,MAAO;MAACE,OAAO,EAAEA,OAAQ;MAAArD,QAAA,gBACtCX,OAAA,CAACtB,YAAY;QAAA2C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAChBxB,OAAA,CAACrB,YAAY;QAAAgC,QAAA,gBACXX,OAAA,CAACpB,WAAW;UAAA+B,QAAA,eACVX,OAAA,CAAC9B,IAAI;YAACoI,KAAK,EAAC,MAAM;YAACtF,UAAU,EAAE,QAAS;YAAAL,QAAA,gBACtCX,OAAA,CAACT,MAAM;cAAC4B,GAAG,EAAEhB,MAAM,CAACiB;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpCxB,OAAA,CAAC7B,OAAO;cAACqI,EAAE,EAAC,MAAM;cAACvF,IAAI,EAAC,IAAI;cAACwF,WAAW,EAAC,YAAY;cAAA9F,QAAA,EAClDR,MAAM,CAACe;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eACdxB,OAAA,CAACnB,gBAAgB;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpBxB,OAAA,CAAClB,SAAS;UAAA6B,QAAA,eACRX,OAAA,CAAC2G,OAAO;YAAAhG,QAAA,eACNX,OAAA;cAAM4G,SAAS,EAAC,YAAY;cAACC,GAAG,EAAElC,gBAAiB;cAAAhE,QAAA,EAChDsD,IAAI,CAAC6C,MAAM,GAAG,CAAC,IACd7C,IAAI,CAAC8C,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,kBAClBjH,OAAA;gBACE6G,GAAG,EAAEI,KAAK,KAAKhD,IAAI,CAAC6C,MAAM,GAAG,CAAC,GAAGlC,cAAc,GAAG,IAAK;gBACvDgC,SAAS,EACPI,GAAG,CAACzB,MAAM,KAAKhB,IAAI,CAACnC,GAAG,GACnB,cAAc,GACd,eACL;gBAAAzB,QAAA,eAGDX,OAAA;kBAAK4G,SAAS,EAAC,YAAY;kBAAAjG,QAAA,gBACzBX,OAAA;oBAAK4G,SAAS,EAAC,UAAU;oBAAAjG,QAAA,gBACvBX,OAAA;sBAAK4G,SAAS,EAAC,eAAe;sBAAAjG,QAAA,EAC3BqG,GAAG,CAACzB,MAAM,KAAKhB,IAAI,CAACnC,GAAG,GAAGjC,MAAM,CAACe,IAAI,GAAGqD,IAAI,CAACrD;oBAAI;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/C,CAAC,eACNxB,OAAA;sBAAK4G,SAAS,EAAC,eAAe;sBAAAjG,QAAA,EAAEqG,GAAG,CAACvB;oBAAI;sBAAApE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5C,CAAC,eAENxB,OAAA;oBAAK4G,SAAS,EAAC,UAAU;oBAAAjG,QAAA,EAAEqG,GAAG,CAAC7C;kBAAO;oBAAA9C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C;cAAC,GAXDwF,GAAG,CAAC5E,GAAG;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAYT,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAEZxB,OAAA,CAACjB,WAAW;UAAA4B,QAAA,eACVX,OAAA,CAAC9B,IAAI;YAACgJ,OAAO,EAAE,QAAS;YAACrF,CAAC,EAAC,MAAM;YAAAlB,QAAA,gBAC/BX,OAAA,CAAChB,QAAQ;cACPmI,KAAK,EAAEhD,OAAQ;cACfiD,QAAQ,EAAE,IAAK;cACfC,QAAQ,EAAGC,CAAC,IAAKlD,UAAU,CAACkD,CAAC,CAACC,MAAM,CAACJ,KAAK,CAAE;cAC5CK,WAAW,EAAC,oBAAoB;cAChClB,KAAK,EAAC,MAAM;cACZhG,EAAE,EAAC;YAAM;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACZxB,OAAA,CAACjC,MAAM;cACLoE,OAAO,EAAEA,CAAA,KAAM0C,WAAW,CAAC,CAAE;cAC7B5C,OAAO,EAAC,OAAO;cACfwF,SAAS,EAAE,UAAW;cAAA9G,QAAA,EACvB;YAED;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACqC,EAAA,CA/JID,UAAU;EAAA,QACsBpF,aAAa,EAIpCqB,WAAW;AAAA;AAAA6H,GAAA,GALpB9D,UAAU;AAiKhB,SAASjB,gBAAgB,EAAE1C,gBAAgB,EAAE2D,UAAU;AAEvD,MAAM+C,OAAO,GAAG/G,MAAM,CAAC+H,GAAI;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GA/FIjB,OAAO;AAAA,IAAAjE,EAAA,EAAAQ,GAAA,EAAAwE,GAAA,EAAAE,GAAA;AAAAC,YAAA,CAAAnF,EAAA;AAAAmF,YAAA,CAAA3E,GAAA;AAAA2E,YAAA,CAAAH,GAAA;AAAAG,YAAA,CAAAD,GAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}