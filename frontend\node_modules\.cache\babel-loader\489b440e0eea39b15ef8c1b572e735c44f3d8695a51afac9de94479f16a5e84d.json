{"ast": null, "code": "import { __assign, __rest } from \"tslib\";\nimport * as React from 'react';\nimport { fullWidthClassName, zeroRightClassName } from 'react-remove-scroll-bar/constants';\nimport { useMergeRefs } from 'use-callback-ref';\nimport { effectCar } from './medium';\nvar nothing = function () {\n  return;\n};\n/**\n * Removes scrollbar from the page and contain the scroll within the Lock\n */\nvar RemoveScroll = React.forwardRef(function (props, parentRef) {\n  var ref = React.useRef(null);\n  var _a = React.useState({\n      onScrollCapture: nothing,\n      onWheelCapture: nothing,\n      onTouchMoveCapture: nothing\n    }),\n    callbacks = _a[0],\n    setCallbacks = _a[1];\n  var forwardProps = props.forwardProps,\n    children = props.children,\n    className = props.className,\n    removeScrollBar = props.removeScrollBar,\n    enabled = props.enabled,\n    shards = props.shards,\n    sideCar = props.sideCar,\n    noIsolation = props.noIsolation,\n    inert = props.inert,\n    allowPinchZoom = props.allowPinchZoom,\n    _b = props.as,\n    Container = _b === void 0 ? 'div' : _b,\n    gapMode = props.gapMode,\n    rest = __rest(props, [\"forwardProps\", \"children\", \"className\", \"removeScrollBar\", \"enabled\", \"shards\", \"sideCar\", \"noIsolation\", \"inert\", \"allowPinchZoom\", \"as\", \"gapMode\"]);\n  var SideCar = sideCar;\n  var containerRef = useMergeRefs([ref, parentRef]);\n  var containerProps = __assign(__assign({}, rest), callbacks);\n  return React.createElement(React.Fragment, null, enabled && React.createElement(SideCar, {\n    sideCar: effectCar,\n    removeScrollBar: removeScrollBar,\n    shards: shards,\n    noIsolation: noIsolation,\n    inert: inert,\n    setCallbacks: setCallbacks,\n    allowPinchZoom: !!allowPinchZoom,\n    lockRef: ref,\n    gapMode: gapMode\n  }), forwardProps ? React.cloneElement(React.Children.only(children), __assign(__assign({}, containerProps), {\n    ref: containerRef\n  })) : React.createElement(Container, __assign({}, containerProps, {\n    className: className,\n    ref: containerRef\n  }), children));\n});\nRemoveScroll.defaultProps = {\n  enabled: true,\n  removeScrollBar: true,\n  inert: false\n};\nRemoveScroll.classNames = {\n  fullWidth: fullWidthClassName,\n  zeroRight: zeroRightClassName\n};\nexport { RemoveScroll };", "map": {"version": 3, "names": ["__assign", "__rest", "React", "fullWidthClassName", "zeroRightClassName", "useMergeRefs", "effectCar", "nothing", "RemoveScroll", "forwardRef", "props", "parentRef", "ref", "useRef", "_a", "useState", "onScrollCapture", "onWheelCapture", "onTouchMoveCapture", "callbacks", "setCallbacks", "forwardProps", "children", "className", "removeScrollBar", "enabled", "shards", "sideCar", "noIsolation", "inert", "allowPinchZoom", "_b", "as", "Container", "gapMode", "rest", "SideCar", "containerRef", "containerProps", "createElement", "Fragment", "lockRef", "cloneElement", "Children", "only", "defaultProps", "classNames", "fullWidth", "zeroRight"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/react-remove-scroll/dist/es2015/UI.js"], "sourcesContent": ["import { __assign, __rest } from \"tslib\";\nimport * as React from 'react';\nimport { fullWidthClassName, zeroRightClassName } from 'react-remove-scroll-bar/constants';\nimport { useMergeRefs } from 'use-callback-ref';\nimport { effectCar } from './medium';\nvar nothing = function () {\n    return;\n};\n/**\n * Removes scrollbar from the page and contain the scroll within the Lock\n */\nvar RemoveScroll = React.forwardRef(function (props, parentRef) {\n    var ref = React.useRef(null);\n    var _a = React.useState({\n        onScrollCapture: nothing,\n        onWheelCapture: nothing,\n        onTouchMoveCapture: nothing,\n    }), callbacks = _a[0], setCallbacks = _a[1];\n    var forwardProps = props.forwardProps, children = props.children, className = props.className, removeScrollBar = props.removeScrollBar, enabled = props.enabled, shards = props.shards, sideCar = props.sideCar, noIsolation = props.noIsolation, inert = props.inert, allowPinchZoom = props.allowPinchZoom, _b = props.as, Container = _b === void 0 ? 'div' : _b, gapMode = props.gapMode, rest = __rest(props, [\"forwardProps\", \"children\", \"className\", \"removeScrollBar\", \"enabled\", \"shards\", \"sideCar\", \"noIsolation\", \"inert\", \"allowPinchZoom\", \"as\", \"gapMode\"]);\n    var SideCar = sideCar;\n    var containerRef = useMergeRefs([ref, parentRef]);\n    var containerProps = __assign(__assign({}, rest), callbacks);\n    return (React.createElement(React.Fragment, null,\n        enabled && (React.createElement(SideCar, { sideCar: effectCar, removeScrollBar: removeScrollBar, shards: shards, noIsolation: noIsolation, inert: inert, setCallbacks: setCallbacks, allowPinchZoom: !!allowPinchZoom, lockRef: ref, gapMode: gapMode })),\n        forwardProps ? (React.cloneElement(React.Children.only(children), __assign(__assign({}, containerProps), { ref: containerRef }))) : (React.createElement(Container, __assign({}, containerProps, { className: className, ref: containerRef }), children))));\n});\nRemoveScroll.defaultProps = {\n    enabled: true,\n    removeScrollBar: true,\n    inert: false,\n};\nRemoveScroll.classNames = {\n    fullWidth: fullWidthClassName,\n    zeroRight: zeroRightClassName,\n};\nexport { RemoveScroll };\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AACxC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkB,EAAEC,kBAAkB,QAAQ,mCAAmC;AAC1F,SAASC,YAAY,QAAQ,kBAAkB;AAC/C,SAASC,SAAS,QAAQ,UAAU;AACpC,IAAIC,OAAO,GAAG,SAAAA,CAAA,EAAY;EACtB;AACJ,CAAC;AACD;AACA;AACA;AACA,IAAIC,YAAY,GAAGN,KAAK,CAACO,UAAU,CAAC,UAAUC,KAAK,EAAEC,SAAS,EAAE;EAC5D,IAAIC,GAAG,GAAGV,KAAK,CAACW,MAAM,CAAC,IAAI,CAAC;EAC5B,IAAIC,EAAE,GAAGZ,KAAK,CAACa,QAAQ,CAAC;MACpBC,eAAe,EAAET,OAAO;MACxBU,cAAc,EAAEV,OAAO;MACvBW,kBAAkB,EAAEX;IACxB,CAAC,CAAC;IAAEY,SAAS,GAAGL,EAAE,CAAC,CAAC,CAAC;IAAEM,YAAY,GAAGN,EAAE,CAAC,CAAC,CAAC;EAC3C,IAAIO,YAAY,GAAGX,KAAK,CAACW,YAAY;IAAEC,QAAQ,GAAGZ,KAAK,CAACY,QAAQ;IAAEC,SAAS,GAAGb,KAAK,CAACa,SAAS;IAAEC,eAAe,GAAGd,KAAK,CAACc,eAAe;IAAEC,OAAO,GAAGf,KAAK,CAACe,OAAO;IAAEC,MAAM,GAAGhB,KAAK,CAACgB,MAAM;IAAEC,OAAO,GAAGjB,KAAK,CAACiB,OAAO;IAAEC,WAAW,GAAGlB,KAAK,CAACkB,WAAW;IAAEC,KAAK,GAAGnB,KAAK,CAACmB,KAAK;IAAEC,cAAc,GAAGpB,KAAK,CAACoB,cAAc;IAAEC,EAAE,GAAGrB,KAAK,CAACsB,EAAE;IAAEC,SAAS,GAAGF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,EAAE;IAAEG,OAAO,GAAGxB,KAAK,CAACwB,OAAO;IAAEC,IAAI,GAAGlC,MAAM,CAACS,KAAK,EAAE,CAAC,cAAc,EAAE,UAAU,EAAE,WAAW,EAAE,iBAAiB,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,aAAa,EAAE,OAAO,EAAE,gBAAgB,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;EAC3iB,IAAI0B,OAAO,GAAGT,OAAO;EACrB,IAAIU,YAAY,GAAGhC,YAAY,CAAC,CAACO,GAAG,EAAED,SAAS,CAAC,CAAC;EACjD,IAAI2B,cAAc,GAAGtC,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEmC,IAAI,CAAC,EAAEhB,SAAS,CAAC;EAC5D,OAAQjB,KAAK,CAACqC,aAAa,CAACrC,KAAK,CAACsC,QAAQ,EAAE,IAAI,EAC5Cf,OAAO,IAAKvB,KAAK,CAACqC,aAAa,CAACH,OAAO,EAAE;IAAET,OAAO,EAAErB,SAAS;IAAEkB,eAAe,EAAEA,eAAe;IAAEE,MAAM,EAAEA,MAAM;IAAEE,WAAW,EAAEA,WAAW;IAAEC,KAAK,EAAEA,KAAK;IAAET,YAAY,EAAEA,YAAY;IAAEU,cAAc,EAAE,CAAC,CAACA,cAAc;IAAEW,OAAO,EAAE7B,GAAG;IAAEsB,OAAO,EAAEA;EAAQ,CAAC,CAAE,EACzPb,YAAY,GAAInB,KAAK,CAACwC,YAAY,CAACxC,KAAK,CAACyC,QAAQ,CAACC,IAAI,CAACtB,QAAQ,CAAC,EAAEtB,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEsC,cAAc,CAAC,EAAE;IAAE1B,GAAG,EAAEyB;EAAa,CAAC,CAAC,CAAC,GAAKnC,KAAK,CAACqC,aAAa,CAACN,SAAS,EAAEjC,QAAQ,CAAC,CAAC,CAAC,EAAEsC,cAAc,EAAE;IAAEf,SAAS,EAAEA,SAAS;IAAEX,GAAG,EAAEyB;EAAa,CAAC,CAAC,EAAEf,QAAQ,CAAE,CAAC;AAClQ,CAAC,CAAC;AACFd,YAAY,CAACqC,YAAY,GAAG;EACxBpB,OAAO,EAAE,IAAI;EACbD,eAAe,EAAE,IAAI;EACrBK,KAAK,EAAE;AACX,CAAC;AACDrB,YAAY,CAACsC,UAAU,GAAG;EACtBC,SAAS,EAAE5C,kBAAkB;EAC7B6C,SAAS,EAAE5C;AACf,CAAC;AACD,SAASI,YAAY"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}