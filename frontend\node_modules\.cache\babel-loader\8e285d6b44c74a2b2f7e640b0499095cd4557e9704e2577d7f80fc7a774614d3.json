{"ast": null, "code": "export var safeProbe = function (cb) {\n  try {\n    return cb();\n  } catch (e) {\n    return undefined;\n  }\n};", "map": {"version": 3, "names": ["safeProbe", "cb", "e", "undefined"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/focus-lock/dist/es2015/utils/safe.js"], "sourcesContent": ["export var safeProbe = function (cb) {\n    try {\n        return cb();\n    }\n    catch (e) {\n        return undefined;\n    }\n};\n"], "mappings": "AAAA,OAAO,IAAIA,SAAS,GAAG,SAAAA,CAAUC,EAAE,EAAE;EACjC,IAAI;IACA,OAAOA,EAAE,CAAC,CAAC;EACf,CAAC,CACD,OAAOC,CAAC,EAAE;IACN,OAAOC,SAAS;EACpB;AACJ,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}