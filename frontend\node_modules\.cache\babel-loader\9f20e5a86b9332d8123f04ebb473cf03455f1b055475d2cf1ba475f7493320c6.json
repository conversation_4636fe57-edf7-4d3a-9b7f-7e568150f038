{"ast": null, "code": "function addDomEvent(target, eventName, handler, options = {\n  passive: true\n}) {\n  target.addEventListener(eventName, handler, options);\n  return () => target.removeEventListener(eventName, handler);\n}\nexport { addDomEvent };", "map": {"version": 3, "names": ["addDomEvent", "target", "eventName", "handler", "options", "passive", "addEventListener", "removeEventListener"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/framer-motion/dist/es/events/add-dom-event.mjs"], "sourcesContent": ["function addDomEvent(target, eventName, handler, options = { passive: true }) {\n    target.addEventListener(eventName, handler, options);\n    return () => target.removeEventListener(eventName, handler);\n}\n\nexport { addDomEvent };\n"], "mappings": "AAAA,SAASA,WAAWA,CAACC,MAAM,EAAEC,SAAS,EAAEC,OAAO,EAAEC,OAAO,GAAG;EAAEC,OAAO,EAAE;AAAK,CAAC,EAAE;EAC1EJ,MAAM,CAACK,gBAAgB,CAACJ,SAAS,EAAEC,OAAO,EAAEC,OAAO,CAAC;EACpD,OAAO,MAAMH,MAAM,CAACM,mBAAmB,CAACL,SAAS,EAAEC,OAAO,CAAC;AAC/D;AAEA,SAASH,WAAW"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}