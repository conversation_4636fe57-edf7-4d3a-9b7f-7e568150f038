{"ast": null, "code": "import { toArray } from './array';\nexport var tabSort = function (a, b) {\n  var tabDiff = a.tabIndex - b.tabIndex;\n  var indexDiff = a.index - b.index;\n  if (tabDiff) {\n    if (!a.tabIndex) {\n      return 1;\n    }\n    if (!b.tabIndex) {\n      return -1;\n    }\n  }\n  return tabDiff || indexDiff;\n};\nexport var orderByTabIndex = function (nodes, filterNegative, keepGuards) {\n  return toArray(nodes).map(function (node, index) {\n    return {\n      node: node,\n      index: index,\n      tabIndex: keepGuards && node.tabIndex === -1 ? (node.dataset || {}).focusGuard ? 0 : -1 : node.tabIndex\n    };\n  }).filter(function (data) {\n    return !filterNegative || data.tabIndex >= 0;\n  }).sort(tabSort);\n};", "map": {"version": 3, "names": ["toArray", "tabSort", "a", "b", "tabDiff", "tabIndex", "indexDiff", "index", "orderByTabIndex", "nodes", "filterNegative", "keep<PERSON><PERSON>s", "map", "node", "dataset", "focusGuard", "filter", "data", "sort"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/focus-lock/dist/es2015/utils/tabOrder.js"], "sourcesContent": ["import { toArray } from './array';\nexport var tabSort = function (a, b) {\n    var tabDiff = a.tabIndex - b.tabIndex;\n    var indexDiff = a.index - b.index;\n    if (tabDiff) {\n        if (!a.tabIndex) {\n            return 1;\n        }\n        if (!b.tabIndex) {\n            return -1;\n        }\n    }\n    return tabDiff || indexDiff;\n};\nexport var orderByTabIndex = function (nodes, filterNegative, keepGuards) {\n    return toArray(nodes)\n        .map(function (node, index) { return ({\n        node: node,\n        index: index,\n        tabIndex: keepGuards && node.tabIndex === -1 ? ((node.dataset || {}).focusGuard ? 0 : -1) : node.tabIndex,\n    }); })\n        .filter(function (data) { return !filterNegative || data.tabIndex >= 0; })\n        .sort(tabSort);\n};\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,SAAS;AACjC,OAAO,IAAIC,OAAO,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;EACjC,IAAIC,OAAO,GAAGF,CAAC,CAACG,QAAQ,GAAGF,CAAC,CAACE,QAAQ;EACrC,IAAIC,SAAS,GAAGJ,CAAC,CAACK,KAAK,GAAGJ,CAAC,CAACI,KAAK;EACjC,IAAIH,OAAO,EAAE;IACT,IAAI,CAACF,CAAC,CAACG,QAAQ,EAAE;MACb,OAAO,CAAC;IACZ;IACA,IAAI,CAACF,CAAC,CAACE,QAAQ,EAAE;MACb,OAAO,CAAC,CAAC;IACb;EACJ;EACA,OAAOD,OAAO,IAAIE,SAAS;AAC/B,CAAC;AACD,OAAO,IAAIE,eAAe,GAAG,SAAAA,CAAUC,KAAK,EAAEC,cAAc,EAAEC,UAAU,EAAE;EACtE,OAAOX,OAAO,CAACS,KAAK,CAAC,CAChBG,GAAG,CAAC,UAAUC,IAAI,EAAEN,KAAK,EAAE;IAAE,OAAQ;MACtCM,IAAI,EAAEA,IAAI;MACVN,KAAK,EAAEA,KAAK;MACZF,QAAQ,EAAEM,UAAU,IAAIE,IAAI,CAACR,QAAQ,KAAK,CAAC,CAAC,GAAI,CAACQ,IAAI,CAACC,OAAO,IAAI,CAAC,CAAC,EAAEC,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC,GAAIF,IAAI,CAACR;IACrG,CAAC;EAAG,CAAC,CAAC,CACDW,MAAM,CAAC,UAAUC,IAAI,EAAE;IAAE,OAAO,CAACP,cAAc,IAAIO,IAAI,CAACZ,QAAQ,IAAI,CAAC;EAAE,CAAC,CAAC,CACzEa,IAAI,CAACjB,OAAO,CAAC;AACtB,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}