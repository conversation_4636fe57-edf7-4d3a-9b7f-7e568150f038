{"ast": null, "code": "import { mix } from './mix.mjs';\nimport { mixColor } from './mix-color.mjs';\nimport { pipe } from './pipe.mjs';\nimport { warning } from './errors.mjs';\nimport { color } from '../value/types/color/index.mjs';\nimport { complex, analyseComplexValue } from '../value/types/complex/index.mjs';\nconst mixImmediate = (origin, target) => p => `${p > 0 ? target : origin}`;\nfunction getMixer(origin, target) {\n  if (typeof origin === \"number\") {\n    return v => mix(origin, target, v);\n  } else if (color.test(origin)) {\n    return mixColor(origin, target);\n  } else {\n    return origin.startsWith(\"var(\") ? mixImmediate(origin, target) : mixComplex(origin, target);\n  }\n}\nconst mixArray = (from, to) => {\n  const output = [...from];\n  const numValues = output.length;\n  const blendValue = from.map((fromThis, i) => getMixer(fromThis, to[i]));\n  return v => {\n    for (let i = 0; i < numValues; i++) {\n      output[i] = blendValue[i](v);\n    }\n    return output;\n  };\n};\nconst mixObject = (origin, target) => {\n  const output = {\n    ...origin,\n    ...target\n  };\n  const blendValue = {};\n  for (const key in output) {\n    if (origin[key] !== undefined && target[key] !== undefined) {\n      blendValue[key] = getMixer(origin[key], target[key]);\n    }\n  }\n  return v => {\n    for (const key in blendValue) {\n      output[key] = blendValue[key](v);\n    }\n    return output;\n  };\n};\nconst mixComplex = (origin, target) => {\n  const template = complex.createTransformer(target);\n  const originStats = analyseComplexValue(origin);\n  const targetStats = analyseComplexValue(target);\n  const canInterpolate = originStats.numVars === targetStats.numVars && originStats.numColors === targetStats.numColors && originStats.numNumbers >= targetStats.numNumbers;\n  if (canInterpolate) {\n    return pipe(mixArray(originStats.values, targetStats.values), template);\n  } else {\n    warning(true, `Complex values '${origin}' and '${target}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`);\n    return mixImmediate(origin, target);\n  }\n};\nexport { mixArray, mixComplex, mixObject };", "map": {"version": 3, "names": ["mix", "mixColor", "pipe", "warning", "color", "complex", "analyseComplexValue", "mixImmediate", "origin", "target", "p", "getMixer", "v", "test", "startsWith", "mixComplex", "mixArray", "from", "to", "output", "numValues", "length", "blendValue", "map", "fromThis", "i", "mixObject", "key", "undefined", "template", "createTransformer", "originStats", "targetStats", "canInterpolate", "numVars", "numColors", "numNumbers", "values"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/framer-motion/dist/es/utils/mix-complex.mjs"], "sourcesContent": ["import { mix } from './mix.mjs';\nimport { mixColor } from './mix-color.mjs';\nimport { pipe } from './pipe.mjs';\nimport { warning } from './errors.mjs';\nimport { color } from '../value/types/color/index.mjs';\nimport { complex, analyseComplexValue } from '../value/types/complex/index.mjs';\n\nconst mixImmediate = (origin, target) => (p) => `${p > 0 ? target : origin}`;\nfunction getMixer(origin, target) {\n    if (typeof origin === \"number\") {\n        return (v) => mix(origin, target, v);\n    }\n    else if (color.test(origin)) {\n        return mixColor(origin, target);\n    }\n    else {\n        return origin.startsWith(\"var(\")\n            ? mixImmediate(origin, target)\n            : mixComplex(origin, target);\n    }\n}\nconst mixArray = (from, to) => {\n    const output = [...from];\n    const numValues = output.length;\n    const blendValue = from.map((fromThis, i) => getMixer(fromThis, to[i]));\n    return (v) => {\n        for (let i = 0; i < numValues; i++) {\n            output[i] = blendValue[i](v);\n        }\n        return output;\n    };\n};\nconst mixObject = (origin, target) => {\n    const output = { ...origin, ...target };\n    const blendValue = {};\n    for (const key in output) {\n        if (origin[key] !== undefined && target[key] !== undefined) {\n            blendValue[key] = getMixer(origin[key], target[key]);\n        }\n    }\n    return (v) => {\n        for (const key in blendValue) {\n            output[key] = blendValue[key](v);\n        }\n        return output;\n    };\n};\nconst mixComplex = (origin, target) => {\n    const template = complex.createTransformer(target);\n    const originStats = analyseComplexValue(origin);\n    const targetStats = analyseComplexValue(target);\n    const canInterpolate = originStats.numVars === targetStats.numVars &&\n        originStats.numColors === targetStats.numColors &&\n        originStats.numNumbers >= targetStats.numNumbers;\n    if (canInterpolate) {\n        return pipe(mixArray(originStats.values, targetStats.values), template);\n    }\n    else {\n        warning(true, `Complex values '${origin}' and '${target}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`);\n        return mixImmediate(origin, target);\n    }\n};\n\nexport { mixArray, mixComplex, mixObject };\n"], "mappings": "AAAA,SAASA,GAAG,QAAQ,WAAW;AAC/B,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,IAAI,QAAQ,YAAY;AACjC,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,KAAK,QAAQ,gCAAgC;AACtD,SAASC,OAAO,EAAEC,mBAAmB,QAAQ,kCAAkC;AAE/E,MAAMC,YAAY,GAAGA,CAACC,MAAM,EAAEC,MAAM,KAAMC,CAAC,IAAM,GAAEA,CAAC,GAAG,CAAC,GAAGD,MAAM,GAAGD,MAAO,EAAC;AAC5E,SAASG,QAAQA,CAACH,MAAM,EAAEC,MAAM,EAAE;EAC9B,IAAI,OAAOD,MAAM,KAAK,QAAQ,EAAE;IAC5B,OAAQI,CAAC,IAAKZ,GAAG,CAACQ,MAAM,EAAEC,MAAM,EAAEG,CAAC,CAAC;EACxC,CAAC,MACI,IAAIR,KAAK,CAACS,IAAI,CAACL,MAAM,CAAC,EAAE;IACzB,OAAOP,QAAQ,CAACO,MAAM,EAAEC,MAAM,CAAC;EACnC,CAAC,MACI;IACD,OAAOD,MAAM,CAACM,UAAU,CAAC,MAAM,CAAC,GAC1BP,YAAY,CAACC,MAAM,EAAEC,MAAM,CAAC,GAC5BM,UAAU,CAACP,MAAM,EAAEC,MAAM,CAAC;EACpC;AACJ;AACA,MAAMO,QAAQ,GAAGA,CAACC,IAAI,EAAEC,EAAE,KAAK;EAC3B,MAAMC,MAAM,GAAG,CAAC,GAAGF,IAAI,CAAC;EACxB,MAAMG,SAAS,GAAGD,MAAM,CAACE,MAAM;EAC/B,MAAMC,UAAU,GAAGL,IAAI,CAACM,GAAG,CAAC,CAACC,QAAQ,EAAEC,CAAC,KAAKd,QAAQ,CAACa,QAAQ,EAAEN,EAAE,CAACO,CAAC,CAAC,CAAC,CAAC;EACvE,OAAQb,CAAC,IAAK;IACV,KAAK,IAAIa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,SAAS,EAAEK,CAAC,EAAE,EAAE;MAChCN,MAAM,CAACM,CAAC,CAAC,GAAGH,UAAU,CAACG,CAAC,CAAC,CAACb,CAAC,CAAC;IAChC;IACA,OAAOO,MAAM;EACjB,CAAC;AACL,CAAC;AACD,MAAMO,SAAS,GAAGA,CAAClB,MAAM,EAAEC,MAAM,KAAK;EAClC,MAAMU,MAAM,GAAG;IAAE,GAAGX,MAAM;IAAE,GAAGC;EAAO,CAAC;EACvC,MAAMa,UAAU,GAAG,CAAC,CAAC;EACrB,KAAK,MAAMK,GAAG,IAAIR,MAAM,EAAE;IACtB,IAAIX,MAAM,CAACmB,GAAG,CAAC,KAAKC,SAAS,IAAInB,MAAM,CAACkB,GAAG,CAAC,KAAKC,SAAS,EAAE;MACxDN,UAAU,CAACK,GAAG,CAAC,GAAGhB,QAAQ,CAACH,MAAM,CAACmB,GAAG,CAAC,EAAElB,MAAM,CAACkB,GAAG,CAAC,CAAC;IACxD;EACJ;EACA,OAAQf,CAAC,IAAK;IACV,KAAK,MAAMe,GAAG,IAAIL,UAAU,EAAE;MAC1BH,MAAM,CAACQ,GAAG,CAAC,GAAGL,UAAU,CAACK,GAAG,CAAC,CAACf,CAAC,CAAC;IACpC;IACA,OAAOO,MAAM;EACjB,CAAC;AACL,CAAC;AACD,MAAMJ,UAAU,GAAGA,CAACP,MAAM,EAAEC,MAAM,KAAK;EACnC,MAAMoB,QAAQ,GAAGxB,OAAO,CAACyB,iBAAiB,CAACrB,MAAM,CAAC;EAClD,MAAMsB,WAAW,GAAGzB,mBAAmB,CAACE,MAAM,CAAC;EAC/C,MAAMwB,WAAW,GAAG1B,mBAAmB,CAACG,MAAM,CAAC;EAC/C,MAAMwB,cAAc,GAAGF,WAAW,CAACG,OAAO,KAAKF,WAAW,CAACE,OAAO,IAC9DH,WAAW,CAACI,SAAS,KAAKH,WAAW,CAACG,SAAS,IAC/CJ,WAAW,CAACK,UAAU,IAAIJ,WAAW,CAACI,UAAU;EACpD,IAAIH,cAAc,EAAE;IAChB,OAAO/B,IAAI,CAACc,QAAQ,CAACe,WAAW,CAACM,MAAM,EAAEL,WAAW,CAACK,MAAM,CAAC,EAAER,QAAQ,CAAC;EAC3E,CAAC,MACI;IACD1B,OAAO,CAAC,IAAI,EAAG,mBAAkBK,MAAO,UAASC,MAAO,0KAAyK,CAAC;IAClO,OAAOF,YAAY,CAACC,MAAM,EAAEC,MAAM,CAAC;EACvC;AACJ,CAAC;AAED,SAASO,QAAQ,EAAED,UAAU,EAAEW,SAAS"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}