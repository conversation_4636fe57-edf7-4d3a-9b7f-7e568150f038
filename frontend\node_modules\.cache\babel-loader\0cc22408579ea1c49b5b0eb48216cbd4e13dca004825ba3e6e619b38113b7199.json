{"ast": null, "code": "import { addDomEvent } from \"./chunk-6K7SS4J6.mjs\";\nimport { getEventPoint } from \"./chunk-6FBKF3LK.mjs\";\nimport { isMouseEvent } from \"./chunk-B7KYFEHM.mjs\";\n\n// src/add-pointer-event.ts\nfunction filter(cb) {\n  return event => {\n    const isMouse = isMouseEvent(event);\n    if (!isMouse || isMouse && event.button === 0) {\n      cb(event);\n    }\n  };\n}\nfunction wrap(cb, filterPrimary = false) {\n  function listener(event) {\n    cb(event, {\n      point: getEventPoint(event)\n    });\n  }\n  const fn = filterPrimary ? filter(listener) : listener;\n  return fn;\n}\nfunction addPointerEvent(target, type, cb, options) {\n  return addDomEvent(target, type, wrap(cb, type === \"pointerdown\"), options);\n}\nexport { addPointerEvent };", "map": {"version": 3, "names": ["addDomEvent", "getEventPoint", "isMouseEvent", "filter", "cb", "event", "isMouse", "button", "wrap", "filterPrimary", "listener", "point", "fn", "addPointerEvent", "target", "type", "options"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/@chakra-ui/event-utils/dist/chunk-KDLSVIYE.mjs"], "sourcesContent": ["import {\n  addDomEvent\n} from \"./chunk-6K7SS4J6.mjs\";\nimport {\n  getEventPoint\n} from \"./chunk-6FBKF3LK.mjs\";\nimport {\n  isMouseEvent\n} from \"./chunk-B7KYFEHM.mjs\";\n\n// src/add-pointer-event.ts\nfunction filter(cb) {\n  return (event) => {\n    const isMouse = isMouseEvent(event);\n    if (!isMouse || isMouse && event.button === 0) {\n      cb(event);\n    }\n  };\n}\nfunction wrap(cb, filterPrimary = false) {\n  function listener(event) {\n    cb(event, { point: getEventPoint(event) });\n  }\n  const fn = filterPrimary ? filter(listener) : listener;\n  return fn;\n}\nfunction addPointerEvent(target, type, cb, options) {\n  return addDomEvent(target, type, wrap(cb, type === \"pointerdown\"), options);\n}\n\nexport {\n  addPointerEvent\n};\n"], "mappings": "AAAA,SACEA,WAAW,QACN,sBAAsB;AAC7B,SACEC,aAAa,QACR,sBAAsB;AAC7B,SACEC,YAAY,QACP,sBAAsB;;AAE7B;AACA,SAASC,MAAMA,CAACC,EAAE,EAAE;EAClB,OAAQC,KAAK,IAAK;IAChB,MAAMC,OAAO,GAAGJ,YAAY,CAACG,KAAK,CAAC;IACnC,IAAI,CAACC,OAAO,IAAIA,OAAO,IAAID,KAAK,CAACE,MAAM,KAAK,CAAC,EAAE;MAC7CH,EAAE,CAACC,KAAK,CAAC;IACX;EACF,CAAC;AACH;AACA,SAASG,IAAIA,CAACJ,EAAE,EAAEK,aAAa,GAAG,KAAK,EAAE;EACvC,SAASC,QAAQA,CAACL,KAAK,EAAE;IACvBD,EAAE,CAACC,KAAK,EAAE;MAAEM,KAAK,EAAEV,aAAa,CAACI,KAAK;IAAE,CAAC,CAAC;EAC5C;EACA,MAAMO,EAAE,GAAGH,aAAa,GAAGN,MAAM,CAACO,QAAQ,CAAC,GAAGA,QAAQ;EACtD,OAAOE,EAAE;AACX;AACA,SAASC,eAAeA,CAACC,MAAM,EAAEC,IAAI,EAAEX,EAAE,EAAEY,OAAO,EAAE;EAClD,OAAOhB,WAAW,CAACc,MAAM,EAAEC,IAAI,EAAEP,IAAI,CAACJ,EAAE,EAAEW,IAAI,KAAK,aAAa,CAAC,EAAEC,OAAO,CAAC;AAC7E;AAEA,SACEH,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}