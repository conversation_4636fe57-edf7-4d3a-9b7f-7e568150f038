{"ast": null, "code": "import { contains } from './utils/DOMutils';\nimport { getAllAffectedNodes } from './utils/all-affected';\nimport { getFirst, toArray } from './utils/array';\nimport { getActiveElement } from './utils/getActiveElement';\nvar focusInFrame = function (frame, activeElement) {\n  return frame === activeElement;\n};\nvar focusInsideIframe = function (topNode, activeElement) {\n  return Boolean(toArray(topNode.querySelectorAll('iframe')).some(function (node) {\n    return focusInFrame(node, activeElement);\n  }));\n};\n/**\n * @returns {Boolean} true, if the current focus is inside given node or nodes.\n * Supports nodes hidden inside shadowDom\n */\nexport var focusInside = function (topNode, activeElement) {\n  // const activeElement = document && getActiveElement();\n  if (activeElement === void 0) {\n    activeElement = getActiveElement(getFirst(topNode).ownerDocument);\n  }\n  if (!activeElement || activeElement.dataset && activeElement.dataset.focusGuard) {\n    return false;\n  }\n  return getAllAffectedNodes(topNode).some(function (node) {\n    return contains(node, activeElement) || focusInsideIframe(node, activeElement);\n  });\n};", "map": {"version": 3, "names": ["contains", "getAllAffectedNodes", "get<PERSON><PERSON><PERSON>", "toArray", "getActiveElement", "focusInFrame", "frame", "activeElement", "focusInsideIframe", "topNode", "Boolean", "querySelectorAll", "some", "node", "focusInside", "ownerDocument", "dataset", "focusGuard"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/focus-lock/dist/es2015/focusInside.js"], "sourcesContent": ["import { contains } from './utils/DOMutils';\nimport { getAllAffectedNodes } from './utils/all-affected';\nimport { getFirst, toArray } from './utils/array';\nimport { getActiveElement } from './utils/getActiveElement';\nvar focusInFrame = function (frame, activeElement) { return frame === activeElement; };\nvar focusInsideIframe = function (topNode, activeElement) {\n    return Boolean(toArray(topNode.querySelectorAll('iframe')).some(function (node) { return focusInFrame(node, activeElement); }));\n};\n/**\n * @returns {Boolean} true, if the current focus is inside given node or nodes.\n * Supports nodes hidden inside shadowDom\n */\nexport var focusInside = function (topNode, activeElement) {\n    // const activeElement = document && getActiveElement();\n    if (activeElement === void 0) { activeElement = getActiveElement(getFirst(topNode).ownerDocument); }\n    if (!activeElement || (activeElement.dataset && activeElement.dataset.focusGuard)) {\n        return false;\n    }\n    return getAllAffectedNodes(topNode).some(function (node) {\n        return contains(node, activeElement) || focusInsideIframe(node, activeElement);\n    });\n};\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,kBAAkB;AAC3C,SAASC,mBAAmB,QAAQ,sBAAsB;AAC1D,SAASC,QAAQ,EAAEC,OAAO,QAAQ,eAAe;AACjD,SAASC,gBAAgB,QAAQ,0BAA0B;AAC3D,IAAIC,YAAY,GAAG,SAAAA,CAAUC,KAAK,EAAEC,aAAa,EAAE;EAAE,OAAOD,KAAK,KAAKC,aAAa;AAAE,CAAC;AACtF,IAAIC,iBAAiB,GAAG,SAAAA,CAAUC,OAAO,EAAEF,aAAa,EAAE;EACtD,OAAOG,OAAO,CAACP,OAAO,CAACM,OAAO,CAACE,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAACC,IAAI,CAAC,UAAUC,IAAI,EAAE;IAAE,OAAOR,YAAY,CAACQ,IAAI,EAAEN,aAAa,CAAC;EAAE,CAAC,CAAC,CAAC;AACnI,CAAC;AACD;AACA;AACA;AACA;AACA,OAAO,IAAIO,WAAW,GAAG,SAAAA,CAAUL,OAAO,EAAEF,aAAa,EAAE;EACvD;EACA,IAAIA,aAAa,KAAK,KAAK,CAAC,EAAE;IAAEA,aAAa,GAAGH,gBAAgB,CAACF,QAAQ,CAACO,OAAO,CAAC,CAACM,aAAa,CAAC;EAAE;EACnG,IAAI,CAACR,aAAa,IAAKA,aAAa,CAACS,OAAO,IAAIT,aAAa,CAACS,OAAO,CAACC,UAAW,EAAE;IAC/E,OAAO,KAAK;EAChB;EACA,OAAOhB,mBAAmB,CAACQ,OAAO,CAAC,CAACG,IAAI,CAAC,UAAUC,IAAI,EAAE;IACrD,OAAOb,QAAQ,CAACa,IAAI,EAAEN,aAAa,CAAC,IAAIC,iBAAiB,CAACK,IAAI,EAAEN,aAAa,CAAC;EAClF,CAAC,CAAC;AACN,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}