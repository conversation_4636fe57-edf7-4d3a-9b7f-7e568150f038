{"ast": null, "code": "import { NEW_FOCUS, newFocus } from './solver';\nimport { getFocusableNodes, getTabbableNodes } from './utils/DOMutils';\nimport { getAllAffectedNodes } from './utils/all-affected';\nimport { asArray, getFirst } from './utils/array';\nimport { pickAutofocus } from './utils/auto-focus';\nimport { getActiveElement } from './utils/getActiveElement';\nimport { isDefined, isNotAGuard } from './utils/is';\nimport { allParentAutofocusables, getTopCommonParent } from './utils/parenting';\nvar reorderNodes = function (srcNodes, dstNodes) {\n  var remap = new Map();\n  // no Set(dstNodes) for IE11 :(\n  dstNodes.forEach(function (entity) {\n    return remap.set(entity.node, entity);\n  });\n  // remap to dstNodes\n  return srcNodes.map(function (node) {\n    return remap.get(node);\n  }).filter(isDefined);\n};\n/**\n * contains the main logic of the `focus-lock` package.\n *\n * ! you probably dont need this function !\n *\n * given top node(s) and the last active element returns the element to be focused next\n * @returns element which should be focused to move focus inside\n * @param topNode\n * @param lastNode\n */\nexport var focusSolver = function (topNode, lastNode) {\n  var activeElement = getActiveElement(asArray(topNode).length > 0 ? document : getFirst(topNode).ownerDocument);\n  var entries = getAllAffectedNodes(topNode).filter(isNotAGuard);\n  var commonParent = getTopCommonParent(activeElement || topNode, topNode, entries);\n  var visibilityCache = new Map();\n  var anyFocusable = getFocusableNodes(entries, visibilityCache);\n  var innerElements = getTabbableNodes(entries, visibilityCache).filter(function (_a) {\n    var node = _a.node;\n    return isNotAGuard(node);\n  });\n  if (!innerElements[0]) {\n    innerElements = anyFocusable;\n    if (!innerElements[0]) {\n      return undefined;\n    }\n  }\n  var outerNodes = getFocusableNodes([commonParent], visibilityCache).map(function (_a) {\n    var node = _a.node;\n    return node;\n  });\n  var orderedInnerElements = reorderNodes(outerNodes, innerElements);\n  var innerNodes = orderedInnerElements.map(function (_a) {\n    var node = _a.node;\n    return node;\n  });\n  var newId = newFocus(innerNodes, outerNodes, activeElement, lastNode);\n  if (newId === NEW_FOCUS) {\n    var focusNode = pickAutofocus(anyFocusable, innerNodes, allParentAutofocusables(entries, visibilityCache));\n    if (focusNode) {\n      return {\n        node: focusNode\n      };\n    } else {\n      console.warn('focus-lock: cannot find any node to move focus into');\n      return undefined;\n    }\n  }\n  if (newId === undefined) {\n    return newId;\n  }\n  return orderedInnerElements[newId];\n};", "map": {"version": 3, "names": ["NEW_FOCUS", "newFocus", "getFocusableNodes", "getTabbableNodes", "getAllAffectedNodes", "asArray", "get<PERSON><PERSON><PERSON>", "pickAutofocus", "getActiveElement", "isDefined", "isNotAGuard", "allParentAutofocusables", "getTopCommonParent", "reorderNodes", "srcNodes", "dstNodes", "remap", "Map", "for<PERSON>ach", "entity", "set", "node", "map", "get", "filter", "focusSolver", "topNode", "lastNode", "activeElement", "length", "document", "ownerDocument", "entries", "commonParent", "visibilityCache", "anyFocusable", "innerElements", "_a", "undefined", "outerNodes", "orderedInnerElements", "innerNodes", "newId", "focusNode", "console", "warn"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/focus-lock/dist/es2015/focusSolver.js"], "sourcesContent": ["import { NEW_FOCUS, newFocus } from './solver';\nimport { getFocusableNodes, getTabbableNodes } from './utils/DOMutils';\nimport { getAllAffectedNodes } from './utils/all-affected';\nimport { asArray, getFirst } from './utils/array';\nimport { pickAutofocus } from './utils/auto-focus';\nimport { getActiveElement } from './utils/getActiveElement';\nimport { isDefined, isNotAGuard } from './utils/is';\nimport { allParentAutofocusables, getTopCommonParent } from './utils/parenting';\nvar reorderNodes = function (srcNodes, dstNodes) {\n    var remap = new Map();\n    // no Set(dstNodes) for IE11 :(\n    dstNodes.forEach(function (entity) { return remap.set(entity.node, entity); });\n    // remap to dstNodes\n    return srcNodes.map(function (node) { return remap.get(node); }).filter(isDefined);\n};\n/**\n * contains the main logic of the `focus-lock` package.\n *\n * ! you probably dont need this function !\n *\n * given top node(s) and the last active element returns the element to be focused next\n * @returns element which should be focused to move focus inside\n * @param topNode\n * @param lastNode\n */\nexport var focusSolver = function (topNode, lastNode) {\n    var activeElement = getActiveElement(asArray(topNode).length > 0 ? document : getFirst(topNode).ownerDocument);\n    var entries = getAllAffectedNodes(topNode).filter(isNotAGuard);\n    var commonParent = getTopCommonParent(activeElement || topNode, topNode, entries);\n    var visibilityCache = new Map();\n    var anyFocusable = getFocusableNodes(entries, visibilityCache);\n    var innerElements = getTabbableNodes(entries, visibilityCache).filter(function (_a) {\n        var node = _a.node;\n        return isNotAGuard(node);\n    });\n    if (!innerElements[0]) {\n        innerElements = anyFocusable;\n        if (!innerElements[0]) {\n            return undefined;\n        }\n    }\n    var outerNodes = getFocusableNodes([commonParent], visibilityCache).map(function (_a) {\n        var node = _a.node;\n        return node;\n    });\n    var orderedInnerElements = reorderNodes(outerNodes, innerElements);\n    var innerNodes = orderedInnerElements.map(function (_a) {\n        var node = _a.node;\n        return node;\n    });\n    var newId = newFocus(innerNodes, outerNodes, activeElement, lastNode);\n    if (newId === NEW_FOCUS) {\n        var focusNode = pickAutofocus(anyFocusable, innerNodes, allParentAutofocusables(entries, visibilityCache));\n        if (focusNode) {\n            return { node: focusNode };\n        }\n        else {\n            console.warn('focus-lock: cannot find any node to move focus into');\n            return undefined;\n        }\n    }\n    if (newId === undefined) {\n        return newId;\n    }\n    return orderedInnerElements[newId];\n};\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,QAAQ,QAAQ,UAAU;AAC9C,SAASC,iBAAiB,EAAEC,gBAAgB,QAAQ,kBAAkB;AACtE,SAASC,mBAAmB,QAAQ,sBAAsB;AAC1D,SAASC,OAAO,EAAEC,QAAQ,QAAQ,eAAe;AACjD,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,gBAAgB,QAAQ,0BAA0B;AAC3D,SAASC,SAAS,EAAEC,WAAW,QAAQ,YAAY;AACnD,SAASC,uBAAuB,EAAEC,kBAAkB,QAAQ,mBAAmB;AAC/E,IAAIC,YAAY,GAAG,SAAAA,CAAUC,QAAQ,EAAEC,QAAQ,EAAE;EAC7C,IAAIC,KAAK,GAAG,IAAIC,GAAG,CAAC,CAAC;EACrB;EACAF,QAAQ,CAACG,OAAO,CAAC,UAAUC,MAAM,EAAE;IAAE,OAAOH,KAAK,CAACI,GAAG,CAACD,MAAM,CAACE,IAAI,EAAEF,MAAM,CAAC;EAAE,CAAC,CAAC;EAC9E;EACA,OAAOL,QAAQ,CAACQ,GAAG,CAAC,UAAUD,IAAI,EAAE;IAAE,OAAOL,KAAK,CAACO,GAAG,CAACF,IAAI,CAAC;EAAE,CAAC,CAAC,CAACG,MAAM,CAACf,SAAS,CAAC;AACtF,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIgB,WAAW,GAAG,SAAAA,CAAUC,OAAO,EAAEC,QAAQ,EAAE;EAClD,IAAIC,aAAa,GAAGpB,gBAAgB,CAACH,OAAO,CAACqB,OAAO,CAAC,CAACG,MAAM,GAAG,CAAC,GAAGC,QAAQ,GAAGxB,QAAQ,CAACoB,OAAO,CAAC,CAACK,aAAa,CAAC;EAC9G,IAAIC,OAAO,GAAG5B,mBAAmB,CAACsB,OAAO,CAAC,CAACF,MAAM,CAACd,WAAW,CAAC;EAC9D,IAAIuB,YAAY,GAAGrB,kBAAkB,CAACgB,aAAa,IAAIF,OAAO,EAAEA,OAAO,EAAEM,OAAO,CAAC;EACjF,IAAIE,eAAe,GAAG,IAAIjB,GAAG,CAAC,CAAC;EAC/B,IAAIkB,YAAY,GAAGjC,iBAAiB,CAAC8B,OAAO,EAAEE,eAAe,CAAC;EAC9D,IAAIE,aAAa,GAAGjC,gBAAgB,CAAC6B,OAAO,EAAEE,eAAe,CAAC,CAACV,MAAM,CAAC,UAAUa,EAAE,EAAE;IAChF,IAAIhB,IAAI,GAAGgB,EAAE,CAAChB,IAAI;IAClB,OAAOX,WAAW,CAACW,IAAI,CAAC;EAC5B,CAAC,CAAC;EACF,IAAI,CAACe,aAAa,CAAC,CAAC,CAAC,EAAE;IACnBA,aAAa,GAAGD,YAAY;IAC5B,IAAI,CAACC,aAAa,CAAC,CAAC,CAAC,EAAE;MACnB,OAAOE,SAAS;IACpB;EACJ;EACA,IAAIC,UAAU,GAAGrC,iBAAiB,CAAC,CAAC+B,YAAY,CAAC,EAAEC,eAAe,CAAC,CAACZ,GAAG,CAAC,UAAUe,EAAE,EAAE;IAClF,IAAIhB,IAAI,GAAGgB,EAAE,CAAChB,IAAI;IAClB,OAAOA,IAAI;EACf,CAAC,CAAC;EACF,IAAImB,oBAAoB,GAAG3B,YAAY,CAAC0B,UAAU,EAAEH,aAAa,CAAC;EAClE,IAAIK,UAAU,GAAGD,oBAAoB,CAAClB,GAAG,CAAC,UAAUe,EAAE,EAAE;IACpD,IAAIhB,IAAI,GAAGgB,EAAE,CAAChB,IAAI;IAClB,OAAOA,IAAI;EACf,CAAC,CAAC;EACF,IAAIqB,KAAK,GAAGzC,QAAQ,CAACwC,UAAU,EAAEF,UAAU,EAAEX,aAAa,EAAED,QAAQ,CAAC;EACrE,IAAIe,KAAK,KAAK1C,SAAS,EAAE;IACrB,IAAI2C,SAAS,GAAGpC,aAAa,CAAC4B,YAAY,EAAEM,UAAU,EAAE9B,uBAAuB,CAACqB,OAAO,EAAEE,eAAe,CAAC,CAAC;IAC1G,IAAIS,SAAS,EAAE;MACX,OAAO;QAAEtB,IAAI,EAAEsB;MAAU,CAAC;IAC9B,CAAC,MACI;MACDC,OAAO,CAACC,IAAI,CAAC,qDAAqD,CAAC;MACnE,OAAOP,SAAS;IACpB;EACJ;EACA,IAAII,KAAK,KAAKJ,SAAS,EAAE;IACrB,OAAOI,KAAK;EAChB;EACA,OAAOF,oBAAoB,CAACE,KAAK,CAAC;AACtC,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}