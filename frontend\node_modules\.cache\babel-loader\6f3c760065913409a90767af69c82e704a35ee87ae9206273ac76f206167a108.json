{"ast": null, "code": "/**\n * Unmemoized version of {@link useRefToCallback}\n * @see {@link useRefToCallback}\n * @param ref\n */\nexport function refToCallback(ref) {\n  return function (newValue) {\n    if (typeof ref === 'function') {\n      ref(newValue);\n    } else if (ref) {\n      ref.current = newValue;\n    }\n  };\n}\nvar nullCallback = function () {\n  return null;\n};\n// lets maintain a weak ref to, well, ref :)\n// not using `kashe` to keep this package small\nvar weakMem = new WeakMap();\nvar weakMemoize = function (ref) {\n  var usedRef = ref || nullCallback;\n  var storedRef = weakMem.get(usedRef);\n  if (storedRef) {\n    return storedRef;\n  }\n  var cb = refToCallback(usedRef);\n  weakMem.set(usedRef, cb);\n  return cb;\n};\n/**\n * Transforms a given `ref` into `callback`.\n *\n * To transform `callback` into ref use {@link useCallbackRef|useCallbackRef(undefined, callback)}\n *\n * @param {ReactRef} ref\n * @returns {Function}\n *\n * @see https://github.com/theKashey/use-callback-ref#reftocallback\n *\n * @example\n * const ref = useRef(0);\n * const setRef = useRefToCallback(ref);\n * 👉 setRef(10);\n * ✅ ref.current === 10\n */\nexport function useRefToCallback(ref) {\n  return weakMemoize(ref);\n}", "map": {"version": 3, "names": ["ref<PERSON><PERSON><PERSON><PERSON><PERSON>", "ref", "newValue", "current", "null<PERSON><PERSON><PERSON>", "weak<PERSON><PERSON>", "WeakMap", "weakMemoize", "usedRef", "storedRef", "get", "cb", "set", "useRefToCallback"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/use-callback-ref/dist/es2015/refToCallback.js"], "sourcesContent": ["/**\n * Unmemoized version of {@link useRefToCallback}\n * @see {@link useRefToCallback}\n * @param ref\n */\nexport function refToCallback(ref) {\n    return function (newValue) {\n        if (typeof ref === 'function') {\n            ref(newValue);\n        }\n        else if (ref) {\n            ref.current = newValue;\n        }\n    };\n}\nvar nullCallback = function () { return null; };\n// lets maintain a weak ref to, well, ref :)\n// not using `kashe` to keep this package small\nvar weakMem = new WeakMap();\nvar weakMemoize = function (ref) {\n    var usedRef = ref || nullCallback;\n    var storedRef = weakMem.get(usedRef);\n    if (storedRef) {\n        return storedRef;\n    }\n    var cb = refToCallback(usedRef);\n    weakMem.set(usedRef, cb);\n    return cb;\n};\n/**\n * Transforms a given `ref` into `callback`.\n *\n * To transform `callback` into ref use {@link useCallbackRef|useCallbackRef(undefined, callback)}\n *\n * @param {ReactRef} ref\n * @returns {Function}\n *\n * @see https://github.com/theKashey/use-callback-ref#reftocallback\n *\n * @example\n * const ref = useRef(0);\n * const setRef = useRefToCallback(ref);\n * 👉 setRef(10);\n * ✅ ref.current === 10\n */\nexport function useRefToCallback(ref) {\n    return weakMemoize(ref);\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,aAAaA,CAACC,GAAG,EAAE;EAC/B,OAAO,UAAUC,QAAQ,EAAE;IACvB,IAAI,OAAOD,GAAG,KAAK,UAAU,EAAE;MAC3BA,GAAG,CAACC,QAAQ,CAAC;IACjB,CAAC,MACI,IAAID,GAAG,EAAE;MACVA,GAAG,CAACE,OAAO,GAAGD,QAAQ;IAC1B;EACJ,CAAC;AACL;AACA,IAAIE,YAAY,GAAG,SAAAA,CAAA,EAAY;EAAE,OAAO,IAAI;AAAE,CAAC;AAC/C;AACA;AACA,IAAIC,OAAO,GAAG,IAAIC,OAAO,CAAC,CAAC;AAC3B,IAAIC,WAAW,GAAG,SAAAA,CAAUN,GAAG,EAAE;EAC7B,IAAIO,OAAO,GAAGP,GAAG,IAAIG,YAAY;EACjC,IAAIK,SAAS,GAAGJ,OAAO,CAACK,GAAG,CAACF,OAAO,CAAC;EACpC,IAAIC,SAAS,EAAE;IACX,OAAOA,SAAS;EACpB;EACA,IAAIE,EAAE,GAAGX,aAAa,CAACQ,OAAO,CAAC;EAC/BH,OAAO,CAACO,GAAG,CAACJ,OAAO,EAAEG,EAAE,CAAC;EACxB,OAAOA,EAAE;AACb,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASE,gBAAgBA,CAACZ,GAAG,EAAE;EAClC,OAAOM,WAAW,CAACN,GAAG,CAAC;AAC3B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}