{"ast": null, "code": "import { globalThisShim as globalThis } from \"../globalThis.js\";\nexport const nextTick = (() => {\n  const isPromiseAvailable = typeof Promise === \"function\" && typeof Promise.resolve === \"function\";\n  if (isPromiseAvailable) {\n    return cb => Promise.resolve().then(cb);\n  } else {\n    return (cb, setTimeoutFn) => setTimeoutFn(cb, 0);\n  }\n})();\nexport const WebSocket = globalThis.WebSocket || globalThis.MozWebSocket;\nexport const usingBrowserWebSocket = true;\nexport const defaultBinaryType = \"arraybuffer\";", "map": {"version": 3, "names": ["globalThisShim", "globalThis", "nextTick", "isPromiseAvailable", "Promise", "resolve", "cb", "then", "setTimeoutFn", "WebSocket", "MozWebSocket", "usingBrowserWebSocket", "defaultBinaryType"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/engine.io-client/build/esm/transports/websocket-constructor.browser.js"], "sourcesContent": ["import { globalThisShim as globalThis } from \"../globalThis.js\";\nexport const nextTick = (() => {\n    const isPromiseAvailable = typeof Promise === \"function\" && typeof Promise.resolve === \"function\";\n    if (isPromiseAvailable) {\n        return (cb) => Promise.resolve().then(cb);\n    }\n    else {\n        return (cb, setTimeoutFn) => setTimeoutFn(cb, 0);\n    }\n})();\nexport const WebSocket = globalThis.WebSocket || globalThis.MozWebSocket;\nexport const usingBrowserWebSocket = true;\nexport const defaultBinaryType = \"arraybuffer\";\n"], "mappings": "AAAA,SAASA,cAAc,IAAIC,UAAU,QAAQ,kBAAkB;AAC/D,OAAO,MAAMC,QAAQ,GAAG,CAAC,MAAM;EAC3B,MAAMC,kBAAkB,GAAG,OAAOC,OAAO,KAAK,UAAU,IAAI,OAAOA,OAAO,CAACC,OAAO,KAAK,UAAU;EACjG,IAAIF,kBAAkB,EAAE;IACpB,OAAQG,EAAE,IAAKF,OAAO,CAACC,OAAO,CAAC,CAAC,CAACE,IAAI,CAACD,EAAE,CAAC;EAC7C,CAAC,MACI;IACD,OAAO,CAACA,EAAE,EAAEE,YAAY,KAAKA,YAAY,CAACF,EAAE,EAAE,CAAC,CAAC;EACpD;AACJ,CAAC,EAAE,CAAC;AACJ,OAAO,MAAMG,SAAS,GAAGR,UAAU,CAACQ,SAAS,IAAIR,UAAU,CAACS,YAAY;AACxE,OAAO,MAAMC,qBAAqB,GAAG,IAAI;AACzC,OAAO,MAAMC,iBAAiB,GAAG,aAAa"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}