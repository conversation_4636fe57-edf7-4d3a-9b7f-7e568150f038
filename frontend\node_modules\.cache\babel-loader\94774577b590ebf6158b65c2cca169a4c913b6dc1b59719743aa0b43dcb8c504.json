{"ast": null, "code": "function createRenderStep(runNextFrame) {\n  let toRun = [];\n  let toRunNextFrame = [];\n  let numToRun = 0;\n  let isProcessing = false;\n  let flushNextFrame = false;\n  const toKeepAlive = new WeakSet();\n  const step = {\n    schedule: (callback, keepAlive = false, immediate = false) => {\n      const addToCurrentFrame = immediate && isProcessing;\n      const buffer = addToCurrentFrame ? toRun : toRunNextFrame;\n      if (keepAlive) toKeepAlive.add(callback);\n      if (buffer.indexOf(callback) === -1) {\n        buffer.push(callback);\n        if (addToCurrentFrame && isProcessing) numToRun = toRun.length;\n      }\n      return callback;\n    },\n    cancel: callback => {\n      const index = toRunNextFrame.indexOf(callback);\n      if (index !== -1) toRunNextFrame.splice(index, 1);\n      toKeepAlive.delete(callback);\n    },\n    process: frameData => {\n      if (isProcessing) {\n        flushNextFrame = true;\n        return;\n      }\n      isProcessing = true;\n      [toRun, toRunNextFrame] = [toRunNextFrame, toRun];\n      toRunNextFrame.length = 0;\n      numToRun = toRun.length;\n      if (numToRun) {\n        for (let i = 0; i < numToRun; i++) {\n          const callback = toRun[i];\n          callback(frameData);\n          if (toKeepAlive.has(callback)) {\n            step.schedule(callback);\n            runNextFrame();\n          }\n        }\n      }\n      isProcessing = false;\n      if (flushNextFrame) {\n        flushNextFrame = false;\n        step.process(frameData);\n      }\n    }\n  };\n  return step;\n}\nexport { createRenderStep };", "map": {"version": 3, "names": ["createRenderStep", "runNextFrame", "to<PERSON>un", "toRunNextFrame", "numToRun", "isProcessing", "flushNextFrame", "toKeepAlive", "WeakSet", "step", "schedule", "callback", "keepAlive", "immediate", "addToCurrentFrame", "buffer", "add", "indexOf", "push", "length", "cancel", "index", "splice", "delete", "process", "frameData", "i", "has"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/framesync/dist/es/create-render-step.mjs"], "sourcesContent": ["function createRenderStep(runNextFrame) {\n    let toRun = [];\n    let toRunNextFrame = [];\n    let numToRun = 0;\n    let isProcessing = false;\n    let flushNextFrame = false;\n    const toKeepAlive = new WeakSet();\n    const step = {\n        schedule: (callback, keepAlive = false, immediate = false) => {\n            const addToCurrentFrame = immediate && isProcessing;\n            const buffer = addToCurrentFrame ? toRun : toRunNextFrame;\n            if (keepAlive)\n                toKeepAlive.add(callback);\n            if (buffer.indexOf(callback) === -1) {\n                buffer.push(callback);\n                if (addToCurrentFrame && isProcessing)\n                    numToRun = toRun.length;\n            }\n            return callback;\n        },\n        cancel: (callback) => {\n            const index = toRunNextFrame.indexOf(callback);\n            if (index !== -1)\n                toRunNextFrame.splice(index, 1);\n            toKeepAlive.delete(callback);\n        },\n        process: (frameData) => {\n            if (isProcessing) {\n                flushNextFrame = true;\n                return;\n            }\n            isProcessing = true;\n            [toRun, toRunNextFrame] = [toRunNextFrame, toRun];\n            toRunNextFrame.length = 0;\n            numToRun = toRun.length;\n            if (numToRun) {\n                for (let i = 0; i < numToRun; i++) {\n                    const callback = toRun[i];\n                    callback(frameData);\n                    if (toKeepAlive.has(callback)) {\n                        step.schedule(callback);\n                        runNextFrame();\n                    }\n                }\n            }\n            isProcessing = false;\n            if (flushNextFrame) {\n                flushNextFrame = false;\n                step.process(frameData);\n            }\n        },\n    };\n    return step;\n}\n\nexport { createRenderStep };\n"], "mappings": "AAAA,SAASA,gBAAgBA,CAACC,YAAY,EAAE;EACpC,IAAIC,KAAK,GAAG,EAAE;EACd,IAAIC,cAAc,GAAG,EAAE;EACvB,IAAIC,QAAQ,GAAG,CAAC;EAChB,IAAIC,YAAY,GAAG,KAAK;EACxB,IAAIC,cAAc,GAAG,KAAK;EAC1B,MAAMC,WAAW,GAAG,IAAIC,OAAO,CAAC,CAAC;EACjC,MAAMC,IAAI,GAAG;IACTC,QAAQ,EAAEA,CAACC,QAAQ,EAAEC,SAAS,GAAG,KAAK,EAAEC,SAAS,GAAG,KAAK,KAAK;MAC1D,MAAMC,iBAAiB,GAAGD,SAAS,IAAIR,YAAY;MACnD,MAAMU,MAAM,GAAGD,iBAAiB,GAAGZ,KAAK,GAAGC,cAAc;MACzD,IAAIS,SAAS,EACTL,WAAW,CAACS,GAAG,CAACL,QAAQ,CAAC;MAC7B,IAAII,MAAM,CAACE,OAAO,CAACN,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;QACjCI,MAAM,CAACG,IAAI,CAACP,QAAQ,CAAC;QACrB,IAAIG,iBAAiB,IAAIT,YAAY,EACjCD,QAAQ,GAAGF,KAAK,CAACiB,MAAM;MAC/B;MACA,OAAOR,QAAQ;IACnB,CAAC;IACDS,MAAM,EAAGT,QAAQ,IAAK;MAClB,MAAMU,KAAK,GAAGlB,cAAc,CAACc,OAAO,CAACN,QAAQ,CAAC;MAC9C,IAAIU,KAAK,KAAK,CAAC,CAAC,EACZlB,cAAc,CAACmB,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;MACnCd,WAAW,CAACgB,MAAM,CAACZ,QAAQ,CAAC;IAChC,CAAC;IACDa,OAAO,EAAGC,SAAS,IAAK;MACpB,IAAIpB,YAAY,EAAE;QACdC,cAAc,GAAG,IAAI;QACrB;MACJ;MACAD,YAAY,GAAG,IAAI;MACnB,CAACH,KAAK,EAAEC,cAAc,CAAC,GAAG,CAACA,cAAc,EAAED,KAAK,CAAC;MACjDC,cAAc,CAACgB,MAAM,GAAG,CAAC;MACzBf,QAAQ,GAAGF,KAAK,CAACiB,MAAM;MACvB,IAAIf,QAAQ,EAAE;QACV,KAAK,IAAIsB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtB,QAAQ,EAAEsB,CAAC,EAAE,EAAE;UAC/B,MAAMf,QAAQ,GAAGT,KAAK,CAACwB,CAAC,CAAC;UACzBf,QAAQ,CAACc,SAAS,CAAC;UACnB,IAAIlB,WAAW,CAACoB,GAAG,CAAChB,QAAQ,CAAC,EAAE;YAC3BF,IAAI,CAACC,QAAQ,CAACC,QAAQ,CAAC;YACvBV,YAAY,CAAC,CAAC;UAClB;QACJ;MACJ;MACAI,YAAY,GAAG,KAAK;MACpB,IAAIC,cAAc,EAAE;QAChBA,cAAc,GAAG,KAAK;QACtBG,IAAI,CAACe,OAAO,CAACC,SAAS,CAAC;MAC3B;IACJ;EACJ,CAAC;EACD,OAAOhB,IAAI;AACf;AAEA,SAAST,gBAAgB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}