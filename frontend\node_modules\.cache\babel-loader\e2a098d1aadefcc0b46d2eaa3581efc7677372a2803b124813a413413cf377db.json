{"ast": null, "code": "import { exportSidecar } from 'use-sidecar';\nimport { RemoveScrollSideCar } from './SideEffect';\nimport { effectCar } from './medium';\nexport default exportSidecar(effectCar, RemoveScrollSideCar);", "map": {"version": 3, "names": ["exportSidecar", "RemoveScrollSideCar", "effectCar"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/react-remove-scroll/dist/es2015/sidecar.js"], "sourcesContent": ["import { exportSidecar } from 'use-sidecar';\nimport { RemoveScrollSideCar } from './SideEffect';\nimport { effectCar } from './medium';\nexport default exportSidecar(effectCar, RemoveScrollSideCar);\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,aAAa;AAC3C,SAASC,mBAAmB,QAAQ,cAAc;AAClD,SAASC,SAAS,QAAQ,UAAU;AACpC,eAAeF,aAAa,CAACE,SAAS,EAAED,mBAAmB,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}