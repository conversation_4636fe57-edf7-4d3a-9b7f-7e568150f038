{"ast": null, "code": "import { __assign } from \"tslib\";\nfunction ItoI(a) {\n  return a;\n}\nfunction innerCreateMedium(defaults, middleware) {\n  if (middleware === void 0) {\n    middleware = ItoI;\n  }\n  var buffer = [];\n  var assigned = false;\n  var medium = {\n    read: function () {\n      if (assigned) {\n        throw new Error('Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.');\n      }\n      if (buffer.length) {\n        return buffer[buffer.length - 1];\n      }\n      return defaults;\n    },\n    useMedium: function (data) {\n      var item = middleware(data, assigned);\n      buffer.push(item);\n      return function () {\n        buffer = buffer.filter(function (x) {\n          return x !== item;\n        });\n      };\n    },\n    assignSyncMedium: function (cb) {\n      assigned = true;\n      while (buffer.length) {\n        var cbs = buffer;\n        buffer = [];\n        cbs.forEach(cb);\n      }\n      buffer = {\n        push: function (x) {\n          return cb(x);\n        },\n        filter: function () {\n          return buffer;\n        }\n      };\n    },\n    assignMedium: function (cb) {\n      assigned = true;\n      var pendingQueue = [];\n      if (buffer.length) {\n        var cbs = buffer;\n        buffer = [];\n        cbs.forEach(cb);\n        pendingQueue = buffer;\n      }\n      var executeQueue = function () {\n        var cbs = pendingQueue;\n        pendingQueue = [];\n        cbs.forEach(cb);\n      };\n      var cycle = function () {\n        return Promise.resolve().then(executeQueue);\n      };\n      cycle();\n      buffer = {\n        push: function (x) {\n          pendingQueue.push(x);\n          cycle();\n        },\n        filter: function (filter) {\n          pendingQueue = pendingQueue.filter(filter);\n          return buffer;\n        }\n      };\n    }\n  };\n  return medium;\n}\nexport function createMedium(defaults, middleware) {\n  if (middleware === void 0) {\n    middleware = ItoI;\n  }\n  return innerCreateMedium(defaults, middleware);\n}\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport function createSidecarMedium(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var medium = innerCreateMedium(null);\n  medium.options = __assign({\n    async: true,\n    ssr: false\n  }, options);\n  return medium;\n}", "map": {"version": 3, "names": ["__assign", "ItoI", "a", "innerCreateMedium", "defaults", "middleware", "buffer", "assigned", "medium", "read", "Error", "length", "useMedium", "data", "item", "push", "filter", "x", "assignSyncMedium", "cb", "cbs", "for<PERSON>ach", "assignMedium", "pendingQueue", "executeQueue", "cycle", "Promise", "resolve", "then", "createMedium", "createSidecarMedium", "options", "async", "ssr"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/use-sidecar/dist/es2015/medium.js"], "sourcesContent": ["import { __assign } from \"tslib\";\nfunction ItoI(a) {\n    return a;\n}\nfunction innerCreateMedium(defaults, middleware) {\n    if (middleware === void 0) { middleware = ItoI; }\n    var buffer = [];\n    var assigned = false;\n    var medium = {\n        read: function () {\n            if (assigned) {\n                throw new Error('Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.');\n            }\n            if (buffer.length) {\n                return buffer[buffer.length - 1];\n            }\n            return defaults;\n        },\n        useMedium: function (data) {\n            var item = middleware(data, assigned);\n            buffer.push(item);\n            return function () {\n                buffer = buffer.filter(function (x) { return x !== item; });\n            };\n        },\n        assignSyncMedium: function (cb) {\n            assigned = true;\n            while (buffer.length) {\n                var cbs = buffer;\n                buffer = [];\n                cbs.forEach(cb);\n            }\n            buffer = {\n                push: function (x) { return cb(x); },\n                filter: function () { return buffer; },\n            };\n        },\n        assignMedium: function (cb) {\n            assigned = true;\n            var pendingQueue = [];\n            if (buffer.length) {\n                var cbs = buffer;\n                buffer = [];\n                cbs.forEach(cb);\n                pendingQueue = buffer;\n            }\n            var executeQueue = function () {\n                var cbs = pendingQueue;\n                pendingQueue = [];\n                cbs.forEach(cb);\n            };\n            var cycle = function () { return Promise.resolve().then(executeQueue); };\n            cycle();\n            buffer = {\n                push: function (x) {\n                    pendingQueue.push(x);\n                    cycle();\n                },\n                filter: function (filter) {\n                    pendingQueue = pendingQueue.filter(filter);\n                    return buffer;\n                },\n            };\n        },\n    };\n    return medium;\n}\nexport function createMedium(defaults, middleware) {\n    if (middleware === void 0) { middleware = ItoI; }\n    return innerCreateMedium(defaults, middleware);\n}\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport function createSidecarMedium(options) {\n    if (options === void 0) { options = {}; }\n    var medium = innerCreateMedium(null);\n    medium.options = __assign({ async: true, ssr: false }, options);\n    return medium;\n}\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,IAAIA,CAACC,CAAC,EAAE;EACb,OAAOA,CAAC;AACZ;AACA,SAASC,iBAAiBA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EAC7C,IAAIA,UAAU,KAAK,KAAK,CAAC,EAAE;IAAEA,UAAU,GAAGJ,IAAI;EAAE;EAChD,IAAIK,MAAM,GAAG,EAAE;EACf,IAAIC,QAAQ,GAAG,KAAK;EACpB,IAAIC,MAAM,GAAG;IACTC,IAAI,EAAE,SAAAA,CAAA,EAAY;MACd,IAAIF,QAAQ,EAAE;QACV,MAAM,IAAIG,KAAK,CAAC,kGAAkG,CAAC;MACvH;MACA,IAAIJ,MAAM,CAACK,MAAM,EAAE;QACf,OAAOL,MAAM,CAACA,MAAM,CAACK,MAAM,GAAG,CAAC,CAAC;MACpC;MACA,OAAOP,QAAQ;IACnB,CAAC;IACDQ,SAAS,EAAE,SAAAA,CAAUC,IAAI,EAAE;MACvB,IAAIC,IAAI,GAAGT,UAAU,CAACQ,IAAI,EAAEN,QAAQ,CAAC;MACrCD,MAAM,CAACS,IAAI,CAACD,IAAI,CAAC;MACjB,OAAO,YAAY;QACfR,MAAM,GAAGA,MAAM,CAACU,MAAM,CAAC,UAAUC,CAAC,EAAE;UAAE,OAAOA,CAAC,KAAKH,IAAI;QAAE,CAAC,CAAC;MAC/D,CAAC;IACL,CAAC;IACDI,gBAAgB,EAAE,SAAAA,CAAUC,EAAE,EAAE;MAC5BZ,QAAQ,GAAG,IAAI;MACf,OAAOD,MAAM,CAACK,MAAM,EAAE;QAClB,IAAIS,GAAG,GAAGd,MAAM;QAChBA,MAAM,GAAG,EAAE;QACXc,GAAG,CAACC,OAAO,CAACF,EAAE,CAAC;MACnB;MACAb,MAAM,GAAG;QACLS,IAAI,EAAE,SAAAA,CAAUE,CAAC,EAAE;UAAE,OAAOE,EAAE,CAACF,CAAC,CAAC;QAAE,CAAC;QACpCD,MAAM,EAAE,SAAAA,CAAA,EAAY;UAAE,OAAOV,MAAM;QAAE;MACzC,CAAC;IACL,CAAC;IACDgB,YAAY,EAAE,SAAAA,CAAUH,EAAE,EAAE;MACxBZ,QAAQ,GAAG,IAAI;MACf,IAAIgB,YAAY,GAAG,EAAE;MACrB,IAAIjB,MAAM,CAACK,MAAM,EAAE;QACf,IAAIS,GAAG,GAAGd,MAAM;QAChBA,MAAM,GAAG,EAAE;QACXc,GAAG,CAACC,OAAO,CAACF,EAAE,CAAC;QACfI,YAAY,GAAGjB,MAAM;MACzB;MACA,IAAIkB,YAAY,GAAG,SAAAA,CAAA,EAAY;QAC3B,IAAIJ,GAAG,GAAGG,YAAY;QACtBA,YAAY,GAAG,EAAE;QACjBH,GAAG,CAACC,OAAO,CAACF,EAAE,CAAC;MACnB,CAAC;MACD,IAAIM,KAAK,GAAG,SAAAA,CAAA,EAAY;QAAE,OAAOC,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAACJ,YAAY,CAAC;MAAE,CAAC;MACxEC,KAAK,CAAC,CAAC;MACPnB,MAAM,GAAG;QACLS,IAAI,EAAE,SAAAA,CAAUE,CAAC,EAAE;UACfM,YAAY,CAACR,IAAI,CAACE,CAAC,CAAC;UACpBQ,KAAK,CAAC,CAAC;QACX,CAAC;QACDT,MAAM,EAAE,SAAAA,CAAUA,MAAM,EAAE;UACtBO,YAAY,GAAGA,YAAY,CAACP,MAAM,CAACA,MAAM,CAAC;UAC1C,OAAOV,MAAM;QACjB;MACJ,CAAC;IACL;EACJ,CAAC;EACD,OAAOE,MAAM;AACjB;AACA,OAAO,SAASqB,YAAYA,CAACzB,QAAQ,EAAEC,UAAU,EAAE;EAC/C,IAAIA,UAAU,KAAK,KAAK,CAAC,EAAE;IAAEA,UAAU,GAAGJ,IAAI;EAAE;EAChD,OAAOE,iBAAiB,CAACC,QAAQ,EAAEC,UAAU,CAAC;AAClD;AACA;AACA,OAAO,SAASyB,mBAAmBA,CAACC,OAAO,EAAE;EACzC,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IAAEA,OAAO,GAAG,CAAC,CAAC;EAAE;EACxC,IAAIvB,MAAM,GAAGL,iBAAiB,CAAC,IAAI,CAAC;EACpCK,MAAM,CAACuB,OAAO,GAAG/B,QAAQ,CAAC;IAAEgC,KAAK,EAAE,IAAI;IAAEC,GAAG,EAAE;EAAM,CAAC,EAAEF,OAAO,CAAC;EAC/D,OAAOvB,MAAM;AACjB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}