{"ast": null, "code": "import { correctNodes } from './utils/correctFocus';\nimport { pickFocusable } from './utils/firstFocus';\nimport { isGuard } from './utils/is';\nexport var NEW_FOCUS = 'NEW_FOCUS';\n/**\n * Main solver for the \"find next focus\" question\n * @param innerNodes\n * @param outerNodes\n * @param activeElement\n * @param lastNode\n * @returns {number|string|undefined|*}\n */\nexport var newFocus = function (innerNodes, outerNodes, activeElement, lastNode) {\n  var cnt = innerNodes.length;\n  var firstFocus = innerNodes[0];\n  var lastFocus = innerNodes[cnt - 1];\n  var isOnGuard = isGuard(activeElement);\n  // focus is inside\n  if (activeElement && innerNodes.indexOf(activeElement) >= 0) {\n    return undefined;\n  }\n  var activeIndex = activeElement !== undefined ? outerNodes.indexOf(activeElement) : -1;\n  var lastIndex = lastNode ? outerNodes.indexOf(lastNode) : activeIndex;\n  var lastNodeInside = lastNode ? innerNodes.indexOf(lastNode) : -1;\n  var indexDiff = activeIndex - lastIndex;\n  var firstNodeIndex = outerNodes.indexOf(firstFocus);\n  var lastNodeIndex = outerNodes.indexOf(lastFocus);\n  var correctedNodes = correctNodes(outerNodes);\n  var correctedIndex = activeElement !== undefined ? correctedNodes.indexOf(activeElement) : -1;\n  var correctedIndexDiff = correctedIndex - (lastNode ? correctedNodes.indexOf(lastNode) : activeIndex);\n  var returnFirstNode = pickFocusable(innerNodes, 0);\n  var returnLastNode = pickFocusable(innerNodes, cnt - 1);\n  // new focus\n  if (activeIndex === -1 || lastNodeInside === -1) {\n    return NEW_FOCUS;\n  }\n  // old focus\n  if (!indexDiff && lastNodeInside >= 0) {\n    return lastNodeInside;\n  }\n  // first element\n  if (activeIndex <= firstNodeIndex && isOnGuard && Math.abs(indexDiff) > 1) {\n    return returnLastNode;\n  }\n  // last element\n  if (activeIndex >= lastNodeIndex && isOnGuard && Math.abs(indexDiff) > 1) {\n    return returnFirstNode;\n  }\n  // jump out, but not on the guard\n  if (indexDiff && Math.abs(correctedIndexDiff) > 1) {\n    return lastNodeInside;\n  }\n  // focus above lock\n  if (activeIndex <= firstNodeIndex) {\n    return returnLastNode;\n  }\n  // focus below lock\n  if (activeIndex > lastNodeIndex) {\n    return returnFirstNode;\n  }\n  // index is inside tab order, but outside Lock\n  if (indexDiff) {\n    if (Math.abs(indexDiff) > 1) {\n      return lastNodeInside;\n    }\n    return (cnt + lastNodeInside + indexDiff) % cnt;\n  }\n  // do nothing\n  return undefined;\n};", "map": {"version": 3, "names": ["correctNodes", "pickFocusable", "<PERSON><PERSON><PERSON>", "NEW_FOCUS", "newFocus", "innerNodes", "outerNodes", "activeElement", "lastNode", "cnt", "length", "firstFocus", "lastFocus", "isOnGuard", "indexOf", "undefined", "activeIndex", "lastIndex", "lastNodeInside", "indexDiff", "firstNodeIndex", "lastNodeIndex", "correctedNodes", "correctedIndex", "correctedIndexDiff", "returnFirstNode", "returnLastNode", "Math", "abs"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/focus-lock/dist/es2015/solver.js"], "sourcesContent": ["import { correctNodes } from './utils/correctFocus';\nimport { pickFocusable } from './utils/firstFocus';\nimport { isGuard } from './utils/is';\nexport var NEW_FOCUS = 'NEW_FOCUS';\n/**\n * Main solver for the \"find next focus\" question\n * @param innerNodes\n * @param outerNodes\n * @param activeElement\n * @param lastNode\n * @returns {number|string|undefined|*}\n */\nexport var newFocus = function (innerNodes, outerNodes, activeElement, lastNode) {\n    var cnt = innerNodes.length;\n    var firstFocus = innerNodes[0];\n    var lastFocus = innerNodes[cnt - 1];\n    var isOnGuard = isGuard(activeElement);\n    // focus is inside\n    if (activeElement && innerNodes.indexOf(activeElement) >= 0) {\n        return undefined;\n    }\n    var activeIndex = activeElement !== undefined ? outerNodes.indexOf(activeElement) : -1;\n    var lastIndex = lastNode ? outerNodes.indexOf(lastNode) : activeIndex;\n    var lastNodeInside = lastNode ? innerNodes.indexOf(lastNode) : -1;\n    var indexDiff = activeIndex - lastIndex;\n    var firstNodeIndex = outerNodes.indexOf(firstFocus);\n    var lastNodeIndex = outerNodes.indexOf(lastFocus);\n    var correctedNodes = correctNodes(outerNodes);\n    var correctedIndex = activeElement !== undefined ? correctedNodes.indexOf(activeElement) : -1;\n    var correctedIndexDiff = correctedIndex - (lastNode ? correctedNodes.indexOf(lastNode) : activeIndex);\n    var returnFirstNode = pickFocusable(innerNodes, 0);\n    var returnLastNode = pickFocusable(innerNodes, cnt - 1);\n    // new focus\n    if (activeIndex === -1 || lastNodeInside === -1) {\n        return NEW_FOCUS;\n    }\n    // old focus\n    if (!indexDiff && lastNodeInside >= 0) {\n        return lastNodeInside;\n    }\n    // first element\n    if (activeIndex <= firstNodeIndex && isOnGuard && Math.abs(indexDiff) > 1) {\n        return returnLastNode;\n    }\n    // last element\n    if (activeIndex >= lastNodeIndex && isOnGuard && Math.abs(indexDiff) > 1) {\n        return returnFirstNode;\n    }\n    // jump out, but not on the guard\n    if (indexDiff && Math.abs(correctedIndexDiff) > 1) {\n        return lastNodeInside;\n    }\n    // focus above lock\n    if (activeIndex <= firstNodeIndex) {\n        return returnLastNode;\n    }\n    // focus below lock\n    if (activeIndex > lastNodeIndex) {\n        return returnFirstNode;\n    }\n    // index is inside tab order, but outside Lock\n    if (indexDiff) {\n        if (Math.abs(indexDiff) > 1) {\n            return lastNodeInside;\n        }\n        return (cnt + lastNodeInside + indexDiff) % cnt;\n    }\n    // do nothing\n    return undefined;\n};\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,sBAAsB;AACnD,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,OAAO,QAAQ,YAAY;AACpC,OAAO,IAAIC,SAAS,GAAG,WAAW;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,QAAQ,GAAG,SAAAA,CAAUC,UAAU,EAAEC,UAAU,EAAEC,aAAa,EAAEC,QAAQ,EAAE;EAC7E,IAAIC,GAAG,GAAGJ,UAAU,CAACK,MAAM;EAC3B,IAAIC,UAAU,GAAGN,UAAU,CAAC,CAAC,CAAC;EAC9B,IAAIO,SAAS,GAAGP,UAAU,CAACI,GAAG,GAAG,CAAC,CAAC;EACnC,IAAII,SAAS,GAAGX,OAAO,CAACK,aAAa,CAAC;EACtC;EACA,IAAIA,aAAa,IAAIF,UAAU,CAACS,OAAO,CAACP,aAAa,CAAC,IAAI,CAAC,EAAE;IACzD,OAAOQ,SAAS;EACpB;EACA,IAAIC,WAAW,GAAGT,aAAa,KAAKQ,SAAS,GAAGT,UAAU,CAACQ,OAAO,CAACP,aAAa,CAAC,GAAG,CAAC,CAAC;EACtF,IAAIU,SAAS,GAAGT,QAAQ,GAAGF,UAAU,CAACQ,OAAO,CAACN,QAAQ,CAAC,GAAGQ,WAAW;EACrE,IAAIE,cAAc,GAAGV,QAAQ,GAAGH,UAAU,CAACS,OAAO,CAACN,QAAQ,CAAC,GAAG,CAAC,CAAC;EACjE,IAAIW,SAAS,GAAGH,WAAW,GAAGC,SAAS;EACvC,IAAIG,cAAc,GAAGd,UAAU,CAACQ,OAAO,CAACH,UAAU,CAAC;EACnD,IAAIU,aAAa,GAAGf,UAAU,CAACQ,OAAO,CAACF,SAAS,CAAC;EACjD,IAAIU,cAAc,GAAGtB,YAAY,CAACM,UAAU,CAAC;EAC7C,IAAIiB,cAAc,GAAGhB,aAAa,KAAKQ,SAAS,GAAGO,cAAc,CAACR,OAAO,CAACP,aAAa,CAAC,GAAG,CAAC,CAAC;EAC7F,IAAIiB,kBAAkB,GAAGD,cAAc,IAAIf,QAAQ,GAAGc,cAAc,CAACR,OAAO,CAACN,QAAQ,CAAC,GAAGQ,WAAW,CAAC;EACrG,IAAIS,eAAe,GAAGxB,aAAa,CAACI,UAAU,EAAE,CAAC,CAAC;EAClD,IAAIqB,cAAc,GAAGzB,aAAa,CAACI,UAAU,EAAEI,GAAG,GAAG,CAAC,CAAC;EACvD;EACA,IAAIO,WAAW,KAAK,CAAC,CAAC,IAAIE,cAAc,KAAK,CAAC,CAAC,EAAE;IAC7C,OAAOf,SAAS;EACpB;EACA;EACA,IAAI,CAACgB,SAAS,IAAID,cAAc,IAAI,CAAC,EAAE;IACnC,OAAOA,cAAc;EACzB;EACA;EACA,IAAIF,WAAW,IAAII,cAAc,IAAIP,SAAS,IAAIc,IAAI,CAACC,GAAG,CAACT,SAAS,CAAC,GAAG,CAAC,EAAE;IACvE,OAAOO,cAAc;EACzB;EACA;EACA,IAAIV,WAAW,IAAIK,aAAa,IAAIR,SAAS,IAAIc,IAAI,CAACC,GAAG,CAACT,SAAS,CAAC,GAAG,CAAC,EAAE;IACtE,OAAOM,eAAe;EAC1B;EACA;EACA,IAAIN,SAAS,IAAIQ,IAAI,CAACC,GAAG,CAACJ,kBAAkB,CAAC,GAAG,CAAC,EAAE;IAC/C,OAAON,cAAc;EACzB;EACA;EACA,IAAIF,WAAW,IAAII,cAAc,EAAE;IAC/B,OAAOM,cAAc;EACzB;EACA;EACA,IAAIV,WAAW,GAAGK,aAAa,EAAE;IAC7B,OAAOI,eAAe;EAC1B;EACA;EACA,IAAIN,SAAS,EAAE;IACX,IAAIQ,IAAI,CAACC,GAAG,CAACT,SAAS,CAAC,GAAG,CAAC,EAAE;MACzB,OAAOD,cAAc;IACzB;IACA,OAAO,CAACT,GAAG,GAAGS,cAAc,GAAGC,SAAS,IAAIV,GAAG;EACnD;EACA;EACA,OAAOM,SAAS;AACpB,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}