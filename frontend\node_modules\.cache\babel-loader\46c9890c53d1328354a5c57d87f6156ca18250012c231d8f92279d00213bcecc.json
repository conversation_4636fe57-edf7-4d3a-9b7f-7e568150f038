{"ast": null, "code": "/**\n * list of the object to be considered as focusable\n */\nexport var tabbables = ['button:enabled', 'select:enabled', 'textarea:enabled', 'input:enabled',\n// elements with explicit roles will also use explicit tabindex\n// '[role=\"button\"]',\n'a[href]', 'area[href]', 'summary', 'iframe', 'object', 'embed', 'audio[controls]', 'video[controls]', '[tabindex]', '[contenteditable]', '[autofocus]'];", "map": {"version": 3, "names": ["tabbables"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/focus-lock/dist/es2015/utils/tabbables.js"], "sourcesContent": ["/**\n * list of the object to be considered as focusable\n */\nexport var tabbables = [\n    'button:enabled',\n    'select:enabled',\n    'textarea:enabled',\n    'input:enabled',\n    // elements with explicit roles will also use explicit tabindex\n    // '[role=\"button\"]',\n    'a[href]',\n    'area[href]',\n    'summary',\n    'iframe',\n    'object',\n    'embed',\n    'audio[controls]',\n    'video[controls]',\n    '[tabindex]',\n    '[contenteditable]',\n    '[autofocus]',\n];\n"], "mappings": "AAAA;AACA;AACA;AACA,OAAO,IAAIA,SAAS,GAAG,CACnB,gBAAgB,EAChB,gBAAgB,EAChB,kBAAkB,EAClB,eAAe;AACf;AACA;AACA,SAAS,EACT,YAAY,EACZ,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,iBAAiB,EACjB,iBAAiB,EACjB,YAAY,EACZ,mBAAmB,EACnB,aAAa,CAChB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}