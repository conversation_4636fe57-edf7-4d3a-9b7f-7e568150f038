{"ast": null, "code": "import { assignRef } from './assignRef';\nimport { useCallbackRef } from './useRef';\n/**\n * Merges two or more refs together providing a single interface to set their value\n * @param {RefObject|Ref} refs\n * @returns {MutableRefObject} - a new ref, which translates all changes to {refs}\n *\n * @see {@link mergeRefs} a version without buit-in memoization\n * @see https://github.com/theKashey/use-callback-ref#usemergerefs\n * @example\n * const Component = React.forwardRef((props, ref) => {\n *   const ownRef = useRef();\n *   const domRef = useMergeRefs([ref, ownRef]); // 👈 merge together\n *   return <div ref={domRef}>...</div>\n * }\n */\nexport function useMergeRefs(refs, defaultValue) {\n  return useCallbackRef(defaultValue || null, function (newValue) {\n    return refs.forEach(function (ref) {\n      return assignRef(ref, newValue);\n    });\n  });\n}", "map": {"version": 3, "names": ["assignRef", "useCallbackRef", "useMergeRefs", "refs", "defaultValue", "newValue", "for<PERSON>ach", "ref"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/use-callback-ref/dist/es2015/useMergeRef.js"], "sourcesContent": ["import { assignRef } from './assignRef';\nimport { useCallbackRef } from './useRef';\n/**\n * Merges two or more refs together providing a single interface to set their value\n * @param {RefObject|Ref} refs\n * @returns {MutableRefObject} - a new ref, which translates all changes to {refs}\n *\n * @see {@link mergeRefs} a version without buit-in memoization\n * @see https://github.com/theKashey/use-callback-ref#usemergerefs\n * @example\n * const Component = React.forwardRef((props, ref) => {\n *   const ownRef = useRef();\n *   const domRef = useMergeRefs([ref, ownRef]); // 👈 merge together\n *   return <div ref={domRef}>...</div>\n * }\n */\nexport function useMergeRefs(refs, defaultValue) {\n    return useCallbackRef(defaultValue || null, function (newValue) { return refs.forEach(function (ref) { return assignRef(ref, newValue); }); });\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,aAAa;AACvC,SAASC,cAAc,QAAQ,UAAU;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,YAAYA,CAACC,IAAI,EAAEC,YAAY,EAAE;EAC7C,OAAOH,cAAc,CAACG,YAAY,IAAI,IAAI,EAAE,UAAUC,QAAQ,EAAE;IAAE,OAAOF,IAAI,CAACG,OAAO,CAAC,UAAUC,GAAG,EAAE;MAAE,OAAOP,SAAS,CAACO,GAAG,EAAEF,QAAQ,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,CAAC;AAClJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}