{"ast": null, "code": "import { useMemo, createElement } from 'react';\nimport { useHTMLProps } from '../html/use-props.mjs';\nimport { filterProps } from './utils/filter-props.mjs';\nimport { isSVGComponent } from './utils/is-svg-component.mjs';\nimport { useSVGProps } from '../svg/use-props.mjs';\nimport { isMotionValue } from '../../value/utils/is-motion-value.mjs';\nfunction createUseRender(forwardMotionProps = false) {\n  const useRender = (Component, props, ref, {\n    latestValues\n  }, isStatic) => {\n    const useVisualProps = isSVGComponent(Component) ? useSVGProps : useHTMLProps;\n    const visualProps = useVisualProps(props, latestValues, isStatic, Component);\n    const filteredProps = filterProps(props, typeof Component === \"string\", forwardMotionProps);\n    const elementProps = {\n      ...filteredProps,\n      ...visualProps,\n      ref\n    };\n    /**\n     * If component has been handed a motion value as its child,\n     * memoise its initial value and render that. Subsequent updates\n     * will be handled by the onChange handler\n     */\n    const {\n      children\n    } = props;\n    const renderedChildren = useMemo(() => isMotionValue(children) ? children.get() : children, [children]);\n    return createElement(Component, {\n      ...elementProps,\n      children: renderedChildren\n    });\n  };\n  return useRender;\n}\nexport { createUseRender };", "map": {"version": 3, "names": ["useMemo", "createElement", "useHTMLProps", "filterProps", "isSVGComponent", "useSVGProps", "isMotionValue", "createUseRender", "forwardMotionProps", "useRender", "Component", "props", "ref", "latestValues", "isStatic", "useVisualProps", "visualProps", "filteredProps", "elementProps", "children", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "get"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/framer-motion/dist/es/render/dom/use-render.mjs"], "sourcesContent": ["import { useMemo, createElement } from 'react';\nimport { useHTMLProps } from '../html/use-props.mjs';\nimport { filterProps } from './utils/filter-props.mjs';\nimport { isSVGComponent } from './utils/is-svg-component.mjs';\nimport { useSVGProps } from '../svg/use-props.mjs';\nimport { isMotionValue } from '../../value/utils/is-motion-value.mjs';\n\nfunction createUseRender(forwardMotionProps = false) {\n    const useRender = (Component, props, ref, { latestValues }, isStatic) => {\n        const useVisualProps = isSVGComponent(Component)\n            ? useSVGProps\n            : useHTMLProps;\n        const visualProps = useVisualProps(props, latestValues, isStatic, Component);\n        const filteredProps = filterProps(props, typeof Component === \"string\", forwardMotionProps);\n        const elementProps = {\n            ...filteredProps,\n            ...visualProps,\n            ref,\n        };\n        /**\n         * If component has been handed a motion value as its child,\n         * memoise its initial value and render that. Subsequent updates\n         * will be handled by the onChange handler\n         */\n        const { children } = props;\n        const renderedChildren = useMemo(() => (isMotionValue(children) ? children.get() : children), [children]);\n        return createElement(Component, {\n            ...elementProps,\n            children: renderedChildren,\n        });\n    };\n    return useRender;\n}\n\nexport { createUseRender };\n"], "mappings": "AAAA,SAASA,OAAO,EAAEC,aAAa,QAAQ,OAAO;AAC9C,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,WAAW,QAAQ,0BAA0B;AACtD,SAASC,cAAc,QAAQ,8BAA8B;AAC7D,SAASC,WAAW,QAAQ,sBAAsB;AAClD,SAASC,aAAa,QAAQ,uCAAuC;AAErE,SAASC,eAAeA,CAACC,kBAAkB,GAAG,KAAK,EAAE;EACjD,MAAMC,SAAS,GAAGA,CAACC,SAAS,EAAEC,KAAK,EAAEC,GAAG,EAAE;IAAEC;EAAa,CAAC,EAAEC,QAAQ,KAAK;IACrE,MAAMC,cAAc,GAAGX,cAAc,CAACM,SAAS,CAAC,GAC1CL,WAAW,GACXH,YAAY;IAClB,MAAMc,WAAW,GAAGD,cAAc,CAACJ,KAAK,EAAEE,YAAY,EAAEC,QAAQ,EAAEJ,SAAS,CAAC;IAC5E,MAAMO,aAAa,GAAGd,WAAW,CAACQ,KAAK,EAAE,OAAOD,SAAS,KAAK,QAAQ,EAAEF,kBAAkB,CAAC;IAC3F,MAAMU,YAAY,GAAG;MACjB,GAAGD,aAAa;MAChB,GAAGD,WAAW;MACdJ;IACJ,CAAC;IACD;AACR;AACA;AACA;AACA;IACQ,MAAM;MAAEO;IAAS,CAAC,GAAGR,KAAK;IAC1B,MAAMS,gBAAgB,GAAGpB,OAAO,CAAC,MAAOM,aAAa,CAACa,QAAQ,CAAC,GAAGA,QAAQ,CAACE,GAAG,CAAC,CAAC,GAAGF,QAAS,EAAE,CAACA,QAAQ,CAAC,CAAC;IACzG,OAAOlB,aAAa,CAACS,SAAS,EAAE;MAC5B,GAAGQ,YAAY;MACfC,QAAQ,EAAEC;IACd,CAAC,CAAC;EACN,CAAC;EACD,OAAOX,SAAS;AACpB;AAEA,SAASF,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}