{"ast": null, "code": "import { isRadioElement } from './is';\nvar findSelectedRadio = function (node, nodes) {\n  return nodes.filter(isRadioElement).filter(function (el) {\n    return el.name === node.name;\n  }).filter(function (el) {\n    return el.checked;\n  })[0] || node;\n};\nexport var correctNode = function (node, nodes) {\n  if (isRadioElement(node) && node.name) {\n    return findSelectedRadio(node, nodes);\n  }\n  return node;\n};\n/**\n * giving a set of radio inputs keeps only selected (tabbable) ones\n * @param nodes\n */\nexport var correctNodes = function (nodes) {\n  // IE11 has no Set(array) constructor\n  var resultSet = new Set();\n  nodes.forEach(function (node) {\n    return resultSet.add(correctNode(node, nodes));\n  });\n  // using filter to support IE11\n  return nodes.filter(function (node) {\n    return resultSet.has(node);\n  });\n};", "map": {"version": 3, "names": ["isRadioElement", "findSelectedRadio", "node", "nodes", "filter", "el", "name", "checked", "correctNode", "correctNodes", "resultSet", "Set", "for<PERSON>ach", "add", "has"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/focus-lock/dist/es2015/utils/correctFocus.js"], "sourcesContent": ["import { isRadioElement } from './is';\nvar findSelectedRadio = function (node, nodes) {\n    return nodes\n        .filter(isRadioElement)\n        .filter(function (el) { return el.name === node.name; })\n        .filter(function (el) { return el.checked; })[0] || node;\n};\nexport var correctNode = function (node, nodes) {\n    if (isRadioElement(node) && node.name) {\n        return findSelectedRadio(node, nodes);\n    }\n    return node;\n};\n/**\n * giving a set of radio inputs keeps only selected (tabbable) ones\n * @param nodes\n */\nexport var correctNodes = function (nodes) {\n    // IE11 has no Set(array) constructor\n    var resultSet = new Set();\n    nodes.forEach(function (node) { return resultSet.add(correctNode(node, nodes)); });\n    // using filter to support IE11\n    return nodes.filter(function (node) { return resultSet.has(node); });\n};\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,MAAM;AACrC,IAAIC,iBAAiB,GAAG,SAAAA,CAAUC,IAAI,EAAEC,KAAK,EAAE;EAC3C,OAAOA,KAAK,CACPC,MAAM,CAACJ,cAAc,CAAC,CACtBI,MAAM,CAAC,UAAUC,EAAE,EAAE;IAAE,OAAOA,EAAE,CAACC,IAAI,KAAKJ,IAAI,CAACI,IAAI;EAAE,CAAC,CAAC,CACvDF,MAAM,CAAC,UAAUC,EAAE,EAAE;IAAE,OAAOA,EAAE,CAACE,OAAO;EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIL,IAAI;AAChE,CAAC;AACD,OAAO,IAAIM,WAAW,GAAG,SAAAA,CAAUN,IAAI,EAAEC,KAAK,EAAE;EAC5C,IAAIH,cAAc,CAACE,IAAI,CAAC,IAAIA,IAAI,CAACI,IAAI,EAAE;IACnC,OAAOL,iBAAiB,CAACC,IAAI,EAAEC,KAAK,CAAC;EACzC;EACA,OAAOD,IAAI;AACf,CAAC;AACD;AACA;AACA;AACA;AACA,OAAO,IAAIO,YAAY,GAAG,SAAAA,CAAUN,KAAK,EAAE;EACvC;EACA,IAAIO,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;EACzBR,KAAK,CAACS,OAAO,CAAC,UAAUV,IAAI,EAAE;IAAE,OAAOQ,SAAS,CAACG,GAAG,CAACL,WAAW,CAACN,IAAI,EAAEC,KAAK,CAAC,CAAC;EAAE,CAAC,CAAC;EAClF;EACA,OAAOA,KAAK,CAACC,MAAM,CAAC,UAAUF,IAAI,EAAE;IAAE,OAAOQ,SAAS,CAACI,GAAG,CAACZ,IAAI,CAAC;EAAE,CAAC,CAAC;AACxE,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}