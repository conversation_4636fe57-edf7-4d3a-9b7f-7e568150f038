{"ast": null, "code": "import { getNonce } from 'get-nonce';\nfunction makeStyleTag() {\n  if (!document) return null;\n  var tag = document.createElement('style');\n  tag.type = 'text/css';\n  var nonce = getNonce();\n  if (nonce) {\n    tag.setAttribute('nonce', nonce);\n  }\n  return tag;\n}\nfunction injectStyles(tag, css) {\n  // @ts-ignore\n  if (tag.styleSheet) {\n    // @ts-ignore\n    tag.styleSheet.cssText = css;\n  } else {\n    tag.appendChild(document.createTextNode(css));\n  }\n}\nfunction insertStyleTag(tag) {\n  var head = document.head || document.getElementsByTagName('head')[0];\n  head.appendChild(tag);\n}\nexport var stylesheetSingleton = function () {\n  var counter = 0;\n  var stylesheet = null;\n  return {\n    add: function (style) {\n      if (counter == 0) {\n        if (stylesheet = makeStyleTag()) {\n          injectStyles(stylesheet, style);\n          insertStyleTag(stylesheet);\n        }\n      }\n      counter++;\n    },\n    remove: function () {\n      counter--;\n      if (!counter && stylesheet) {\n        stylesheet.parentNode && stylesheet.parentNode.removeChild(stylesheet);\n        stylesheet = null;\n      }\n    }\n  };\n};", "map": {"version": 3, "names": ["getNonce", "makeStyleTag", "document", "tag", "createElement", "type", "nonce", "setAttribute", "injectStyles", "css", "styleSheet", "cssText", "append<PERSON><PERSON><PERSON>", "createTextNode", "insertStyleTag", "head", "getElementsByTagName", "stylesheetSingleton", "counter", "stylesheet", "add", "style", "remove", "parentNode", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/react-style-singleton/dist/es2015/singleton.js"], "sourcesContent": ["import { getNonce } from 'get-nonce';\nfunction makeStyleTag() {\n    if (!document)\n        return null;\n    var tag = document.createElement('style');\n    tag.type = 'text/css';\n    var nonce = getNonce();\n    if (nonce) {\n        tag.setAttribute('nonce', nonce);\n    }\n    return tag;\n}\nfunction injectStyles(tag, css) {\n    // @ts-ignore\n    if (tag.styleSheet) {\n        // @ts-ignore\n        tag.styleSheet.cssText = css;\n    }\n    else {\n        tag.appendChild(document.createTextNode(css));\n    }\n}\nfunction insertStyleTag(tag) {\n    var head = document.head || document.getElementsByTagName('head')[0];\n    head.appendChild(tag);\n}\nexport var stylesheetSingleton = function () {\n    var counter = 0;\n    var stylesheet = null;\n    return {\n        add: function (style) {\n            if (counter == 0) {\n                if ((stylesheet = makeStyleTag())) {\n                    injectStyles(stylesheet, style);\n                    insertStyleTag(stylesheet);\n                }\n            }\n            counter++;\n        },\n        remove: function () {\n            counter--;\n            if (!counter && stylesheet) {\n                stylesheet.parentNode && stylesheet.parentNode.removeChild(stylesheet);\n                stylesheet = null;\n            }\n        },\n    };\n};\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,WAAW;AACpC,SAASC,YAAYA,CAAA,EAAG;EACpB,IAAI,CAACC,QAAQ,EACT,OAAO,IAAI;EACf,IAAIC,GAAG,GAAGD,QAAQ,CAACE,aAAa,CAAC,OAAO,CAAC;EACzCD,GAAG,CAACE,IAAI,GAAG,UAAU;EACrB,IAAIC,KAAK,GAAGN,QAAQ,CAAC,CAAC;EACtB,IAAIM,KAAK,EAAE;IACPH,GAAG,CAACI,YAAY,CAAC,OAAO,EAAED,KAAK,CAAC;EACpC;EACA,OAAOH,GAAG;AACd;AACA,SAASK,YAAYA,CAACL,GAAG,EAAEM,GAAG,EAAE;EAC5B;EACA,IAAIN,GAAG,CAACO,UAAU,EAAE;IAChB;IACAP,GAAG,CAACO,UAAU,CAACC,OAAO,GAAGF,GAAG;EAChC,CAAC,MACI;IACDN,GAAG,CAACS,WAAW,CAACV,QAAQ,CAACW,cAAc,CAACJ,GAAG,CAAC,CAAC;EACjD;AACJ;AACA,SAASK,cAAcA,CAACX,GAAG,EAAE;EACzB,IAAIY,IAAI,GAAGb,QAAQ,CAACa,IAAI,IAAIb,QAAQ,CAACc,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;EACpED,IAAI,CAACH,WAAW,CAACT,GAAG,CAAC;AACzB;AACA,OAAO,IAAIc,mBAAmB,GAAG,SAAAA,CAAA,EAAY;EACzC,IAAIC,OAAO,GAAG,CAAC;EACf,IAAIC,UAAU,GAAG,IAAI;EACrB,OAAO;IACHC,GAAG,EAAE,SAAAA,CAAUC,KAAK,EAAE;MAClB,IAAIH,OAAO,IAAI,CAAC,EAAE;QACd,IAAKC,UAAU,GAAGlB,YAAY,CAAC,CAAC,EAAG;UAC/BO,YAAY,CAACW,UAAU,EAAEE,KAAK,CAAC;UAC/BP,cAAc,CAACK,UAAU,CAAC;QAC9B;MACJ;MACAD,OAAO,EAAE;IACb,CAAC;IACDI,MAAM,EAAE,SAAAA,CAAA,EAAY;MAChBJ,OAAO,EAAE;MACT,IAAI,CAACA,OAAO,IAAIC,UAAU,EAAE;QACxBA,UAAU,CAACI,UAAU,IAAIJ,UAAU,CAACI,UAAU,CAACC,WAAW,CAACL,UAAU,CAAC;QACtEA,UAAU,GAAG,IAAI;MACrB;IACJ;EACJ,CAAC;AACL,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}