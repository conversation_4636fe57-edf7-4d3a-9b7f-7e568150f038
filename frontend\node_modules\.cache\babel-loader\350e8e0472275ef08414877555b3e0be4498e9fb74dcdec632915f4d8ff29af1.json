{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n\n/* eslint-disable react/require-default-props */\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport * as constants from 'focus-lock/constants';\nimport { inlineProp } from './util';\nvar AutoFocusInside = function AutoFocusInside(_ref) {\n  var _ref$disabled = _ref.disabled,\n    disabled = _ref$disabled === void 0 ? false : _ref$disabled,\n    children = _ref.children,\n    _ref$className = _ref.className,\n    className = _ref$className === void 0 ? undefined : _ref$className;\n  return /*#__PURE__*/React.createElement(\"div\", _extends({}, inlineProp(constants.FOCUS_AUTO, !disabled), {\n    className: className\n  }), children);\n};\nAutoFocusInside.propTypes = process.env.NODE_ENV !== \"production\" ? {\n  children: PropTypes.node.isRequired,\n  disabled: PropTypes.bool,\n  className: PropTypes.string\n} : {};\nexport default AutoFocusInside;", "map": {"version": 3, "names": ["_extends", "React", "PropTypes", "constants", "inlineProp", "AutoFocusInside", "_ref", "_ref$disabled", "disabled", "children", "_ref$className", "className", "undefined", "createElement", "FOCUS_AUTO", "propTypes", "process", "env", "NODE_ENV", "node", "isRequired", "bool", "string"], "sources": ["C:/Users/<USER>/Downloads/RecipeHub-Recipe-Sharing-Platform/frontend/node_modules/react-focus-lock/dist/es2015/AutoFocusInside.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n\n/* eslint-disable react/require-default-props */\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport * as constants from 'focus-lock/constants';\nimport { inlineProp } from './util';\n\nvar AutoFocusInside = function AutoFocusInside(_ref) {\n  var _ref$disabled = _ref.disabled,\n      disabled = _ref$disabled === void 0 ? false : _ref$disabled,\n      children = _ref.children,\n      _ref$className = _ref.className,\n      className = _ref$className === void 0 ? undefined : _ref$className;\n  return /*#__PURE__*/React.createElement(\"div\", _extends({}, inlineProp(constants.FOCUS_AUTO, !disabled), {\n    className: className\n  }), children);\n};\n\nAutoFocusInside.propTypes = process.env.NODE_ENV !== \"production\" ? {\n  children: PropTypes.node.isRequired,\n  disabled: PropTypes.bool,\n  className: PropTypes.string\n} : {};\nexport default AutoFocusInside;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;;AAEzD;AACA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAO,KAAKC,SAAS,MAAM,sBAAsB;AACjD,SAASC,UAAU,QAAQ,QAAQ;AAEnC,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAACC,IAAI,EAAE;EACnD,IAAIC,aAAa,GAAGD,IAAI,CAACE,QAAQ;IAC7BA,QAAQ,GAAGD,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,aAAa;IAC3DE,QAAQ,GAAGH,IAAI,CAACG,QAAQ;IACxBC,cAAc,GAAGJ,IAAI,CAACK,SAAS;IAC/BA,SAAS,GAAGD,cAAc,KAAK,KAAK,CAAC,GAAGE,SAAS,GAAGF,cAAc;EACtE,OAAO,aAAaT,KAAK,CAACY,aAAa,CAAC,KAAK,EAAEb,QAAQ,CAAC,CAAC,CAAC,EAAEI,UAAU,CAACD,SAAS,CAACW,UAAU,EAAE,CAACN,QAAQ,CAAC,EAAE;IACvGG,SAAS,EAAEA;EACb,CAAC,CAAC,EAAEF,QAAQ,CAAC;AACf,CAAC;AAEDJ,eAAe,CAACU,SAAS,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG;EAClET,QAAQ,EAAEP,SAAS,CAACiB,IAAI,CAACC,UAAU;EACnCZ,QAAQ,EAAEN,SAAS,CAACmB,IAAI;EACxBV,SAAS,EAAET,SAAS,CAACoB;AACvB,CAAC,GAAG,CAAC,CAAC;AACN,eAAejB,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}