{"ast": null, "code": "// src/anatomy.ts\nfunction anatomy(name, map = {}) {\n  let called = false;\n  function assert() {\n    if (!called) {\n      called = true;\n      return;\n    }\n    throw new Error(\"[anatomy] .part(...) should only be called once. Did you mean to use .extend(...) ?\");\n  }\n  function parts(...values) {\n    assert();\n    for (const part of values) {\n      ;\n      map[part] = toPart(part);\n    }\n    return anatomy(name, map);\n  }\n  function extend(...parts2) {\n    for (const part of parts2) {\n      if (part in map) continue;\n      map[part] = toPart(part);\n    }\n    return anatomy(name, map);\n  }\n  function selectors() {\n    const value = Object.fromEntries(Object.entries(map).map(([key, part]) => [key, part.selector]));\n    return value;\n  }\n  function classnames() {\n    const value = Object.fromEntries(Object.entries(map).map(([key, part]) => [key, part.className]));\n    return value;\n  }\n  function toPart(part) {\n    const el = [\"container\", \"root\"].includes(part != null ? part : \"\") ? [name] : [name, part];\n    const attr = el.filter(Boolean).join(\"__\");\n    const className = `chakra-${attr}`;\n    const partObj = {\n      className,\n      selector: `.${className}`,\n      toString: () => part\n    };\n    return partObj;\n  }\n  const __type = {};\n  return {\n    parts,\n    toPart,\n    extend,\n    selectors,\n    classnames,\n    get keys() {\n      return Object.keys(map);\n    },\n    __type\n  };\n}\nexport { anatomy };", "map": {"version": 3, "names": ["anatomy", "name", "map", "called", "assert", "Error", "parts", "values", "part", "to<PERSON><PERSON>", "extend", "parts2", "selectors", "value", "Object", "fromEntries", "entries", "key", "selector", "classnames", "className", "el", "includes", "attr", "filter", "Boolean", "join", "partObj", "toString", "__type", "keys"], "sources": ["C:\\Users\\<USER>\\Downloads\\RecipeHub-Recipe-Sharing-Platform\\frontend\\node_modules\\@chakra-ui\\anatomy\\src\\anatomy.ts"], "sourcesContent": ["/**\n * Used to define the anatomy/parts of a component in a way that provides\n * a consistent API for `className`, css selector and `theming`.\n */\nexport function anatomy<T extends string = string>(\n  name: string,\n  map = {} as Record<T, Part>,\n): Anatomy<T> {\n  let called = false\n\n  /**\n   * Prevents user from calling `.parts` multiple times.\n   * It should only be called once.\n   */\n  function assert() {\n    if (!called) {\n      called = true\n      return\n    }\n\n    throw new Error(\n      \"[anatomy] .part(...) should only be called once. Did you mean to use .extend(...) ?\",\n    )\n  }\n\n  /**\n   * Add the core parts of the components\n   */\n  function parts<V extends string>(...values: V[]) {\n    assert()\n    for (const part of values) {\n      ;(map as any)[part] = toPart(part)\n    }\n    return anatomy(name, map) as unknown as Omit<Anatomy<V>, \"parts\">\n  }\n\n  /**\n   * Extend the component anatomy to includes new parts\n   */\n  function extend<U extends string>(...parts: U[]) {\n    for (const part of parts) {\n      if (part in map) continue\n      ;(map as any)[part] = toPart(part)\n    }\n    return anatomy(name, map) as unknown as Omit<Anatomy<T | U>, \"parts\">\n  }\n\n  /**\n   * Get all selectors for the component anatomy\n   */\n  function selectors() {\n    const value = Object.fromEntries(\n      Object.entries(map).map(([key, part]) => [key, (part as any).selector]),\n    )\n    return value as Record<T, string>\n  }\n\n  /**\n   * Get all classNames for the component anatomy\n   */\n  function classnames() {\n    const value = Object.fromEntries(\n      Object.entries(map).map(([key, part]) => [key, (part as any).className]),\n    )\n    return value as Record<T, string>\n  }\n\n  /**\n   * Creates the part object for the given part\n   */\n  function toPart(part: string) {\n    const el = [\"container\", \"root\"].includes(part ?? \"\")\n      ? [name]\n      : [name, part]\n    const attr = el.filter(Boolean).join(\"__\")\n    const className = `chakra-${attr}`\n\n    const partObj = {\n      className,\n      selector: `.${className}`,\n      toString: () => part,\n    }\n\n    return partObj as typeof partObj & string\n  }\n\n  /**\n   * Used to get the derived type of the anatomy\n   */\n  const __type = {} as T\n\n  return {\n    parts,\n    toPart,\n    extend,\n    selectors,\n    classnames,\n    get keys(): T[] {\n      return Object.keys(map) as T[]\n    },\n    __type,\n  }\n}\n\ntype Part = {\n  className: string\n  selector: string\n  toString: () => string\n}\n\ntype Anatomy<T extends string> = {\n  parts: <V extends string>(...values: V[]) => Omit<Anatomy<V>, \"parts\">\n  toPart: (part: string) => Part\n  extend: <U extends string>(...parts: U[]) => Omit<Anatomy<T | U>, \"parts\">\n  selectors: () => Record<T, string>\n  classnames: () => Record<T, string>\n  keys: T[]\n  __type: T\n}\n"], "mappings": ";AAIO,SAASA,QACdC,IAAA,EACAC,GAAA,GAAM,CAAC,GACK;EACZ,IAAIC,MAAA,GAAS;EAMb,SAASC,OAAA,EAAS;IAChB,IAAI,CAACD,MAAA,EAAQ;MACXA,MAAA,GAAS;MACT;IACF;IAEA,MAAM,IAAIE,KAAA,CACR,qFACF;EACF;EAKA,SAASC,MAAA,GAA2BC,MAAA,EAAa;IAC/CH,MAAA,CAAO;IACP,WAAWI,IAAA,IAAQD,MAAA,EAAQ;MACzB;MAAEL,GAAA,CAAYM,IAAI,IAAIC,MAAA,CAAOD,IAAI;IACnC;IACA,OAAOR,OAAA,CAAQC,IAAA,EAAMC,GAAG;EAC1B;EAKA,SAASQ,OAAA,GAA4BC,MAAA,EAAY;IAC/C,WAAWH,IAAA,IAAQG,MAAA,EAAO;MACxB,IAAIH,IAAA,IAAQN,GAAA,EAAK;MACfA,GAAA,CAAYM,IAAI,IAAIC,MAAA,CAAOD,IAAI;IACnC;IACA,OAAOR,OAAA,CAAQC,IAAA,EAAMC,GAAG;EAC1B;EAKA,SAASU,UAAA,EAAY;IACnB,MAAMC,KAAA,GAAQC,MAAA,CAAOC,WAAA,CACnBD,MAAA,CAAOE,OAAA,CAAQd,GAAG,EAAEA,GAAA,CAAI,CAAC,CAACe,GAAA,EAAKT,IAAI,MAAM,CAACS,GAAA,EAAMT,IAAA,CAAaU,QAAQ,CAAC,CACxE;IACA,OAAOL,KAAA;EACT;EAKA,SAASM,WAAA,EAAa;IACpB,MAAMN,KAAA,GAAQC,MAAA,CAAOC,WAAA,CACnBD,MAAA,CAAOE,OAAA,CAAQd,GAAG,EAAEA,GAAA,CAAI,CAAC,CAACe,GAAA,EAAKT,IAAI,MAAM,CAACS,GAAA,EAAMT,IAAA,CAAaY,SAAS,CAAC,CACzE;IACA,OAAOP,KAAA;EACT;EAKA,SAASJ,OAAOD,IAAA,EAAc;IAC5B,MAAMa,EAAA,GAAK,CAAC,aAAa,MAAM,EAAEC,QAAA,CAASd,IAAA,WAAAA,IAAA,GAAQ,EAAE,IAChD,CAACP,IAAI,IACL,CAACA,IAAA,EAAMO,IAAI;IACf,MAAMe,IAAA,GAAOF,EAAA,CAAGG,MAAA,CAAOC,OAAO,EAAEC,IAAA,CAAK,IAAI;IACzC,MAAMN,SAAA,GAAY,UAAUG,IAAI;IAEhC,MAAMI,OAAA,GAAU;MACdP,SAAA;MACAF,QAAA,EAAU,IAAIE,SAAS;MACvBQ,QAAA,EAAUA,CAAA,KAAMpB;IAClB;IAEA,OAAOmB,OAAA;EACT;EAKA,MAAME,MAAA,GAAS,CAAC;EAEhB,OAAO;IACLvB,KAAA;IACAG,MAAA;IACAC,MAAA;IACAE,SAAA;IACAO,UAAA;IACA,IAAIW,KAAA,EAAY;MACd,OAAOhB,MAAA,CAAOgB,IAAA,CAAK5B,GAAG;IACxB;IACA2B;EACF;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}